const express = require('express');
const app = express();
const path = require('path');
const compression = require('compression');
const fs = require('fs');
const crypto = require("crypto");

app.use(compression());
const env = process.env.NODE_ENV || 'development';

if(env =='development'){
	app.use('/assets', express.static('sr/public/public/assets'));
}
app.get('*', (req, res) => {
	//res.sendFile(path.join(__dirname + '/../public/public/index.html'))
		fs.readFile(path.join(__dirname + '/../sr/public/public/assets/index.html'), 'utf8', (err, data) => {
			if (err) {
				console.error(err);
				return;
			}
			//const id = crypto.randomBytes(16).toString("hex");
			const rand_nonce = crypto.randomBytes(16).toString('base64')
			//res.sendFile(path.join(__dirname + '/../public/public/index.html')) 
			res.set("Content-type", "text/html");
			res.set("Cache-Control", "no-store");
			//res.send(data.replace(/**CSP_NONCE**/g, rand_nonce));
			res.send(data.replace(/\*\*CSP_NONCE\*\*/g, rand_nonce));
		});

});
app.listen(3005, () => console.log('Example app listening on port 3005!'));
