/*! For license information please see @sentry.dc570a7c5e41710edd7e.js.LICENSE.txt */
"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@sentry"],{78484:function(t,e,n){n.d(e,{S1:function(){return ee},jp:function(){return ne}});var s=n(74379),r=n(1182),i=n(13278),a=n(3886);const o=[];function c(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;e.forEach((t=>{t.isDefaultInstance=!0})),r=Array.isArray(n)?[...e,...n]:"function"===typeof n?(0,s.lE)(n(e)):e;const i=function(t){const e={};return t.forEach((t=>{const{name:n}=t,s=e[n];s&&!s.isDefaultInstance&&t.isDefaultInstance||(e[n]=t)})),Object.keys(e).map((t=>e[t]))}(r),a=function(t,e){for(let n=0;n<t.length;n++)if(!0===e(t[n]))return n;return-1}(i,(t=>"Debug"===t.name));if(-1!==a){const[t]=i.splice(a,1);i.push(t)}return i}function l(t,e,n){if(n[e.name]=e,-1===o.indexOf(e.name)&&(e.setupOnce(i.cc,a.Gd),o.push(e.name)),t.on&&"function"===typeof e.preprocessEvent){const n=e.preprocessEvent.bind(e);t.on("preprocessEvent",((e,s)=>n(e,s,t)))}if(t.addEventProcessor&&"function"===typeof e.processEvent){const n=e.processEvent.bind(e),s=Object.assign(((e,s)=>n(e,s,t)),{id:e.name});t.addEventProcessor(s)}("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`Integration installed: ${e.name}`)}var u=n(7221),d=n(15126);const h="7";function p(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function f(t,e={}){const n="string"===typeof e?e:e.tunnel,s="string"!==typeof e&&e._metadata?e._metadata.sdk:void 0;return n||`${function(t){return`${p(t)}${t.projectId}/envelope/`}(t)}?${function(t,e){return(0,u._j)({sentry_key:t.publicKey,sentry_version:h,...e&&{sentry_client:`${e.name}/${e.version}`}})}(t,s)}`}var m=n(57291);const _=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],g=[/^.*\/healthcheck$/,/^.*\/healthy$/,/^.*\/live$/,/^.*\/ready$/,/^.*\/heartbeat$/,/^.*\/health$/,/^.*\/healthz$/];class y{static __initStatic(){this.id="InboundFilters"}constructor(t={}){this.name=y.id,this._options=t}setupOnce(t,e){}processEvent(t,e,n){const i=n.getOptions(),a=function(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:_],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[],...t.disableTransactionDefaults?[]:g],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(this._options,i);return function(t,e){if(e.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(e){}return!1}(t))return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Event dropped due to being internal Sentry Error.\nEvent: ${(0,s.jH)(t)}`),!0;if(function(t,e){if(t.type||!e||!e.length)return!1;return function(t){const e=[];t.message&&e.push(t.message);let n;try{n=t.exception.values[t.exception.values.length-1]}catch(i){}n&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`));"undefined"!==typeof __SENTRY_DEBUG__&&!__SENTRY_DEBUG__||0!==e.length||r.kg.error(`Could not extract message for event ${(0,s.jH)(t)}`);return e}(t).some((t=>(0,m.U0)(t,e)))}(t,e.ignoreErrors))return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Event dropped due to being matched by \`ignoreErrors\` option.\nEvent: ${(0,s.jH)(t)}`),!0;if(function(t,e){if("transaction"!==t.type||!e||!e.length)return!1;const n=t.transaction;return!!n&&(0,m.U0)(n,e)}(t,e.ignoreTransactions))return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.\nEvent: ${(0,s.jH)(t)}`),!0;if(function(t,e){if(!e||!e.length)return!1;const n=b(t);return!!n&&(0,m.U0)(n,e)}(t,e.denyUrls))return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Event dropped due to being matched by \`denyUrls\` option.\nEvent: ${(0,s.jH)(t)}.\nUrl: ${b(t)}`),!0;if(!function(t,e){if(!e||!e.length)return!0;const n=b(t);return!n||(0,m.U0)(n,e)}(t,e.allowUrls))return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Event dropped due to not being matched by \`allowUrls\` option.\nEvent: ${(0,s.jH)(t)}.\nUrl: ${b(t)}`),!0;return!1}(t,a)?null:t}}function b(t){try{let n;try{n=t.exception.values[0].stacktrace.frames}catch(e){}return n?function(t=[]){for(let e=t.length-1;e>=0;e--){const n=t[e];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(n):null}catch(n){return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error(`Cannot extract url for event ${(0,s.jH)(t)}`),null}}let v;y.__initStatic();class S{static __initStatic(){this.id="FunctionToString"}constructor(){this.name=S.id}setupOnce(){v=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=(0,u.HK)(this)||this;return v.apply(e,t)}}catch(t){}}}S.__initStatic();var k=n(56813),w=n(21939),E=n(29722),I=n(66353),C=n(82048),T=n(97366);class x extends Error{constructor(t,e="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=e}}var R=n(97049),A=n(8330),D=n(86274);const N="Not capturing exception because it's already been captured.";class O{constructor(t){if(this._options=t,this._integrations={},this._integrationsInitialized=!1,this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=(0,d.vK)(t.dsn):("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("No DSN provided, client will not send events."),this._dsn){const e=f(this._dsn,t);this._transport=t.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:e})}}captureException(t,e,n){if((0,s.YO)(t))return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(N));let i=e&&e.event_id;return this._process(this.eventFromException(t,e).then((t=>this._captureEvent(t,e,n))).then((t=>{i=t}))),i}captureMessage(t,e,n,s){let r=n&&n.event_id;const i=(0,I.pt)(t)?this.eventFromMessage(String(t),e,n):this.eventFromException(t,n);return this._process(i.then((t=>this._captureEvent(t,n,s))).then((t=>{r=t}))),r}captureEvent(t,e,n){if(e&&e.originalException&&(0,s.YO)(e.originalException))return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(N));let i=e&&e.event_id;return this._process(this._captureEvent(t,e,n).then((t=>{i=t}))),i}captureSession(t){"string"!==typeof t.release?("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),(0,R.CT)(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const e=this._transport;return e?this._isClientDoneProcessing(t).then((n=>e.flush(t).then((t=>n&&t)))):(0,C.WD)(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,t)))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}setupIntegrations(t){(t&&!this._integrationsInitialized||this._isEnabled()&&!this._integrationsInitialized)&&(this._integrations=function(t,e){const n={};return e.forEach((e=>{e&&l(t,e,n)})),n}(this,this._options.integrations),this._integrationsInitialized=!0)}getIntegrationById(t){return this._integrations[t]}getIntegration(t){try{return this._integrations[t.id]||null}catch(e){return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Cannot retrieve integration ${t.id} from the current Client`),null}}addIntegration(t){l(this,t,this._integrations)}sendEvent(t,e={}){this.emit("beforeSendEvent",t,e);let n=function(t,e,n,s){const r=(0,T.HY)(n),i=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,e){e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]])}(t,n&&n.sdk);const a=(0,T.Cd)(t,r,s,e);delete t.sdkProcessingMetadata;const o=[{type:i},t];return(0,T.Jd)(a,[o])}(t,this._dsn,this._options._metadata,this._options.tunnel);for(const r of e.attachments||[])n=(0,T.BO)(n,(0,T.zQ)(r,this._options.transportOptions&&this._options.transportOptions.textEncoder));const s=this._sendEnvelope(n);s&&s.then((e=>this.emit("afterSendEvent",t,e)),null)}sendSession(t){const e=function(t,e,n,s){const r=(0,T.HY)(n),i={sent_at:(new Date).toISOString(),...r&&{sdk:r},...!!s&&e&&{dsn:(0,d.RA)(e)}},a="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()];return(0,T.Jd)(i,[a])}(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(e)}recordDroppedEvent(t,e,n){if(this._options.sendClientReports){const n=`${t}:${e}`;("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`Adding outcome: "${n}"`),this._outcomes[n]=this._outcomes[n]+1||1}}on(t,e){this._hooks[t]||(this._hooks[t]=[]),this._hooks[t].push(e)}emit(t,...e){this._hooks[t]&&this._hooks[t].forEach((t=>t(...e)))}_updateSessionFromEvent(t,e){let n=!1,s=!1;const r=e.exception&&e.exception.values;if(r){s=!0;for(const t of r){const e=t.mechanism;if(e&&!1===e.handled){n=!0;break}}}const i="ok"===t.status;(i&&0===t.errors||i&&n)&&((0,R.CT)(t,{...n&&{status:"crashed"},errors:t.errors||Number(s||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new C.cW((e=>{let n=0;const s=setInterval((()=>{0==this._numProcessing?(clearInterval(s),e(!0)):(n+=1,t&&n>=t&&(clearInterval(s),e(!1)))}),1)}))}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(t,e,n){const s=this.getOptions(),r=Object.keys(this._integrations);return!e.integrations&&r.length>0&&(e.integrations=r),this.emit("preprocessEvent",t,e),(0,D.R)(s,t,e,n,this).then((t=>{if(null===t)return t;const{propagationContext:e}=t.sdkProcessingMetadata||{};if(!(t.contexts&&t.contexts.trace)&&e){const{traceId:s,spanId:r,parentSpanId:i,dsc:a}=e;t.contexts={trace:{trace_id:s,span_id:r,parent_span_id:i},...t.contexts};const o=a||(0,A._)(s,this,n);t.sdkProcessingMetadata={dynamicSamplingContext:o,...t.sdkProcessingMetadata}}return t}))}_captureEvent(t,e={},n){return this._processEvent(t,e,n).then((t=>t.event_id),(t=>{if("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__){const e=t;"log"===e.logLevel?r.kg.log(e.message):r.kg.warn(e)}}))}_processEvent(t,e,n){const s=this.getOptions(),{sampleRate:r}=s,i=U(t),a=M(t),o=t.type||"error",c=`before send for type \`${o}\``;if(a&&"number"===typeof r&&Math.random()>r)return this.recordDroppedEvent("sample_rate","error",t),(0,C.$2)(new x(`Discarding event because it's not included in the random sample (sampling rate = ${r})`,"log"));const l="replay_event"===o?"replay":o;return this._prepareEvent(t,e,n).then((n=>{if(null===n)throw this.recordDroppedEvent("event_processor",l,t),new x("An event processor returned `null`, will not send event.","log");if(e.data&&!0===e.data.__sentry__)return n;const r=function(t,e,n){const{beforeSend:s,beforeSendTransaction:r}=t;if(M(e)&&s)return s(e,n);if(U(e)&&r)return r(e,n);return e}(s,n,e);return function(t,e){const n=`${e} must return \`null\` or a valid event.`;if((0,I.J8)(t))return t.then((t=>{if(!(0,I.PO)(t)&&null!==t)throw new x(n);return t}),(t=>{throw new x(`${e} rejected with ${t}`)}));if(!(0,I.PO)(t)&&null!==t)throw new x(n);return t}(r,c)})).then((s=>{if(null===s)throw this.recordDroppedEvent("before_send",l,t),new x(`${c} returned \`null\`, will not send event.`,"log");const r=n&&n.getSession();!i&&r&&this._updateSessionFromEvent(r,s);const a=s.transaction_info;if(i&&a&&s.transaction!==t.transaction){const t="custom";s.transaction_info={...a,source:t}}return this.sendEvent(s,e),s})).then(null,(t=>{if(t instanceof x)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new x(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}_process(t){this._numProcessing++,t.then((t=>(this._numProcessing--,t)),(t=>(this._numProcessing--,t)))}_sendEnvelope(t){if(this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport)return this._transport.send(t).then(null,(t=>{("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error("Error while sending event:",t)}));("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error("Transport disabled")}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.keys(t).map((e=>{const[n,s]=e.split(":");return{reason:n,category:s,quantity:t[e]}}))}}function M(t){return void 0===t.type}function U(t){return"transaction"===t.type}var B=n(63123),G=n(52661),L=n(96400);var F=n(78643);function z(t,e){const n=P(t,e),s={type:e&&e.name,value:$(e)};return n.length&&(s.stacktrace={frames:n}),void 0===s.type&&""===s.value&&(s.value="Unrecoverable error caught"),s}function Y(t,e){return{exception:{values:[z(t,e)]}}}function P(t,e){const n=e.stacktrace||e.stack||"",s=function(t){if(t){if("number"===typeof t.framesToPop)return t.framesToPop;if(Z.test(t.message))return 1}return 0}(e);try{return t(n,s)}catch(r){}return[]}const Z=/Minified React error #\d+;/i;function $(t){const e=t&&t.message;return e?e.error&&"string"===typeof e.error.message?e.error.message:e:"No error message"}function W(t,e,n,r,i){let o;if((0,I.VW)(e)&&e.error){return Y(t,e.error)}if((0,I.TX)(e)||(0,I.fm)(e)){const i=e;if("stack"in e)o=Y(t,e);else{const e=i.name||((0,I.TX)(i)?"DOMError":"DOMException"),a=i.message?`${e}: ${i.message}`:e;o=j(t,a,n,r),(0,s.Db)(o,a)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}if((0,I.VZ)(e))return Y(t,e);if((0,I.PO)(e)||(0,I.cO)(e)){return o=function(t,e,n,s){const r=(0,a.Gd)().getClient(),i=r&&r.getOptions().normalizeDepth,o={exception:{values:[{type:(0,I.cO)(e)?e.constructor.name:s?"UnhandledRejection":"Error",value:H(e,{isUnhandledRejection:s})}]},extra:{__serialized__:(0,F.Qy)(e,i)}};if(n){const e=P(t,n);e.length&&(o.exception.values[0].stacktrace={frames:e})}return o}(t,e,n,i),(0,s.EG)(o,{synthetic:!0}),o}return o=j(t,e,n,r),(0,s.Db)(o,`${e}`,void 0),(0,s.EG)(o,{synthetic:!0}),o}function j(t,e,n,s){const r={message:e};if(s&&n){const s=P(t,n);s.length&&(r.exception={values:[{value:e,stacktrace:{frames:s}}]})}return r}function H(t,{isUnhandledRejection:e}){const n=(0,u.zf)(t),s=e?"promise rejection":"exception";if((0,I.VW)(t))return`Event \`ErrorEvent\` captured as ${s} with message \`${t.message}\``;if((0,I.cO)(t)){return`Event \`${function(t){try{const e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch(e){}}(t)}\` (type=${t.type}) captured as ${s}`}return`Object captured as ${s} with keys: ${n}`}var V=n(22272);const K=n(1846).n2;let J=0;function q(){return J>0}function X(t,e={},n){if("function"!==typeof t)return t;try{const e=t.__sentry_wrapped__;if(e)return e;if((0,u.HK)(t))return t}catch(i){return t}const r=function(){const r=Array.prototype.slice.call(arguments);try{n&&"function"===typeof n&&n.apply(this,arguments);const s=r.map((t=>X(t,e)));return t.apply(this,s)}catch(i){throw J++,setTimeout((()=>{J--})),(0,V.$e)((t=>{t.addEventProcessor((t=>(e.mechanism&&((0,s.Db)(t,void 0,void 0),(0,s.EG)(t,e.mechanism)),t.extra={...t.extra,arguments:r},t))),(0,V.Tb)(i)})),i}};try{for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(r[e]=t[e])}catch(a){}(0,u.$Q)(r,t),(0,u.xp)(t,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get:()=>t.name})}catch(a){}return r}class Q extends O{constructor(t){const e=K.SENTRY_SDK_SOURCE||(0,G.S)();t._metadata=t._metadata||{},t._metadata.sdk=t._metadata.sdk||{name:"sentry.javascript.browser",packages:[{name:`${e}:@sentry/browser`,version:B.J}],version:B.J},super(t),t.sendClientReports&&K.document&&K.document.addEventListener("visibilitychange",(()=>{"hidden"===K.document.visibilityState&&this._flushOutcomes()}))}eventFromException(t,e){return function(t,e,n,r){const i=W(t,e,n&&n.syntheticException||void 0,r);return(0,s.EG)(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),(0,C.WD)(i)}(this._options.stackParser,t,e,this._options.attachStacktrace)}eventFromMessage(t,e="info",n){return function(t,e,n="info",s,r){const i=j(t,e,s&&s.syntheticException||void 0,r);return i.level=n,s&&s.event_id&&(i.event_id=s.event_id),(0,C.WD)(i)}(this._options.stackParser,t,e,n,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled())return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("SDK not enabled, will not capture user feedback."));const e=function(t,{metadata:e,tunnel:n,dsn:s}){const r={event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&e.sdk&&{sdk:{name:e.sdk.name,version:e.sdk.version}},...!!n&&!!s&&{dsn:(0,d.RA)(s)}},i=function(t){return[{type:"user_report"},t]}(t);return(0,T.Jd)(r,[i])}(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this._sendEnvelope(e)}_prepareEvent(t,e,n){return t.platform=t.platform||"javascript",super._prepareEvent(t,e,n)}_flushOutcomes(){const t=this._clearOutcomes();if(0===t.length)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("No outcomes to send"));if(!this._dsn)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("No dsn provided, will not send outcomes"));("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("Sending outcomes:",t);const e=function(t,e,n){const s=[{type:"client_report"},{timestamp:n||(0,L.yW)(),discarded_events:t}];return(0,T.Jd)(e?{dsn:e}:{},[s])}(t,this._options.tunnel&&(0,d.RA)(this._dsn));this._sendEnvelope(e)}}var tt=n(84236);class et{static __initStatic(){this.id="GlobalHandlers"}constructor(t){this.name=et.id,this._options={onerror:!0,onunhandledrejection:!0,...t},this._installFunc={onerror:nt,onunhandledrejection:st}}setupOnce(){Error.stackTraceLimit=50;const t=this._options;for(const n in t){const s=this._installFunc[n];s&&t[n]&&(e=n,("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`Global Handler attached: ${e}`),s(),this._installFunc[n]=void 0)}var e}}function nt(){(0,E.oq)("error",(t=>{const[e,n,s]=at();if(!e.getIntegration(et))return;const{msg:r,url:i,line:a,column:o,error:c}=t;if(q()||c&&c.__sentry_own_request__)return;const l=void 0===c&&(0,I.HD)(r)?function(t,e,n,s){const r=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;let i=(0,I.VW)(t)?t.message:t,a="Error";const o=i.match(r);o&&(a=o[1],i=o[2]);const c={exception:{values:[{type:a,value:i}]}};return rt(c,e,n,s)}(r,i,a,o):rt(W(n,c||r,void 0,s,!1),i,a,o);l.level="error",it(e,c,l,"onerror")}))}function st(){(0,E.oq)("unhandledrejection",(t=>{const[e,n,s]=at();if(!e.getIntegration(et))return;let r=t;try{"reason"in t?r=t.reason:"detail"in t&&"reason"in t.detail&&(r=t.detail.reason)}catch(a){}if(q()||r&&r.__sentry_own_request__)return!0;const i=(0,I.pt)(r)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(r)}`}]}}:W(n,r,void 0,s,!0);i.level="error",it(e,r,i,"onunhandledrejection")}))}function rt(t,e,n,s){const r=t.exception=t.exception||{},i=r.values=r.values||[],a=i[0]=i[0]||{},o=a.stacktrace=a.stacktrace||{},c=o.frames=o.frames||[],l=isNaN(parseInt(s,10))?void 0:s,u=isNaN(parseInt(n,10))?void 0:n,d=(0,I.HD)(e)&&e.length>0?e:(0,tt.l4)();return 0===c.length&&c.push({colno:l,filename:d,function:"?",in_app:!0,lineno:u}),t}function it(t,e,n,r){(0,s.EG)(n,{handled:!1,type:r}),t.captureEvent(n,{originalException:e})}function at(){const t=(0,a.Gd)(),e=t.getClient(),n=e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return[t,n.stackParser,n.attachStacktrace]}et.__initStatic();const ot=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"];class ct{static __initStatic(){this.id="TryCatch"}constructor(t){this.name=ct.id,this._options={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t}}setupOnce(){this._options.setTimeout&&(0,u.hl)(K,"setTimeout",lt),this._options.setInterval&&(0,u.hl)(K,"setInterval",lt),this._options.requestAnimationFrame&&(0,u.hl)(K,"requestAnimationFrame",ut),this._options.XMLHttpRequest&&"XMLHttpRequest"in K&&(0,u.hl)(XMLHttpRequest.prototype,"send",dt);const t=this._options.eventTarget;if(t){(Array.isArray(t)?t:ot).forEach(ht)}}}function lt(t){return function(...e){const n=e[0];return e[0]=X(n,{mechanism:{data:{function:(0,k.$P)(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function ut(t){return function(e){return t.apply(this,[X(e,{mechanism:{data:{function:"requestAnimationFrame",handler:(0,k.$P)(t)},handled:!1,type:"instrument"}})])}}function dt(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in n&&"function"===typeof n[t]&&(0,u.hl)(n,t,(function(e){const n={mechanism:{data:{function:t,handler:(0,k.$P)(e)},handled:!1,type:"instrument"}},s=(0,u.HK)(e);return s&&(n.mechanism.data.handler=(0,k.$P)(s)),X(e,n)}))})),t.apply(this,e)}}function ht(t){const e=K,n=e[t]&&e[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&((0,u.hl)(n,"addEventListener",(function(e){return function(n,s,r){try{"function"===typeof s.handleEvent&&(s.handleEvent=X(s.handleEvent,{mechanism:{data:{function:"handleEvent",handler:(0,k.$P)(s),target:t},handled:!1,type:"instrument"}}))}catch(i){}return e.apply(this,[n,X(s,{mechanism:{data:{function:"addEventListener",handler:(0,k.$P)(s),target:t},handled:!1,type:"instrument"}}),r])}})),(0,u.hl)(n,"removeEventListener",(function(t){return function(e,n,s){const r=n;try{const n=r&&r.__sentry_wrapped__;n&&t.call(this,e,n,s)}catch(i){}return t.call(this,e,r,s)}})))}ct.__initStatic();const pt=["fatal","error","warning","log","info","debug"];function ft(t){return"warn"===t?"warning":pt.includes(t)?t:"log"}function mt(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",s=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:s,relative:e[5]+n+s}}const _t=1024;class gt{static __initStatic(){this.id="Breadcrumbs"}constructor(t){this.name=gt.id,this.options={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t}}setupOnce(){if(this.options.console&&(0,E.oq)("console",bt),this.options.dom&&(0,E.oq)("dom",function(t){function e(e){let n,s="object"===typeof t?t.serializeAttribute:void 0,i="object"===typeof t&&"number"===typeof t.maxStringLength?t.maxStringLength:void 0;i&&i>_t&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${i} was configured. Sentry will use 1024 instead.`),i=_t),"string"===typeof s&&(s=[s]);try{const t=e.event;n=function(t){return!!t&&!!t.target}(t)?(0,tt.Rt)(t.target,{keyAttrs:s,maxStringLength:i}):(0,tt.Rt)(t,{keyAttrs:s,maxStringLength:i})}catch(o){n="<unknown>"}0!==n.length&&(0,a.Gd)().addBreadcrumb({category:`ui.${e.name}`,message:n},{event:e.event,name:e.name,global:e.global})}return e}(this.options.dom)),this.options.xhr&&(0,E.oq)("xhr",vt),this.options.fetch&&(0,E.oq)("fetch",St),this.options.history&&(0,E.oq)("history",kt),this.options.sentry){const t=(0,a.Gd)().getClient();t&&t.on&&t.on("beforeSendEvent",yt)}}}function yt(t){(0,a.Gd)().addBreadcrumb({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:(0,s.jH)(t)},{event:t})}function bt(t){const e={category:"console",data:{arguments:t.args,logger:"console"},level:ft(t.level),message:(0,m.nK)(t.args," ")};if("assert"===t.level){if(!1!==t.args[0])return;e.message=`Assertion failed: ${(0,m.nK)(t.args.slice(1)," ")||"console.assert"}`,e.data.arguments=t.args.slice(1)}(0,a.Gd)().addBreadcrumb(e,{input:t.args,level:t.level})}function vt(t){const{startTimestamp:e,endTimestamp:n}=t,s=t.xhr[E.xU];if(!e||!n||!s)return;const{method:r,url:i,status_code:o,body:c}=s,l={method:r,url:i,status_code:o},u={xhr:t.xhr,input:c,startTimestamp:e,endTimestamp:n};(0,a.Gd)().addBreadcrumb({category:"xhr",data:l,type:"http"},u)}function St(t){const{startTimestamp:e,endTimestamp:n}=t;if(n&&(!t.fetchData.url.match(/sentry_key/)||"POST"!==t.fetchData.method))if(t.error){const s=t.fetchData,r={data:t.error,input:t.args,startTimestamp:e,endTimestamp:n};(0,a.Gd)().addBreadcrumb({category:"fetch",data:s,level:"error",type:"http"},r)}else{const s={...t.fetchData,status_code:t.response&&t.response.status},r={input:t.args,response:t.response,startTimestamp:e,endTimestamp:n};(0,a.Gd)().addBreadcrumb({category:"fetch",data:s,type:"http"},r)}}function kt(t){let e=t.from,n=t.to;const s=mt(K.location.href);let r=mt(e);const i=mt(n);r.path||(r=s),s.protocol===i.protocol&&s.host===i.host&&(n=i.relative),s.protocol===r.protocol&&s.host===r.host&&(e=r.relative),(0,a.Gd)().addBreadcrumb({category:"navigation",data:{from:e,to:n}})}function wt(t,e,n=250,s,r,i,a){if(!i.exception||!i.exception.values||!a||!(0,I.V9)(a.originalException,Error))return;const o=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;var c,l;o&&(i.exception.values=(c=Et(t,e,r,a.originalException,s,i.exception.values,o,0),l=n,c.map((t=>(t.value&&(t.value=(0,m.$G)(t.value,l)),t)))))}function Et(t,e,n,s,r,i,a,o){if(i.length>=n+1)return i;let c=[...i];if((0,I.V9)(s[r],Error)){It(a,o);const i=t(e,s[r]),l=c.length;Ct(i,r,l,o),c=Et(t,e,n,s[r],r,[i,...c],i,l)}return Array.isArray(s.errors)&&s.errors.forEach(((s,i)=>{if((0,I.V9)(s,Error)){It(a,o);const l=t(e,s),u=c.length;Ct(l,`errors[${i}]`,u,o),c=Et(t,e,n,s,r,[l,...c],l,u)}})),c}function It(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,is_exception_group:!0,exception_id:e}}function Ct(t,e,n,s){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:s}}gt.__initStatic();class Tt{static __initStatic(){this.id="LinkedErrors"}constructor(t={}){this.name=Tt.id,this._key=t.key||"cause",this._limit=t.limit||5}setupOnce(){}preprocessEvent(t,e,n){const s=n.getOptions();wt(z,s.stackParser,s.maxValueLength,this._key,this._limit,t,e)}}Tt.__initStatic();class xt{static __initStatic(){this.id="HttpContext"}constructor(){this.name=xt.id}setupOnce(){}preprocessEvent(t){if(!K.navigator&&!K.location&&!K.document)return;const e=t.request&&t.request.url||K.location&&K.location.href,{referrer:n}=K.document||{},{userAgent:s}=K.navigator||{},r={...t.request&&t.request.headers,...n&&{Referer:n},...s&&{"User-Agent":s}},i={...t.request,...e&&{url:e},headers:r};t.request=i}}xt.__initStatic();class Rt{static __initStatic(){this.id="Dedupe"}constructor(){this.name=Rt.id}setupOnce(t,e){}processEvent(t){if(t.type)return t;try{if(function(t,e){if(!e)return!1;if(function(t,e){const n=t.message,s=e.message;if(!n&&!s)return!1;if(n&&!s||!n&&s)return!1;if(n!==s)return!1;if(!Dt(t,e))return!1;if(!At(t,e))return!1;return!0}(t,e))return!0;if(function(t,e){const n=Nt(e),s=Nt(t);if(!n||!s)return!1;if(n.type!==s.type||n.value!==s.value)return!1;if(!Dt(t,e))return!1;if(!At(t,e))return!1;return!0}(t,e))return!0;return!1}(t,this._previousEvent))return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(e){}return this._previousEvent=t}}function At(t,e){let n=Ot(t),s=Ot(e);if(!n&&!s)return!0;if(n&&!s||!n&&s)return!1;if(s.length!==n.length)return!1;for(let r=0;r<s.length;r++){const t=s[r],e=n[r];if(t.filename!==e.filename||t.lineno!==e.lineno||t.colno!==e.colno||t.function!==e.function)return!1}return!0}function Dt(t,e){let n=t.fingerprint,s=e.fingerprint;if(!n&&!s)return!0;if(n&&!s||!n&&s)return!1;try{return!(n.join("")!==s.join(""))}catch(r){return!1}}function Nt(t){return t.exception&&t.exception.values&&t.exception.values[0]}function Ot(t){const e=t.exception;if(e)try{return e.values[0].stacktrace.frames}catch(n){return}}Rt.__initStatic();const Mt="?";function Ut(t,e,n,s){const r={filename:t,function:e,in_app:!0};return void 0!==n&&(r.lineno=n),void 0!==s&&(r.colno=s),r}const Bt=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Gt=/\((\S*)(?::(\d+))(?::(\d+))\)/,Lt=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Ft=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,zt=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,Yt=[[30,t=>{const e=Bt.exec(t);if(e){if(e[2]&&0===e[2].indexOf("eval")){const t=Gt.exec(e[2]);t&&(e[2]=t[1],e[3]=t[2],e[4]=t[3])}const[t,n]=Zt(e[1]||Mt,e[2]);return Ut(n,t,e[3]?+e[3]:void 0,e[4]?+e[4]:void 0)}}],[50,t=>{const e=Lt.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const t=Ft.exec(e[3]);t&&(e[1]=e[1]||"eval",e[3]=t[1],e[4]=t[2],e[5]="")}let t=e[3],n=e[1]||Mt;return[n,t]=Zt(n,t),Ut(t,n,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}],[40,t=>{const e=zt.exec(t);return e?Ut(e[2],e[1]||Mt,+e[3],e[4]?+e[4]:void 0):void 0}]],Pt=(0,k.pE)(...Yt),Zt=(t,e)=>{const n=-1!==t.indexOf("safari-extension"),s=-1!==t.indexOf("safari-web-extension");return n||s?[-1!==t.indexOf("@")?t.split("@")[0]:Mt,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]};function $t(t){const e=[];function n(t){return e.splice(e.indexOf(t),1)[0]}return{$:e,add:function(s){if(!(void 0===t||e.length<t))return(0,C.$2)(new x("Not adding Promise because buffer limit was reached."));const r=s();return-1===e.indexOf(r)&&e.push(r),r.then((()=>n(r))).then(null,(()=>n(r).then(null,(()=>{})))),r},drain:function(t){return new C.cW(((n,s)=>{let r=e.length;if(!r)return n(!0);const i=setTimeout((()=>{t&&t>0&&n(!1)}),t);e.forEach((t=>{(0,C.WD)(t).then((()=>{--r||(clearTimeout(i),n(!0))}),s)}))}))}}}const Wt=6e4;function jt(t,{statusCode:e,headers:n},s=Date.now()){const r={...t},i=n&&n["x-sentry-rate-limits"],a=n&&n["retry-after"];if(i)for(const o of i.trim().split(",")){const[t,e]=o.split(":",2),n=parseInt(t,10),i=1e3*(isNaN(n)?60:n);if(e)for(const a of e.split(";"))r[a]=s+i;else r.all=s+i}else a?r.all=s+function(t,e=Date.now()){const n=parseInt(`${t}`,10);if(!isNaN(n))return 1e3*n;const s=Date.parse(`${t}`);return isNaN(s)?Wt:s-e}(a,s):429===e&&(r.all=s+6e4);return r}const Ht=30;function Vt(t,e,n=$t(t.bufferSize||Ht)){let s={};function i(i){const a=[];if((0,T.gv)(i,((e,n)=>{const r=(0,T.mL)(n);if(function(t,e,n=Date.now()){return function(t,e){return t[e]||t.all||0}(t,e)>n}(s,r)){const s=Kt(e,n);t.recordDroppedEvent("ratelimit_backoff",r,s)}else a.push(e)})),0===a.length)return(0,C.WD)();const o=(0,T.Jd)(i[0],a),c=e=>{(0,T.gv)(o,((n,s)=>{const r=Kt(n,s);t.recordDroppedEvent(e,(0,T.mL)(s),r)}))};return n.add((()=>e({body:(0,T.V$)(o,t.textEncoder)}).then((t=>(void 0!==t.statusCode&&(t.statusCode<200||t.statusCode>=300)&&("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Sentry responded with status code ${t.statusCode} to sent event.`),s=jt(s,t),t)),(t=>{throw c("network_error"),t})))).then((t=>t),(t=>{if(t instanceof x)return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error("Skipped sending event because buffer is full."),c("queue_overflow"),(0,C.WD)();throw t}))}return i.__sentry__baseTransport__=!0,{send:i,flush:t=>n.drain(t)}}function Kt(t,e){if("event"===e||"transaction"===e)return Array.isArray(t)?t[1]:void 0}let Jt;function qt(t,e=function(){if(Jt)return Jt;if((0,w.Du)(K.fetch))return Jt=K.fetch.bind(K);const t=K.document;let e=K.fetch;if(t&&"function"===typeof t.createElement)try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n);const s=n.contentWindow;s&&s.fetch&&(e=s.fetch),t.head.removeChild(n)}catch(n){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return Jt=e.bind(K)}()){let n=0,s=0;return Vt(t,(function(r){const i=r.body.length;n+=i,s++;const a={body:r.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:n<=6e4&&s<15,...t.fetchOptions};try{return e(t.url,a).then((t=>(n-=i,s--,{statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}catch(o){return Jt=void 0,n-=i,s--,(0,C.$2)(o)}}))}const Xt=4;function Qt(t){return Vt(t,(function(e){return new C.cW(((n,s)=>{const r=new XMLHttpRequest;r.onerror=s,r.onreadystatechange=()=>{r.readyState===Xt&&n({statusCode:r.status,headers:{"x-sentry-rate-limits":r.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":r.getResponseHeader("Retry-After")}})},r.open("POST",t.url);for(const e in t.headers)Object.prototype.hasOwnProperty.call(t.headers,e)&&r.setRequestHeader(e,t.headers[e]);r.send(e.body)}))}))}const te=[new y,new S,new ct,new gt,new et,new Tt,new Rt,new xt];function ee(t={}){void 0===t.defaultIntegrations&&(t.defaultIntegrations=te),void 0===t.release&&("string"===typeof __SENTRY_RELEASE__&&(t.release=__SENTRY_RELEASE__),K.SENTRY_RELEASE&&K.SENTRY_RELEASE.id&&(t.release=K.SENTRY_RELEASE.id)),void 0===t.autoSessionTracking&&(t.autoSessionTracking=!0),void 0===t.sendClientReports&&(t.sendClientReports=!0);const e={...t,stackParser:(0,k.Sq)(t.stackParser||Pt),integrations:c(t),transport:t.transport||((0,w.Ak)()?qt:Qt)};!function(t,e){!0===e.debug&&("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__?r.kg.enable():console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle."));const n=(0,a.Gd)();n.getScope().update(e.initialScope);const s=new t(e);n.bindClient(s)}(Q,e),t.autoSessionTracking&&function(){if("undefined"===typeof K.document)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("Session tracking in non-browser environment with @sentry/browser is not supported."));const t=(0,a.Gd)();if(!t.captureSession)return;se(t),(0,E.oq)("history",(({from:t,to:e})=>{void 0!==t&&t!==e&&se((0,a.Gd)())}))}()}function ne(t={},e=(0,a.Gd)()){if(!K.document)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error("Global document not defined in showReportDialog call"));const{client:n,scope:s}=e.getStackTop(),i=t.dsn||n&&n.getDsn();if(!i)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error("DSN not configured for showReportDialog call"));s&&(t.user={...s.getUser(),...t.user}),t.eventId||(t.eventId=e.lastEventId());const o=K.document.createElement("script");o.async=!0,o.crossOrigin="anonymous",o.src=function(t,e){const n=(0,d.vK)(t);if(!n)return"";const s=`${p(n)}embed/error-page/`;let r=`dsn=${(0,d.RA)(n)}`;for(const i in e)if("dsn"!==i)if("user"===i){const t=e.user;if(!t)continue;t.name&&(r+=`&name=${encodeURIComponent(t.name)}`),t.email&&(r+=`&email=${encodeURIComponent(t.email)}`)}else r+=`&${encodeURIComponent(i)}=${encodeURIComponent(e[i])}`;return`${s}?${r}`}(i,t),t.onLoad&&(o.onload=t.onLoad);const c=K.document.head||K.document.body;c?c.appendChild(o):("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error("Not injecting report dialog. No injection point found in HTML")}function se(t){t.startSession({ignoreDuration:!0}),t.captureSession()}},83168:function(t,e,n){n.d(e,{J:function(){return s}});const s="production"},13278:function(t,e,n){n.d(e,{cc:function(){return c},fH:function(){return o},RP:function(){return l}});var s=n(1846),r=n(82048),i=n(1182),a=n(66353);function o(){return(0,s.YO)("globalEventProcessors",(()=>[]))}function c(t){o().push(t)}function l(t,e,n,s=0){return new r.cW(((r,o)=>{const c=t[s];if(null===e||"function"!==typeof c)r(e);else{const u=c({...e},n);("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&c.id&&null===u&&i.kg.log(`Event processor "${c.id}" dropped event`),(0,a.J8)(u)?u.then((e=>l(t,e,n,s+1).then(r))).then(null,o):l(t,u,n,s+1).then(r).then(null,o)}}))}},22272:function(t,e,n){n.d(e,{Tb:function(){return r},v:function(){return i},$e:function(){return a}});var s=n(3886);function r(t,e){return(0,s.Gd)().captureException(t,{captureContext:e})}function i(t,e){(0,s.Gd)().setContext(t,e)}function a(t){(0,s.Gd)().withScope(t)}},3886:function(t,e,n){n.d(e,{Gd:function(){return m},cu:function(){return p}});var s=n(74379),r=n(96400),i=n(1182),a=n(1846),o=n(83168),c=n(24334),l=n(97049);const u=4,d=100;class h{constructor(t,e=new c.s,n=u){this._version=n,this._stack=[{scope:e}],t&&this.bindClient(t)}isOlderThan(t){return this._version<t}bindClient(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){const t=c.s.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return!(this.getStack().length<=1)&&!!this.getStack().pop()}withScope(t){const e=this.pushScope();try{t(e)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(t,e){const n=this._lastEventId=e&&e.event_id?e.event_id:(0,s.DM)(),r=new Error("Sentry syntheticException");return this._withClient(((s,i)=>{s.captureException(t,{originalException:t,syntheticException:r,...e,event_id:n},i)})),n}captureMessage(t,e,n){const r=this._lastEventId=n&&n.event_id?n.event_id:(0,s.DM)(),i=new Error(t);return this._withClient(((s,a)=>{s.captureMessage(t,e,{originalException:t,syntheticException:i,...n,event_id:r},a)})),r}captureEvent(t,e){const n=e&&e.event_id?e.event_id:(0,s.DM)();return t.type||(this._lastEventId=n),this._withClient(((s,r)=>{s.captureEvent(t,{...e,event_id:n},r)})),n}lastEventId(){return this._lastEventId}addBreadcrumb(t,e){const{scope:n,client:s}=this.getStackTop();if(!s)return;const{beforeBreadcrumb:a=null,maxBreadcrumbs:o=d}=s.getOptions&&s.getOptions()||{};if(o<=0)return;const c={timestamp:(0,r.yW)(),...t},l=a?(0,i.Cf)((()=>a(c,e))):c;null!==l&&(s.emit&&s.emit("beforeAddBreadcrumb",l,e),n.addBreadcrumb(l,o))}setUser(t){this.getScope().setUser(t)}setTags(t){this.getScope().setTags(t)}setExtras(t){this.getScope().setExtras(t)}setTag(t,e){this.getScope().setTag(t,e)}setExtra(t,e){this.getScope().setExtra(t,e)}setContext(t,e){this.getScope().setContext(t,e)}configureScope(t){const{scope:e,client:n}=this.getStackTop();n&&t(e)}run(t){const e=f(this);try{t(this)}finally{f(e)}}getIntegration(t){const e=this.getClient();if(!e)return null;try{return e.getIntegration(t)}catch(n){return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&i.kg.warn(`Cannot retrieve integration ${t.id} from the current Hub`),null}}startTransaction(t,e){const n=this._callExtensionMethod("startTransaction",t,e);if(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&!n){this.getClient()?console.warn("Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':\nSentry.addTracingExtensions();\nSentry.init({...});\n"):console.warn("Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'")}return n}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this._sendSessionUpdate()}endSession(){const t=this.getStackTop().scope,e=t.getSession();e&&(0,l.RJ)(e),this._sendSessionUpdate(),t.setSession()}startSession(t){const{scope:e,client:n}=this.getStackTop(),{release:s,environment:r=o.J}=n&&n.getOptions()||{},{userAgent:i}=a.n2.navigator||{},c=(0,l.Hv)({release:s,environment:r,user:e.getUser(),...i&&{userAgent:i},...t}),u=e.getSession&&e.getSession();return u&&"ok"===u.status&&(0,l.CT)(u,{status:"exited"}),this.endSession(),e.setSession(c),c}shouldSendDefaultPii(){const t=this.getClient(),e=t&&t.getOptions();return Boolean(e&&e.sendDefaultPii)}_sendSessionUpdate(){const{scope:t,client:e}=this.getStackTop(),n=t.getSession();n&&e&&e.captureSession&&e.captureSession(n)}_withClient(t){const{scope:e,client:n}=this.getStackTop();n&&t(n,e)}_callExtensionMethod(t,...e){const n=p().__SENTRY__;if(n&&n.extensions&&"function"===typeof n.extensions[t])return n.extensions[t].apply(this,e);("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&i.kg.warn(`Extension method ${t} couldn't be found, doing nothing.`)}}function p(){return a.n2.__SENTRY__=a.n2.__SENTRY__||{extensions:{},hub:void 0},a.n2}function f(t){const e=p(),n=y(e);return b(e,t),n}function m(){const t=p();if(t.__SENTRY__&&t.__SENTRY__.acs){const e=t.__SENTRY__.acs.getCurrentHub();if(e)return e}return _(t)}function _(t=p()){return g(t)&&!y(t).isOlderThan(u)||b(t,new h),y(t)}function g(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function y(t){return(0,a.YO)("hub",(()=>new h),t)}function b(t,e){if(!t)return!1;return(t.__SENTRY__=t.__SENTRY__||{}).hub=e,!0}},24334:function(t,e,n){n.d(e,{s:function(){return c}});var s=n(66353),r=n(96400),i=n(74379),a=n(13278),o=n(97049);class c{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=l()}static clone(t){const e=new c;return t&&(e._breadcrumbs=[...t._breadcrumbs],e._tags={...t._tags},e._extra={...t._extra},e._contexts={...t._contexts},e._user=t._user,e._level=t._level,e._span=t._span,e._session=t._session,e._transactionName=t._transactionName,e._fingerprint=t._fingerprint,e._eventProcessors=[...t._eventProcessors],e._requestSession=t._requestSession,e._attachments=[...t._attachments],e._sdkProcessingMetadata={...t._sdkProcessingMetadata},e._propagationContext={...t._propagationContext}),e}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{},this._session&&(0,o.CT)(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,e){return this._tags={...this._tags,[t]:e},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,e){return this._extra={...this._extra,[t]:e},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,e){return null===e?delete this._contexts[t]:this._contexts[t]=e,this._notifyScopeListeners(),this}setSpan(t){return this._span=t,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){const t=this.getSpan();return t&&t.transaction}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;if("function"===typeof t){const e=t(this);return e instanceof c?e:this}return t instanceof c?(this._tags={...this._tags,...t._tags},this._extra={...this._extra,...t._extra},this._contexts={...this._contexts,...t._contexts},t._user&&Object.keys(t._user).length&&(this._user=t._user),t._level&&(this._level=t._level),t._fingerprint&&(this._fingerprint=t._fingerprint),t._requestSession&&(this._requestSession=t._requestSession),t._propagationContext&&(this._propagationContext=t._propagationContext)):(0,s.PO)(t)&&(this._tags={...this._tags,...t.tags},this._extra={...this._extra,...t.extra},this._contexts={...this._contexts,...t.contexts},t.user&&(this._user=t.user),t.level&&(this._level=t.level),t.fingerprint&&(this._fingerprint=t.fingerprint),t.requestSession&&(this._requestSession=t.requestSession),t.propagationContext&&(this._propagationContext=t.propagationContext)),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=l(),this}addBreadcrumb(t,e){const n="number"===typeof e?e:100;if(n<=0)return this;const s={timestamp:(0,r.yW)(),...t},i=this._breadcrumbs;return i.push(s),this._breadcrumbs=i.length>n?i.slice(-n):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}getAttachments(){return this._attachments}clearAttachments(){return this._attachments=[],this}applyToEvent(t,e={},n){if(this._extra&&Object.keys(this._extra).length&&(t.extra={...this._extra,...t.extra}),this._tags&&Object.keys(this._tags).length&&(t.tags={...this._tags,...t.tags}),this._user&&Object.keys(this._user).length&&(t.user={...this._user,...t.user}),this._contexts&&Object.keys(this._contexts).length&&(t.contexts={...this._contexts,...t.contexts}),this._level&&(t.level=this._level),this._transactionName&&(t.transaction=this._transactionName),this._span){t.contexts={trace:this._span.getTraceContext(),...t.contexts};const e=this._span.transaction;if(e){t.sdkProcessingMetadata={dynamicSamplingContext:e.getDynamicSamplingContext(),...t.sdkProcessingMetadata};const n=e.name;n&&(t.tags={transaction:n,...t.tags})}}this._applyFingerprint(t);const s=this._getBreadcrumbs(),r=[...t.breadcrumbs||[],...s];return t.breadcrumbs=r.length>0?r:void 0,t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...this._sdkProcessingMetadata,propagationContext:this._propagationContext},(0,a.RP)([...n||[],...(0,a.fH)(),...this._eventProcessors],t,e)}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}_getBreadcrumbs(){return this._breadcrumbs}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((t=>{t(this)})),this._notifyingListeners=!1)}_applyFingerprint(t){t.fingerprint=t.fingerprint?(0,i.lE)(t.fingerprint):[],this._fingerprint&&(t.fingerprint=t.fingerprint.concat(this._fingerprint)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}}function l(){return{traceId:(0,i.DM)(),spanId:(0,i.DM)().substring(16)}}},97049:function(t,e,n){n.d(e,{RJ:function(){return c},Hv:function(){return a},CT:function(){return o}});var s=n(96400),r=n(74379),i=n(7221);function a(t){const e=(0,s.ph)(),n={sid:(0,r.DM)(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return(0,i.Jr)({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"===typeof t.did||"string"===typeof t.did?`${t.did}`:void 0,duration:t.duration,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(n)};return t&&o(n,t),n}function o(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||(0,s.ph)(),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:(0,r.DM)()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"===typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"===typeof e.duration)t.duration=e.duration;else{const e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"===typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}function c(t,e){let n={};e?n={status:e}:"ok"===t.status&&(n={status:"exited"}),o(t,n)}},8330:function(t,e,n){n.d(e,{_:function(){return i}});var s=n(7221),r=n(83168);function i(t,e,n){const i=e.getOptions(),{publicKey:a}=e.getDsn()||{},{segment:o}=n&&n.getUser()||{},c=(0,s.Jr)({environment:i.environment||r.J,release:i.release,user_segment:o,public_key:a,trace_id:t});return e.emit&&e.emit("createDsc",c),c}},17119:function(t,e,n){n.d(e,{T:function(){return g},l:function(){return _}});var s=n(1182),r=n(3886),i=n(29722),a=n(12373);let o=!1;function c(){const t=(0,a.x1)();if(t){const e="internal_error";("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.log(`[Tracing] Transaction: ${e} -> Global error occured`),t.setStatus(e)}}c.tag="sentry_tracingErrorCallback";var l=n(40923),u=n(66353),d=n(11805);function h(t,e,n){if(!(0,d.z)(e))return t.sampled=!1,t;if(void 0!==t.sampled)return t.setMetadata({sampleRate:Number(t.sampled)}),t;let r;return"function"===typeof e.tracesSampler?(r=e.tracesSampler(n),t.setMetadata({sampleRate:Number(r)})):void 0!==n.parentSampled?r=n.parentSampled:"undefined"!==typeof e.tracesSampleRate?(r=e.tracesSampleRate,t.setMetadata({sampleRate:Number(r)})):(r=1,t.setMetadata({sampleRate:r})),function(t){if((0,u.i2)(t)||"number"!==typeof t&&"boolean"!==typeof t)return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(t)} of type ${JSON.stringify(typeof t)}.`),!1;if(t<0||t>1)return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${t}.`),!1;return!0}(r)?r?(t.sampled=Math.random()<r,t.sampled?(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.log(`[Tracing] starting ${t.op} transaction - ${t.name}`),t):(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(r)})`),t)):(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.log("[Tracing] Discarding transaction because "+("function"===typeof e.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),t.sampled=!1,t):(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.warn("[Tracing] Discarding transaction because of invalid sample rate."),t.sampled=!1,t)}var p=n(35586);function f(){const t=this.getScope().getSpan();return t?{"sentry-trace":t.toTraceparent()}:{}}function m(t,e){const n=this.getClient(),r=n&&n.getOptions()||{},i=r.instrumenter||"sentry",a=t.instrumenter||"sentry";i!==a&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.error(`A transaction was started with instrumenter=\`${a}\`, but the SDK is configured with the \`${i}\` instrumenter.\nThe transaction will not be sampled. Please use the ${i} instrumentation to start transactions.`),t.sampled=!1);let o=new p.Y(t,this);return o=h(o,r,{parentSampled:t.parentSampled,transactionContext:t,...e}),o.sampled&&o.initSpanRecorder(r._experiments&&r._experiments.maxSpans),n&&n.emit&&n.emit("startTransaction",o),o}function _(t,e,n,s,r,i,a){const o=t.getClient(),c=o&&o.getOptions()||{};let u=new l.io(e,t,n,s,a,r);return u=h(u,c,{parentSampled:e.parentSampled,transactionContext:e,...i}),u.sampled&&u.initSpanRecorder(c._experiments&&c._experiments.maxSpans),o&&o.emit&&o.emit("startTransaction",u),u}function g(){const t=(0,r.cu)();t.__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=m),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=f),o||(o=!0,(0,i.oq)("error",c),(0,i.oq)("unhandledrejection",c)))}},40923:function(t,e,n){n.d(e,{io:function(){return u},AT:function(){return o}});var s=n(96400),r=n(1182),i=n(36956),a=n(35586);const o={idleTimeout:1e3,finalTimeout:3e4,heartbeatInterval:5e3},c=["heartbeatFailed","idleTimeout","documentHidden","finalTimeout","externalFinish","cancelled"];class l extends i.gB{constructor(t,e,n,s){super(s),this._pushActivity=t,this._popActivity=e,this.transactionSpanId=n}add(t){t.spanId!==this.transactionSpanId&&(t.finish=e=>{t.endTimestamp="number"===typeof e?e:(0,s.ph)(),this._popActivity(t.spanId)},void 0===t.endTimestamp&&this._pushActivity(t.spanId)),super.add(t)}}class u extends a.Y{constructor(t,e,n=o.idleTimeout,s=o.finalTimeout,i=o.heartbeatInterval,a=!1){super(t,e),this._idleHub=e,this._idleTimeout=n,this._finalTimeout=s,this._heartbeatInterval=i,this._onScope=a,this.activities={},this._heartbeatCounter=0,this._finished=!1,this._idleTimeoutCanceledPermanently=!1,this._beforeFinishCallbacks=[],this._finishReason=c[4],a&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`Setting idle transaction on scope. Span ID: ${this.spanId}`),e.configureScope((t=>t.setSpan(this)))),this._restartIdleTimeout(),setTimeout((()=>{this._finished||(this.setStatus("deadline_exceeded"),this._finishReason=c[3],this.finish())}),this._finalTimeout)}finish(t=(0,s.ph)()){if(this._finished=!0,this.activities={},"ui.action.click"===this.op&&this.setTag("finishReason",this._finishReason),this.spanRecorder){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] finishing IdleTransaction",new Date(1e3*t).toISOString(),this.op);for(const e of this._beforeFinishCallbacks)e(this,t);this.spanRecorder.spans=this.spanRecorder.spans.filter((e=>{if(e.spanId===this.spanId)return!0;e.endTimestamp||(e.endTimestamp=t,e.setStatus("cancelled"),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] cancelling span since transaction ended early",JSON.stringify(e,void 0,2)));const n=e.startTimestamp<t,s=(this._finalTimeout+this._idleTimeout)/1e3,i=e.endTimestamp-this.startTimestamp<s;if("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__){const t=JSON.stringify(e,void 0,2);n?i||r.kg.log("[Tracing] discarding Span since it finished after Transaction final timeout",t):r.kg.log("[Tracing] discarding Span since it happened after Transaction was finished",t)}return n&&i})),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] flushing IdleTransaction")}else("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] No active IdleTransaction");if(this._onScope){const t=this._idleHub.getScope();t.getTransaction()===this&&t.setSpan(void 0)}return super.finish(t)}registerBeforeFinishCallback(t){this._beforeFinishCallbacks.push(t)}initSpanRecorder(t){if(!this.spanRecorder){const e=t=>{this._finished||this._pushActivity(t)},n=t=>{this._finished||this._popActivity(t)};this.spanRecorder=new l(e,n,this.spanId,t),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("Starting heartbeat"),this._pingHeartbeat()}this.spanRecorder.add(this)}cancelIdleTimeout(t,{restartOnChildSpanChange:e}={restartOnChildSpanChange:!0}){this._idleTimeoutCanceledPermanently=!1===e,this._idleTimeoutID&&(clearTimeout(this._idleTimeoutID),this._idleTimeoutID=void 0,0===Object.keys(this.activities).length&&this._idleTimeoutCanceledPermanently&&(this._finishReason=c[5],this.finish(t)))}setFinishReason(t){this._finishReason=t}_restartIdleTimeout(t){this.cancelIdleTimeout(),this._idleTimeoutID=setTimeout((()=>{this._finished||0!==Object.keys(this.activities).length||(this._finishReason=c[1],this.finish(t))}),this._idleTimeout)}_pushActivity(t){this.cancelIdleTimeout(void 0,{restartOnChildSpanChange:!this._idleTimeoutCanceledPermanently}),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`[Tracing] pushActivity: ${t}`),this.activities[t]=!0,("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] new activities count",Object.keys(this.activities).length)}_popActivity(t){if(this.activities[t]&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`[Tracing] popActivity ${t}`),delete this.activities[t],("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] new activities count",Object.keys(this.activities).length)),0===Object.keys(this.activities).length){const t=(0,s.ph)();this._idleTimeoutCanceledPermanently?(this._finishReason=c[5],this.finish(t)):this._restartIdleTimeout(t+this._idleTimeout/1e3)}}_beat(){if(this._finished)return;const t=Object.keys(this.activities).join("");t===this._prevHeartbeatString?this._heartbeatCounter++:this._heartbeatCounter=1,this._prevHeartbeatString=t,this._heartbeatCounter>=3?(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] Transaction finished because of no change for 3 heart beats"),this.setStatus("deadline_exceeded"),this._finishReason=c[0],this.finish()):this._pingHeartbeat()}_pingHeartbeat(){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`pinging Heartbeat -> current counter: ${this._heartbeatCounter}`),setTimeout((()=>{this._beat()}),this._heartbeatInterval)}}},36956:function(t,e,n){n.d(e,{Dr:function(){return l},gB:function(){return c}});var s=n(74379),r=n(96400),i=n(1182),a=n(86118),o=n(7221);class c{constructor(t=1e3){this._maxlen=t,this.spans=[]}add(t){this.spans.length>this._maxlen?t.spanRecorder=void 0:this.spans.push(t)}}class l{constructor(t={}){this.traceId=t.traceId||(0,s.DM)(),this.spanId=t.spanId||(0,s.DM)().substring(16),this.startTimestamp=t.startTimestamp||(0,r.ph)(),this.tags=t.tags||{},this.data=t.data||{},this.instrumenter=t.instrumenter||"sentry",this.origin=t.origin||"manual",t.parentSpanId&&(this.parentSpanId=t.parentSpanId),"sampled"in t&&(this.sampled=t.sampled),t.op&&(this.op=t.op),t.description&&(this.description=t.description),t.name&&(this.description=t.name),t.status&&(this.status=t.status),t.endTimestamp&&(this.endTimestamp=t.endTimestamp)}get name(){return this.description||""}set name(t){this.setName(t)}startChild(t){const e=new l({...t,parentSpanId:this.spanId,sampled:this.sampled,traceId:this.traceId});if(e.spanRecorder=this.spanRecorder,e.spanRecorder&&e.spanRecorder.add(e),e.transaction=this.transaction,("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&e.transaction){const n=`[Tracing] Starting '${t&&t.op||"< unknown op >"}' span on transaction '${e.transaction.name||"< unknown name >"}' (${e.transaction.spanId}).`;e.transaction.metadata.spanMetadata[e.spanId]={logMessage:n},i.kg.log(n)}return e}setTag(t,e){return this.tags={...this.tags,[t]:e},this}setData(t,e){return this.data={...this.data,[t]:e},this}setStatus(t){return this.status=t,this}setHttpStatus(t){this.setTag("http.status_code",String(t)),this.setData("http.response.status_code",t);const e=function(t){if(t<400&&t>=100)return"ok";if(t>=400&&t<500)switch(t){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(t>=500&&t<600)switch(t){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}(t);return"unknown_error"!==e&&this.setStatus(e),this}setName(t){this.description=t}isSuccess(){return"ok"===this.status}finish(t){if(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&this.transaction&&this.transaction.spanId!==this.spanId){const{logMessage:t}=this.transaction.metadata.spanMetadata[this.spanId];t&&i.kg.log(t.replace("Starting","Finishing"))}this.endTimestamp="number"===typeof t?t:(0,r.ph)()}toTraceparent(){return(0,a.$p)(this.traceId,this.spanId,this.sampled)}toContext(){return(0,o.Jr)({data:this.data,description:this.description,endTimestamp:this.endTimestamp,op:this.op,parentSpanId:this.parentSpanId,sampled:this.sampled,spanId:this.spanId,startTimestamp:this.startTimestamp,status:this.status,tags:this.tags,traceId:this.traceId})}updateWithContext(t){return this.data=t.data||{},this.description=t.description,this.endTimestamp=t.endTimestamp,this.op=t.op,this.parentSpanId=t.parentSpanId,this.sampled=t.sampled,this.spanId=t.spanId||this.spanId,this.startTimestamp=t.startTimestamp||this.startTimestamp,this.status=t.status,this.tags=t.tags||{},this.traceId=t.traceId||this.traceId,this}getTraceContext(){return(0,o.Jr)({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,trace_id:this.traceId})}toJSON(){return(0,o.Jr)({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,start_timestamp:this.startTimestamp,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.endTimestamp,trace_id:this.traceId,origin:this.origin})}}},35586:function(t,e,n){n.d(e,{Y:function(){return c}});var s=n(7221),r=n(1182),i=n(3886),a=n(8330),o=n(36956);class c extends o.Dr{constructor(t,e){super(t),delete this.description,this._measurements={},this._contexts={},this._hub=e||(0,i.Gd)(),this._name=t.name||"",this.metadata={source:"custom",...t.metadata,spanMetadata:{}},this._trimEnd=t.trimEnd,this.transaction=this;const n=this.metadata.dynamicSamplingContext;n&&(this._frozenDynamicSamplingContext={...n})}get name(){return this._name}set name(t){this.setName(t)}setName(t,e="custom"){this._name=t,this.metadata.source=e}initSpanRecorder(t=1e3){this.spanRecorder||(this.spanRecorder=new o.gB(t)),this.spanRecorder.add(this)}setContext(t,e){null===e?delete this._contexts[t]:this._contexts[t]=e}setMeasurement(t,e,n=""){this._measurements[t]={value:e,unit:n}}setMetadata(t){this.metadata={...this.metadata,...t}}finish(t){const e=this._finishTransaction(t);if(e)return this._hub.captureEvent(e)}toContext(){const t=super.toContext();return(0,s.Jr)({...t,name:this.name,trimEnd:this._trimEnd})}updateWithContext(t){return super.updateWithContext(t),this.name=t.name||"",this._trimEnd=t.trimEnd,this}getDynamicSamplingContext(){if(this._frozenDynamicSamplingContext)return this._frozenDynamicSamplingContext;const t=this._hub||(0,i.Gd)(),e=t.getClient();if(!e)return{};const n=t.getScope(),s=(0,a._)(this.traceId,e,n),r=this.metadata.sampleRate;void 0!==r&&(s.sample_rate=`${r}`);const o=this.metadata.source;return o&&"url"!==o&&(s.transaction=this.name),void 0!==this.sampled&&(s.sampled=String(this.sampled)),s}setHub(t){this._hub=t}_finishTransaction(t){if(void 0!==this.endTimestamp)return;this.name||(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this.name="<unlabeled transaction>"),super.finish(t);const e=this._hub.getClient();if(e&&e.emit&&e.emit("finishTransaction",this),!0!==this.sampled)return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),void(e&&e.recordDroppedEvent("sample_rate","transaction"));const n=this.spanRecorder?this.spanRecorder.spans.filter((t=>t!==this&&t.endTimestamp)):[];this._trimEnd&&n.length>0&&(this.endTimestamp=n.reduce(((t,e)=>t.endTimestamp&&e.endTimestamp?t.endTimestamp>e.endTimestamp?t:e:t)).endTimestamp);const s=this.metadata,i={contexts:{...this._contexts,trace:this.getTraceContext()},spans:n,start_timestamp:this.startTimestamp,tags:this.tags,timestamp:this.endTimestamp,transaction:this.name,type:"transaction",sdkProcessingMetadata:{...s,dynamicSamplingContext:this.getDynamicSamplingContext()},...s.source&&{transaction_info:{source:s.source}}};return Object.keys(this._measurements).length>0&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log("[Measurements] Adding measurements to transaction",JSON.stringify(this._measurements,void 0,2)),i.measurements=this._measurements),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.log(`[Tracing] Finishing ${this.op} transaction: ${this.name}.`),i}}},12373:function(t,e,n){n.d(e,{x1:function(){return r}});var s=n(3886);function r(t){return(t||(0,s.Gd)()).getScope().getTransaction()}},11805:function(t,e,n){n.d(e,{z:function(){return r}});var s=n(3886);function r(t){if("boolean"===typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const e=(0,s.Gd)().getClient(),n=t||e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}},86274:function(t,e,n){n.d(e,{R:function(){return h}});var s=n(74379),r=n(96400),i=n(82048),a=n(57291),o=n(1846),c=n(78643),l=n(83168),u=n(13278),d=n(24334);function h(t,e,n,h,f){const{normalizeDepth:m=3,normalizeMaxBreadth:_=1e3}=t,g={...e,event_id:e.event_id||n.event_id||(0,s.DM)(),timestamp:e.timestamp||(0,r.yW)()},y=n.integrations||t.integrations.map((t=>t.name));!function(t,e){const{environment:n,release:s,dist:r,maxValueLength:i=250}=e;"environment"in t||(t.environment="environment"in e?n:l.J);void 0===t.release&&void 0!==s&&(t.release=s);void 0===t.dist&&void 0!==r&&(t.dist=r);t.message&&(t.message=(0,a.$G)(t.message,i));const o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=(0,a.$G)(o.value,i));const c=t.request;c&&c.url&&(c.url=(0,a.$G)(c.url,i))}(g,t),function(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}(g,y),void 0===e.type&&function(t,e){const n=o.n2._sentryDebugIds;if(!n)return;let s;const r=p.get(e);r?s=r:(s=new Map,p.set(e,s));const i=Object.keys(n).reduce(((t,r)=>{let i;const a=s.get(r);a?i=a:(i=e(r),s.set(r,i));for(let e=i.length-1;e>=0;e--){const s=i[e];if(s.filename){t[s.filename]=n[r];break}}return t}),{});try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.filename&&(t.debug_id=i[t.filename])}))}))}catch(a){}}(g,t.stackParser);let b=h;n.captureContext&&(b=d.s.clone(b).update(n.captureContext));let v=(0,i.WD)(g);const S=f&&f.getEventProcessors?f.getEventProcessors():[];if(b){if(b.getAttachments){const t=[...n.attachments||[],...b.getAttachments()];t.length&&(n.attachments=t)}v=b.applyToEvent(g,n,S)}else v=(0,u.RP)([...S,...(0,u.fH)()],g,n);return v.then((t=>(t&&function(t){const e={};try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.debug_id&&(t.abs_path?e[t.abs_path]=t.debug_id:t.filename&&(e[t.filename]=t.debug_id),delete t.debug_id)}))}))}catch(s){}if(0===Object.keys(e).length)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const n=t.debug_meta.images;Object.keys(e).forEach((t=>{n.push({type:"sourcemap",code_file:t,debug_id:e[t]})}))}(t),"number"===typeof m&&m>0?function(t,e,n){if(!t)return null;const s={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:(0,c.Fv)(t.data,e,n)}})))},...t.user&&{user:(0,c.Fv)(t.user,e,n)},...t.contexts&&{contexts:(0,c.Fv)(t.contexts,e,n)},...t.extra&&{extra:(0,c.Fv)(t.extra,e,n)}};t.contexts&&t.contexts.trace&&s.contexts&&(s.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(s.contexts.trace.data=(0,c.Fv)(t.contexts.trace.data,e,n)));t.spans&&(s.spans=t.spans.map((t=>(t.data&&(t.data=(0,c.Fv)(t.data,e,n)),t))));return s}(t,m,_):t)))}const p=new WeakMap},63123:function(t,e,n){n.d(e,{J:function(){return s}});const s="7.74.1"},60728:function(t,e,n){n.d(e,{SV:function(){return d}});var s=n(3886),r=n(78484),i=n(22272),a=n(66353),o=n(74379),c=n(1182),l=(n(50198),n(89526));const u={componentStack:null,error:null,eventId:null};class d extends l.Component{constructor(t){super(t),d.prototype.__init.call(this),this.state=u,this._openFallbackReportDialog=!0;const e=(0,s.Gd)().getClient();e&&e.on&&t.showDialog&&(this._openFallbackReportDialog=!1,e.on("afterSendEvent",(e=>{e.type||e.event_id!==this._lastEventId||(0,r.jp)({...t.dialogOptions,eventId:this._lastEventId})})))}componentDidCatch(t,{componentStack:e}){const{beforeCapture:n,onError:s,showDialog:c,dialogOptions:u}=this.props;(0,i.$e)((d=>{if(function(t){const e=t.match(/^([^.]+)/);return null!==e&&parseInt(e[0])>=17}(l.version)&&(0,a.VZ)(t)){const n=new Error(t.message);n.name=`React ErrorBoundary ${t.name}`,n.stack=e,function(t,e){const n=new WeakMap;!function t(e,s){if(!n.has(e))return e.cause?(n.set(e,!0),t(e.cause,s)):void(e.cause=s)}(t,e)}(t,n)}n&&n(d,t,e),d.addEventProcessor((t=>((0,o.EG)(t,{handled:!1}),t)));const h=(0,i.Tb)(t,{contexts:{react:{componentStack:e}}});s&&s(t,e,h),c&&(this._lastEventId=h,this._openFallbackReportDialog&&(0,r.jp)({...u,eventId:h})),this.setState({error:t,componentStack:e,eventId:h})}))}componentDidMount(){const{onMount:t}=this.props;t&&t()}componentWillUnmount(){const{error:t,componentStack:e,eventId:n}=this.state,{onUnmount:s}=this.props;s&&s(t,e,n)}__init(){this.resetErrorBoundary=()=>{const{onReset:t}=this.props,{error:e,componentStack:n,eventId:s}=this.state;t&&t(e,n,s),this.setState(u)}}render(){const{fallback:t,children:e}=this.props,n=this.state;if(n.error){let e;return e="function"===typeof t?t({error:n.error,componentStack:n.componentStack,resetError:this.resetErrorBoundary,eventId:n.eventId}):t,l.isValidElement(e)?e:(t&&("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&c.kg.warn("fallback did not produce a valid ReactElement"),null)}return"function"===typeof e?e():e}}},66405:function(t,e,n){n.d(e,{S:function(){return i}});var s=n(63123),r=n(78484);function i(t){const e={_metadata:{},...t};e._metadata.sdk=e._metadata.sdk||{name:"sentry.javascript.react",packages:[{name:"npm:@sentry/react",version:s.J}],version:s.J},(0,r.S1)(e)}},50198:function(t,e,n){var s=n(338),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},o={};function c(t){return s.isMemo(t)?a:o[t.$$typeof]||r}o[s.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},o[s.Memo]=a;var l=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,h=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,f=Object.prototype;t.exports=function t(e,n,s){if("string"!==typeof n){if(f){var r=p(n);r&&r!==f&&t(e,r,s)}var a=u(n);d&&(a=a.concat(d(n)));for(var o=c(e),m=c(n),_=0;_<a.length;++_){var g=a[_];if(!i[g]&&(!s||!s[g])&&(!m||!m[g])&&(!o||!o[g])){var y=h(n,g);try{l(e,g,y)}catch(b){}}}}return e}},33633:function(t,e,n){function s(t,e){const n=e.getClient(),s=n&&n.getDsn(),i=n&&n.getOptions().tunnel;return function(t,e){return!!e&&t.includes(e.host)}(t,s)||function(t,e){if(!e)return!1;return r(t)===r(e)}(t,i)}function r(t){return"/"===t[t.length-1]?t.slice(0,-1):t}n.d(e,{U:function(){return As}});var i=n(3886),a=n(13278),o=n(86274),c=n(22272),l=n(1846),u=n(78643),d=n(7221),h=n(84236),p=n(1182),f=n(74379),m=n(29722),_=n(57291),g=n(96400),y=n(97366),b=n(64305);const v=l.n2,S="sentryReplaySession",k="replay_event",w="Unable to send Replay",E=15e4,I=5e3,C=2e7,T=36e5;var x;function R(t){const e=null===t||void 0===t?void 0:t.host;return Boolean((null===e||void 0===e?void 0:e.shadowRoot)===t)}function A(t){return"[object ShadowRoot]"===Object.prototype.toString.call(t)}function D(t){try{const n=t.rules||t.cssRules;return n?((e=Array.from(n,N).join("")).includes(" background-clip: text;")&&!e.includes(" -webkit-background-clip: text;")&&(e=e.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),e):null}catch(n){return null}var e}function N(t){let e;if(function(t){return"styleSheet"in t}(t))try{e=D(t.styleSheet)||function(t){const{cssText:e}=t;if(e.split('"').length<3)return e;const n=["@import",`url(${JSON.stringify(t.href)})`];return""===t.layerName?n.push("layer"):t.layerName&&n.push(`layer(${t.layerName})`),t.supportsText&&n.push(`supports(${t.supportsText})`),t.media.length&&n.push(t.media.mediaText),n.join(" ")+";"}(t)}catch(n){}else if(function(t){return"selectorText"in t}(t)&&t.selectorText.includes(":"))return function(t){const e=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm;return t.replace(e,"$1\\$2")}(t.cssText);return e||t.cssText}!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(x||(x={}));class O{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(t){var e;if(!t)return-1;const n=null===(e=this.getMeta(t))||void 0===e?void 0:e.id;return null!==n&&void 0!==n?n:-1}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){const e=this.getId(t);this.idNodeMap.delete(e),t.childNodes&&t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){const n=e.id;this.idNodeMap.set(n,t),this.nodeMetaMap.set(t,e)}replace(t,e){const n=this.getNode(t);if(n){const t=this.nodeMetaMap.get(n);t&&this.nodeMetaMap.set(e,t)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function M({maskInputOptions:t,tagName:e,type:n}){return"OPTION"===e&&(e="SELECT"),Boolean(t[e.toLowerCase()]||n&&t[n]||"password"===n||"INPUT"===e&&!n&&t.text)}function U({isMasked:t,element:e,value:n,maskInputFn:s}){let r=n||"";return t?(s&&(r=s(r,e)),"*".repeat(r.length)):r}function B(t){return t.toLowerCase()}function G(t){return t.toUpperCase()}const L="__rrweb_original__";function F(t){const e=t.type;return t.hasAttribute("data-rr-is-password")?"password":e?B(e):null}function z(t,e,n){return"INPUT"!==e||"radio"!==n&&"checkbox"!==n?t.value:t.getAttribute("value")||""}let Y=1;const P=new RegExp("[^a-z0-9-_:]"),Z=-2;function $(){return Y++}let W,j;const H=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,V=/^(?:[a-z+]+:)?\/\//i,K=/^www\..*/i,J=/^(data:)([^,]*),(.*)/i;function q(t,e){return(t||"").replace(H,((t,n,s,r,i,a)=>{const o=s||i||a,c=n||r||"";if(!o)return t;if(V.test(o)||K.test(o))return`url(${c}${o}${c})`;if(J.test(o))return`url(${c}${o}${c})`;if("/"===o[0])return`url(${c}${function(t){let e="";return e=t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0],e=e.split("?")[0],e}(e)+o}${c})`;const l=e.split("/"),u=o.split("/");l.pop();for(const e of u)"."!==e&&(".."===e?l.pop():l.push(e));return`url(${c}${l.join("/")}${c})`}))}const X=/^[^ \t\n\r\u000c]+/,Q=/^[, \t\n\r\u000c]+/;function tt(t,e){if(!e||""===e.trim())return e;const n=t.createElement("a");return n.href=e,n.href}function et(t){return Boolean("svg"===t.tagName||t.ownerSVGElement)}function nt(){const t=document.createElement("a");return t.href="",t.href}function st(t,e,n,s,r,i){return s?"src"===n||"href"===n&&("use"!==e||"#"!==s[0])||"xlink:href"===n&&"#"!==s[0]?tt(t,s):"background"!==n||"table"!==e&&"td"!==e&&"th"!==e?"srcset"===n?function(t,e){if(""===e.trim())return e;let n=0;function s(t){let s;const r=t.exec(e.substring(n));return r?(s=r[0],n+=s.length,s):""}const r=[];for(;s(Q),!(n>=e.length);){let i=s(X);if(","===i.slice(-1))i=tt(t,i.substring(0,i.length-1)),r.push(i);else{let s="";i=tt(t,i);let a=!1;for(;;){const t=e.charAt(n);if(""===t){r.push((i+s).trim());break}if(a)")"===t&&(a=!1);else{if(","===t){n+=1,r.push((i+s).trim());break}"("===t&&(a=!0)}s+=t,n+=1}}}return r.join(", ")}(t,s):"style"===n?q(s,nt()):"object"===e&&"data"===n?tt(t,s):"function"===typeof i?i(n,s,r):s:tt(t,s):s}function rt(t,e,n){return("video"===t||"audio"===t)&&"autoplay"===e}function it(t,e,n=1/0,s=0){return t?t.nodeType!==t.ELEMENT_NODE||s>n?-1:e(t)?s:it(t.parentNode,e,n,s+1):-1}function at(t,e){return n=>{const s=n;if(null===s)return!1;if(t)if("string"===typeof t){if(s.matches(`.${t}`))return!0}else if(function(t,e){for(let n=t.classList.length;n--;){const s=t.classList[n];if(e.test(s))return!0}return!1}(s,t))return!0;return!(!e||!s.matches(e))}}function ot(t,e,n,s,r,i){try{const a=t.nodeType===t.ELEMENT_NODE?t:t.parentElement;if(null===a)return!1;let o=-1,c=-1;if(i){if(c=it(a,at(s,r)),c<0)return!0;o=it(a,at(e,n),c>=0?c:1/0)}else{if(o=it(a,at(e,n)),o<0)return!1;c=it(a,at(s,r),o>=0?o:1/0)}return o>=0?!(c>=0)||o<=c:!(c>=0)&&!!i}catch(a){}return!!i}function ct(t,e){const{doc:n,mirror:s,blockClass:r,blockSelector:i,unblockSelector:a,maskAllText:o,maskAttributeFn:c,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,inlineStylesheet:p,maskInputOptions:f={},maskTextFn:m,maskInputFn:_,dataURLOptions:g={},inlineImages:y,recordCanvas:b,keepIframeSrcFn:v,newlyAddedElement:S=!1}=e,k=function(t,e){if(!e.hasNode(t))return;const n=e.getId(t);return 1===n?void 0:n}(n,s);switch(t.nodeType){case t.DOCUMENT_NODE:return"CSS1Compat"!==t.compatMode?{type:x.Document,childNodes:[],compatMode:t.compatMode}:{type:x.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:x.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:k};case t.ELEMENT_NODE:return function(t,e){const{doc:n,blockClass:s,blockSelector:r,unblockSelector:i,inlineStylesheet:a,maskInputOptions:o={},maskAttributeFn:c,maskInputFn:l,dataURLOptions:u={},inlineImages:d,recordCanvas:h,keepIframeSrcFn:p,newlyAddedElement:f=!1,rootId:m,maskAllText:_,maskTextClass:g,unmaskTextClass:y,maskTextSelector:b,unmaskTextSelector:v}=e,S=function(t,e,n,s){try{if(s&&t.matches(s))return!1;if("string"===typeof e){if(t.classList.contains(e))return!0}else for(let n=t.classList.length;n--;){const s=t.classList[n];if(e.test(s))return!0}if(n)return t.matches(n)}catch(r){}return!1}(t,s,r,i),k=function(t){if(t instanceof HTMLFormElement)return"form";const e=B(t.tagName);return P.test(e)?"div":e}(t);let w={};const E=t.attributes.length;for(let T=0;T<E;T++){const e=t.attributes[T];rt(k,e.name,e.value)||(w[e.name]=st(n,k,B(e.name),e.value,t,c))}if("link"===k&&a){const e=Array.from(n.styleSheets).find((e=>e.href===t.href));let s=null;e&&(s=D(e)),s&&(delete w.rel,delete w.href,w._cssText=q(s,e.href))}if("style"===k&&t.sheet&&!(t.innerText||t.textContent||"").trim().length){const e=D(t.sheet);e&&(w._cssText=q(e,nt()))}if("input"===k||"textarea"===k||"select"===k||"option"===k){const e=t,n=F(e),s=z(e,G(k),n),r=e.checked;if("submit"!==n&&"button"!==n&&s){const t=ot(e,g,b,y,v,M({type:n,tagName:G(k),maskInputOptions:o}));w.value=U({isMasked:t,element:e,value:s,maskInputFn:l})}r&&(w.checked=r)}"option"===k&&(t.selected&&!o.select?w.selected=!0:delete w.selected);if("canvas"===k&&h)if("2d"===t.__context)(function(t){const e=t.getContext("2d");if(!e)return!0;for(let n=0;n<t.width;n+=50)for(let s=0;s<t.height;s+=50){const r=e.getImageData,i=L in r?r[L]:r;if(new Uint32Array(i.call(e,n,s,Math.min(50,t.width-n),Math.min(50,t.height-s)).data.buffer).some((t=>0!==t)))return!1}return!0})(t)||(w.rr_dataURL=t.toDataURL(u.type,u.quality));else if(!("__context"in t)){const e=t.toDataURL(u.type,u.quality),n=document.createElement("canvas");n.width=t.width,n.height=t.height;e!==n.toDataURL(u.type,u.quality)&&(w.rr_dataURL=e)}if("img"===k&&d){W||(W=n.createElement("canvas"),j=W.getContext("2d"));const e=t,s=e.crossOrigin;e.crossOrigin="anonymous";const r=()=>{e.removeEventListener("load",r);try{W.width=e.naturalWidth,W.height=e.naturalHeight,j.drawImage(e,0,0),w.rr_dataURL=W.toDataURL(u.type,u.quality)}catch(t){console.warn(`Cannot inline img src=${e.currentSrc}! Error: ${t}`)}s?w.crossOrigin=s:e.removeAttribute("crossorigin")};e.complete&&0!==e.naturalWidth?r():e.addEventListener("load",r)}"audio"!==k&&"video"!==k||(w.rr_mediaState=t.paused?"paused":"played",w.rr_mediaCurrentTime=t.currentTime);f||(t.scrollLeft&&(w.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(w.rr_scrollTop=t.scrollTop));if(S){const{width:e,height:n}=t.getBoundingClientRect();w={class:w.class,rr_width:`${e}px`,rr_height:`${n}px`}}"iframe"!==k||p(w.src)||(t.contentDocument||(w.rr_src=w.src),delete w.src);let I;try{customElements.get(k)&&(I=!0)}catch(C){}return{type:x.Element,tagName:k,attributes:w,childNodes:[],isSVG:et(t)||void 0,needBlock:S,rootId:m,isCustom:I}}(t,{doc:n,blockClass:r,blockSelector:i,unblockSelector:a,inlineStylesheet:p,maskAttributeFn:c,maskInputOptions:f,maskInputFn:_,dataURLOptions:g,inlineImages:y,recordCanvas:b,keepIframeSrcFn:v,newlyAddedElement:S,rootId:k,maskAllText:o,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h});case t.TEXT_NODE:return function(t,e){var n;const{maskAllText:s,maskTextClass:r,unmaskTextClass:i,maskTextSelector:a,unmaskTextSelector:o,maskTextFn:c,maskInputOptions:l,maskInputFn:u,rootId:d}=e,h=t.parentNode&&t.parentNode.tagName;let p=t.textContent;const f="STYLE"===h||void 0,m="SCRIPT"===h||void 0,_="TEXTAREA"===h||void 0;if(f&&p){try{t.nextSibling||t.previousSibling||(null===(n=t.parentNode.sheet)||void 0===n?void 0:n.cssRules)&&(p=D(t.parentNode.sheet))}catch(y){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${y}`,t)}p=q(p,nt())}m&&(p="SCRIPT_PLACEHOLDER");const g=ot(t,r,a,i,o,s);f||m||_||!p||!g||(p=c?c(p):p.replace(/[\S]/g,"*"));_&&p&&(l.textarea||g)&&(p=u?u(p,t.parentNode):p.replace(/[\S]/g,"*"));if("OPTION"===h&&p){p=U({isMasked:ot(t,r,a,i,o,M({type:null,tagName:h,maskInputOptions:l})),element:t,value:p,maskInputFn:u})}return{type:x.Text,textContent:p||"",isStyle:f,rootId:d}}(t,{maskAllText:o,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,maskTextFn:m,maskInputOptions:f,maskInputFn:_,rootId:k});case t.CDATA_SECTION_NODE:return{type:x.CDATA,textContent:"",rootId:k};case t.COMMENT_NODE:return{type:x.Comment,textContent:t.textContent||"",rootId:k};default:return!1}}function lt(t){return void 0===t||null===t?"":t.toLowerCase()}function ut(t,e){const{doc:n,mirror:s,blockClass:r,blockSelector:i,unblockSelector:a,maskAllText:o,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:h=!1,inlineStylesheet:p=!0,maskInputOptions:f={},maskAttributeFn:m,maskTextFn:_,maskInputFn:g,slimDOMOptions:y,dataURLOptions:b={},inlineImages:v=!1,recordCanvas:S=!1,onSerialize:k,onIframeLoad:w,iframeLoadTimeout:E=5e3,onStylesheetLoad:I,stylesheetLoadTimeout:C=5e3,keepIframeSrcFn:T=(()=>!1),newlyAddedElement:D=!1}=e;let{preserveWhiteSpace:N=!0}=e;const O=ct(t,{doc:n,mirror:s,blockClass:r,blockSelector:i,maskAllText:o,unblockSelector:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,inlineStylesheet:p,maskInputOptions:f,maskAttributeFn:m,maskTextFn:_,maskInputFn:g,dataURLOptions:b,inlineImages:v,recordCanvas:S,keepIframeSrcFn:T,newlyAddedElement:D});if(!O)return console.warn(t,"not serialized"),null;let M;M=s.hasNode(t)?s.getId(t):!function(t,e){if(e.comment&&t.type===x.Comment)return!0;if(t.type===x.Element){if(e.script&&("script"===t.tagName||"link"===t.tagName&&("preload"===t.attributes.rel||"modulepreload"===t.attributes.rel)&&"script"===t.attributes.as||"link"===t.tagName&&"prefetch"===t.attributes.rel&&"string"===typeof t.attributes.href&&t.attributes.href.endsWith(".js")))return!0;if(e.headFavicon&&("link"===t.tagName&&"shortcut icon"===t.attributes.rel||"meta"===t.tagName&&(lt(t.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===lt(t.attributes.name)||"icon"===lt(t.attributes.rel)||"apple-touch-icon"===lt(t.attributes.rel)||"shortcut icon"===lt(t.attributes.rel))))return!0;if("meta"===t.tagName){if(e.headMetaDescKeywords&&lt(t.attributes.name).match(/^description|keywords$/))return!0;if(e.headMetaSocial&&(lt(t.attributes.property).match(/^(og|twitter|fb):/)||lt(t.attributes.name).match(/^(og|twitter):/)||"pinterest"===lt(t.attributes.name)))return!0;if(e.headMetaRobots&&("robots"===lt(t.attributes.name)||"googlebot"===lt(t.attributes.name)||"bingbot"===lt(t.attributes.name)))return!0;if(e.headMetaHttpEquiv&&void 0!==t.attributes["http-equiv"])return!0;if(e.headMetaAuthorship&&("author"===lt(t.attributes.name)||"generator"===lt(t.attributes.name)||"framework"===lt(t.attributes.name)||"publisher"===lt(t.attributes.name)||"progid"===lt(t.attributes.name)||lt(t.attributes.property).match(/^article:/)||lt(t.attributes.property).match(/^product:/)))return!0;if(e.headMetaVerification&&("google-site-verification"===lt(t.attributes.name)||"yandex-verification"===lt(t.attributes.name)||"csrf-token"===lt(t.attributes.name)||"p:domain_verify"===lt(t.attributes.name)||"verify-v1"===lt(t.attributes.name)||"verification"===lt(t.attributes.name)||"shopify-checkout-api-token"===lt(t.attributes.name)))return!0}}return!1}(O,y)&&(N||O.type!==x.Text||O.isStyle||O.textContent.replace(/^\s+|\s+$/gm,"").length)?$():Z;const U=Object.assign(O,{id:M});if(s.add(t,U),M===Z)return null;k&&k(t);let B=!h;if(U.type===x.Element){B=B&&!U.needBlock,delete U.needBlock;const e=t.shadowRoot;e&&A(e)&&(U.isShadowHost=!0)}if((U.type===x.Document||U.type===x.Element)&&B){y.headWhitespace&&U.type===x.Element&&"head"===U.tagName&&(N=!1);const e={doc:n,mirror:s,blockClass:r,blockSelector:i,maskAllText:o,unblockSelector:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:h,inlineStylesheet:p,maskInputOptions:f,maskAttributeFn:m,maskTextFn:_,maskInputFn:g,slimDOMOptions:y,dataURLOptions:b,inlineImages:v,recordCanvas:S,preserveWhiteSpace:N,onSerialize:k,onIframeLoad:w,iframeLoadTimeout:E,onStylesheetLoad:I,stylesheetLoadTimeout:C,keepIframeSrcFn:T};for(const n of Array.from(t.childNodes)){const t=ut(n,e);t&&U.childNodes.push(t)}if(function(t){return t.nodeType===t.ELEMENT_NODE}(t)&&t.shadowRoot)for(const n of Array.from(t.shadowRoot.childNodes)){const s=ut(n,e);s&&(A(t.shadowRoot)&&(s.isShadow=!0),U.childNodes.push(s))}}return t.parentNode&&R(t.parentNode)&&A(t.parentNode)&&(U.isShadow=!0),U.type===x.Element&&"iframe"===U.tagName&&function(t,e,n){const s=t.contentWindow;if(!s)return;let r,i=!1;try{r=s.document.readyState}catch(o){return}if("complete"!==r){const s=setTimeout((()=>{i||(e(),i=!0)}),n);return void t.addEventListener("load",(()=>{clearTimeout(s),i=!0,e()}))}const a="about:blank";if(s.location.href!==a||t.src===a||""===t.src)return setTimeout(e,0),t.addEventListener("load",e);t.addEventListener("load",e)}(t,(()=>{const e=t.contentDocument;if(e&&w){const n=ut(e,{doc:e,mirror:s,blockClass:r,blockSelector:i,unblockSelector:a,maskAllText:o,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:p,maskInputOptions:f,maskAttributeFn:m,maskTextFn:_,maskInputFn:g,slimDOMOptions:y,dataURLOptions:b,inlineImages:v,recordCanvas:S,preserveWhiteSpace:N,onSerialize:k,onIframeLoad:w,iframeLoadTimeout:E,onStylesheetLoad:I,stylesheetLoadTimeout:C,keepIframeSrcFn:T});n&&w(t,n)}}),E),U.type===x.Element&&"link"===U.tagName&&"stylesheet"===U.attributes.rel&&function(t,e,n){let s,r=!1;try{s=t.sheet}catch(a){return}if(s)return;const i=setTimeout((()=>{r||(e(),r=!0)}),n);t.addEventListener("load",(()=>{clearTimeout(i),r=!0,e()}))}(t,(()=>{if(I){const e=ut(t,{doc:n,mirror:s,blockClass:r,blockSelector:i,unblockSelector:a,maskAllText:o,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:p,maskInputOptions:f,maskAttributeFn:m,maskTextFn:_,maskInputFn:g,slimDOMOptions:y,dataURLOptions:b,inlineImages:v,recordCanvas:S,preserveWhiteSpace:N,onSerialize:k,onIframeLoad:w,iframeLoadTimeout:E,onStylesheetLoad:I,stylesheetLoadTimeout:C,keepIframeSrcFn:T});e&&I(t,e)}}),C),U}function dt(t,e,n=document){const s={capture:!0,passive:!0};return n.addEventListener(t,e,s),()=>n.removeEventListener(t,e,s)}const ht="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let pt={map:{},getId:()=>(console.error(ht),-1),getNode:()=>(console.error(ht),null),removeNodeFromMap(){console.error(ht)},has:()=>(console.error(ht),!1),reset(){console.error(ht)}};function ft(t,e,n={}){let s=null,r=0;return function(...i){const a=Date.now();r||!1!==n.leading||(r=a);const o=e-(a-r),c=this;o<=0||o>e?(s&&(clearTimeout(s),s=null),r=a,t.apply(c,i)):s||!1===n.trailing||(s=setTimeout((()=>{r=!1===n.leading?0:Date.now(),s=null,t.apply(c,i)}),o))}}function mt(t,e,n,s,r=window){const i=r.Object.getOwnPropertyDescriptor(t,e);return r.Object.defineProperty(t,e,s?n:{set(t){setTimeout((()=>{n.set.call(this,t)}),0),i&&i.set&&i.set.call(this,t)}}),()=>mt(t,e,i||{},!0)}function _t(t,e,n){try{if(!(e in t))return()=>{};const s=t[e],r=n(s);return"function"===typeof r&&(r.prototype=r.prototype||{},Object.defineProperties(r,{__rrweb_original__:{enumerable:!1,value:s}})),t[e]=r,()=>{t[e]=s}}catch(s){return()=>{}}}"undefined"!==typeof window&&window.Proxy&&window.Reflect&&(pt=new Proxy(pt,{get:(t,e,n)=>("map"===e&&console.error(ht),Reflect.get(t,e,n))}));let gt=Date.now;function yt(t){var e,n,s,r,i,a;const o=t.document;return{left:o.scrollingElement?o.scrollingElement.scrollLeft:void 0!==t.pageXOffset?t.pageXOffset:(null===o||void 0===o?void 0:o.documentElement.scrollLeft)||(null===(n=null===(e=null===o||void 0===o?void 0:o.body)||void 0===e?void 0:e.parentElement)||void 0===n?void 0:n.scrollLeft)||(null===(s=null===o||void 0===o?void 0:o.body)||void 0===s?void 0:s.scrollLeft)||0,top:o.scrollingElement?o.scrollingElement.scrollTop:void 0!==t.pageYOffset?t.pageYOffset:(null===o||void 0===o?void 0:o.documentElement.scrollTop)||(null===(i=null===(r=null===o||void 0===o?void 0:o.body)||void 0===r?void 0:r.parentElement)||void 0===i?void 0:i.scrollTop)||(null===(a=null===o||void 0===o?void 0:o.body)||void 0===a?void 0:a.scrollTop)||0}}function bt(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function vt(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function St(t,e,n,s,r){if(!t)return!1;const i=t.nodeType===t.ELEMENT_NODE?t:t.parentElement;if(!i)return!1;const a=at(e,n);if(!r){const t=s&&i.matches(s);return a(i)&&!t}const o=it(i,a);let c=-1;return!(o<0)&&(s&&(c=it(i,at(null,s))),o>-1&&c<0||o<c)}function kt(t,e){return e.getId(t)===Z}function wt(t,e){if(R(t))return!1;const n=e.getId(t);return!e.has(n)||(!t.parentNode||t.parentNode.nodeType!==t.DOCUMENT_NODE)&&(!t.parentNode||wt(t.parentNode,e))}function Et(t){return Boolean(t.changedTouches)}function It(t,e){return Boolean("IFRAME"===t.nodeName&&e.getMeta(t))}function Ct(t,e){return Boolean("LINK"===t.nodeName&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&"stylesheet"===t.getAttribute("rel")&&e.getMeta(t))}function Tt(t){return Boolean(null===t||void 0===t?void 0:t.shadowRoot)}/[1-9][0-9]{12}/.test(Date.now().toString())||(gt=()=>(new Date).getTime());class xt{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(t){var e;return null!==(e=this.styleIDMap.get(t))&&void 0!==e?e:-1}has(t){return this.styleIDMap.has(t)}add(t,e){if(this.has(t))return this.getId(t);let n;return n=void 0===e?this.id++:e,this.styleIDMap.set(t,n),this.idStyleMap.set(n,t),n}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function Rt(t){var e,n;let s=null;return(null===(n=null===(e=t.getRootNode)||void 0===e?void 0:e.call(t))||void 0===n?void 0:n.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&t.getRootNode().host&&(s=t.getRootNode().host),s}function At(t){const e=t.ownerDocument;if(!e)return!1;const n=function(t){let e,n=t;for(;e=Rt(n);)n=e;return n}(t);return e.contains(n)}function Dt(t){const e=t.ownerDocument;return!!e&&(e.contains(t)||At(t))}var Nt=(t=>(t[t.DomContentLoaded=0]="DomContentLoaded",t[t.Load=1]="Load",t[t.FullSnapshot=2]="FullSnapshot",t[t.IncrementalSnapshot=3]="IncrementalSnapshot",t[t.Meta=4]="Meta",t[t.Custom=5]="Custom",t[t.Plugin=6]="Plugin",t))(Nt||{}),Ot=(t=>(t[t.Mutation=0]="Mutation",t[t.MouseMove=1]="MouseMove",t[t.MouseInteraction=2]="MouseInteraction",t[t.Scroll=3]="Scroll",t[t.ViewportResize=4]="ViewportResize",t[t.Input=5]="Input",t[t.TouchMove=6]="TouchMove",t[t.MediaInteraction=7]="MediaInteraction",t[t.StyleSheetRule=8]="StyleSheetRule",t[t.CanvasMutation=9]="CanvasMutation",t[t.Font=10]="Font",t[t.Log=11]="Log",t[t.Drag=12]="Drag",t[t.StyleDeclaration=13]="StyleDeclaration",t[t.Selection=14]="Selection",t[t.AdoptedStyleSheet=15]="AdoptedStyleSheet",t[t.CustomElement=16]="CustomElement",t))(Ot||{}),Mt=(t=>(t[t.MouseUp=0]="MouseUp",t[t.MouseDown=1]="MouseDown",t[t.Click=2]="Click",t[t.ContextMenu=3]="ContextMenu",t[t.DblClick=4]="DblClick",t[t.Focus=5]="Focus",t[t.Blur=6]="Blur",t[t.TouchStart=7]="TouchStart",t[t.TouchMove_Departed=8]="TouchMove_Departed",t[t.TouchEnd=9]="TouchEnd",t[t.TouchCancel=10]="TouchCancel",t))(Mt||{}),Ut=(t=>(t[t.Mouse=0]="Mouse",t[t.Pen=1]="Pen",t[t.Touch=2]="Touch",t))(Ut||{}),Bt=(t=>(t[t["2D"]=0]="2D",t[t.WebGL=1]="WebGL",t[t.WebGL2=2]="WebGL2",t))(Bt||{});function Gt(t){return"__ln"in t}class Lt{constructor(){this.length=0,this.head=null,this.tail=null}get(t){if(t>=this.length)throw new Error("Position outside of list range");let e=this.head;for(let n=0;n<t;n++)e=(null===e||void 0===e?void 0:e.next)||null;return e}addNode(t){const e={value:t,previous:null,next:null};if(t.__ln=e,t.previousSibling&&Gt(t.previousSibling)){const n=t.previousSibling.__ln.next;e.next=n,e.previous=t.previousSibling.__ln,t.previousSibling.__ln.next=e,n&&(n.previous=e)}else if(t.nextSibling&&Gt(t.nextSibling)&&t.nextSibling.__ln.previous){const n=t.nextSibling.__ln.previous;e.previous=n,e.next=t.nextSibling.__ln,t.nextSibling.__ln.previous=e,n&&(n.next=e)}else this.head&&(this.head.previous=e),e.next=this.head,this.head=e;null===e.next&&(this.tail=e),this.length++}removeNode(t){const e=t.__ln;this.head&&(e.previous?(e.previous.next=e.next,e.next?e.next.previous=e.previous:this.tail=e.previous):(this.head=e.next,this.head?this.head.previous=null:this.tail=null),t.__ln&&delete t.__ln,this.length--)}}const Ft=(t,e)=>`${t}@${e}`;class zt{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=t=>{t.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const t=[],e=new Set,n=new Lt,s=t=>{let e=t,n=Z;for(;n===Z;)e=e&&e.nextSibling,n=e&&this.mirror.getId(e);return n},r=r=>{if(!r.parentNode||!Dt(r))return;const i=R(r.parentNode)?this.mirror.getId(Rt(r)):this.mirror.getId(r.parentNode),a=s(r);if(-1===i||-1===a)return n.addNode(r);const o=ut(r,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:t=>{It(t,this.mirror)&&this.iframeManager.addIframe(t),Ct(t,this.mirror)&&this.stylesheetManager.trackLinkElement(t),Tt(r)&&this.shadowDomManager.addShadowRoot(r.shadowRoot,this.doc)},onIframeLoad:(t,e)=>{this.iframeManager.attachIframe(t,e),this.shadowDomManager.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{this.stylesheetManager.attachLinkElement(t,e)}});o&&(t.push({parentId:i,nextId:a,node:o}),e.add(o.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const o of this.movedSet)Pt(this.removes,o,this.mirror)&&!this.movedSet.has(o.parentNode)||r(o);for(const o of this.addedSet)$t(this.droppedSet,o)||Pt(this.removes,o,this.mirror)?$t(this.movedSet,o)?r(o):this.droppedSet.add(o):r(o);let i=null;for(;n.length;){let t=null;if(i){const e=this.mirror.getId(i.value.parentNode),n=s(i.value);-1!==e&&-1!==n&&(t=i)}if(!t){let e=n.tail;for(;e;){const n=e;if(e=e.previous,n){const e=this.mirror.getId(n.value.parentNode);if(-1===s(n.value))continue;if(-1!==e){t=n;break}{const e=n.value;if(e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const s=e.parentNode.host;if(-1!==this.mirror.getId(s)){t=n;break}}}}}}if(!t){for(;n.head;)n.removeNode(n.head.value);break}i=t.previous,n.removeNode(t.value),r(t.value)}const a={texts:this.texts.map((t=>({id:this.mirror.getId(t.node),value:t.value}))).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),attributes:this.attributes.map((t=>{const{attributes:e}=t;if("string"===typeof e.style){const n=JSON.stringify(t.styleDiff),s=JSON.stringify(t._unchangedStyles);n.length<e.style.length&&(n+s).split("var(").length===e.style.split("var(").length&&(e.style=t.styleDiff)}return{id:this.mirror.getId(t.node),attributes:e}})).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),removes:this.removes,adds:t};(a.texts.length||a.attributes.length||a.removes.length||a.adds.length)&&(this.texts=[],this.attributes=[],this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(a))},this.processMutation=t=>{if(kt(t.target,this.mirror))return;let e;try{e=document.implementation.createHTMLDocument()}catch(n){e=this.doc}switch(t.type){case"characterData":{const e=t.target.textContent;St(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||e===t.oldValue||this.texts.push({value:ot(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&e?this.maskTextFn?this.maskTextFn(e):e.replace(/[\S]/g,"*"):e,node:t.target});break}case"attributes":{const n=t.target;let s=t.attributeName,r=t.target.getAttribute(s);if("value"===s){const e=F(n),s=n.tagName;r=z(n,s,e);const i=M({maskInputOptions:this.maskInputOptions,tagName:s,type:e});r=U({isMasked:ot(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,i),element:n,value:r,maskInputFn:this.maskInputFn})}if(St(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||r===t.oldValue)return;let i=this.attributes.find((e=>e.node===t.target));if("IFRAME"===n.tagName&&"src"===s&&!this.keepIframeSrcFn(r)){if(n.contentDocument)return;s="rr_src"}if(i||(i={node:t.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(i)),"type"===s&&"INPUT"===n.tagName&&"password"===(t.oldValue||"").toLowerCase()&&n.setAttribute("data-rr-is-password","true"),!rt(n.tagName,s)&&(i.attributes[s]=st(this.doc,B(n.tagName),B(s),r,n,this.maskAttributeFn),"style"===s)){const s=e.createElement("span");t.oldValue&&s.setAttribute("style",t.oldValue);for(const t of Array.from(n.style)){const e=n.style.getPropertyValue(t),r=n.style.getPropertyPriority(t);e!==s.style.getPropertyValue(t)||r!==s.style.getPropertyPriority(t)?i.styleDiff[t]=""===r?e:[e,r]:i._unchangedStyles[t]=[e,r]}for(const t of Array.from(s.style))""===n.style.getPropertyValue(t)&&(i.styleDiff[t]=!1)}break}case"childList":if(St(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;t.addedNodes.forEach((e=>this.genAdds(e,t.target))),t.removedNodes.forEach((e=>{const n=this.mirror.getId(e),s=R(t.target)?this.mirror.getId(t.target.host):this.mirror.getId(t.target);St(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||kt(e,this.mirror)||!function(t,e){return-1!==e.getId(t)}(e,this.mirror)||(this.addedSet.has(e)?(Yt(this.addedSet,e),this.droppedSet.add(e)):this.addedSet.has(t.target)&&-1===n||wt(t.target,this.mirror)||(this.movedSet.has(e)&&this.movedMap[Ft(n,s)]?Yt(this.movedSet,e):this.removes.push({parentId:s,id:n,isShadow:!(!R(t.target)||!A(t.target))||void 0})),this.mapRemoves.push(e))}))}},this.genAdds=(t,e)=>{if(!this.processedNodeManager.inOtherBuffer(t,this)&&!this.addedSet.has(t)&&!this.movedSet.has(t)){if(this.mirror.hasNode(t)){if(kt(t,this.mirror))return;this.movedSet.add(t);let n=null;e&&this.mirror.hasNode(e)&&(n=this.mirror.getId(e)),n&&-1!==n&&(this.movedMap[Ft(this.mirror.getId(t),n)]=!0)}else this.addedSet.add(t),this.droppedSet.delete(t);St(t,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(t.childNodes.forEach((t=>this.genAdds(t))),Tt(t)&&t.shadowRoot.childNodes.forEach((e=>{this.processedNodeManager.add(e,this),this.genAdds(e,t)})))}}}init(t){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((e=>{this[e]=t[e]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function Yt(t,e){t.delete(e),e.childNodes.forEach((e=>Yt(t,e)))}function Pt(t,e,n){return 0!==t.length&&Zt(t,e,n)}function Zt(t,e,n){const{parentNode:s}=e;if(!s)return!1;const r=n.getId(s);return!!t.some((t=>t.id===r))||Zt(t,s,n)}function $t(t,e){return 0!==t.size&&Wt(t,e)}function Wt(t,e){const{parentNode:n}=e;return!!n&&(!!t.has(n)||Wt(t,n))}let jt;function Ht(t){jt=t}function Vt(){jt=void 0}const Kt=t=>{if(!jt)return t;return(...e)=>{try{return t(...e)}catch(n){if(jt&&!0===jt(n))return()=>{};throw n}}},Jt=[];function qt(t){try{if("composedPath"in t){const e=t.composedPath();if(e.length)return e[0]}else if("path"in t&&t.path.length)return t.path[0]}catch(e){}return t&&t.target}function Xt(t,e){var n,s;const r=new zt;Jt.push(r),r.init(t);let i=window.MutationObserver||window.__rrMutationObserver;const a=null===(s=null===(n=null===window||void 0===window?void 0:window.Zone)||void 0===n?void 0:n.__symbol__)||void 0===s?void 0:s.call(n,"MutationObserver");a&&window[a]&&(i=window[a]);const o=new i(Kt((e=>{t.onMutation&&!1===t.onMutation(e)||r.processMutations.bind(r)(e)})));return o.observe(e,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),o}function Qt({mouseInteractionCb:t,doc:e,mirror:n,blockClass:s,blockSelector:r,unblockSelector:i,sampling:a}){if(!1===a.mouseInteraction)return()=>{};const o=!0===a.mouseInteraction||void 0===a.mouseInteraction?{}:a.mouseInteraction,c=[];let l=null;return Object.keys(Mt).filter((t=>Number.isNaN(Number(t))&&!t.endsWith("_Departed")&&!1!==o[t])).forEach((a=>{let o=B(a);const u=(e=>a=>{const o=qt(a);if(St(o,s,r,i,!0))return;let c=null,u=e;if("pointerType"in a){switch(a.pointerType){case"mouse":c=Ut.Mouse;break;case"touch":c=Ut.Touch;break;case"pen":c=Ut.Pen}c===Ut.Touch?Mt[e]===Mt.MouseDown?u="TouchStart":Mt[e]===Mt.MouseUp&&(u="TouchEnd"):Ut.Pen}else Et(a)&&(c=Ut.Touch);null!==c?(l=c,(u.startsWith("Touch")&&c===Ut.Touch||u.startsWith("Mouse")&&c===Ut.Mouse)&&(c=null)):Mt[e]===Mt.Click&&(c=l,l=null);const d=Et(a)?a.changedTouches[0]:a;if(!d)return;const h=n.getId(o),{clientX:p,clientY:f}=d;Kt(t)(Object.assign({type:Mt[u],id:h,x:p,y:f},null!==c&&{pointerType:c}))})(a);if(window.PointerEvent)switch(Mt[a]){case Mt.MouseDown:case Mt.MouseUp:o=o.replace("mouse","pointer");break;case Mt.TouchStart:case Mt.TouchEnd:return}c.push(dt(o,u,e))})),Kt((()=>{c.forEach((t=>t()))}))}function te({scrollCb:t,doc:e,mirror:n,blockClass:s,blockSelector:r,unblockSelector:i,sampling:a}){return dt("scroll",Kt(ft(Kt((a=>{const o=qt(a);if(!o||St(o,s,r,i,!0))return;const c=n.getId(o);if(o===e&&e.defaultView){const n=yt(e.defaultView);t({id:c,x:n.left,y:n.top})}else t({id:c,x:o.scrollLeft,y:o.scrollTop})})),a.scroll||100)),e)}function ee(t,e){const n=Object.assign({},t);return e||delete n.userTriggered,n}const ne=["INPUT","TEXTAREA","SELECT"],se=new WeakMap;function re(t){return function(t,e){if(ce("CSSGroupingRule")&&t.parentRule instanceof CSSGroupingRule||ce("CSSMediaRule")&&t.parentRule instanceof CSSMediaRule||ce("CSSSupportsRule")&&t.parentRule instanceof CSSSupportsRule||ce("CSSConditionRule")&&t.parentRule instanceof CSSConditionRule){const n=Array.from(t.parentRule.cssRules).indexOf(t);e.unshift(n)}else if(t.parentStyleSheet){const n=Array.from(t.parentStyleSheet.cssRules).indexOf(t);e.unshift(n)}return e}(t,[])}function ie(t,e,n){let s,r;return t?(t.ownerNode?s=e.getId(t.ownerNode):r=n.getId(t),{styleId:r,id:s}):{}}function ae({mirror:t,stylesheetManager:e},n){var s,r,i;let a=null;a="#document"===n.nodeName?t.getId(n):t.getId(n.host);const o="#document"===n.nodeName?null===(s=n.defaultView)||void 0===s?void 0:s.Document:null===(i=null===(r=n.ownerDocument)||void 0===r?void 0:r.defaultView)||void 0===i?void 0:i.ShadowRoot,c=(null===o||void 0===o?void 0:o.prototype)?Object.getOwnPropertyDescriptor(null===o||void 0===o?void 0:o.prototype,"adoptedStyleSheets"):void 0;return null!==a&&-1!==a&&o&&c?(Object.defineProperty(n,"adoptedStyleSheets",{configurable:c.configurable,enumerable:c.enumerable,get(){var t;return null===(t=c.get)||void 0===t?void 0:t.call(this)},set(t){var n;const s=null===(n=c.set)||void 0===n?void 0:n.call(this,t);if(null!==a&&-1!==a)try{e.adoptStyleSheets(t,a)}catch(r){}return s}}),Kt((()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:c.configurable,enumerable:c.enumerable,get:c.get,set:c.set})}))):()=>{}}function oe(t,e={}){const n=t.doc.defaultView;if(!n)return()=>{};!function(t,e){const{mutationCb:n,mousemoveCb:s,mouseInteractionCb:r,scrollCb:i,viewportResizeCb:a,inputCb:o,mediaInteractionCb:c,styleSheetRuleCb:l,styleDeclarationCb:u,canvasMutationCb:d,fontCb:h,selectionCb:p,customElementCb:f}=t;t.mutationCb=(...t)=>{e.mutation&&e.mutation(...t),n(...t)},t.mousemoveCb=(...t)=>{e.mousemove&&e.mousemove(...t),s(...t)},t.mouseInteractionCb=(...t)=>{e.mouseInteraction&&e.mouseInteraction(...t),r(...t)},t.scrollCb=(...t)=>{e.scroll&&e.scroll(...t),i(...t)},t.viewportResizeCb=(...t)=>{e.viewportResize&&e.viewportResize(...t),a(...t)},t.inputCb=(...t)=>{e.input&&e.input(...t),o(...t)},t.mediaInteractionCb=(...t)=>{e.mediaInteaction&&e.mediaInteaction(...t),c(...t)},t.styleSheetRuleCb=(...t)=>{e.styleSheetRule&&e.styleSheetRule(...t),l(...t)},t.styleDeclarationCb=(...t)=>{e.styleDeclaration&&e.styleDeclaration(...t),u(...t)},t.canvasMutationCb=(...t)=>{e.canvasMutation&&e.canvasMutation(...t),d(...t)},t.fontCb=(...t)=>{e.font&&e.font(...t),h(...t)},t.selectionCb=(...t)=>{e.selection&&e.selection(...t),p(...t)},t.customElementCb=(...t)=>{e.customElement&&e.customElement(...t),f(...t)}}(t,e);const s=Xt(t,t.doc),r=function({mousemoveCb:t,sampling:e,doc:n,mirror:s}){if(!1===e.mousemove)return()=>{};const r="number"===typeof e.mousemove?e.mousemove:50,i="number"===typeof e.mousemoveCallback?e.mousemoveCallback:500;let a,o=[];const c=ft(Kt((e=>{const n=Date.now()-a;t(o.map((t=>(t.timeOffset-=n,t))),e),o=[],a=null})),i),l=Kt(ft(Kt((t=>{const e=qt(t),{clientX:n,clientY:r}=Et(t)?t.changedTouches[0]:t;a||(a=gt()),o.push({x:n,y:r,id:s.getId(e),timeOffset:gt()-a}),c("undefined"!==typeof DragEvent&&t instanceof DragEvent?Ot.Drag:t instanceof MouseEvent?Ot.MouseMove:Ot.TouchMove)})),r,{trailing:!1})),u=[dt("mousemove",l,n),dt("touchmove",l,n),dt("drag",l,n)];return Kt((()=>{u.forEach((t=>t()))}))}(t),i=Qt(t),a=te(t),o=function({viewportResizeCb:t},{win:e}){let n=-1,s=-1;return dt("resize",Kt(ft(Kt((()=>{const e=bt(),r=vt();n===e&&s===r||(t({width:Number(r),height:Number(e)}),n=e,s=r)})),200)),e)}(t,{win:n}),c=function({inputCb:t,doc:e,mirror:n,blockClass:s,blockSelector:r,unblockSelector:i,ignoreClass:a,ignoreSelector:o,maskInputOptions:c,maskInputFn:l,sampling:u,userTriggeredOnInput:d,maskTextClass:h,unmaskTextClass:p,maskTextSelector:f,unmaskTextSelector:m}){function _(t){let n=qt(t);const u=t.isTrusted,_=n&&G(n.tagName);if("OPTION"===_&&(n=n.parentElement),!n||!_||ne.indexOf(_)<0||St(n,s,r,i,!0))return;const y=n;if(y.classList.contains(a)||o&&y.matches(o))return;const b=F(n);let v=z(y,_,b),S=!1;const k=M({maskInputOptions:c,tagName:_,type:b}),w=ot(n,h,f,p,m,k);"radio"!==b&&"checkbox"!==b||(S=n.checked),v=U({isMasked:w,element:n,value:v,maskInputFn:l}),g(n,Kt(ee)({text:v,isChecked:S,userTriggered:u},d));const E=n.name;"radio"===b&&E&&S&&e.querySelectorAll(`input[type="radio"][name="${E}"]`).forEach((t=>{if(t!==n){const e=U({isMasked:w,element:t,value:z(t,_,b),maskInputFn:l});g(t,Kt(ee)({text:e,isChecked:!S,userTriggered:!1},d))}}))}function g(e,s){const r=se.get(e);if(!r||r.text!==s.text||r.isChecked!==s.isChecked){se.set(e,s);const r=n.getId(e);Kt(t)(Object.assign(Object.assign({},s),{id:r}))}}const y=("last"===u.input?["change"]:["input","change"]).map((t=>dt(t,Kt(_),e))),b=e.defaultView;if(!b)return()=>{y.forEach((t=>t()))};const v=b.Object.getOwnPropertyDescriptor(b.HTMLInputElement.prototype,"value"),S=[[b.HTMLInputElement.prototype,"value"],[b.HTMLInputElement.prototype,"checked"],[b.HTMLSelectElement.prototype,"value"],[b.HTMLTextAreaElement.prototype,"value"],[b.HTMLSelectElement.prototype,"selectedIndex"],[b.HTMLOptionElement.prototype,"selected"]];return v&&v.set&&y.push(...S.map((t=>mt(t[0],t[1],{set(){Kt(_)({target:this,isTrusted:!1})}},!1,b)))),Kt((()=>{y.forEach((t=>t()))}))}(t),l=function({mediaInteractionCb:t,blockClass:e,blockSelector:n,unblockSelector:s,mirror:r,sampling:i,doc:a}){const o=Kt((a=>ft(Kt((i=>{const o=qt(i);if(!o||St(o,e,n,s,!0))return;const{currentTime:c,volume:l,muted:u,playbackRate:d}=o;t({type:a,id:r.getId(o),currentTime:c,volume:l,muted:u,playbackRate:d})})),i.media||500))),c=[dt("play",o(0),a),dt("pause",o(1),a),dt("seeked",o(2),a),dt("volumechange",o(3),a),dt("ratechange",o(4),a)];return Kt((()=>{c.forEach((t=>t()))}))}(t),u=function({styleSheetRuleCb:t,mirror:e,stylesheetManager:n},{win:s}){if(!s.CSSStyleSheet||!s.CSSStyleSheet.prototype)return()=>{};const r=s.CSSStyleSheet.prototype.insertRule;s.CSSStyleSheet.prototype.insertRule=new Proxy(r,{apply:Kt(((s,r,i)=>{const[a,o]=i,{id:c,styleId:l}=ie(r,e,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&t({id:c,styleId:l,adds:[{rule:a,index:o}]}),s.apply(r,i)}))});const i=s.CSSStyleSheet.prototype.deleteRule;let a,o;s.CSSStyleSheet.prototype.deleteRule=new Proxy(i,{apply:Kt(((s,r,i)=>{const[a]=i,{id:o,styleId:c}=ie(r,e,n.styleMirror);return(o&&-1!==o||c&&-1!==c)&&t({id:o,styleId:c,removes:[{index:a}]}),s.apply(r,i)}))}),s.CSSStyleSheet.prototype.replace&&(a=s.CSSStyleSheet.prototype.replace,s.CSSStyleSheet.prototype.replace=new Proxy(a,{apply:Kt(((s,r,i)=>{const[a]=i,{id:o,styleId:c}=ie(r,e,n.styleMirror);return(o&&-1!==o||c&&-1!==c)&&t({id:o,styleId:c,replace:a}),s.apply(r,i)}))})),s.CSSStyleSheet.prototype.replaceSync&&(o=s.CSSStyleSheet.prototype.replaceSync,s.CSSStyleSheet.prototype.replaceSync=new Proxy(o,{apply:Kt(((s,r,i)=>{const[a]=i,{id:o,styleId:c}=ie(r,e,n.styleMirror);return(o&&-1!==o||c&&-1!==c)&&t({id:o,styleId:c,replaceSync:a}),s.apply(r,i)}))}));const c={};le("CSSGroupingRule")?c.CSSGroupingRule=s.CSSGroupingRule:(le("CSSMediaRule")&&(c.CSSMediaRule=s.CSSMediaRule),le("CSSConditionRule")&&(c.CSSConditionRule=s.CSSConditionRule),le("CSSSupportsRule")&&(c.CSSSupportsRule=s.CSSSupportsRule));const l={};return Object.entries(c).forEach((([s,r])=>{l[s]={insertRule:r.prototype.insertRule,deleteRule:r.prototype.deleteRule},r.prototype.insertRule=new Proxy(l[s].insertRule,{apply:Kt(((s,r,i)=>{const[a,o]=i,{id:c,styleId:l}=ie(r.parentStyleSheet,e,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&t({id:c,styleId:l,adds:[{rule:a,index:[...re(r),o||0]}]}),s.apply(r,i)}))}),r.prototype.deleteRule=new Proxy(l[s].deleteRule,{apply:Kt(((s,r,i)=>{const[a]=i,{id:o,styleId:c}=ie(r.parentStyleSheet,e,n.styleMirror);return(o&&-1!==o||c&&-1!==c)&&t({id:o,styleId:c,removes:[{index:[...re(r),a]}]}),s.apply(r,i)}))})})),Kt((()=>{s.CSSStyleSheet.prototype.insertRule=r,s.CSSStyleSheet.prototype.deleteRule=i,a&&(s.CSSStyleSheet.prototype.replace=a),o&&(s.CSSStyleSheet.prototype.replaceSync=o),Object.entries(c).forEach((([t,e])=>{e.prototype.insertRule=l[t].insertRule,e.prototype.deleteRule=l[t].deleteRule}))}))}(t,{win:n}),d=ae(t,t.doc),h=function({styleDeclarationCb:t,mirror:e,ignoreCSSAttributes:n,stylesheetManager:s},{win:r}){const i=r.CSSStyleDeclaration.prototype.setProperty;r.CSSStyleDeclaration.prototype.setProperty=new Proxy(i,{apply:Kt(((r,a,o)=>{var c;const[l,u,d]=o;if(n.has(l))return i.apply(a,[l,u,d]);const{id:h,styleId:p}=ie(null===(c=a.parentRule)||void 0===c?void 0:c.parentStyleSheet,e,s.styleMirror);return(h&&-1!==h||p&&-1!==p)&&t({id:h,styleId:p,set:{property:l,value:u,priority:d},index:re(a.parentRule)}),r.apply(a,o)}))});const a=r.CSSStyleDeclaration.prototype.removeProperty;return r.CSSStyleDeclaration.prototype.removeProperty=new Proxy(a,{apply:Kt(((r,i,o)=>{var c;const[l]=o;if(n.has(l))return a.apply(i,[l]);const{id:u,styleId:d}=ie(null===(c=i.parentRule)||void 0===c?void 0:c.parentStyleSheet,e,s.styleMirror);return(u&&-1!==u||d&&-1!==d)&&t({id:u,styleId:d,remove:{property:l},index:re(i.parentRule)}),r.apply(i,o)}))}),Kt((()=>{r.CSSStyleDeclaration.prototype.setProperty=i,r.CSSStyleDeclaration.prototype.removeProperty=a}))}(t,{win:n}),p=t.collectFonts?function({fontCb:t,doc:e}){const n=e.defaultView;if(!n)return()=>{};const s=[],r=new WeakMap,i=n.FontFace;n.FontFace=function(t,e,n){const s=new i(t,e,n);return r.set(s,{family:t,buffer:"string"!==typeof e,descriptors:n,fontSource:"string"===typeof e?e:JSON.stringify(Array.from(new Uint8Array(e)))}),s};const a=_t(e.fonts,"add",(function(e){return function(n){return setTimeout(Kt((()=>{const e=r.get(n);e&&(t(e),r.delete(n))})),0),e.apply(this,[n])}}));return s.push((()=>{n.FontFace=i})),s.push(a),Kt((()=>{s.forEach((t=>t()))}))}(t):()=>{},f=function(t){const{doc:e,mirror:n,blockClass:s,blockSelector:r,unblockSelector:i,selectionCb:a}=t;let o=!0;const c=Kt((()=>{const t=e.getSelection();if(!t||o&&(null===t||void 0===t?void 0:t.isCollapsed))return;o=t.isCollapsed||!1;const c=[],l=t.rangeCount||0;for(let e=0;e<l;e++){const a=t.getRangeAt(e),{startContainer:o,startOffset:l,endContainer:u,endOffset:d}=a;St(o,s,r,i,!0)||St(u,s,r,i,!0)||c.push({start:n.getId(o),startOffset:l,end:n.getId(u),endOffset:d})}a({ranges:c})}));return c(),dt("selectionchange",c)}(t),m=function({doc:t,customElementCb:e}){const n=t.defaultView;return n&&n.customElements?_t(n.customElements,"define",(function(t){return function(n,s,r){try{e({define:{name:n}})}catch(i){}return t.apply(this,[n,s,r])}})):()=>{}}(t),_=[];for(const g of t.plugins)_.push(g.observer(g.callback,n,g.options));return Kt((()=>{Jt.forEach((t=>t.reset())),s.disconnect(),r(),i(),a(),o(),c(),l(),u(),d(),h(),p(),f(),m(),_.forEach((t=>t()))}))}function ce(t){return"undefined"!==typeof window[t]}function le(t){return Boolean("undefined"!==typeof window[t]&&window[t].prototype&&"insertRule"in window[t].prototype&&"deleteRule"in window[t].prototype)}class ue{constructor(t){this.generateIdFn=t,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(t,e,n,s){const r=n||this.getIdToRemoteIdMap(t),i=s||this.getRemoteIdToIdMap(t);let a=r.get(e);return a||(a=this.generateIdFn(),r.set(e,a),i.set(a,e)),a}getIds(t,e){const n=this.getIdToRemoteIdMap(t),s=this.getRemoteIdToIdMap(t);return e.map((e=>this.getId(t,e,n,s)))}getRemoteId(t,e,n){const s=n||this.getRemoteIdToIdMap(t);if("number"!==typeof e)return e;const r=s.get(e);return r||-1}getRemoteIds(t,e){const n=this.getRemoteIdToIdMap(t);return e.map((e=>this.getRemoteId(t,e,n)))}reset(t){if(!t)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(t),this.iframeRemoteIdToIdMap.delete(t)}getIdToRemoteIdMap(t){let e=this.iframeIdToRemoteIdMap.get(t);return e||(e=new Map,this.iframeIdToRemoteIdMap.set(t,e)),e}getRemoteIdToIdMap(t){let e=this.iframeRemoteIdToIdMap.get(t);return e||(e=new Map,this.iframeRemoteIdToIdMap.set(t,e)),e}}class de{constructor(t){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new ue($),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=t.mutationCb,this.wrappedEmit=t.wrappedEmit,this.stylesheetManager=t.stylesheetManager,this.recordCrossOriginIframes=t.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new ue(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=t.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(t){this.iframes.set(t,!0),t.contentWindow&&this.crossOriginIframeMap.set(t.contentWindow,t)}addLoadListener(t){this.loadListener=t}attachIframe(t,e){var n;this.mutationCb({adds:[{parentId:this.mirror.getId(t),nextId:null,node:e}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),null===(n=this.loadListener)||void 0===n||n.call(this,t),t.contentDocument&&t.contentDocument.adoptedStyleSheets&&t.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(t.contentDocument.adoptedStyleSheets,this.mirror.getId(t.contentDocument))}handleMessage(t){const e=t;if("rrweb"!==e.data.type||e.origin!==e.data.origin)return;if(!t.source)return;const n=this.crossOriginIframeMap.get(t.source);if(!n)return;const s=this.transformCrossOriginEvent(n,e.data.event);s&&this.wrappedEmit(s,e.data.isCheckout)}transformCrossOriginEvent(t,e){var n;switch(e.type){case Nt.FullSnapshot:{this.crossOriginIframeMirror.reset(t),this.crossOriginIframeStyleMirror.reset(t),this.replaceIdOnNode(e.data.node,t);const n=e.data.node.id;return this.crossOriginIframeRootIdMap.set(t,n),this.patchRootIdOnNode(e.data.node,n),{timestamp:e.timestamp,type:Nt.IncrementalSnapshot,data:{source:Ot.Mutation,adds:[{parentId:this.mirror.getId(t),nextId:null,node:e.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case Nt.Meta:case Nt.Load:case Nt.DomContentLoaded:return!1;case Nt.Plugin:return e;case Nt.Custom:return this.replaceIds(e.data.payload,t,["id","parentId","previousId","nextId"]),e;case Nt.IncrementalSnapshot:switch(e.data.source){case Ot.Mutation:return e.data.adds.forEach((e=>{this.replaceIds(e,t,["parentId","nextId","previousId"]),this.replaceIdOnNode(e.node,t);const n=this.crossOriginIframeRootIdMap.get(t);n&&this.patchRootIdOnNode(e.node,n)})),e.data.removes.forEach((e=>{this.replaceIds(e,t,["parentId","id"])})),e.data.attributes.forEach((e=>{this.replaceIds(e,t,["id"])})),e.data.texts.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case Ot.Drag:case Ot.TouchMove:case Ot.MouseMove:return e.data.positions.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case Ot.ViewportResize:return!1;case Ot.MediaInteraction:case Ot.MouseInteraction:case Ot.Scroll:case Ot.CanvasMutation:case Ot.Input:return this.replaceIds(e.data,t,["id"]),e;case Ot.StyleSheetRule:case Ot.StyleDeclaration:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleId"]),e;case Ot.Font:return e;case Ot.Selection:return e.data.ranges.forEach((e=>{this.replaceIds(e,t,["start","end"])})),e;case Ot.AdoptedStyleSheet:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleIds"]),null===(n=e.data.styles)||void 0===n||n.forEach((e=>{this.replaceStyleIds(e,t,["styleId"])})),e}}return!1}replace(t,e,n,s){for(const r of s)(Array.isArray(e[r])||"number"===typeof e[r])&&(Array.isArray(e[r])?e[r]=t.getIds(n,e[r]):e[r]=t.getId(n,e[r]));return e}replaceIds(t,e,n){return this.replace(this.crossOriginIframeMirror,t,e,n)}replaceStyleIds(t,e,n){return this.replace(this.crossOriginIframeStyleMirror,t,e,n)}replaceIdOnNode(t,e){this.replaceIds(t,e,["id","rootId"]),"childNodes"in t&&t.childNodes.forEach((t=>{this.replaceIdOnNode(t,e)}))}patchRootIdOnNode(t,e){t.type===x.Document||t.rootId||(t.rootId=e),"childNodes"in t&&t.childNodes.forEach((t=>{this.patchRootIdOnNode(t,e)}))}}class he{constructor(t){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=t.mutationCb,this.scrollCb=t.scrollCb,this.bypassOptions=t.bypassOptions,this.mirror=t.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(t,e){if(!A(t))return;if(this.shadowDoms.has(t))return;this.shadowDoms.add(t);const n=Xt(Object.assign(Object.assign({},this.bypassOptions),{doc:e,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),t);this.restoreHandlers.push((()=>n.disconnect())),this.restoreHandlers.push(te(Object.assign(Object.assign({},this.bypassOptions),{scrollCb:this.scrollCb,doc:t,mirror:this.mirror}))),setTimeout((()=>{t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(t.adoptedStyleSheets,this.mirror.getId(t.host)),this.restoreHandlers.push(ae({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},t))}),0)}observeAttachShadow(t){t.contentWindow&&t.contentDocument&&this.patchAttachShadow(t.contentWindow.Element,t.contentDocument)}patchAttachShadow(t,e){const n=this;this.restoreHandlers.push(_t(t.prototype,"attachShadow",(function(t){return function(s){const r=t.call(this,s);return this.shadowRoot&&Dt(this)&&n.addShadowRoot(this.shadowRoot,e),r}})))}reset(){this.restoreHandlers.forEach((t=>{try{t()}catch(e){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}function pe(t,e,n,s){return new(n||(n=Promise))((function(r,i){function a(t){try{c(s.next(t))}catch(e){i(e)}}function o(t){try{c(s.throw(t))}catch(e){i(e)}}function c(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,o)}c((s=s.apply(t,e||[])).next())}))}for(var fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",me="undefined"===typeof Uint8Array?[]:new Uint8Array(256),_e=0;_e<64;_e++)me[fe.charCodeAt(_e)]=_e;const ge=new Map;const ye=(t,e,n)=>{if(!t||!Se(t,e)&&"object"!==typeof t)return;const s=function(t,e){let n=ge.get(t);return n||(n=new Map,ge.set(t,n)),n.has(e)||n.set(e,[]),n.get(e)}(n,t.constructor.name);let r=s.indexOf(t);return-1===r&&(r=s.length,s.push(t)),r};function be(t,e,n){if(t instanceof Array)return t.map((t=>be(t,e,n)));if(null===t)return t;if(t instanceof Float32Array||t instanceof Float64Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Uint8Array||t instanceof Uint16Array||t instanceof Int16Array||t instanceof Int8Array||t instanceof Uint8ClampedArray){return{rr_type:t.constructor.name,args:[Object.values(t)]}}if(t instanceof ArrayBuffer){const e=t.constructor.name,n=function(t){var e,n=new Uint8Array(t),s=n.length,r="";for(e=0;e<s;e+=3)r+=fe[n[e]>>2],r+=fe[(3&n[e])<<4|n[e+1]>>4],r+=fe[(15&n[e+1])<<2|n[e+2]>>6],r+=fe[63&n[e+2]];return s%3===2?r=r.substring(0,r.length-1)+"=":s%3===1&&(r=r.substring(0,r.length-2)+"=="),r}(t);return{rr_type:e,base64:n}}if(t instanceof DataView){return{rr_type:t.constructor.name,args:[be(t.buffer,e,n),t.byteOffset,t.byteLength]}}if(t instanceof HTMLImageElement){const e=t.constructor.name,{src:n}=t;return{rr_type:e,src:n}}if(t instanceof HTMLCanvasElement){return{rr_type:"HTMLImageElement",src:t.toDataURL()}}if(t instanceof ImageData){return{rr_type:t.constructor.name,args:[be(t.data,e,n),t.width,t.height]}}if(Se(t,e)||"object"===typeof t){return{rr_type:t.constructor.name,index:ye(t,e,n)}}return t}const ve=(t,e,n)=>[...t].map((t=>be(t,e,n))),Se=(t,e)=>{const n=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter((t=>"function"===typeof e[t]));return Boolean(n.find((n=>t instanceof e[n])))};function ke(t,e,n,s,r){const i=[];try{const a=_t(t.HTMLCanvasElement.prototype,"getContext",(function(t){return function(i,...a){if(!St(this,e,n,s,!0)){const t=function(t){return"experimental-webgl"===t?"webgl":t}(i);if("__context"in this||(this.__context=t),r&&["webgl","webgl2"].includes(t))if(a[0]&&"object"===typeof a[0]){const t=a[0];t.preserveDrawingBuffer||(t.preserveDrawingBuffer=!0)}else a.splice(0,1,{preserveDrawingBuffer:!0})}return t.apply(this,[i,...a])}}));i.push(a)}catch(a){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{i.forEach((t=>t()))}}function we(t,e,n,s,r,i,a,o){const c=[],l=Object.getOwnPropertyNames(t);for(const d of l)if(!["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(d))try{if("function"!==typeof t[d])continue;const a=_t(t,d,(function(t){return function(...a){const c=t.apply(this,a);if(ye(c,o,this),"tagName"in this.canvas&&!St(this.canvas,s,r,i,!0)){const t=ve([...a],o,this),s={type:e,property:d,args:t};n(this.canvas,s)}return c}}));c.push(a)}catch(u){const s=mt(t,d,{set(t){n(this.canvas,{type:e,property:d,args:[t],setter:!0})}});c.push(s)}return c}function Ee(t,e,n){var s=void 0===e?null:e,r=function(t,e){var n=atob(t);if(e){for(var s=new Uint8Array(n.length),r=0,i=n.length;r<i;++r)s[r]=n.charCodeAt(r);return String.fromCharCode.apply(null,new Uint16Array(s.buffer))}return n}(t,void 0!==n&&n),i=r.indexOf("\n",10)+1,a=r.substring(i)+(s?"//# sourceMappingURL="+s:""),o=new Blob([a],{type:"application/javascript"});return URL.createObjectURL(o)}var Ie=function(t,e,n){var s;return function(r){return s=s||Ee(t,e,n),new Worker(s,r)}}("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",null,!1);class Ce{reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}constructor(t){this.pendingCanvasMutations=new Map,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=(t,e)=>{!(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId)&&this.rafStamps.invokeId||(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(t)||this.pendingCanvasMutations.set(t,[]),this.pendingCanvasMutations.get(t).push(e)};const{sampling:e="all",win:n,blockClass:s,blockSelector:r,unblockSelector:i,recordCanvas:a,dataURLOptions:o}=t;this.mutationCb=t.mutationCb,this.mirror=t.mirror,a&&"all"===e&&this.initCanvasMutationObserver(n,s,r,i),a&&"number"===typeof e&&this.initCanvasFPSObserver(e,n,s,r,i,{dataURLOptions:o})}initCanvasFPSObserver(t,e,n,s,r,i){const a=ke(e,n,s,r,!0),o=new Map,c=new Ie;c.onmessage=t=>{const{id:e}=t.data;if(o.set(e,!1),!("base64"in t.data))return;const{base64:n,type:s,width:r,height:i}=t.data;this.mutationCb({id:e,type:Bt["2D"],commands:[{property:"clearRect",args:[0,0,r,i]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:n}],type:s}]},0,0]}]})};const l=1e3/t;let u,d=0;const h=t=>{d&&t-d<l||(d=t,(()=>{const t=[];return e.document.querySelectorAll("canvas").forEach((e=>{St(e,n,s,r,!0)||t.push(e)})),t})().forEach((t=>pe(this,void 0,void 0,(function*(){var e;const n=this.mirror.getId(t);if(o.get(n))return;if(o.set(n,!0),["webgl","webgl2"].includes(t.__context)){const n=t.getContext(t.__context);!1===(null===(e=null===n||void 0===n?void 0:n.getContextAttributes())||void 0===e?void 0:e.preserveDrawingBuffer)&&n.clear(n.COLOR_BUFFER_BIT)}const s=yield createImageBitmap(t);c.postMessage({id:n,bitmap:s,width:t.width,height:t.height,dataURLOptions:i.dataURLOptions},[s])}))))),u=requestAnimationFrame(h)};u=requestAnimationFrame(h),this.resetObservers=()=>{a(),cancelAnimationFrame(u)}}initCanvasMutationObserver(t,e,n,s){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();const r=ke(t,e,n,s,!1),i=function(t,e,n,s,r){const i=[],a=Object.getOwnPropertyNames(e.CanvasRenderingContext2D.prototype);for(const c of a)try{if("function"!==typeof e.CanvasRenderingContext2D.prototype[c])continue;const a=_t(e.CanvasRenderingContext2D.prototype,c,(function(i){return function(...a){return St(this.canvas,n,s,r,!0)||setTimeout((()=>{const n=ve([...a],e,this);t(this.canvas,{type:Bt["2D"],property:c,args:n})}),0),i.apply(this,a)}}));i.push(a)}catch(o){const n=mt(e.CanvasRenderingContext2D.prototype,c,{set(e){t(this.canvas,{type:Bt["2D"],property:c,args:[e],setter:!0})}});i.push(n)}return()=>{i.forEach((t=>t()))}}(this.processMutation.bind(this),t,e,n,s),a=function(t,e,n,s,r,i){const a=[];return a.push(...we(e.WebGLRenderingContext.prototype,Bt.WebGL,t,n,s,r,0,e)),"undefined"!==typeof e.WebGL2RenderingContext&&a.push(...we(e.WebGL2RenderingContext.prototype,Bt.WebGL2,t,n,s,r,0,e)),()=>{a.forEach((t=>t()))}}(this.processMutation.bind(this),t,e,n,s,this.mirror);this.resetObservers=()=>{r(),i(),a()}}startPendingCanvasMutationFlusher(){requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}startRAFTimestamping(){const t=e=>{this.rafStamps.latestId=e,requestAnimationFrame(t)};requestAnimationFrame(t)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach(((t,e)=>{const n=this.mirror.getId(e);this.flushPendingCanvasMutationFor(e,n)})),requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}flushPendingCanvasMutationFor(t,e){if(this.frozen||this.locked)return;const n=this.pendingCanvasMutations.get(t);if(!n||-1===e)return;const s=n.map((t=>{const e=function(t,e){var n={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(n[s]=t[s]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(s=Object.getOwnPropertySymbols(t);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(t,s[r])&&(n[s[r]]=t[s[r]])}return n}(t,["type"]);return e})),{type:r}=n[0];this.mutationCb({id:e,type:r,commands:s}),this.pendingCanvasMutations.delete(t)}}class Te{constructor(t){this.trackedLinkElements=new WeakSet,this.styleMirror=new xt,this.mutationCb=t.mutationCb,this.adoptedStyleSheetCb=t.adoptedStyleSheetCb}attachLinkElement(t,e){"_cssText"in e.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:e.id,attributes:e.attributes}]}),this.trackLinkElement(t)}trackLinkElement(t){this.trackedLinkElements.has(t)||(this.trackedLinkElements.add(t),this.trackStylesheetInLinkElement(t))}adoptStyleSheets(t,e){if(0===t.length)return;const n={id:e,styleIds:[]},s=[];for(const r of t){let t;this.styleMirror.has(r)?t=this.styleMirror.getId(r):(t=this.styleMirror.add(r),s.push({styleId:t,rules:Array.from(r.rules||CSSRule,((t,e)=>({rule:N(t),index:e})))})),n.styleIds.push(t)}s.length>0&&(n.styles=s),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(t){}}class xe{constructor(){this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}periodicallyClear(){requestAnimationFrame((()=>{this.clear(),this.loop&&this.periodicallyClear()}))}inOtherBuffer(t,e){const n=this.nodeMap.get(t);return n&&Array.from(n).some((t=>t!==e))}add(t,e){this.nodeMap.set(t,(this.nodeMap.get(t)||new Set).add(e))}clear(){this.nodeMap=new WeakMap}destroy(){this.loop=!1}}function Re(t){return Object.assign(Object.assign({},t),{timestamp:gt()})}let Ae,De,Ne,Oe=!1;const Me=new O;function Ue(t={}){const{emit:e,checkoutEveryNms:n,checkoutEveryNth:s,blockClass:r="rr-block",blockSelector:i=null,unblockSelector:a=null,ignoreClass:o="rr-ignore",ignoreSelector:c=null,maskAllText:l=!1,maskTextClass:u="rr-mask",unmaskTextClass:d=null,maskTextSelector:h=null,unmaskTextSelector:p=null,inlineStylesheet:f=!0,maskAllInputs:m,maskInputOptions:_,slimDOMOptions:g,maskAttributeFn:y,maskInputFn:b,maskTextFn:v,hooks:S,packFn:k,sampling:w={},dataURLOptions:E={},mousemoveWait:I,recordCanvas:C=!1,recordCrossOriginIframes:T=!1,recordAfter:x=("DOMContentLoaded"===t.recordAfter?t.recordAfter:"load"),userTriggeredOnInput:R=!1,collectFonts:A=!1,inlineImages:D=!1,plugins:N,keepIframeSrcFn:M=(()=>!1),ignoreCSSAttributes:U=new Set([]),errorHandler:B,onMutation:G}=t;Ht(B);const L=!T||window.parent===window;let F=!1;if(!L)try{window.parent.document&&(F=!1)}catch(X){F=!0}if(L&&!e)throw new Error("emit function is required");void 0!==I&&void 0===w.mousemove&&(w.mousemove=I),Me.reset();const z=!0===m?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==_?_:{},Y=!0===g||"all"===g?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===g,headMetaDescKeywords:"all"===g}:g||{};let P;!function(t=window){"NodeList"in t&&!t.NodeList.prototype.forEach&&(t.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach&&(t.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...t)=>{let e=t[0];if(!(0 in t))throw new TypeError("1 argument is required");do{if(this===e)return!0}while(e=e&&e.parentNode);return!1})}();let Z=0;const $=t=>{for(const e of N||[])e.eventProcessor&&(t=e.eventProcessor(t));return k&&!F&&(t=k(t)),t};Ae=(t,r)=>{var i;if(!(null===(i=Jt[0])||void 0===i?void 0:i.isFrozen())||t.type===Nt.FullSnapshot||t.type===Nt.IncrementalSnapshot&&t.data.source===Ot.Mutation||Jt.forEach((t=>t.unfreeze())),L)null===e||void 0===e||e($(t),r);else if(F){const e={type:"rrweb",event:$(t),origin:window.location.origin,isCheckout:r};window.parent.postMessage(e,"*")}if(t.type===Nt.FullSnapshot)P=t,Z=0;else if(t.type===Nt.IncrementalSnapshot){if(t.data.source===Ot.Mutation&&t.data.isAttachIframe)return;Z++;const e=s&&Z>=s,r=n&&t.timestamp-P.timestamp>n;(e||r)&&De(!0)}};const W=t=>{Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.Mutation},t)}))},j=t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.Scroll},t)})),H=t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.CanvasMutation},t)})),V=new Te({mutationCb:W,adoptedStyleSheetCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.AdoptedStyleSheet},t)}))}),K=new de({mirror:Me,mutationCb:W,stylesheetManager:V,recordCrossOriginIframes:T,wrappedEmit:Ae});for(const O of N||[])O.getMirror&&O.getMirror({nodeMirror:Me,crossOriginIframeMirror:K.crossOriginIframeMirror,crossOriginIframeStyleMirror:K.crossOriginIframeStyleMirror});const J=new xe;Ne=new Ce({recordCanvas:C,mutationCb:H,win:window,blockClass:r,blockSelector:i,unblockSelector:a,mirror:Me,sampling:w.canvas,dataURLOptions:E});const q=new he({mutationCb:W,scrollCb:j,bypassOptions:{onMutation:G,blockClass:r,blockSelector:i,unblockSelector:a,maskAllText:l,maskTextClass:u,unmaskTextClass:d,maskTextSelector:h,unmaskTextSelector:p,inlineStylesheet:f,maskInputOptions:z,dataURLOptions:E,maskAttributeFn:y,maskTextFn:v,maskInputFn:b,recordCanvas:C,inlineImages:D,sampling:w,slimDOMOptions:Y,iframeManager:K,stylesheetManager:V,canvasManager:Ne,keepIframeSrcFn:M,processedNodeManager:J},mirror:Me});De=(t=!1)=>{Ae(Re({type:Nt.Meta,data:{href:window.location.href,width:vt(),height:bt()}}),t),V.reset(),q.init(),Jt.forEach((t=>t.lock()));const e=function(t,e){const{mirror:n=new O,blockClass:s="rr-block",blockSelector:r=null,unblockSelector:i=null,maskAllText:a=!1,maskTextClass:o="rr-mask",unmaskTextClass:c=null,maskTextSelector:l=null,unmaskTextSelector:u=null,inlineStylesheet:d=!0,inlineImages:h=!1,recordCanvas:p=!1,maskAllInputs:f=!1,maskAttributeFn:m,maskTextFn:_,maskInputFn:g,slimDOM:y=!1,dataURLOptions:b,preserveWhiteSpace:v,onSerialize:S,onIframeLoad:k,iframeLoadTimeout:w,onStylesheetLoad:E,stylesheetLoadTimeout:I,keepIframeSrcFn:C=(()=>!1)}=e||{};return ut(t,{doc:t,mirror:n,blockClass:s,blockSelector:r,unblockSelector:i,maskAllText:a,maskTextClass:o,unmaskTextClass:c,maskTextSelector:l,unmaskTextSelector:u,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===f?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===f?{}:f,maskAttributeFn:m,maskTextFn:_,maskInputFn:g,slimDOMOptions:!0===y||"all"===y?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===y,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===y?{}:y,dataURLOptions:b,inlineImages:h,recordCanvas:p,preserveWhiteSpace:v,onSerialize:S,onIframeLoad:k,iframeLoadTimeout:w,onStylesheetLoad:E,stylesheetLoadTimeout:I,keepIframeSrcFn:C,newlyAddedElement:!1})}(document,{mirror:Me,blockClass:r,blockSelector:i,unblockSelector:a,maskAllText:l,maskTextClass:u,unmaskTextClass:d,maskTextSelector:h,unmaskTextSelector:p,inlineStylesheet:f,maskAllInputs:z,maskAttributeFn:y,maskInputFn:b,maskTextFn:v,slimDOM:Y,dataURLOptions:E,recordCanvas:C,inlineImages:D,onSerialize:t=>{It(t,Me)&&K.addIframe(t),Ct(t,Me)&&V.trackLinkElement(t),Tt(t)&&q.addShadowRoot(t.shadowRoot,document)},onIframeLoad:(t,e)=>{K.attachIframe(t,e),q.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{V.attachLinkElement(t,e)},keepIframeSrcFn:M});if(!e)return console.warn("Failed to snapshot the document");Ae(Re({type:Nt.FullSnapshot,data:{node:e,initialOffset:yt(window)}}),t),Jt.forEach((t=>t.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&V.adoptStyleSheets(document.adoptedStyleSheets,Me.getId(document))};try{const t=[],e=t=>{var e;return Kt(oe)({onMutation:G,mutationCb:W,mousemoveCb:(t,e)=>Ae(Re({type:Nt.IncrementalSnapshot,data:{source:e,positions:t}})),mouseInteractionCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.MouseInteraction},t)})),scrollCb:j,viewportResizeCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.ViewportResize},t)})),inputCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.Input},t)})),mediaInteractionCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.MediaInteraction},t)})),styleSheetRuleCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.StyleSheetRule},t)})),styleDeclarationCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.StyleDeclaration},t)})),canvasMutationCb:H,fontCb:t=>Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.Font},t)})),selectionCb:t=>{Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.Selection},t)}))},customElementCb:t=>{Ae(Re({type:Nt.IncrementalSnapshot,data:Object.assign({source:Ot.CustomElement},t)}))},blockClass:r,ignoreClass:o,ignoreSelector:c,maskAllText:l,maskTextClass:u,unmaskTextClass:d,maskTextSelector:h,unmaskTextSelector:p,maskInputOptions:z,inlineStylesheet:f,sampling:w,recordCanvas:C,inlineImages:D,userTriggeredOnInput:R,collectFonts:A,doc:t,maskAttributeFn:y,maskInputFn:b,maskTextFn:v,keepIframeSrcFn:M,blockSelector:i,unblockSelector:a,slimDOMOptions:Y,dataURLOptions:E,mirror:Me,iframeManager:K,stylesheetManager:V,shadowDomManager:q,processedNodeManager:J,canvasManager:Ne,ignoreCSSAttributes:U,plugins:(null===(e=null===N||void 0===N?void 0:N.filter((t=>t.observer)))||void 0===e?void 0:e.map((t=>({observer:t.observer,options:t.options,callback:e=>Ae(Re({type:Nt.Plugin,data:{plugin:t.name,payload:e}}))}))))||[]},S)};K.addLoadListener((n=>{try{t.push(e(n.contentDocument))}catch(s){console.warn(s)}}));const n=()=>{De(),t.push(e(document)),Oe=!0};return"interactive"===document.readyState||"complete"===document.readyState?n():(t.push(dt("DOMContentLoaded",(()=>{Ae(Re({type:Nt.DomContentLoaded,data:{}})),"DOMContentLoaded"===x&&n()}))),t.push(dt("load",(()=>{Ae(Re({type:Nt.Load,data:{}})),"load"===x&&n()}),window))),()=>{t.forEach((t=>t())),J.destroy(),Oe=!1,Vt()}}catch(Q){console.warn(Q)}}function Be(t){return t>9999999999?t:1e3*t}function Ge(t,e){"sentry.transaction"!==e.category&&(["ui.click","ui.input"].includes(e.category)?t.triggerUserActivity():t.checkAndHandleExpiredSession(),t.addUpdate((()=>(t.throttledAddEvent({type:Nt.Custom,timestamp:1e3*(e.timestamp||0),data:{tag:"breadcrumb",payload:(0,u.Fv)(e,10,1e3)}}),"console"===e.category))))}Ue.addCustomEvent=(t,e)=>{if(!Oe)throw new Error("please add custom event after start recording");Ae(Re({type:Nt.Custom,data:{tag:t,payload:e}}))},Ue.freezePage=()=>{Jt.forEach((t=>t.freeze()))},Ue.takeFullSnapshot=t=>{if(!Oe)throw new Error("please take full snapshot after start recording");De(t)},Ue.mirror=Me;const Le="button,a";function Fe(t){const e=ze(t);if(!e||!(e instanceof Element))return e;return e.closest(Le)||e}function ze(t){return function(t){return"object"===typeof t&&!!t&&"target"in t}(t)?t.target:t}let Ye;function Pe(t){return Ye||(Ye=[],(0,d.hl)(v,"open",(function(t){return function(...e){if(Ye)try{Ye.forEach((t=>t()))}catch(n){}return t.apply(v,e)}}))),Ye.push(t),()=>{const e=Ye?Ye.indexOf(t):-1;e>-1&&Ye.splice(e,1)}}class Ze{constructor(t,e,n=Ge){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=e.timeout/1e3,this._threshold=e.threshold/1e3,this._scollTimeout=e.scrollTimeout/1e3,this._replay=t,this._ignoreSelector=e.ignoreSelector,this._addBreadcrumbEvent=n}addListeners(){const t=()=>{this._lastScroll=We()},e=Pe((()=>{this._lastMutation=We()})),n=t=>{if(!t.target)return;const e=Fe(t);e&&this._handleMultiClick(e)},s=new MutationObserver((()=>{this._lastMutation=We()}));s.observe(v.document.documentElement,{attributes:!0,characterData:!0,childList:!0,subtree:!0}),v.addEventListener("scroll",t,{passive:!0}),v.addEventListener("click",n,{passive:!0}),this._teardown=()=>{v.removeEventListener("scroll",t),v.removeEventListener("click",n),e(),s.disconnect(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(t,e){if(function(t,e){if(!$e.includes(t.tagName))return!0;if("INPUT"===t.tagName&&!["submit","button"].includes(t.getAttribute("type")||""))return!0;if("A"===t.tagName&&(t.hasAttribute("download")||t.hasAttribute("target")&&"_self"!==t.getAttribute("target")))return!0;if(e&&t.matches(e))return!0;return!1}(e,this._ignoreSelector)||!function(t){return!(!t.data||"number"!==typeof t.data.nodeId||!t.timestamp)}(t))return;const n={timestamp:(s=t.timestamp,s>9999999999?s/1e3:s),clickBreadcrumb:t,clickCount:0,node:e};var s;this._clicks.some((t=>t.node===n.node&&Math.abs(t.timestamp-n.timestamp)<1))||(this._clicks.push(n),1===this._clicks.length&&this._scheduleCheckClicks())}_handleMultiClick(t){this._getClicks(t).forEach((t=>{t.clickCount++}))}_getClicks(t){return this._clicks.filter((e=>e.node===t))}_checkClicks(){const t=[],e=We();this._clicks.forEach((n=>{!n.mutationAfter&&this._lastMutation&&(n.mutationAfter=n.timestamp<=this._lastMutation?this._lastMutation-n.timestamp:void 0),!n.scrollAfter&&this._lastScroll&&(n.scrollAfter=n.timestamp<=this._lastScroll?this._lastScroll-n.timestamp:void 0),n.timestamp+this._timeout<=e&&t.push(n)}));for(const n of t){const t=this._clicks.indexOf(n);t>-1&&(this._generateBreadcrumbs(n),this._clicks.splice(t,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(t){const e=this._replay,n=t.scrollAfter&&t.scrollAfter<=this._scollTimeout,s=t.mutationAfter&&t.mutationAfter<=this._threshold,r=!n&&!s,{clickCount:i,clickBreadcrumb:a}=t;if(r){const n=1e3*Math.min(t.mutationAfter||this._timeout,this._timeout),s=n<1e3*this._timeout?"mutation":"timeout",r={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.slowClickDetected",data:{...a.data,url:v.location.href,route:e.getCurrentRoute(),timeAfterClickMs:n,endReason:s,clickCount:i||1}};this._addBreadcrumbEvent(e,r)}else if(i>1){const t={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.multiClick",data:{...a.data,url:v.location.href,route:e.getCurrentRoute(),clickCount:i,metric:!0}};this._addBreadcrumbEvent(e,t)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=setTimeout((()=>this._checkClicks()),1e3)}}const $e=["A","BUTTON","INPUT"];function We(){return Date.now()/1e3}function je(t){return{timestamp:Date.now()/1e3,type:"default",...t}}var He;!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(He||(He={}));const Ve=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled"]);function Ke(t){const e={};for(const n in t)if(Ve.has(n)){let s=n;"data-testid"!==n&&"data-test-id"!==n||(s="testId"),e[s]=t[n]}return e}const Je=t=>e=>{if(!t.isEnabled())return;const n=function(t){const{target:e,message:n}=function(t){const e="click"===t.name;let n,s=null;try{s=e?Fe(t.event):ze(t.event),n=(0,h.Rt)(s,{maxStringLength:200})||"<unknown>"}catch(r){n="<unknown>"}return{target:s,message:n}}(t);return je({category:`ui.${t.name}`,...qe(e,n)})}(e);if(!n)return;const s="click"===e.name,r=s&&e.event;!(s&&t.clickDetector&&r)||r.altKey||r.metaKey||r.ctrlKey||r.shiftKey||function(t,e,n){t.handleClick(e,n)}(t.clickDetector,n,Fe(e.event)),Ge(t,n)};function qe(t,e){const n=Ue.mirror.getId(t),s=n&&Ue.mirror.getNode(n),r=s&&Ue.mirror.getMeta(s),i=r&&function(t){return t.type===He.Element}(r)?r:null;return{message:e,data:i?{nodeId:n,node:{id:n,tagName:i.tagName,textContent:Array.from(i.childNodes).map((t=>t.type===He.Text&&t.textContent)).filter(Boolean).map((t=>t.trim())).join(""),attributes:Ke(i.attributes)}}:{}}}function Xe(t,e){if(!t.isEnabled())return;t.updateUserActivity();const n=function(t){const{metaKey:e,shiftKey:n,ctrlKey:s,altKey:r,key:i,target:a}=t;if(!a||function(t){return"INPUT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable}(a)||!i)return null;const o=e||s||r,c=1===i.length;if(!o&&c)return null;const l=(0,h.Rt)(a,{maxStringLength:200})||"<unknown>",u=qe(a,l);return je({category:"ui.keyDown",message:l,data:{...u.data,metaKey:e,shiftKey:n,ctrlKey:s,altKey:r,key:i}})}(e);n&&Ge(t,n)}const Qe=["name","type","startTime","transferSize","duration"];function tn(t){return function(e){return Qe.every((n=>t[n]===e[n]))}}function en(t){const e=new PerformanceObserver((e=>{const n=function(t,e){const[n,s,r]=t.reduce(((t,e)=>("navigation"===e.entryType?t[0].push(e):"largest-contentful-paint"===e.entryType?t[1].push(e):t[2].push(e),t)),[[],[],[]]),i=[],a=[];let o=s.length?s[s.length-1]:void 0;return e.forEach((t=>{if("largest-contentful-paint"!==t.entryType)if("navigation"!==t.entryType)i.push(t);else{const e=t;t.duration>0&&!n.find(tn(e))&&!a.find(tn(e))&&a.push(e)}else(!o||o.startTime<t.startTime)&&(o=t)})),[...o?[o]:[],...n,...r,...i,...a].sort(((t,e)=>t.startTime-e.startTime))}(t.performanceEvents,e.getEntries());t.performanceEvents=n}));return["element","event","first-input","largest-contentful-paint","layout-shift","longtask","navigation","paint","resource"].forEach((t=>{try{e.observe({type:t,buffered:!0})}catch(n){}})),e}const nn='/*! pako 2.1.0 https://github.com/nodeca/pako @license (MIT AND Zlib) */\nfunction t(t){let e=t.length;for(;--e>=0;)t[e]=0}const e=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),a=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),i=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),n=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=new Array(576);t(s);const r=new Array(60);t(r);const o=new Array(512);t(o);const l=new Array(256);t(l);const h=new Array(29);t(h);const d=new Array(30);function _(t,e,a,i,n){this.static_tree=t,this.extra_bits=e,this.extra_base=a,this.elems=i,this.max_length=n,this.has_stree=t&&t.length}let f,c,u;function w(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}t(d);const m=t=>t<256?o[t]:o[256+(t>>>7)],b=(t,e)=>{t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255},g=(t,e,a)=>{t.bi_valid>16-a?(t.bi_buf|=e<<t.bi_valid&65535,b(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=a-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=a)},p=(t,e,a)=>{g(t,a[2*e],a[2*e+1])},k=(t,e)=>{let a=0;do{a|=1&t,t>>>=1,a<<=1}while(--e>0);return a>>>1},v=(t,e,a)=>{const i=new Array(16);let n,s,r=0;for(n=1;n<=15;n++)r=r+a[n-1]<<1,i[n]=r;for(s=0;s<=e;s++){let e=t[2*s+1];0!==e&&(t[2*s]=k(i[e]++,e))}},y=t=>{let e;for(e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.sym_next=t.matches=0},x=t=>{t.bi_valid>8?b(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0},z=(t,e,a,i)=>{const n=2*e,s=2*a;return t[n]<t[s]||t[n]===t[s]&&i[e]<=i[a]},A=(t,e,a)=>{const i=t.heap[a];let n=a<<1;for(;n<=t.heap_len&&(n<t.heap_len&&z(e,t.heap[n+1],t.heap[n],t.depth)&&n++,!z(e,i,t.heap[n],t.depth));)t.heap[a]=t.heap[n],a=n,n<<=1;t.heap[a]=i},E=(t,i,n)=>{let s,r,o,_,f=0;if(0!==t.sym_next)do{s=255&t.pending_buf[t.sym_buf+f++],s+=(255&t.pending_buf[t.sym_buf+f++])<<8,r=t.pending_buf[t.sym_buf+f++],0===s?p(t,r,i):(o=l[r],p(t,o+256+1,i),_=e[o],0!==_&&(r-=h[o],g(t,r,_)),s--,o=m(s),p(t,o,n),_=a[o],0!==_&&(s-=d[o],g(t,s,_)))}while(f<t.sym_next);p(t,256,i)},R=(t,e)=>{const a=e.dyn_tree,i=e.stat_desc.static_tree,n=e.stat_desc.has_stree,s=e.stat_desc.elems;let r,o,l,h=-1;for(t.heap_len=0,t.heap_max=573,r=0;r<s;r++)0!==a[2*r]?(t.heap[++t.heap_len]=h=r,t.depth[r]=0):a[2*r+1]=0;for(;t.heap_len<2;)l=t.heap[++t.heap_len]=h<2?++h:0,a[2*l]=1,t.depth[l]=0,t.opt_len--,n&&(t.static_len-=i[2*l+1]);for(e.max_code=h,r=t.heap_len>>1;r>=1;r--)A(t,a,r);l=s;do{r=t.heap[1],t.heap[1]=t.heap[t.heap_len--],A(t,a,1),o=t.heap[1],t.heap[--t.heap_max]=r,t.heap[--t.heap_max]=o,a[2*l]=a[2*r]+a[2*o],t.depth[l]=(t.depth[r]>=t.depth[o]?t.depth[r]:t.depth[o])+1,a[2*r+1]=a[2*o+1]=l,t.heap[1]=l++,A(t,a,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],((t,e)=>{const a=e.dyn_tree,i=e.max_code,n=e.stat_desc.static_tree,s=e.stat_desc.has_stree,r=e.stat_desc.extra_bits,o=e.stat_desc.extra_base,l=e.stat_desc.max_length;let h,d,_,f,c,u,w=0;for(f=0;f<=15;f++)t.bl_count[f]=0;for(a[2*t.heap[t.heap_max]+1]=0,h=t.heap_max+1;h<573;h++)d=t.heap[h],f=a[2*a[2*d+1]+1]+1,f>l&&(f=l,w++),a[2*d+1]=f,d>i||(t.bl_count[f]++,c=0,d>=o&&(c=r[d-o]),u=a[2*d],t.opt_len+=u*(f+c),s&&(t.static_len+=u*(n[2*d+1]+c)));if(0!==w){do{for(f=l-1;0===t.bl_count[f];)f--;t.bl_count[f]--,t.bl_count[f+1]+=2,t.bl_count[l]--,w-=2}while(w>0);for(f=l;0!==f;f--)for(d=t.bl_count[f];0!==d;)_=t.heap[--h],_>i||(a[2*_+1]!==f&&(t.opt_len+=(f-a[2*_+1])*a[2*_],a[2*_+1]=f),d--)}})(t,e),v(a,h,t.bl_count)},Z=(t,e,a)=>{let i,n,s=-1,r=e[1],o=0,l=7,h=4;for(0===r&&(l=138,h=3),e[2*(a+1)+1]=65535,i=0;i<=a;i++)n=r,r=e[2*(i+1)+1],++o<l&&n===r||(o<h?t.bl_tree[2*n]+=o:0!==n?(n!==s&&t.bl_tree[2*n]++,t.bl_tree[32]++):o<=10?t.bl_tree[34]++:t.bl_tree[36]++,o=0,s=n,0===r?(l=138,h=3):n===r?(l=6,h=3):(l=7,h=4))},U=(t,e,a)=>{let i,n,s=-1,r=e[1],o=0,l=7,h=4;for(0===r&&(l=138,h=3),i=0;i<=a;i++)if(n=r,r=e[2*(i+1)+1],!(++o<l&&n===r)){if(o<h)do{p(t,n,t.bl_tree)}while(0!=--o);else 0!==n?(n!==s&&(p(t,n,t.bl_tree),o--),p(t,16,t.bl_tree),g(t,o-3,2)):o<=10?(p(t,17,t.bl_tree),g(t,o-3,3)):(p(t,18,t.bl_tree),g(t,o-11,7));o=0,s=n,0===r?(l=138,h=3):n===r?(l=6,h=3):(l=7,h=4)}};let S=!1;const D=(t,e,a,i)=>{g(t,0+(i?1:0),3),x(t),b(t,a),b(t,~a),a&&t.pending_buf.set(t.window.subarray(e,e+a),t.pending),t.pending+=a};var T=(t,e,a,i)=>{let o,l,h=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=(t=>{let e,a=4093624447;for(e=0;e<=31;e++,a>>>=1)if(1&a&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<256;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0})(t)),R(t,t.l_desc),R(t,t.d_desc),h=(t=>{let e;for(Z(t,t.dyn_ltree,t.l_desc.max_code),Z(t,t.dyn_dtree,t.d_desc.max_code),R(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*n[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e})(t),o=t.opt_len+3+7>>>3,l=t.static_len+3+7>>>3,l<=o&&(o=l)):o=l=a+5,a+4<=o&&-1!==e?D(t,e,a,i):4===t.strategy||l===o?(g(t,2+(i?1:0),3),E(t,s,r)):(g(t,4+(i?1:0),3),((t,e,a,i)=>{let s;for(g(t,e-257,5),g(t,a-1,5),g(t,i-4,4),s=0;s<i;s++)g(t,t.bl_tree[2*n[s]+1],3);U(t,t.dyn_ltree,e-1),U(t,t.dyn_dtree,a-1)})(t,t.l_desc.max_code+1,t.d_desc.max_code+1,h+1),E(t,t.dyn_ltree,t.dyn_dtree)),y(t),i&&x(t)},O={_tr_init:t=>{S||((()=>{let t,n,w,m,b;const g=new Array(16);for(w=0,m=0;m<28;m++)for(h[m]=w,t=0;t<1<<e[m];t++)l[w++]=m;for(l[w-1]=m,b=0,m=0;m<16;m++)for(d[m]=b,t=0;t<1<<a[m];t++)o[b++]=m;for(b>>=7;m<30;m++)for(d[m]=b<<7,t=0;t<1<<a[m]-7;t++)o[256+b++]=m;for(n=0;n<=15;n++)g[n]=0;for(t=0;t<=143;)s[2*t+1]=8,t++,g[8]++;for(;t<=255;)s[2*t+1]=9,t++,g[9]++;for(;t<=279;)s[2*t+1]=7,t++,g[7]++;for(;t<=287;)s[2*t+1]=8,t++,g[8]++;for(v(s,287,g),t=0;t<30;t++)r[2*t+1]=5,r[2*t]=k(t,5);f=new _(s,e,257,286,15),c=new _(r,a,0,30,15),u=new _(new Array(0),i,0,19,7)})(),S=!0),t.l_desc=new w(t.dyn_ltree,f),t.d_desc=new w(t.dyn_dtree,c),t.bl_desc=new w(t.bl_tree,u),t.bi_buf=0,t.bi_valid=0,y(t)},_tr_stored_block:D,_tr_flush_block:T,_tr_tally:(t,e,a)=>(t.pending_buf[t.sym_buf+t.sym_next++]=e,t.pending_buf[t.sym_buf+t.sym_next++]=e>>8,t.pending_buf[t.sym_buf+t.sym_next++]=a,0===e?t.dyn_ltree[2*a]++:(t.matches++,e--,t.dyn_ltree[2*(l[a]+256+1)]++,t.dyn_dtree[2*m(e)]++),t.sym_next===t.sym_end),_tr_align:t=>{g(t,2,3),p(t,256,s),(t=>{16===t.bi_valid?(b(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)})(t)}};var F=(t,e,a,i)=>{let n=65535&t|0,s=t>>>16&65535|0,r=0;for(;0!==a;){r=a>2e3?2e3:a,a-=r;do{n=n+e[i++]|0,s=s+n|0}while(--r);n%=65521,s%=65521}return n|s<<16|0};const L=new Uint32Array((()=>{let t,e=[];for(var a=0;a<256;a++){t=a;for(var i=0;i<8;i++)t=1&t?3988292384^t>>>1:t>>>1;e[a]=t}return e})());var N=(t,e,a,i)=>{const n=L,s=i+a;t^=-1;for(let a=i;a<s;a++)t=t>>>8^n[255&(t^e[a])];return-1^t},I={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},B={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};const{_tr_init:C,_tr_stored_block:H,_tr_flush_block:M,_tr_tally:j,_tr_align:K}=O,{Z_NO_FLUSH:P,Z_PARTIAL_FLUSH:Y,Z_FULL_FLUSH:G,Z_FINISH:X,Z_BLOCK:W,Z_OK:q,Z_STREAM_END:J,Z_STREAM_ERROR:Q,Z_DATA_ERROR:V,Z_BUF_ERROR:$,Z_DEFAULT_COMPRESSION:tt,Z_FILTERED:et,Z_HUFFMAN_ONLY:at,Z_RLE:it,Z_FIXED:nt,Z_DEFAULT_STRATEGY:st,Z_UNKNOWN:rt,Z_DEFLATED:ot}=B,lt=(t,e)=>(t.msg=I[e],e),ht=t=>2*t-(t>4?9:0),dt=t=>{let e=t.length;for(;--e>=0;)t[e]=0},_t=t=>{let e,a,i,n=t.w_size;e=t.hash_size,i=e;do{a=t.head[--i],t.head[i]=a>=n?a-n:0}while(--e);e=n,i=e;do{a=t.prev[--i],t.prev[i]=a>=n?a-n:0}while(--e)};let ft=(t,e,a)=>(e<<t.hash_shift^a)&t.hash_mask;const ct=t=>{const e=t.state;let a=e.pending;a>t.avail_out&&(a=t.avail_out),0!==a&&(t.output.set(e.pending_buf.subarray(e.pending_out,e.pending_out+a),t.next_out),t.next_out+=a,e.pending_out+=a,t.total_out+=a,t.avail_out-=a,e.pending-=a,0===e.pending&&(e.pending_out=0))},ut=(t,e)=>{M(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,ct(t.strm)},wt=(t,e)=>{t.pending_buf[t.pending++]=e},mt=(t,e)=>{t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e},bt=(t,e,a,i)=>{let n=t.avail_in;return n>i&&(n=i),0===n?0:(t.avail_in-=n,e.set(t.input.subarray(t.next_in,t.next_in+n),a),1===t.state.wrap?t.adler=F(t.adler,e,n,a):2===t.state.wrap&&(t.adler=N(t.adler,e,n,a)),t.next_in+=n,t.total_in+=n,n)},gt=(t,e)=>{let a,i,n=t.max_chain_length,s=t.strstart,r=t.prev_length,o=t.nice_match;const l=t.strstart>t.w_size-262?t.strstart-(t.w_size-262):0,h=t.window,d=t.w_mask,_=t.prev,f=t.strstart+258;let c=h[s+r-1],u=h[s+r];t.prev_length>=t.good_match&&(n>>=2),o>t.lookahead&&(o=t.lookahead);do{if(a=e,h[a+r]===u&&h[a+r-1]===c&&h[a]===h[s]&&h[++a]===h[s+1]){s+=2,a++;do{}while(h[++s]===h[++a]&&h[++s]===h[++a]&&h[++s]===h[++a]&&h[++s]===h[++a]&&h[++s]===h[++a]&&h[++s]===h[++a]&&h[++s]===h[++a]&&h[++s]===h[++a]&&s<f);if(i=258-(f-s),s=f-258,i>r){if(t.match_start=e,r=i,i>=o)break;c=h[s+r-1],u=h[s+r]}}}while((e=_[e&d])>l&&0!=--n);return r<=t.lookahead?r:t.lookahead},pt=t=>{const e=t.w_size;let a,i,n;do{if(i=t.window_size-t.lookahead-t.strstart,t.strstart>=e+(e-262)&&(t.window.set(t.window.subarray(e,e+e-i),0),t.match_start-=e,t.strstart-=e,t.block_start-=e,t.insert>t.strstart&&(t.insert=t.strstart),_t(t),i+=e),0===t.strm.avail_in)break;if(a=bt(t.strm,t.window,t.strstart+t.lookahead,i),t.lookahead+=a,t.lookahead+t.insert>=3)for(n=t.strstart-t.insert,t.ins_h=t.window[n],t.ins_h=ft(t,t.ins_h,t.window[n+1]);t.insert&&(t.ins_h=ft(t,t.ins_h,t.window[n+3-1]),t.prev[n&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=n,n++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<262&&0!==t.strm.avail_in)},kt=(t,e)=>{let a,i,n,s=t.pending_buf_size-5>t.w_size?t.w_size:t.pending_buf_size-5,r=0,o=t.strm.avail_in;do{if(a=65535,n=t.bi_valid+42>>3,t.strm.avail_out<n)break;if(n=t.strm.avail_out-n,i=t.strstart-t.block_start,a>i+t.strm.avail_in&&(a=i+t.strm.avail_in),a>n&&(a=n),a<s&&(0===a&&e!==X||e===P||a!==i+t.strm.avail_in))break;r=e===X&&a===i+t.strm.avail_in?1:0,H(t,0,0,r),t.pending_buf[t.pending-4]=a,t.pending_buf[t.pending-3]=a>>8,t.pending_buf[t.pending-2]=~a,t.pending_buf[t.pending-1]=~a>>8,ct(t.strm),i&&(i>a&&(i=a),t.strm.output.set(t.window.subarray(t.block_start,t.block_start+i),t.strm.next_out),t.strm.next_out+=i,t.strm.avail_out-=i,t.strm.total_out+=i,t.block_start+=i,a-=i),a&&(bt(t.strm,t.strm.output,t.strm.next_out,a),t.strm.next_out+=a,t.strm.avail_out-=a,t.strm.total_out+=a)}while(0===r);return o-=t.strm.avail_in,o&&(o>=t.w_size?(t.matches=2,t.window.set(t.strm.input.subarray(t.strm.next_in-t.w_size,t.strm.next_in),0),t.strstart=t.w_size,t.insert=t.strstart):(t.window_size-t.strstart<=o&&(t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,t.insert>t.strstart&&(t.insert=t.strstart)),t.window.set(t.strm.input.subarray(t.strm.next_in-o,t.strm.next_in),t.strstart),t.strstart+=o,t.insert+=o>t.w_size-t.insert?t.w_size-t.insert:o),t.block_start=t.strstart),t.high_water<t.strstart&&(t.high_water=t.strstart),r?4:e!==P&&e!==X&&0===t.strm.avail_in&&t.strstart===t.block_start?2:(n=t.window_size-t.strstart,t.strm.avail_in>n&&t.block_start>=t.w_size&&(t.block_start-=t.w_size,t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,n+=t.w_size,t.insert>t.strstart&&(t.insert=t.strstart)),n>t.strm.avail_in&&(n=t.strm.avail_in),n&&(bt(t.strm,t.window,t.strstart,n),t.strstart+=n,t.insert+=n>t.w_size-t.insert?t.w_size-t.insert:n),t.high_water<t.strstart&&(t.high_water=t.strstart),n=t.bi_valid+42>>3,n=t.pending_buf_size-n>65535?65535:t.pending_buf_size-n,s=n>t.w_size?t.w_size:n,i=t.strstart-t.block_start,(i>=s||(i||e===X)&&e!==P&&0===t.strm.avail_in&&i<=n)&&(a=i>n?n:i,r=e===X&&0===t.strm.avail_in&&a===i?1:0,H(t,t.block_start,a,r),t.block_start+=a,ct(t.strm)),r?3:1)},vt=(t,e)=>{let a,i;for(;;){if(t.lookahead<262){if(pt(t),t.lookahead<262&&e===P)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=ft(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==a&&t.strstart-a<=t.w_size-262&&(t.match_length=gt(t,a)),t.match_length>=3)if(i=j(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=ft(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=ft(t,t.ins_h,t.window[t.strstart+1]);else i=j(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(i&&(ut(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,e===X?(ut(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(ut(t,!1),0===t.strm.avail_out)?1:2},yt=(t,e)=>{let a,i,n;for(;;){if(t.lookahead<262){if(pt(t),t.lookahead<262&&e===P)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=ft(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==a&&t.prev_length<t.max_lazy_match&&t.strstart-a<=t.w_size-262&&(t.match_length=gt(t,a),t.match_length<=5&&(t.strategy===et||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){n=t.strstart+t.lookahead-3,i=j(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=n&&(t.ins_h=ft(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,i&&(ut(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if(i=j(t,0,t.window[t.strstart-1]),i&&ut(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(i=j(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,e===X?(ut(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(ut(t,!1),0===t.strm.avail_out)?1:2};function xt(t,e,a,i,n){this.good_length=t,this.max_lazy=e,this.nice_length=a,this.max_chain=i,this.func=n}const zt=[new xt(0,0,0,0,kt),new xt(4,4,8,4,vt),new xt(4,5,16,8,vt),new xt(4,6,32,32,vt),new xt(4,4,16,16,yt),new xt(8,16,32,32,yt),new xt(8,16,128,128,yt),new xt(8,32,128,256,yt),new xt(32,128,258,1024,yt),new xt(32,258,258,4096,yt)];function At(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=ot,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),dt(this.dyn_ltree),dt(this.dyn_dtree),dt(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),dt(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),dt(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const Et=t=>{if(!t)return 1;const e=t.state;return!e||e.strm!==t||42!==e.status&&57!==e.status&&69!==e.status&&73!==e.status&&91!==e.status&&103!==e.status&&113!==e.status&&666!==e.status?1:0},Rt=t=>{if(Et(t))return lt(t,Q);t.total_in=t.total_out=0,t.data_type=rt;const e=t.state;return e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=2===e.wrap?57:e.wrap?42:113,t.adler=2===e.wrap?0:1,e.last_flush=-2,C(e),q},Zt=t=>{const e=Rt(t);var a;return e===q&&((a=t.state).window_size=2*a.w_size,dt(a.head),a.max_lazy_match=zt[a.level].max_lazy,a.good_match=zt[a.level].good_length,a.nice_match=zt[a.level].nice_length,a.max_chain_length=zt[a.level].max_chain,a.strstart=0,a.block_start=0,a.lookahead=0,a.insert=0,a.match_length=a.prev_length=2,a.match_available=0,a.ins_h=0),e},Ut=(t,e,a,i,n,s)=>{if(!t)return Q;let r=1;if(e===tt&&(e=6),i<0?(r=0,i=-i):i>15&&(r=2,i-=16),n<1||n>9||a!==ot||i<8||i>15||e<0||e>9||s<0||s>nt||8===i&&1!==r)return lt(t,Q);8===i&&(i=9);const o=new At;return t.state=o,o.strm=t,o.status=42,o.wrap=r,o.gzhead=null,o.w_bits=i,o.w_size=1<<o.w_bits,o.w_mask=o.w_size-1,o.hash_bits=n+7,o.hash_size=1<<o.hash_bits,o.hash_mask=o.hash_size-1,o.hash_shift=~~((o.hash_bits+3-1)/3),o.window=new Uint8Array(2*o.w_size),o.head=new Uint16Array(o.hash_size),o.prev=new Uint16Array(o.w_size),o.lit_bufsize=1<<n+6,o.pending_buf_size=4*o.lit_bufsize,o.pending_buf=new Uint8Array(o.pending_buf_size),o.sym_buf=o.lit_bufsize,o.sym_end=3*(o.lit_bufsize-1),o.level=e,o.strategy=s,o.method=a,Zt(t)};var St={deflateInit:(t,e)=>Ut(t,e,ot,15,8,st),deflateInit2:Ut,deflateReset:Zt,deflateResetKeep:Rt,deflateSetHeader:(t,e)=>Et(t)||2!==t.state.wrap?Q:(t.state.gzhead=e,q),deflate:(t,e)=>{if(Et(t)||e>W||e<0)return t?lt(t,Q):Q;const a=t.state;if(!t.output||0!==t.avail_in&&!t.input||666===a.status&&e!==X)return lt(t,0===t.avail_out?$:Q);const i=a.last_flush;if(a.last_flush=e,0!==a.pending){if(ct(t),0===t.avail_out)return a.last_flush=-1,q}else if(0===t.avail_in&&ht(e)<=ht(i)&&e!==X)return lt(t,$);if(666===a.status&&0!==t.avail_in)return lt(t,$);if(42===a.status&&0===a.wrap&&(a.status=113),42===a.status){let e=ot+(a.w_bits-8<<4)<<8,i=-1;if(i=a.strategy>=at||a.level<2?0:a.level<6?1:6===a.level?2:3,e|=i<<6,0!==a.strstart&&(e|=32),e+=31-e%31,mt(a,e),0!==a.strstart&&(mt(a,t.adler>>>16),mt(a,65535&t.adler)),t.adler=1,a.status=113,ct(t),0!==a.pending)return a.last_flush=-1,q}if(57===a.status)if(t.adler=0,wt(a,31),wt(a,139),wt(a,8),a.gzhead)wt(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),wt(a,255&a.gzhead.time),wt(a,a.gzhead.time>>8&255),wt(a,a.gzhead.time>>16&255),wt(a,a.gzhead.time>>24&255),wt(a,9===a.level?2:a.strategy>=at||a.level<2?4:0),wt(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(wt(a,255&a.gzhead.extra.length),wt(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(t.adler=N(t.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=69;else if(wt(a,0),wt(a,0),wt(a,0),wt(a,0),wt(a,0),wt(a,9===a.level?2:a.strategy>=at||a.level<2?4:0),wt(a,3),a.status=113,ct(t),0!==a.pending)return a.last_flush=-1,q;if(69===a.status){if(a.gzhead.extra){let e=a.pending,i=(65535&a.gzhead.extra.length)-a.gzindex;for(;a.pending+i>a.pending_buf_size;){let n=a.pending_buf_size-a.pending;if(a.pending_buf.set(a.gzhead.extra.subarray(a.gzindex,a.gzindex+n),a.pending),a.pending=a.pending_buf_size,a.gzhead.hcrc&&a.pending>e&&(t.adler=N(t.adler,a.pending_buf,a.pending-e,e)),a.gzindex+=n,ct(t),0!==a.pending)return a.last_flush=-1,q;e=0,i-=n}let n=new Uint8Array(a.gzhead.extra);a.pending_buf.set(n.subarray(a.gzindex,a.gzindex+i),a.pending),a.pending+=i,a.gzhead.hcrc&&a.pending>e&&(t.adler=N(t.adler,a.pending_buf,a.pending-e,e)),a.gzindex=0}a.status=73}if(73===a.status){if(a.gzhead.name){let e,i=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>i&&(t.adler=N(t.adler,a.pending_buf,a.pending-i,i)),ct(t),0!==a.pending)return a.last_flush=-1,q;i=0}e=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,wt(a,e)}while(0!==e);a.gzhead.hcrc&&a.pending>i&&(t.adler=N(t.adler,a.pending_buf,a.pending-i,i)),a.gzindex=0}a.status=91}if(91===a.status){if(a.gzhead.comment){let e,i=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>i&&(t.adler=N(t.adler,a.pending_buf,a.pending-i,i)),ct(t),0!==a.pending)return a.last_flush=-1,q;i=0}e=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,wt(a,e)}while(0!==e);a.gzhead.hcrc&&a.pending>i&&(t.adler=N(t.adler,a.pending_buf,a.pending-i,i))}a.status=103}if(103===a.status){if(a.gzhead.hcrc){if(a.pending+2>a.pending_buf_size&&(ct(t),0!==a.pending))return a.last_flush=-1,q;wt(a,255&t.adler),wt(a,t.adler>>8&255),t.adler=0}if(a.status=113,ct(t),0!==a.pending)return a.last_flush=-1,q}if(0!==t.avail_in||0!==a.lookahead||e!==P&&666!==a.status){let i=0===a.level?kt(a,e):a.strategy===at?((t,e)=>{let a;for(;;){if(0===t.lookahead&&(pt(t),0===t.lookahead)){if(e===P)return 1;break}if(t.match_length=0,a=j(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,a&&(ut(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===X?(ut(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(ut(t,!1),0===t.strm.avail_out)?1:2})(a,e):a.strategy===it?((t,e)=>{let a,i,n,s;const r=t.window;for(;;){if(t.lookahead<=258){if(pt(t),t.lookahead<=258&&e===P)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(n=t.strstart-1,i=r[n],i===r[++n]&&i===r[++n]&&i===r[++n])){s=t.strstart+258;do{}while(i===r[++n]&&i===r[++n]&&i===r[++n]&&i===r[++n]&&i===r[++n]&&i===r[++n]&&i===r[++n]&&i===r[++n]&&n<s);t.match_length=258-(s-n),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(a=j(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(a=j(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),a&&(ut(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===X?(ut(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(ut(t,!1),0===t.strm.avail_out)?1:2})(a,e):zt[a.level].func(a,e);if(3!==i&&4!==i||(a.status=666),1===i||3===i)return 0===t.avail_out&&(a.last_flush=-1),q;if(2===i&&(e===Y?K(a):e!==W&&(H(a,0,0,!1),e===G&&(dt(a.head),0===a.lookahead&&(a.strstart=0,a.block_start=0,a.insert=0))),ct(t),0===t.avail_out))return a.last_flush=-1,q}return e!==X?q:a.wrap<=0?J:(2===a.wrap?(wt(a,255&t.adler),wt(a,t.adler>>8&255),wt(a,t.adler>>16&255),wt(a,t.adler>>24&255),wt(a,255&t.total_in),wt(a,t.total_in>>8&255),wt(a,t.total_in>>16&255),wt(a,t.total_in>>24&255)):(mt(a,t.adler>>>16),mt(a,65535&t.adler)),ct(t),a.wrap>0&&(a.wrap=-a.wrap),0!==a.pending?q:J)},deflateEnd:t=>{if(Et(t))return Q;const e=t.state.status;return t.state=null,113===e?lt(t,V):q},deflateSetDictionary:(t,e)=>{let a=e.length;if(Et(t))return Q;const i=t.state,n=i.wrap;if(2===n||1===n&&42!==i.status||i.lookahead)return Q;if(1===n&&(t.adler=F(t.adler,e,a,0)),i.wrap=0,a>=i.w_size){0===n&&(dt(i.head),i.strstart=0,i.block_start=0,i.insert=0);let t=new Uint8Array(i.w_size);t.set(e.subarray(a-i.w_size,a),0),e=t,a=i.w_size}const s=t.avail_in,r=t.next_in,o=t.input;for(t.avail_in=a,t.next_in=0,t.input=e,pt(i);i.lookahead>=3;){let t=i.strstart,e=i.lookahead-2;do{i.ins_h=ft(i,i.ins_h,i.window[t+3-1]),i.prev[t&i.w_mask]=i.head[i.ins_h],i.head[i.ins_h]=t,t++}while(--e);i.strstart=t,i.lookahead=2,pt(i)}return i.strstart+=i.lookahead,i.block_start=i.strstart,i.insert=i.lookahead,i.lookahead=0,i.match_length=i.prev_length=2,i.match_available=0,t.next_in=r,t.input=o,t.avail_in=s,i.wrap=n,q},deflateInfo:"pako deflate (from Nodeca project)"};const Dt=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var Tt=function(t){const e=Array.prototype.slice.call(arguments,1);for(;e.length;){const a=e.shift();if(a){if("object"!=typeof a)throw new TypeError(a+"must be non-object");for(const e in a)Dt(a,e)&&(t[e]=a[e])}}return t},Ot=t=>{let e=0;for(let a=0,i=t.length;a<i;a++)e+=t[a].length;const a=new Uint8Array(e);for(let e=0,i=0,n=t.length;e<n;e++){let n=t[e];a.set(n,i),i+=n.length}return a};let Ft=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){Ft=!1}const Lt=new Uint8Array(256);for(let t=0;t<256;t++)Lt[t]=t>=252?6:t>=248?5:t>=240?4:t>=224?3:t>=192?2:1;Lt[254]=Lt[254]=1;var Nt=t=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);let e,a,i,n,s,r=t.length,o=0;for(n=0;n<r;n++)a=t.charCodeAt(n),55296==(64512&a)&&n+1<r&&(i=t.charCodeAt(n+1),56320==(64512&i)&&(a=65536+(a-55296<<10)+(i-56320),n++)),o+=a<128?1:a<2048?2:a<65536?3:4;for(e=new Uint8Array(o),s=0,n=0;s<o;n++)a=t.charCodeAt(n),55296==(64512&a)&&n+1<r&&(i=t.charCodeAt(n+1),56320==(64512&i)&&(a=65536+(a-55296<<10)+(i-56320),n++)),a<128?e[s++]=a:a<2048?(e[s++]=192|a>>>6,e[s++]=128|63&a):a<65536?(e[s++]=224|a>>>12,e[s++]=128|a>>>6&63,e[s++]=128|63&a):(e[s++]=240|a>>>18,e[s++]=128|a>>>12&63,e[s++]=128|a>>>6&63,e[s++]=128|63&a);return e},It=(t,e)=>{const a=e||t.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(t.subarray(0,e));let i,n;const s=new Array(2*a);for(n=0,i=0;i<a;){let e=t[i++];if(e<128){s[n++]=e;continue}let r=Lt[e];if(r>4)s[n++]=65533,i+=r-1;else{for(e&=2===r?31:3===r?15:7;r>1&&i<a;)e=e<<6|63&t[i++],r--;r>1?s[n++]=65533:e<65536?s[n++]=e:(e-=65536,s[n++]=55296|e>>10&1023,s[n++]=56320|1023&e)}}return((t,e)=>{if(e<65534&&t.subarray&&Ft)return String.fromCharCode.apply(null,t.length===e?t:t.subarray(0,e));let a="";for(let i=0;i<e;i++)a+=String.fromCharCode(t[i]);return a})(s,n)},Bt=(t,e)=>{(e=e||t.length)>t.length&&(e=t.length);let a=e-1;for(;a>=0&&128==(192&t[a]);)a--;return a<0||0===a?e:a+Lt[t[a]]>e?a:e};var Ct=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};const Ht=Object.prototype.toString,{Z_NO_FLUSH:Mt,Z_SYNC_FLUSH:jt,Z_FULL_FLUSH:Kt,Z_FINISH:Pt,Z_OK:Yt,Z_STREAM_END:Gt,Z_DEFAULT_COMPRESSION:Xt,Z_DEFAULT_STRATEGY:Wt,Z_DEFLATED:qt}=B;function Jt(t){this.options=Tt({level:Xt,method:qt,chunkSize:16384,windowBits:15,memLevel:8,strategy:Wt},t||{});let e=this.options;e.raw&&e.windowBits>0?e.windowBits=-e.windowBits:e.gzip&&e.windowBits>0&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Ct,this.strm.avail_out=0;let a=St.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(a!==Yt)throw new Error(I[a]);if(e.header&&St.deflateSetHeader(this.strm,e.header),e.dictionary){let t;if(t="string"==typeof e.dictionary?Nt(e.dictionary):"[object ArrayBuffer]"===Ht.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,a=St.deflateSetDictionary(this.strm,t),a!==Yt)throw new Error(I[a]);this._dict_set=!0}}function Qt(t,e){const a=new Jt(e);if(a.push(t,!0),a.err)throw a.msg||I[a.err];return a.result}Jt.prototype.push=function(t,e){const a=this.strm,i=this.options.chunkSize;let n,s;if(this.ended)return!1;for(s=e===~~e?e:!0===e?Pt:Mt,"string"==typeof t?a.input=Nt(t):"[object ArrayBuffer]"===Ht.call(t)?a.input=new Uint8Array(t):a.input=t,a.next_in=0,a.avail_in=a.input.length;;)if(0===a.avail_out&&(a.output=new Uint8Array(i),a.next_out=0,a.avail_out=i),(s===jt||s===Kt)&&a.avail_out<=6)this.onData(a.output.subarray(0,a.next_out)),a.avail_out=0;else{if(n=St.deflate(a,s),n===Gt)return a.next_out>0&&this.onData(a.output.subarray(0,a.next_out)),n=St.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===Yt;if(0!==a.avail_out){if(s>0&&a.next_out>0)this.onData(a.output.subarray(0,a.next_out)),a.avail_out=0;else if(0===a.avail_in)break}else this.onData(a.output)}return!0},Jt.prototype.onData=function(t){this.chunks.push(t)},Jt.prototype.onEnd=function(t){t===Yt&&(this.result=Ot(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};var Vt={Deflate:Jt,deflate:Qt,deflateRaw:function(t,e){return(e=e||{}).raw=!0,Qt(t,e)},gzip:function(t,e){return(e=e||{}).gzip=!0,Qt(t,e)},constants:B};var $t=function(t,e){let a,i,n,s,r,o,l,h,d,_,f,c,u,w,m,b,g,p,k,v,y,x,z,A;const E=t.state;a=t.next_in,z=t.input,i=a+(t.avail_in-5),n=t.next_out,A=t.output,s=n-(e-t.avail_out),r=n+(t.avail_out-257),o=E.dmax,l=E.wsize,h=E.whave,d=E.wnext,_=E.window,f=E.hold,c=E.bits,u=E.lencode,w=E.distcode,m=(1<<E.lenbits)-1,b=(1<<E.distbits)-1;t:do{c<15&&(f+=z[a++]<<c,c+=8,f+=z[a++]<<c,c+=8),g=u[f&m];e:for(;;){if(p=g>>>24,f>>>=p,c-=p,p=g>>>16&255,0===p)A[n++]=65535&g;else{if(!(16&p)){if(0==(64&p)){g=u[(65535&g)+(f&(1<<p)-1)];continue e}if(32&p){E.mode=16191;break t}t.msg="invalid literal/length code",E.mode=16209;break t}k=65535&g,p&=15,p&&(c<p&&(f+=z[a++]<<c,c+=8),k+=f&(1<<p)-1,f>>>=p,c-=p),c<15&&(f+=z[a++]<<c,c+=8,f+=z[a++]<<c,c+=8),g=w[f&b];a:for(;;){if(p=g>>>24,f>>>=p,c-=p,p=g>>>16&255,!(16&p)){if(0==(64&p)){g=w[(65535&g)+(f&(1<<p)-1)];continue a}t.msg="invalid distance code",E.mode=16209;break t}if(v=65535&g,p&=15,c<p&&(f+=z[a++]<<c,c+=8,c<p&&(f+=z[a++]<<c,c+=8)),v+=f&(1<<p)-1,v>o){t.msg="invalid distance too far back",E.mode=16209;break t}if(f>>>=p,c-=p,p=n-s,v>p){if(p=v-p,p>h&&E.sane){t.msg="invalid distance too far back",E.mode=16209;break t}if(y=0,x=_,0===d){if(y+=l-p,p<k){k-=p;do{A[n++]=_[y++]}while(--p);y=n-v,x=A}}else if(d<p){if(y+=l+d-p,p-=d,p<k){k-=p;do{A[n++]=_[y++]}while(--p);if(y=0,d<k){p=d,k-=p;do{A[n++]=_[y++]}while(--p);y=n-v,x=A}}}else if(y+=d-p,p<k){k-=p;do{A[n++]=_[y++]}while(--p);y=n-v,x=A}for(;k>2;)A[n++]=x[y++],A[n++]=x[y++],A[n++]=x[y++],k-=3;k&&(A[n++]=x[y++],k>1&&(A[n++]=x[y++]))}else{y=n-v;do{A[n++]=A[y++],A[n++]=A[y++],A[n++]=A[y++],k-=3}while(k>2);k&&(A[n++]=A[y++],k>1&&(A[n++]=A[y++]))}break}}break}}while(a<i&&n<r);k=c>>3,a-=k,c-=k<<3,f&=(1<<c)-1,t.next_in=a,t.next_out=n,t.avail_in=a<i?i-a+5:5-(a-i),t.avail_out=n<r?r-n+257:257-(n-r),E.hold=f,E.bits=c};const te=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),ee=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),ae=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),ie=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);var ne=(t,e,a,i,n,s,r,o)=>{const l=o.bits;let h,d,_,f,c,u,w=0,m=0,b=0,g=0,p=0,k=0,v=0,y=0,x=0,z=0,A=null;const E=new Uint16Array(16),R=new Uint16Array(16);let Z,U,S,D=null;for(w=0;w<=15;w++)E[w]=0;for(m=0;m<i;m++)E[e[a+m]]++;for(p=l,g=15;g>=1&&0===E[g];g--);if(p>g&&(p=g),0===g)return n[s++]=20971520,n[s++]=20971520,o.bits=1,0;for(b=1;b<g&&0===E[b];b++);for(p<b&&(p=b),y=1,w=1;w<=15;w++)if(y<<=1,y-=E[w],y<0)return-1;if(y>0&&(0===t||1!==g))return-1;for(R[1]=0,w=1;w<15;w++)R[w+1]=R[w]+E[w];for(m=0;m<i;m++)0!==e[a+m]&&(r[R[e[a+m]]++]=m);if(0===t?(A=D=r,u=20):1===t?(A=te,D=ee,u=257):(A=ae,D=ie,u=0),z=0,m=0,w=b,c=s,k=p,v=0,_=-1,x=1<<p,f=x-1,1===t&&x>852||2===t&&x>592)return 1;for(;;){Z=w-v,r[m]+1<u?(U=0,S=r[m]):r[m]>=u?(U=D[r[m]-u],S=A[r[m]-u]):(U=96,S=0),h=1<<w-v,d=1<<k,b=d;do{d-=h,n[c+(z>>v)+d]=Z<<24|U<<16|S|0}while(0!==d);for(h=1<<w-1;z&h;)h>>=1;if(0!==h?(z&=h-1,z+=h):z=0,m++,0==--E[w]){if(w===g)break;w=e[a+r[m]]}if(w>p&&(z&f)!==_){for(0===v&&(v=p),c+=b,k=w-v,y=1<<k;k+v<g&&(y-=E[k+v],!(y<=0));)k++,y<<=1;if(x+=1<<k,1===t&&x>852||2===t&&x>592)return 1;_=z&f,n[_]=p<<24|k<<16|c-s|0}}return 0!==z&&(n[c+z]=w-v<<24|64<<16|0),o.bits=p,0};const{Z_FINISH:se,Z_BLOCK:re,Z_TREES:oe,Z_OK:le,Z_STREAM_END:he,Z_NEED_DICT:de,Z_STREAM_ERROR:_e,Z_DATA_ERROR:fe,Z_MEM_ERROR:ce,Z_BUF_ERROR:ue,Z_DEFLATED:we}=B,me=16209,be=t=>(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24);function ge(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const pe=t=>{if(!t)return 1;const e=t.state;return!e||e.strm!==t||e.mode<16180||e.mode>16211?1:0},ke=t=>{if(pe(t))return _e;const e=t.state;return t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=16180,e.last=0,e.havedict=0,e.flags=-1,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new Int32Array(852),e.distcode=e.distdyn=new Int32Array(592),e.sane=1,e.back=-1,le},ve=t=>{if(pe(t))return _e;const e=t.state;return e.wsize=0,e.whave=0,e.wnext=0,ke(t)},ye=(t,e)=>{let a;if(pe(t))return _e;const i=t.state;return e<0?(a=0,e=-e):(a=5+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?_e:(null!==i.window&&i.wbits!==e&&(i.window=null),i.wrap=a,i.wbits=e,ve(t))},xe=(t,e)=>{if(!t)return _e;const a=new ge;t.state=a,a.strm=t,a.window=null,a.mode=16180;const i=ye(t,e);return i!==le&&(t.state=null),i};let ze,Ae,Ee=!0;const Re=t=>{if(Ee){ze=new Int32Array(512),Ae=new Int32Array(32);let e=0;for(;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(ne(1,t.lens,0,288,ze,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;ne(2,t.lens,0,32,Ae,0,t.work,{bits:5}),Ee=!1}t.lencode=ze,t.lenbits=9,t.distcode=Ae,t.distbits=5},Ze=(t,e,a,i)=>{let n;const s=t.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new Uint8Array(s.wsize)),i>=s.wsize?(s.window.set(e.subarray(a-s.wsize,a),0),s.wnext=0,s.whave=s.wsize):(n=s.wsize-s.wnext,n>i&&(n=i),s.window.set(e.subarray(a-i,a-i+n),s.wnext),(i-=n)?(s.window.set(e.subarray(a-i,a),0),s.wnext=i,s.whave=s.wsize):(s.wnext+=n,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=n))),0};var Ue={inflateReset:ve,inflateReset2:ye,inflateResetKeep:ke,inflateInit:t=>xe(t,15),inflateInit2:xe,inflate:(t,e)=>{let a,i,n,s,r,o,l,h,d,_,f,c,u,w,m,b,g,p,k,v,y,x,z=0;const A=new Uint8Array(4);let E,R;const Z=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(pe(t)||!t.output||!t.input&&0!==t.avail_in)return _e;a=t.state,16191===a.mode&&(a.mode=16192),r=t.next_out,n=t.output,l=t.avail_out,s=t.next_in,i=t.input,o=t.avail_in,h=a.hold,d=a.bits,_=o,f=l,x=le;t:for(;;)switch(a.mode){case 16180:if(0===a.wrap){a.mode=16192;break}for(;d<16;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(2&a.wrap&&35615===h){0===a.wbits&&(a.wbits=15),a.check=0,A[0]=255&h,A[1]=h>>>8&255,a.check=N(a.check,A,2,0),h=0,d=0,a.mode=16181;break}if(a.head&&(a.head.done=!1),!(1&a.wrap)||(((255&h)<<8)+(h>>8))%31){t.msg="incorrect header check",a.mode=me;break}if((15&h)!==we){t.msg="unknown compression method",a.mode=me;break}if(h>>>=4,d-=4,y=8+(15&h),0===a.wbits&&(a.wbits=y),y>15||y>a.wbits){t.msg="invalid window size",a.mode=me;break}a.dmax=1<<a.wbits,a.flags=0,t.adler=a.check=1,a.mode=512&h?16189:16191,h=0,d=0;break;case 16181:for(;d<16;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(a.flags=h,(255&a.flags)!==we){t.msg="unknown compression method",a.mode=me;break}if(57344&a.flags){t.msg="unknown header flags set",a.mode=me;break}a.head&&(a.head.text=h>>8&1),512&a.flags&&4&a.wrap&&(A[0]=255&h,A[1]=h>>>8&255,a.check=N(a.check,A,2,0)),h=0,d=0,a.mode=16182;case 16182:for(;d<32;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}a.head&&(a.head.time=h),512&a.flags&&4&a.wrap&&(A[0]=255&h,A[1]=h>>>8&255,A[2]=h>>>16&255,A[3]=h>>>24&255,a.check=N(a.check,A,4,0)),h=0,d=0,a.mode=16183;case 16183:for(;d<16;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}a.head&&(a.head.xflags=255&h,a.head.os=h>>8),512&a.flags&&4&a.wrap&&(A[0]=255&h,A[1]=h>>>8&255,a.check=N(a.check,A,2,0)),h=0,d=0,a.mode=16184;case 16184:if(1024&a.flags){for(;d<16;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}a.length=h,a.head&&(a.head.extra_len=h),512&a.flags&&4&a.wrap&&(A[0]=255&h,A[1]=h>>>8&255,a.check=N(a.check,A,2,0)),h=0,d=0}else a.head&&(a.head.extra=null);a.mode=16185;case 16185:if(1024&a.flags&&(c=a.length,c>o&&(c=o),c&&(a.head&&(y=a.head.extra_len-a.length,a.head.extra||(a.head.extra=new Uint8Array(a.head.extra_len)),a.head.extra.set(i.subarray(s,s+c),y)),512&a.flags&&4&a.wrap&&(a.check=N(a.check,i,c,s)),o-=c,s+=c,a.length-=c),a.length))break t;a.length=0,a.mode=16186;case 16186:if(2048&a.flags){if(0===o)break t;c=0;do{y=i[s+c++],a.head&&y&&a.length<65536&&(a.head.name+=String.fromCharCode(y))}while(y&&c<o);if(512&a.flags&&4&a.wrap&&(a.check=N(a.check,i,c,s)),o-=c,s+=c,y)break t}else a.head&&(a.head.name=null);a.length=0,a.mode=16187;case 16187:if(4096&a.flags){if(0===o)break t;c=0;do{y=i[s+c++],a.head&&y&&a.length<65536&&(a.head.comment+=String.fromCharCode(y))}while(y&&c<o);if(512&a.flags&&4&a.wrap&&(a.check=N(a.check,i,c,s)),o-=c,s+=c,y)break t}else a.head&&(a.head.comment=null);a.mode=16188;case 16188:if(512&a.flags){for(;d<16;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(4&a.wrap&&h!==(65535&a.check)){t.msg="header crc mismatch",a.mode=me;break}h=0,d=0}a.head&&(a.head.hcrc=a.flags>>9&1,a.head.done=!0),t.adler=a.check=0,a.mode=16191;break;case 16189:for(;d<32;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}t.adler=a.check=be(h),h=0,d=0,a.mode=16190;case 16190:if(0===a.havedict)return t.next_out=r,t.avail_out=l,t.next_in=s,t.avail_in=o,a.hold=h,a.bits=d,de;t.adler=a.check=1,a.mode=16191;case 16191:if(e===re||e===oe)break t;case 16192:if(a.last){h>>>=7&d,d-=7&d,a.mode=16206;break}for(;d<3;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}switch(a.last=1&h,h>>>=1,d-=1,3&h){case 0:a.mode=16193;break;case 1:if(Re(a),a.mode=16199,e===oe){h>>>=2,d-=2;break t}break;case 2:a.mode=16196;break;case 3:t.msg="invalid block type",a.mode=me}h>>>=2,d-=2;break;case 16193:for(h>>>=7&d,d-=7&d;d<32;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if((65535&h)!=(h>>>16^65535)){t.msg="invalid stored block lengths",a.mode=me;break}if(a.length=65535&h,h=0,d=0,a.mode=16194,e===oe)break t;case 16194:a.mode=16195;case 16195:if(c=a.length,c){if(c>o&&(c=o),c>l&&(c=l),0===c)break t;n.set(i.subarray(s,s+c),r),o-=c,s+=c,l-=c,r+=c,a.length-=c;break}a.mode=16191;break;case 16196:for(;d<14;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(a.nlen=257+(31&h),h>>>=5,d-=5,a.ndist=1+(31&h),h>>>=5,d-=5,a.ncode=4+(15&h),h>>>=4,d-=4,a.nlen>286||a.ndist>30){t.msg="too many length or distance symbols",a.mode=me;break}a.have=0,a.mode=16197;case 16197:for(;a.have<a.ncode;){for(;d<3;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}a.lens[Z[a.have++]]=7&h,h>>>=3,d-=3}for(;a.have<19;)a.lens[Z[a.have++]]=0;if(a.lencode=a.lendyn,a.lenbits=7,E={bits:a.lenbits},x=ne(0,a.lens,0,19,a.lencode,0,a.work,E),a.lenbits=E.bits,x){t.msg="invalid code lengths set",a.mode=me;break}a.have=0,a.mode=16198;case 16198:for(;a.have<a.nlen+a.ndist;){for(;z=a.lencode[h&(1<<a.lenbits)-1],m=z>>>24,b=z>>>16&255,g=65535&z,!(m<=d);){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(g<16)h>>>=m,d-=m,a.lens[a.have++]=g;else{if(16===g){for(R=m+2;d<R;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(h>>>=m,d-=m,0===a.have){t.msg="invalid bit length repeat",a.mode=me;break}y=a.lens[a.have-1],c=3+(3&h),h>>>=2,d-=2}else if(17===g){for(R=m+3;d<R;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}h>>>=m,d-=m,y=0,c=3+(7&h),h>>>=3,d-=3}else{for(R=m+7;d<R;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}h>>>=m,d-=m,y=0,c=11+(127&h),h>>>=7,d-=7}if(a.have+c>a.nlen+a.ndist){t.msg="invalid bit length repeat",a.mode=me;break}for(;c--;)a.lens[a.have++]=y}}if(a.mode===me)break;if(0===a.lens[256]){t.msg="invalid code -- missing end-of-block",a.mode=me;break}if(a.lenbits=9,E={bits:a.lenbits},x=ne(1,a.lens,0,a.nlen,a.lencode,0,a.work,E),a.lenbits=E.bits,x){t.msg="invalid literal/lengths set",a.mode=me;break}if(a.distbits=6,a.distcode=a.distdyn,E={bits:a.distbits},x=ne(2,a.lens,a.nlen,a.ndist,a.distcode,0,a.work,E),a.distbits=E.bits,x){t.msg="invalid distances set",a.mode=me;break}if(a.mode=16199,e===oe)break t;case 16199:a.mode=16200;case 16200:if(o>=6&&l>=258){t.next_out=r,t.avail_out=l,t.next_in=s,t.avail_in=o,a.hold=h,a.bits=d,$t(t,f),r=t.next_out,n=t.output,l=t.avail_out,s=t.next_in,i=t.input,o=t.avail_in,h=a.hold,d=a.bits,16191===a.mode&&(a.back=-1);break}for(a.back=0;z=a.lencode[h&(1<<a.lenbits)-1],m=z>>>24,b=z>>>16&255,g=65535&z,!(m<=d);){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(b&&0==(240&b)){for(p=m,k=b,v=g;z=a.lencode[v+((h&(1<<p+k)-1)>>p)],m=z>>>24,b=z>>>16&255,g=65535&z,!(p+m<=d);){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}h>>>=p,d-=p,a.back+=p}if(h>>>=m,d-=m,a.back+=m,a.length=g,0===b){a.mode=16205;break}if(32&b){a.back=-1,a.mode=16191;break}if(64&b){t.msg="invalid literal/length code",a.mode=me;break}a.extra=15&b,a.mode=16201;case 16201:if(a.extra){for(R=a.extra;d<R;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}a.length+=h&(1<<a.extra)-1,h>>>=a.extra,d-=a.extra,a.back+=a.extra}a.was=a.length,a.mode=16202;case 16202:for(;z=a.distcode[h&(1<<a.distbits)-1],m=z>>>24,b=z>>>16&255,g=65535&z,!(m<=d);){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(0==(240&b)){for(p=m,k=b,v=g;z=a.distcode[v+((h&(1<<p+k)-1)>>p)],m=z>>>24,b=z>>>16&255,g=65535&z,!(p+m<=d);){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}h>>>=p,d-=p,a.back+=p}if(h>>>=m,d-=m,a.back+=m,64&b){t.msg="invalid distance code",a.mode=me;break}a.offset=g,a.extra=15&b,a.mode=16203;case 16203:if(a.extra){for(R=a.extra;d<R;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}a.offset+=h&(1<<a.extra)-1,h>>>=a.extra,d-=a.extra,a.back+=a.extra}if(a.offset>a.dmax){t.msg="invalid distance too far back",a.mode=me;break}a.mode=16204;case 16204:if(0===l)break t;if(c=f-l,a.offset>c){if(c=a.offset-c,c>a.whave&&a.sane){t.msg="invalid distance too far back",a.mode=me;break}c>a.wnext?(c-=a.wnext,u=a.wsize-c):u=a.wnext-c,c>a.length&&(c=a.length),w=a.window}else w=n,u=r-a.offset,c=a.length;c>l&&(c=l),l-=c,a.length-=c;do{n[r++]=w[u++]}while(--c);0===a.length&&(a.mode=16200);break;case 16205:if(0===l)break t;n[r++]=a.length,l--,a.mode=16200;break;case 16206:if(a.wrap){for(;d<32;){if(0===o)break t;o--,h|=i[s++]<<d,d+=8}if(f-=l,t.total_out+=f,a.total+=f,4&a.wrap&&f&&(t.adler=a.check=a.flags?N(a.check,n,f,r-f):F(a.check,n,f,r-f)),f=l,4&a.wrap&&(a.flags?h:be(h))!==a.check){t.msg="incorrect data check",a.mode=me;break}h=0,d=0}a.mode=16207;case 16207:if(a.wrap&&a.flags){for(;d<32;){if(0===o)break t;o--,h+=i[s++]<<d,d+=8}if(4&a.wrap&&h!==(4294967295&a.total)){t.msg="incorrect length check",a.mode=me;break}h=0,d=0}a.mode=16208;case 16208:x=he;break t;case me:x=fe;break t;case 16210:return ce;default:return _e}return t.next_out=r,t.avail_out=l,t.next_in=s,t.avail_in=o,a.hold=h,a.bits=d,(a.wsize||f!==t.avail_out&&a.mode<me&&(a.mode<16206||e!==se))&&Ze(t,t.output,t.next_out,f-t.avail_out),_-=t.avail_in,f-=t.avail_out,t.total_in+=_,t.total_out+=f,a.total+=f,4&a.wrap&&f&&(t.adler=a.check=a.flags?N(a.check,n,f,t.next_out-f):F(a.check,n,f,t.next_out-f)),t.data_type=a.bits+(a.last?64:0)+(16191===a.mode?128:0)+(16199===a.mode||16194===a.mode?256:0),(0===_&&0===f||e===se)&&x===le&&(x=ue),x},inflateEnd:t=>{if(pe(t))return _e;let e=t.state;return e.window&&(e.window=null),t.state=null,le},inflateGetHeader:(t,e)=>{if(pe(t))return _e;const a=t.state;return 0==(2&a.wrap)?_e:(a.head=e,e.done=!1,le)},inflateSetDictionary:(t,e)=>{const a=e.length;let i,n,s;return pe(t)?_e:(i=t.state,0!==i.wrap&&16190!==i.mode?_e:16190===i.mode&&(n=1,n=F(n,e,a,0),n!==i.check)?fe:(s=Ze(t,e,a,a),s?(i.mode=16210,ce):(i.havedict=1,le)))},inflateInfo:"pako inflate (from Nodeca project)"};var Se=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1};const De=Object.prototype.toString,{Z_NO_FLUSH:Te,Z_FINISH:Oe,Z_OK:Fe,Z_STREAM_END:Le,Z_NEED_DICT:Ne,Z_STREAM_ERROR:Ie,Z_DATA_ERROR:Be,Z_MEM_ERROR:Ce}=B;function He(t){this.options=Tt({chunkSize:65536,windowBits:15,to:""},t||{});const e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(e.windowBits>=0&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Ct,this.strm.avail_out=0;let a=Ue.inflateInit2(this.strm,e.windowBits);if(a!==Fe)throw new Error(I[a]);if(this.header=new Se,Ue.inflateGetHeader(this.strm,this.header),e.dictionary&&("string"==typeof e.dictionary?e.dictionary=Nt(e.dictionary):"[object ArrayBuffer]"===De.call(e.dictionary)&&(e.dictionary=new Uint8Array(e.dictionary)),e.raw&&(a=Ue.inflateSetDictionary(this.strm,e.dictionary),a!==Fe)))throw new Error(I[a])}He.prototype.push=function(t,e){const a=this.strm,i=this.options.chunkSize,n=this.options.dictionary;let s,r,o;if(this.ended)return!1;for(r=e===~~e?e:!0===e?Oe:Te,"[object ArrayBuffer]"===De.call(t)?a.input=new Uint8Array(t):a.input=t,a.next_in=0,a.avail_in=a.input.length;;){for(0===a.avail_out&&(a.output=new Uint8Array(i),a.next_out=0,a.avail_out=i),s=Ue.inflate(a,r),s===Ne&&n&&(s=Ue.inflateSetDictionary(a,n),s===Fe?s=Ue.inflate(a,r):s===Be&&(s=Ne));a.avail_in>0&&s===Le&&a.state.wrap>0&&0!==t[a.next_in];)Ue.inflateReset(a),s=Ue.inflate(a,r);switch(s){case Ie:case Be:case Ne:case Ce:return this.onEnd(s),this.ended=!0,!1}if(o=a.avail_out,a.next_out&&(0===a.avail_out||s===Le))if("string"===this.options.to){let t=Bt(a.output,a.next_out),e=a.next_out-t,n=It(a.output,t);a.next_out=e,a.avail_out=i-e,e&&a.output.set(a.output.subarray(t,t+e),0),this.onData(n)}else this.onData(a.output.length===a.next_out?a.output:a.output.subarray(0,a.next_out));if(s!==Fe||0!==o){if(s===Le)return s=Ue.inflateEnd(this.strm),this.onEnd(s),this.ended=!0,!0;if(0===a.avail_in)break}}return!0},He.prototype.onData=function(t){this.chunks.push(t)},He.prototype.onEnd=function(t){t===Fe&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=Ot(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};const{Deflate:Me,deflate:je,deflateRaw:Ke,gzip:Pe}=Vt;var Ye=Me,Ge=je,Xe=B;const We=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const e=this._hasEvents?",":"";this.deflate.push(e+t,Xe.Z_SYNC_FLUSH),this._hasEvents=!0}finish(){if(this.deflate.push("]",Xe.Z_FINISH),this.deflate.err)throw this.deflate.err;const t=this.deflate.result;return this._init(),t}_init(){this._hasEvents=!1,this.deflate=new Ye,this.deflate.push("[",Xe.Z_NO_FLUSH)}},qe={clear:()=>{We.clear()},addEvent:t=>We.addEvent(t),finish:()=>We.finish(),compress:t=>function(t){return Ge(t)}(t)};addEventListener("message",(function(t){const e=t.data.method,a=t.data.id,i=t.data.arg;if(e in qe&&"function"==typeof qe[e])try{const t=qe[e](i);postMessage({id:a,method:e,success:!0,response:t})}catch(t){postMessage({id:a,method:e,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});';function sn(t,e){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&(p.kg.info(t),e&&an(t))}function rn(t,e){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&(p.kg.info(t),e&&setTimeout((()=>{an(t)}),0))}function an(t){(0,i.Gd)().addBreadcrumb({category:"console",data:{logger:"replay"},level:"info",message:t},{level:"info"})}class on extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class cn{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(t){const e=JSON.stringify(t).length;if(this._totalSize+=e,this._totalSize>C)throw new on;this.events.push(t)}finish(){return new Promise((t=>{const e=this.events;this.clear(),t(JSON.stringify(e))}))}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){const t=this.events.map((t=>t.timestamp)).sort()[0];return t?Be(t):null}}class ln{constructor(t){this._worker=t,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise(((t,e)=>{this._worker.addEventListener("message",(({data:n})=>{n.success?t():e()}),{once:!0}),this._worker.addEventListener("error",(t=>{e(t)}),{once:!0})}))),this._ensureReadyPromise}destroy(){sn("[Replay] Destroying compression worker"),this._worker.terminate()}postMessage(t,e){const n=this._getAndIncrementId();return new Promise(((s,r)=>{const i=({data:e})=>{const a=e;if(a.method===t&&a.id===n){if(this._worker.removeEventListener("message",i),!a.success)return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay]",a.response),void r(new Error("Error in compression worker"));s(a.response)}};this._worker.addEventListener("message",i),this._worker.postMessage({id:n,method:t,arg:e})}))}_getAndIncrementId(){return this._id++}}class un{constructor(t){this._worker=new ln(t),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(t){const e=Be(t.timestamp);(!this._earliestTimestamp||e<this._earliestTimestamp)&&(this._earliestTimestamp=e);const n=JSON.stringify(t);return this._totalSize+=n.length,this._totalSize>C?Promise.reject(new on):this._sendEventToWorker(n)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear")}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(t){return this._worker.postMessage("addEvent",t)}async _finishRequest(){const t=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,t}}class dn{constructor(t){this._fallback=new cn,this._compression=new un(t),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(t){this._used.hasCheckout=t}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(t){return this._used.addEvent(t)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(t){return void sn("[Replay] Failed to load the compression worker, falling back to simple buffer")}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){const{events:t,hasCheckout:e}=this._fallback,n=[];for(const r of t)n.push(this._compression.addEvent(r));this._compression.hasCheckout=e,this._used=this._compression;try{await Promise.all(n)}catch(s){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.warn("[Replay] Failed to add events when switching buffers.",s)}}}function hn({useCompression:t}){if(t&&window.Worker)try{const t=function(){const t=new Blob([nn]);return URL.createObjectURL(t)}();sn("[Replay] Using compression worker");const e=new Worker(t);return new dn(e)}catch(e){sn("[Replay] Failed to create compression worker")}return sn("[Replay] Using simple buffer"),new cn}function pn(){try{return"sessionStorage"in v&&!!v.sessionStorage}catch(t){return!1}}function fn(t){!function(){if(!pn())return;try{v.sessionStorage.removeItem(S)}catch(t){}}(),t.session=void 0}function mn(t){return void 0!==t&&Math.random()<t}function _n(t){if(pn())try{v.sessionStorage.setItem(S,JSON.stringify(t))}catch(e){}}function gn(t){const e=Date.now();return{id:t.id||(0,f.DM)(),started:t.started||e,lastActivity:t.lastActivity||e,segmentId:t.segmentId||0,sampled:t.sampled,previousSessionId:t.previousSessionId}}function yn({sessionSampleRate:t,allowBuffering:e,stickySession:n=!1},{previousSessionId:s}={}){const r=function(t,e){return mn(t)?"session":!!e&&"buffer"}(t,e),i=gn({sampled:r,previousSessionId:s});return n&&_n(i),i}function bn(t,e,n=+new Date){return null===t||void 0===e||e<0||0!==e&&t+e<=n}function vn(t,{maxReplayDuration:e,sessionIdleExpire:n,targetTime:s=Date.now()}){return bn(t.started,e,s)||bn(t.lastActivity,n,s)}function Sn(t,{sessionIdleExpire:e,maxReplayDuration:n}){return!!vn(t,{sessionIdleExpire:e,maxReplayDuration:n})&&("buffer"!==t.sampled||0!==t.segmentId)}function kn({traceInternals:t,sessionIdleExpire:e,maxReplayDuration:n,previousSessionId:s},r){const i=r.stickySession&&function(t){if(!pn())return null;try{const e=v.sessionStorage.getItem(S);if(!e)return null;const n=JSON.parse(e);return rn("[Replay] Loading existing session",t),gn(n)}catch(e){return null}}(t);return i?Sn(i,{sessionIdleExpire:e,maxReplayDuration:n})?(rn("[Replay] Session in sessionStorage is expired, creating new one..."),yn(r,{previousSessionId:i.id})):i:(rn("[Replay] Creating new session",t),yn(r,{previousSessionId:s}))}function wn(t,e,n){return!!In(t,e)&&(En(t,e,n),!0)}async function En(t,e,n){if(!t.eventBuffer)return null;try{n&&"buffer"===t.recordingMode&&t.eventBuffer.clear(),n&&(t.eventBuffer.hasCheckout=!0);const s=function(t,e){try{if("function"===typeof e&&function(t){return t.type===Nt.Custom}(t))return e(t)}catch(n){return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay] An error occured in the `beforeAddRecordingEvent` callback, skipping the event...",n),null}return t}(e,t.getOptions().beforeAddRecordingEvent);if(!s)return;return await t.eventBuffer.addEvent(s)}catch(s){const e=s&&s instanceof on?"addEventSizeExceeded":"addEvent";("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error(s),await t.stop({reason:e});const n=(0,i.Gd)().getClient();n&&n.recordDroppedEvent("internal_sdk_error","replay")}}function In(t,e){if(!t.eventBuffer||t.isPaused()||!t.isEnabled())return!1;const n=Be(e.timestamp);return!(n+t.timeouts.sessionIdlePause<Date.now())&&(!(n>t.getContext().initialTimestamp+t.getOptions().maxReplayDuration)||(sn(`[Replay] Skipping event with timestamp ${n} because it is after maxReplayDuration`,t.getOptions()._experiments.traceInternals),!1))}function Cn(t){return!t.type}function Tn(t){return"transaction"===t.type}function xn(t){const e=function(){const t=(0,i.Gd)().getClient();if(!t)return!1;const e=t.getTransport();if(!e)return!1;return e.send.__sentry__baseTransport__||!1}();return(n,s)=>{if(!t.isEnabled()||!Cn(n)&&!Tn(n))return;const r=s&&s.statusCode;e&&(!r||r<200||r>=300)||(Tn(n)?function(t,e){const n=t.getContext();e.contexts&&e.contexts.trace&&e.contexts.trace.trace_id&&n.traceIds.size<100&&n.traceIds.add(e.contexts.trace.trace_id)}(t,n):function(t,e){const n=t.getContext();e.event_id&&n.errorIds.size<100&&n.errorIds.add(e.event_id);"buffer"===t.recordingMode&&e.tags&&e.tags.replayId&&setTimeout((()=>{t.sendBufferedReplayOrFlush()}))}(t,n))}}function Rn(t,e=!1){const n=e?xn(t):void 0;return Object.assign(((e,s)=>{if(!t.isEnabled())return e;if(function(t){return"replay_event"===t.type}(e))return delete e.breadcrumbs,e;if(!Cn(e)&&!Tn(e))return e;if(!t.checkAndHandleExpiredSession())return e;if(function(t,e){return!(t.type||!t.exception||!t.exception.values||!t.exception.values.length)&&(!(!e.originalException||!e.originalException.__rrweb__)||t.exception.values.some((t=>!!(t.stacktrace&&t.stacktrace.frames&&t.stacktrace.frames.length)&&t.stacktrace.frames.some((t=>t.filename&&t.filename.includes("/rrweb/src/"))))))}(e,s)&&!t.getOptions()._experiments.captureExceptions)return("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.log("[Replay] Ignoring error from rrweb internals",e),null;const r=function(t,e){return"buffer"===t.recordingMode&&e.message!==w&&!(!e.exception||e.type)&&mn(t.getOptions().errorSampleRate)}(t,e);return(r||"session"===t.recordingMode)&&(e.tags={...e.tags,replayId:t.getSessionId()}),n&&n(e,{statusCode:200}),e}),{id:"Replay"})}function An(t,e){return e.map((({type:e,start:n,end:s,name:r,data:i})=>{const a=t.throttledAddEvent({type:Nt.Custom,timestamp:n,data:{tag:"performanceSpan",payload:{op:e,description:r,startTimestamp:n,endTimestamp:s,data:i}}});return"string"===typeof a?Promise.resolve(null):a}))}function Dn(t){return e=>{if(!t.isEnabled())return;const n=function(t){const{from:e,to:n}=t,s=Date.now()/1e3;return{type:"navigation.push",start:s,end:s,name:n,data:{previous:e}}}(e);null!==n&&(t.getContext().urls.push(n.name),t.triggerUserActivity(),t.addUpdate((()=>(An(t,[n]),!1))))}}function Nn(t,e){t.isEnabled()&&null!==e&&(function(t,e){return("undefined"!==typeof __SENTRY_DEBUG__&&!__SENTRY_DEBUG__||!t.getOptions()._experiments.traceInternals)&&s(e,(0,i.Gd)())}(t,e.name)||t.addUpdate((()=>(An(t,[e]),!0))))}function On(t){return e=>{if(!t.isEnabled())return;const n=function(t){const{startTimestamp:e,endTimestamp:n,xhr:s}=t,r=s[m.xU];if(!e||!n||!r)return null;const{method:i,url:a,status_code:o}=r;return void 0===a?null:{type:"resource.xhr",name:a,start:e/1e3,end:n/1e3,data:{method:i,statusCode:o}}}(e);Nn(t,n)}}const Mn=10,Un=11,Bn=12,Gn=13,Ln=14,Fn=15,zn=20,Yn=21,Pn=22,Zn=23,$n=["true","false","null"];function Wn(t,e){if(!e.length)return t;let n=t;const s=e.length-1;n=function(t,e){switch(e){case Mn:return`${t}"~~":"~~"`;case Un:return`${t}:"~~"`;case Bn:return`${t}~~":"~~"`;case Gn:return function(t){const e=t.lastIndexOf(":"),n=t.slice(e+1);if($n.includes(n.trim()))return`${t},"~~":"~~"`;return`${t.slice(0,e+1)}"~~"`}(t);case Ln:return`${t}~~"`;case Fn:return`${t},"~~":"~~"`;case zn:return`${t}"~~"`;case Yn:return function(t){const e=function(t){for(let e=t.length-1;e>=0;e--){const n=t[e];if(","===n||"["===n)return e}return-1}(t);if(e>-1){const n=t.slice(e+1);return $n.includes(n.trim())?`${t},"~~"`:`${t.slice(0,e+1)}"~~"`}return t}(t);case Pn:return`${t}~~"`;case Zn:return`${t},"~~"`}return t}(n,e[s]);for(let r=s;r>=0;r--){switch(e[r]){case Mn:n=`${n}}`;break;case zn:n=`${n}]`}}return n}function jn(t,e,n){const s=t[t.length-1],r=e[n];if(!/\s/.test(r))if('"'!==r||Hn(e,n))switch(r){case"{":!function(t,e){if(!e)return void t.push(Mn);if(e===Gn)return void t.push(Mn);e===Yn&&t.push(Mn);if(e===zn)t.push(Mn)}(t,s);break;case"[":!function(t,e){if(!e)return t.push(zn),void t.push(Yn);if(e===Gn)return t.push(zn),void t.push(Yn);e===Yn&&(t.push(zn),t.push(Yn));if(e===zn)t.push(zn),t.push(Yn)}(t,s);break;case":":!function(t,e){e===Un&&(t.pop(),t.push(Gn))}(t,s);break;case",":!function(t,e){if(e===Gn)return void t.pop();if(e===Fn)return t.pop(),void t.pop();if(e===Yn)return;if(e===Zn)t.pop()}(t,s);break;case"}":!function(t,e){e===Mn&&t.pop();e===Gn&&(t.pop(),t.pop());e===Fn&&(t.pop(),t.pop(),t.pop());t[t.length-1]===Gn&&t.push(Fn);t[t.length-1]===Yn&&t.push(Zn)}(t,s);break;case"]":!function(t,e){e===zn&&t.pop();e===Yn&&(t.pop(),t.pop());e===Zn&&(t.pop(),t.pop(),t.pop());t[t.length-1]===Gn&&t.push(Fn);t[t.length-1]===Yn&&t.push(Zn)}(t,s)}else!function(t,e){if(e===Ln)return t.pop(),void t.push(Fn);if(e===Pn)return t.pop(),void t.push(Zn);if(e===Gn)return void t.push(Ln);if(e===Yn)return void t.push(Pn);if(e===Mn)return void t.push(Bn);if(e===Bn)t.pop(),t.push(Un)}(t,s)}function Hn(t,e){return"\\"===t[e-1]&&!Hn(t,e-1)}function Vn(t){return Wn(t,function(t){const e=[];for(let n=0;n<t.length;n++)jn(e,t,n);return e}(t))}function Kn(t,e){if(t)try{if("string"===typeof t)return e.encode(t).length;if(t instanceof URLSearchParams)return e.encode(t.toString()).length;if(t instanceof FormData){const n=ns(t);return e.encode(n).length}if(t instanceof Blob)return t.size;if(t instanceof ArrayBuffer)return t.byteLength}catch(n){}}function Jn(t){if(!t)return;const e=parseInt(t,10);return isNaN(e)?void 0:e}function qn(t){return"string"===typeof t?t:t instanceof URLSearchParams?t.toString():t instanceof FormData?ns(t):void 0}function Xn(t,e){if(!e)return null;const{startTimestamp:n,endTimestamp:s,url:r,method:i,statusCode:a,request:o,response:c}=e;return{type:t,start:n/1e3,end:s/1e3,name:r,data:(0,d.Jr)({method:i,statusCode:a,request:o,response:c})}}function Qn(t){return{headers:{},size:t,_meta:{warnings:["URL_SKIPPED"]}}}function ts(t,e,n){if(!e&&0===Object.keys(t).length)return;if(!e)return{headers:t};if(!n)return{headers:t,size:e};const s={headers:t,size:e},{body:r,warnings:i}=function(t){if(!t||"string"!==typeof t)return{body:t,warnings:[]};const e=t.length>E;if(function(t){const e=t[0],n=t[t.length-1];return"["===e&&"]"===n||"{"===e&&"}"===n}(t))try{const n=e?Vn(t.slice(0,E)):t;return{body:JSON.parse(n),warnings:e?["JSON_TRUNCATED"]:[]}}catch(n){return{body:e?`${t.slice(0,E)}\u2026`:t,warnings:e?["INVALID_JSON","TEXT_TRUNCATED"]:["INVALID_JSON"]}}return{body:e?`${t.slice(0,E)}\u2026`:t,warnings:e?["TEXT_TRUNCATED"]:[]}}(n);return s.body=r,i.length>0&&(s._meta={warnings:i}),s}function es(t,e){return Object.keys(t).reduce(((n,s)=>{const r=s.toLowerCase();return e.includes(r)&&t[s]&&(n[r]=t[s]),n}),{})}function ns(t){return new URLSearchParams(t).toString()}function ss(t,e){const n=function(t,e=v.document.baseURI){if(t.startsWith("http://")||t.startsWith("https://")||t.startsWith(v.location.origin))return t;const n=new URL(t,e);if(n.origin!==new URL(e).origin)return t;const s=n.href;if(!t.endsWith("/")&&s.endsWith("/"))return s.slice(0,-1);return s}(t);return(0,_.U0)(n,e)}async function rs(t,e,n){try{const s=await async function(t,e,n){const{startTimestamp:s,endTimestamp:r}=e,{url:i,method:a,status_code:o=0,request_body_size:c,response_body_size:l}=t.data,u=ss(i,n.networkDetailAllowUrls)&&!ss(i,n.networkDetailDenyUrls),d=u?function({networkCaptureBodies:t,networkRequestHeaders:e},n,s){const r=function(t,e){if(1===t.length&&"string"!==typeof t[0])return os(t[0],e);if(2===t.length)return os(t[1],e);return{}}(n,e);if(!t)return ts(r,s,void 0);const i=qn(is(n));return ts(r,s,i)}(n,e.input,c):Qn(c),h=await async function(t,{networkCaptureBodies:e,textEncoder:n,networkResponseHeaders:s},r,i){if(!t&&void 0!==i)return Qn(i);const a=as(r.headers,s);if(!e&&void 0!==i)return ts(a,i,void 0);try{const s=r.clone(),o=await async function(t){try{return await t.text()}catch(e){return}}(s),c=o&&o.length&&void 0===i?Kn(o,n):i;return t?ts(a,c,e?o:void 0):Qn(c)}catch(o){return ts(a,i,void 0)}}(u,n,e.response,l);return{startTimestamp:s,endTimestamp:r,url:i,method:a,statusCode:o,request:d,response:h}}(t,e,n),r=Xn("resource.fetch",s);Nn(n.replay,r)}catch(s){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay] Failed to capture fetch breadcrumb",s)}}function is(t=[]){if(2===t.length&&"object"===typeof t[1])return t[1].body}function as(t,e){const n={};return e.forEach((e=>{t.get(e)&&(n[e]=t.get(e))})),n}function os(t,e){if(!t)return{};const n=t.headers;return n?n instanceof Headers?as(n,e):Array.isArray(n)?{}:es(n,e):{}}async function cs(t,e,n){try{const s=function(t,e,n){const{startTimestamp:s,endTimestamp:r,input:i,xhr:a}=e,{url:o,method:c,status_code:l=0,request_body_size:u,response_body_size:d}=t.data;if(!o)return null;if(!ss(o,n.networkDetailAllowUrls)||ss(o,n.networkDetailDenyUrls)){return{startTimestamp:s,endTimestamp:r,url:o,method:c,statusCode:l,request:Qn(u),response:Qn(d)}}const h=a[m.xU],p=h?es(h.request_headers,n.networkRequestHeaders):{},f=es(function(t){const e=t.getAllResponseHeaders();if(!e)return{};return e.split("\r\n").reduce(((t,e)=>{const[n,s]=e.split(": ");return t[n.toLowerCase()]=s,t}),{})}(a),n.networkResponseHeaders),_=ts(p,u,n.networkCaptureBodies?qn(i):void 0),g=ts(f,d,n.networkCaptureBodies?e.xhr.responseText:void 0);return{startTimestamp:s,endTimestamp:r,url:o,method:c,statusCode:l,request:_,response:g}}(t,e,n),r=Xn("resource.xhr",s);Nn(n.replay,r)}catch(s){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay] Failed to capture fetch breadcrumb",s)}}function ls(t){const e=(0,i.Gd)().getClient();try{const n=new TextEncoder,{networkDetailAllowUrls:s,networkDetailDenyUrls:r,networkCaptureBodies:i,networkRequestHeaders:a,networkResponseHeaders:o}=t.getOptions(),c={replay:t,textEncoder:n,networkDetailAllowUrls:s,networkDetailDenyUrls:r,networkCaptureBodies:i,networkRequestHeaders:a,networkResponseHeaders:o};e&&e.on?e.on("beforeAddBreadcrumb",((t,e)=>function(t,e,n){if(!e.data)return;try{(function(t){return"xhr"===t.category})(e)&&function(t){return t&&t.xhr}(n)&&(!function(t,e,n){const{xhr:s,input:r}=e,i=Kn(r,n.textEncoder),a=s.getResponseHeader("content-length")?Jn(s.getResponseHeader("content-length")):Kn(s.response,n.textEncoder);void 0!==i&&(t.data.request_body_size=i),void 0!==a&&(t.data.response_body_size=a)}(e,n,t),cs(e,n,t)),function(t){return"fetch"===t.category}(e)&&function(t){return t&&t.response}(n)&&(!function(t,e,n){const{input:s,response:r}=e,i=Kn(is(s),n.textEncoder),a=r?Jn(r.headers.get("content-length")):void 0;void 0!==i&&(t.data.request_body_size=i),void 0!==a&&(t.data.response_body_size=a)}(e,n,t),rs(e,n,t))}catch(s){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.warn("Error when enriching network breadcrumb")}}(c,t,e))):((0,m.oq)("fetch",function(t){return e=>{if(!t.isEnabled())return;const n=function(t){const{startTimestamp:e,endTimestamp:n,fetchData:s,response:r}=t;if(!n)return null;const{method:i,url:a}=s;return{type:"resource.fetch",start:e/1e3,end:n/1e3,name:a,data:{method:i,statusCode:r?r.status:void 0}}}(e);Nn(t,n)}}(t)),(0,m.oq)("xhr",On(t)))}catch(n){}}let us=null;const ds=t=>e=>{if(!t.isEnabled())return;const n=function(t){const e=t.getLastBreadcrumb&&t.getLastBreadcrumb();if(us===e||!e)return null;if(us=e,!function(t){return!!t.category}(e)||["fetch","xhr","sentry.event","sentry.transaction"].includes(e.category)||e.category.startsWith("ui."))return null;if("console"===e.category)return function(t){const e=t.data&&t.data.arguments;if(!Array.isArray(e)||0===e.length)return je(t);let n=!1;const s=e.map((t=>{if(!t)return t;if("string"===typeof t)return t.length>I?(n=!0,`${t.slice(0,I)}\u2026`):t;if("object"===typeof t)try{const e=(0,u.Fv)(t,7),s=JSON.stringify(e);if(s.length>I){const t=Vn(s.slice(0,I)),e=JSON.parse(t);return n=!0,e}return e}catch(e){}return t}));return je({...t,data:{...t.data,arguments:s,...n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(e);return je(e)}(e);n&&Ge(t,n)};function hs(t){return!(!t||!t.on)}function ps(t){const{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:s}=t,r=Date.now()/1e3;return{type:"memory",name:"memory",start:r,end:r,data:{memory:{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:s}}}}const fs={resource:function(t){const{entryType:e,initiatorType:n,name:s,responseEnd:r,startTime:i,decodedBodySize:a,encodedBodySize:o,responseStatus:c,transferSize:l}=t;if(["fetch","xmlhttprequest"].includes(n))return null;return{type:`${e}.${n}`,start:_s(i),end:_s(r),name:s,data:{size:l,statusCode:c,decodedBodySize:a,encodedBodySize:o}}},paint:function(t){const{duration:e,entryType:n,name:s,startTime:r}=t,i=_s(r);return{type:n,name:s,start:i,end:i+e,data:void 0}},navigation:function(t){const{entryType:e,name:n,decodedBodySize:s,duration:r,domComplete:i,encodedBodySize:a,domContentLoadedEventStart:o,domContentLoadedEventEnd:c,domInteractive:l,loadEventStart:u,loadEventEnd:d,redirectCount:h,startTime:p,transferSize:f,type:m}=t;if(0===r)return null;return{type:`${e}.${m}`,start:_s(p),end:_s(i),name:n,data:{size:f,decodedBodySize:s,encodedBodySize:a,duration:r,domInteractive:l,domContentLoadedEventStart:o,domContentLoadedEventEnd:c,loadEventStart:u,loadEventEnd:d,domComplete:i,redirectCount:h}}},"largest-contentful-paint":function(t){const{entryType:e,startTime:n,size:s}=t;let r=0;if(v.performance){const t=v.performance.getEntriesByType("navigation")[0];r=t&&t.activationStart||0}const i=Math.max(n-r,0),a=_s(r)+i/1e3;return{type:e,name:e,start:a,end:a,data:{value:i,size:s,nodeId:Ue.mirror.getId(t.element)}}}};function ms(t){return void 0===fs[t.entryType]?null:fs[t.entryType](t)}function _s(t){return((g.Z1||v.performance.timeOrigin)+t)/1e3}function gs(t){let e=!1;return(n,s)=>{if(!t.checkAndHandleExpiredSession())return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.warn("[Replay] Received replay event after session expired."));const r=s||!e;e=!0,t.addUpdate((()=>{if("buffer"===t.recordingMode&&r&&t.setInitialState(),!wn(t,n,r))return!0;if(!r)return!1;if(function(t,e){if(!e||!t.session||0!==t.session.segmentId)return;wn(t,function(t){const e=t.getOptions();return{type:Nt.Custom,timestamp:Date.now(),data:{tag:"options",payload:{sessionSampleRate:e.sessionSampleRate,errorSampleRate:e.errorSampleRate,useCompressionOption:e.useCompression,blockAllMedia:e.blockAllMedia,maskAllText:e.maskAllText,maskAllInputs:e.maskAllInputs,useCompression:!!t.eventBuffer&&"worker"===t.eventBuffer.type,networkDetailHasUrls:e.networkDetailAllowUrls.length>0,networkCaptureBodies:e.networkCaptureBodies,networkRequestHasHeaders:e.networkRequestHeaders.length>0,networkResponseHasHeaders:e.networkResponseHeaders.length>0}}}}(t),!1)}(t,r),t.session&&t.session.previousSessionId)return!0;if("buffer"===t.recordingMode&&t.session&&t.eventBuffer){const e=t.eventBuffer.getEarliestTimestamp();e&&(sn(`[Replay] Updating session start time to earliest event in buffer to ${new Date(e)}`,t.getOptions()._experiments.traceInternals),t.session.started=e,t.getOptions().stickySession&&_n(t.session))}return"session"===t.recordingMode&&t.flush(),!0}))}}function ys(t,e,n,s){return(0,y.Jd)((0,y.Cd)(t,(0,y.HY)(t),s,n),[[{type:"replay_event"},t],[{type:"replay_recording",length:"string"===typeof e?(new TextEncoder).encode(e).length:e.length},e]])}async function bs({recordingData:t,replayId:e,segmentId:n,eventContext:s,timestamp:r,session:a}){const c=function({recordingData:t,headers:e}){let n;const s=`${JSON.stringify(e)}\n`;if("string"===typeof t)n=`${s}${t}`;else{const e=(new TextEncoder).encode(s);n=new Uint8Array(e.length+t.length),n.set(e),n.set(t,e.length)}return n}({recordingData:t,headers:{segment_id:n}}),{urls:l,errorIds:u,traceIds:d,initialTimestamp:h}=s,p=(0,i.Gd)(),f=p.getClient(),m=p.getScope(),_=f&&f.getTransport(),g=f&&f.getDsn();if(!f||!_||!g||!a.sampled)return;const y={type:k,replay_start_timestamp:h/1e3,timestamp:r/1e3,error_ids:u,trace_ids:d,urls:l,replay_id:e,segment_id:n,replay_type:a.sampled},b=await async function({client:t,scope:e,replayId:n,event:s}){const r={event_id:n,integrations:"object"!==typeof t._integrations||null===t._integrations||Array.isArray(t._integrations)?void 0:Object.keys(t._integrations)};t.emit&&t.emit("preprocessEvent",s,r);const i=await(0,o.R)(t.getOptions(),s,r,e,t);if(!i)return null;i.platform=i.platform||"javascript";const a=t.getSdkMetadata&&t.getSdkMetadata(),{name:c,version:l}=a&&a.sdk||{};return i.sdk={...i.sdk,name:c||"sentry.javascript.unknown",version:l||"0.0.0"},i}({scope:m,client:f,replayId:e,event:y});if(!b)return f.recordDroppedEvent("event_processor","replay",y),void sn("An event processor returned `null`, will not send event.");delete b.sdkProcessingMetadata;const v=ys(b,c,g,f.getOptions().tunnel);let S;try{S=await _.send(v)}catch(E){const t=new Error(w);try{t.cause=E}catch(I){}throw t}if(!S)return S;if("number"===typeof S.statusCode&&(S.statusCode<200||S.statusCode>=300))throw new vs(S.statusCode);return S}class vs extends Error{constructor(t){super(`Transport returned status code ${t}`)}}async function Ss(t,e={count:0,interval:5e3}){const{recordingData:n,options:s}=t;if(n.length)try{return await bs(t),!0}catch(r){if(r instanceof vs)throw r;if((0,c.v)("Replays",{_retryCount:e.count}),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s._experiments&&s._experiments.captureExceptions&&(0,c.Tb)(r),e.count>=3){const t=new Error(`${w} - max retries exceeded`);try{t.cause=r}catch(i){}throw t}return e.interval*=++e.count,new Promise(((n,s)=>{setTimeout((async()=>{try{await Ss(t,e),n(!0)}catch(r){s(r)}}),e.interval)}))}}const ks="__THROTTLED";function ws(t,e,n){const s=new Map;let r=!1;return(...i)=>{const a=Math.floor(Date.now()/1e3);if((t=>{const e=t-n;s.forEach(((t,n)=>{n<e&&s.delete(n)}))})(a),[...s.values()].reduce(((t,e)=>t+e),0)>=e){const t=r;return r=!0,t?"__SKIPPED":ks}r=!1;const o=s.get(a)||0;return s.set(a,o+1),t(...i)}}class Es{constructor({options:t,recordingOptions:e}){Es.prototype.__init.call(this),Es.prototype.__init2.call(this),Es.prototype.__init3.call(this),Es.prototype.__init4.call(this),Es.prototype.__init5.call(this),Es.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEvents=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=e,this._options=t,this._debouncedFlush=function(t,e,n){let s,r,i;const a=n&&n.maxWait?Math.max(n.maxWait,e):0;function o(){return c(),s=t(),s}function c(){void 0!==r&&clearTimeout(r),void 0!==i&&clearTimeout(i),r=i=void 0}function l(){return r&&clearTimeout(r),r=setTimeout(o,e),a&&void 0===i&&(i=setTimeout(o,a)),s}return l.cancel=c,l.flush=function(){return void 0!==r||void 0!==i?o():s},l}((()=>this._flush()),this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=ws(((t,e)=>function(t,e,n){return In(t,e)?En(t,e,n):Promise.resolve(null)}(this,t,e)),300,5);const{slowClickTimeout:n,slowClickIgnoreSelectors:s}=this.getOptions(),r=n?{threshold:Math.min(3e3,n),timeout:n,scrollTimeout:300,ignoreSelector:s?s.join(","):""}:void 0;r&&(this.clickDetector=new Ze(this,r))}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}getOptions(){return this._options}initializeSampling(t){const{errorSampleRate:e,sessionSampleRate:n}=this._options;e<=0&&n<=0||(this._initializeSessionForSampling(t),this.session?!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",rn(`[Replay] Starting replay in ${this.recordingMode} mode`,this._options._experiments.traceInternals),this._initializeRecording()):this._handleException(new Error("Unable to initialize and create session")))}start(){if(this._isEnabled&&"session"===this.recordingMode)throw new Error("Replay recording is already in progress");if(this._isEnabled&&"buffer"===this.recordingMode)throw new Error("Replay buffering is in progress, call `flush()` to save the replay");rn("[Replay] Starting replay in session mode",this._options._experiments.traceInternals);const t=kn({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=t,this._initializeRecording()}startBuffering(){if(this._isEnabled)throw new Error("Replay recording is already in progress");rn("[Replay] Starting replay in buffer mode",this._options._experiments.traceInternals);const t=kn({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=t,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{this._stopRecording=Ue({...this._recordingOptions,..."buffer"===this.recordingMode&&{checkoutEveryNms:6e4},emit:gs(this),onMutation:this._onMutationHandler})}catch(t){this._handleException(t)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(t){return this._handleException(t),!1}}async stop({forceFlush:t=!1,reason:e}={}){if(this._isEnabled){this._isEnabled=!1;try{sn("[Replay] Stopping Replay"+(e?` triggered by ${e}`:""),this._options._experiments.traceInternals),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),t&&await this._flush({force:!0}),this.eventBuffer&&this.eventBuffer.destroy(),this.eventBuffer=null,fn(this)}catch(n){this._handleException(n)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording(),sn("[Replay] Pausing replay",this._options._experiments.traceInternals))}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording(),sn("[Replay] Resuming replay",this._options._experiments.traceInternals))}async sendBufferedReplayOrFlush({continueRecording:t=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();const e=Date.now();sn("[Replay] Converting buffer to session",this._options._experiments.traceInternals),await this.flushImmediate();const n=this.stopRecording();t&&n&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(e),this._updateSessionActivity(e),this._maybeSaveSession()),this.startRecording())}addUpdate(t){const e=t();"buffer"!==this.recordingMode&&!0!==e&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),this._stopRecording)this.checkAndHandleExpiredSession(),this._updateSessionActivity();else{if(!this._checkSession())return;this.resume()}}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session&&this.session.id}checkAndHandleExpiredSession(){if(!(this._lastActivity&&bn(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled))return!!this._checkSession();this.pause()}setInitialState(){const t=`${v.location.pathname}${v.location.hash}${v.location.search}`,e=`${v.location.origin}${t}`;this.performanceEvents=[],this._clearContext(),this._context.initialUrl=e,this._context.initialTimestamp=Date.now(),this._context.urls.push(e)}throttledAddEvent(t,e){const n=this._throttledAddEvent(t,e);if(n===ks){const t=je({category:"replay.throttled"});this.addUpdate((()=>!wn(this,{type:5,timestamp:t.timestamp||0,data:{tag:"breadcrumb",payload:t,metric:!0}})))}return n}getCurrentRoute(){const t=this.lastTransaction||(0,i.Gd)().getScope().getTransaction();if(t&&["route","custom"].includes(t.metadata.source))return t.name}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=hn({useCompression:this._options.useCompression}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_handleException(t){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay]",t),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&this._options._experiments&&this._options._experiments.captureExceptions&&(0,c.Tb)(t)}_initializeSessionForSampling(t){const e=this._options.errorSampleRate>0,n=kn({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals,previousSessionId:t},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:e});this.session=n}_checkSession(){if(!this.session)return!1;const t=this.session;return!Sn(t,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(t),!1)}async _refreshSession(t){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(t.id))}_addListeners(){try{v.document.addEventListener("visibilitychange",this._handleVisibilityChange),v.addEventListener("blur",this._handleWindowBlur),v.addEventListener("focus",this._handleWindowFocus),v.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(!function(t){const e=(0,i.Gd)().getScope(),n=(0,i.Gd)().getClient();e.addScopeListener(ds(t)),(0,m.oq)("dom",Je(t)),(0,m.oq)("history",Dn(t)),ls(t);const s=Rn(t,!hs(n));n&&n.addEventProcessor?n.addEventProcessor(s):(0,a.cc)(s),hs(n)&&(n.on("afterSendEvent",xn(t)),n.on("createDsc",(e=>{const n=t.getSessionId();n&&t.isEnabled()&&"session"===t.recordingMode&&(e.replay_id=n)})),n.on("startTransaction",(e=>{t.lastTransaction=e})),n.on("finishTransaction",(e=>{t.lastTransaction=e})))}(this),this._hasInitializedCoreListeners=!0)}catch(t){this._handleException(t)}"PerformanceObserver"in v&&(this._performanceObserver=en(this))}_removeListeners(){try{v.document.removeEventListener("visibilitychange",this._handleVisibilityChange),v.removeEventListener("blur",this._handleWindowBlur),v.removeEventListener("focus",this._handleWindowFocus),v.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceObserver&&(this._performanceObserver.disconnect(),this._performanceObserver=void 0)}catch(t){this._handleException(t)}}__init(){this._handleVisibilityChange=()=>{"visible"===v.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()}}__init2(){this._handleWindowBlur=()=>{const t=je({category:"ui.blur"});this._doChangeToBackgroundTasks(t)}}__init3(){this._handleWindowFocus=()=>{const t=je({category:"ui.focus"});this._doChangeToForegroundTasks(t)}}__init4(){this._handleKeyboardEvent=t=>{Xe(this,t)}}_doChangeToBackgroundTasks(t){if(!this.session)return;vn(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(t&&this._createCustomBreadcrumb(t),this.conditionalFlush())}_doChangeToForegroundTasks(t){if(!this.session)return;this.checkAndHandleExpiredSession()?t&&this._createCustomBreadcrumb(t):sn("[Replay] Document has become active, but session has expired")}_triggerFullSnapshot(t=!0){try{sn("[Replay] Taking full rrweb snapshot"),Ue.takeFullSnapshot(t)}catch(e){this._handleException(e)}}_updateUserActivity(t=Date.now()){this._lastActivity=t}_updateSessionActivity(t=Date.now()){this.session&&(this.session.lastActivity=t,this._maybeSaveSession())}_createCustomBreadcrumb(t){this.addUpdate((()=>{this.throttledAddEvent({type:Nt.Custom,timestamp:t.timestamp||0,data:{tag:"breadcrumb",payload:t}})}))}_addPerformanceEntries(){const t=[...this.performanceEvents];return this.performanceEvents=[],Promise.all(An(this,function(t){return t.map(ms).filter(Boolean)}(t)))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){const{session:t,eventBuffer:e}=this;if(!t||!e)return;if(t.segmentId)return;const n=e.getEarliestTimestamp();n&&n<this._context.initialTimestamp&&(this._context.initialTimestamp=n)}_popEventContext(){const t={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),t}async _runFlush(){const t=this.getSessionId();if(this.session&&this.eventBuffer&&t){if(await this._addPerformanceEntries(),this.eventBuffer&&this.eventBuffer.hasEvents&&(await async function(t){try{return Promise.all(An(t,[ps(v.performance.memory)]))}catch(e){return[]}}(this),this.eventBuffer&&t===this.getSessionId()))try{this._updateInitialTimestampFromEventBuffer();const e=Date.now();if(e-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw new Error("Session is too long, not sending replay");const n=this._popEventContext(),s=this.session.segmentId++;this._maybeSaveSession();const r=await this.eventBuffer.finish();await Ss({replayId:t,recordingData:r,segmentId:s,eventContext:n,session:this.session,options:this.getOptions(),timestamp:e})}catch(e){this._handleException(e),this.stop({reason:"sendReplay"});const t=(0,i.Gd)().getClient();t&&t.recordDroppedEvent("send_error","replay")}}else("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay] No session or eventBuffer found to flush.")}__init5(){this._flush=async({force:t=!1}={})=>{if(!this._isEnabled&&!t)return;if(!this.checkAndHandleExpiredSession())return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay] Attempting to finish replay event after session expired."));if(!this.session)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error("[Replay] No session found to flush."));const e=this.session.started,n=Date.now()-e;this._debouncedFlush.cancel();const s=n<this._options.minReplayDuration,r=n>this._options.maxReplayDuration+5e3;if(s||r)return sn(`[Replay] Session duration (${Math.floor(n/1e3)}s) is too ${s?"short":"long"}, not sending replay.`,this._options._experiments.traceInternals),void(s&&this._debouncedFlush());const i=this.eventBuffer;if(i&&0===this.session.segmentId&&!i.hasCheckout&&sn("[Replay] Flushing initial segment without checkout.",this._options._experiments.traceInternals),!this._flushLock)return this._flushLock=this._runFlush(),await this._flushLock,void(this._flushLock=void 0);try{await this._flushLock}catch(a){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&p.kg.error(a)}finally{this._debouncedFlush()}}}_maybeSaveSession(){this.session&&this._options.stickySession&&_n(this.session)}__init6(){this._onMutationHandler=t=>{const e=t.length,n=this._options.mutationLimit,s=n&&e>n;if(e>this._options.mutationBreadcrumbLimit||s){const t=je({category:"replay.mutations",data:{count:e,limit:s}});this._createCustomBreadcrumb(t)}return!s||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}}function Is(t,e,n,s){const r=[...t,..."string"===typeof s?s.split(","):[],...e];return"undefined"!==typeof n&&("string"===typeof n&&r.push(`.${n}`),console.warn("[Replay] You are using a deprecated configuration item for privacy. Read the documentation on how to use the new privacy configuration.")),r.join(",")}function Cs(){return"undefined"!==typeof window&&(!(0,b.KV)()||"undefined"!==typeof process&&"renderer"===process.type)}const Ts='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',xs=["content-length","content-type","accept"];let Rs=!1;class As{static __initStatic(){this.id="Replay"}constructor({flushMinDelay:t=5e3,flushMaxDelay:e=5500,minReplayDuration:n=4999,maxReplayDuration:s=36e5,stickySession:r=!0,useCompression:i=!0,_experiments:a={},sessionSampleRate:o,errorSampleRate:c,maskAllText:l=!0,maskAllInputs:u=!0,blockAllMedia:d=!0,mutationBreadcrumbLimit:h=750,mutationLimit:p=1e4,slowClickTimeout:f=7e3,slowClickIgnoreSelectors:m=[],networkDetailAllowUrls:_=[],networkDetailDenyUrls:g=[],networkCaptureBodies:y=!0,networkRequestHeaders:b=[],networkResponseHeaders:v=[],mask:S=[],maskAttributes:k=["title","placeholder"],unmask:w=[],block:E=[],unblock:I=[],ignore:C=[],maskFn:x,beforeAddRecordingEvent:R,blockClass:A,blockSelector:D,maskInputOptions:N,maskTextClass:O,maskTextSelector:M,ignoreClass:U}={}){this.name=As.id;const B=function({mask:t,unmask:e,block:n,unblock:s,ignore:r,blockClass:i,blockSelector:a,maskTextClass:o,maskTextSelector:c,ignoreClass:l}){const u={maskTextSelector:Is(t,[".sentry-mask","[data-sentry-mask]"],o,c),unmaskTextSelector:Is(e,[".sentry-unmask","[data-sentry-unmask]"]),blockSelector:Is(n,[".sentry-block","[data-sentry-block]",'base[href="/"]'],i,a),unblockSelector:Is(s,[".sentry-unblock","[data-sentry-unblock]"]),ignoreSelector:Is(r,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'],l)};return i instanceof RegExp&&(u.blockClass=i),o instanceof RegExp&&(u.maskTextClass=o),u}({mask:S,unmask:w,block:E,unblock:I,ignore:C,blockClass:A,blockSelector:D,maskTextClass:O,maskTextSelector:M,ignoreClass:U});if(this._recordingOptions={maskAllInputs:u,maskAllText:l,maskInputOptions:{...N||{},password:!0},maskTextFn:x,maskInputFn:x,maskAttributeFn:(t,e,n)=>function({el:t,key:e,maskAttributes:n,maskAllText:s,privacyOptions:r,value:i}){return s?r.unmaskTextSelector&&t.matches(r.unmaskTextSelector)?i:n.includes(e)||"value"===e&&"INPUT"===t.tagName&&["submit","button"].includes(t.getAttribute("type")||"")?i.replace(/[\S]/g,"*"):i:i}({maskAttributes:k,maskAllText:l,privacyOptions:B,key:t,value:e,el:n}),...B,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:t=>{try{t.__rrweb__=!0}catch(e){}}},this._initialOptions={flushMinDelay:t,flushMaxDelay:e,minReplayDuration:Math.min(n,15e3),maxReplayDuration:Math.min(s,T),stickySession:r,sessionSampleRate:o,errorSampleRate:c,useCompression:i,blockAllMedia:d,maskAllInputs:u,maskAllText:l,mutationBreadcrumbLimit:h,mutationLimit:p,slowClickTimeout:f,slowClickIgnoreSelectors:m,networkDetailAllowUrls:_,networkDetailDenyUrls:g,networkCaptureBodies:y,networkRequestHeaders:Ds(b),networkResponseHeaders:Ds(v),beforeAddRecordingEvent:R,_experiments:a},"number"===typeof o&&(console.warn(`[Replay] You are passing \`sessionSampleRate\` to the Replay integration.\nThis option is deprecated and will be removed soon.\nInstead, configure \`replaysSessionSampleRate\` directly in the SDK init options, e.g.:\nSentry.init({ replaysSessionSampleRate: ${o} })`),this._initialOptions.sessionSampleRate=o),"number"===typeof c&&(console.warn(`[Replay] You are passing \`errorSampleRate\` to the Replay integration.\nThis option is deprecated and will be removed soon.\nInstead, configure \`replaysOnErrorSampleRate\` directly in the SDK init options, e.g.:\nSentry.init({ replaysOnErrorSampleRate: ${c} })`),this._initialOptions.errorSampleRate=c),this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${Ts}`:Ts),this._isInitialized&&Cs())throw new Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return Rs}set _isInitialized(t){Rs=t}setupOnce(){Cs()&&(this._setup(),setTimeout((()=>this._initialize())))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(t){return this._replay&&this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(t):Promise.resolve()}getReplayId(){if(this._replay&&this._replay.isEnabled())return this._replay.getSessionId()}_initialize(){this._replay&&this._replay.initializeSampling()}_setup(){const t=function(t){const e=(0,i.Gd)().getClient(),n=e&&e.getOptions(),s={sessionSampleRate:0,errorSampleRate:0,...(0,d.Jr)(t)};if(!n)return console.warn("SDK client is not available."),s;null==t.sessionSampleRate&&null==t.errorSampleRate&&null==n.replaysSessionSampleRate&&null==n.replaysOnErrorSampleRate&&console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.");"number"===typeof n.replaysSessionSampleRate&&(s.sessionSampleRate=n.replaysSessionSampleRate);"number"===typeof n.replaysOnErrorSampleRate&&(s.errorSampleRate=n.replaysOnErrorSampleRate);return s}(this._initialOptions);this._replay=new Es({options:t,recordingOptions:this._recordingOptions})}}function Ds(t){return[...xs,...t.map((t=>t.toLowerCase()))]}As.__initStatic()},2543:function(t,e,n){n.d(e,{bU:function(){return i},EN:function(){return l},IQ:function(){return u}});var s=n(66353),r=n(1182);const i="baggage",a="sentry-",o=/^sentry-/,c=8192;function l(t){if(!(0,s.HD)(t)&&!Array.isArray(t))return;let e={};if(Array.isArray(t))e=t.reduce(((t,e)=>({...t,...d(e)})),{});else{if(!t)return;e=d(t)}const n=Object.entries(e).reduce(((t,[e,n])=>{if(e.match(o)){t[e.slice(a.length)]=n}return t}),{});return Object.keys(n).length>0?n:void 0}function u(t){if(!t)return;return function(t){if(0===Object.keys(t).length)return;return Object.entries(t).reduce(((t,[e,n],s)=>{const i=`${encodeURIComponent(e)}=${encodeURIComponent(n)}`,a=0===s?i:`${t},${i}`;return a.length>c?(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn(`Not adding key: ${e} with val: ${n} to baggage header due to exceeding baggage size limits.`),t):a}),"")}(Object.entries(t).reduce(((t,[e,n])=>(n&&(t[`${a}${e}`]=n),t)),{}))}function d(t){return t.split(",").map((t=>t.split("=").map((t=>decodeURIComponent(t.trim()))))).reduce(((t,[e,n])=>(t[e]=n,t)),{})}},84236:function(t,e,n){n.d(e,{qT:function(){return l},l4:function(){return c},Rt:function(){return a}});var s=n(66353);const r=(0,n(1846).Rf)(),i=80;function a(t,e={}){try{let n=t;const s=5,r=[];let a=0,c=0;const l=" > ",u=l.length;let d;const h=Array.isArray(e)?e:e.keyAttrs,p=!Array.isArray(e)&&e.maxStringLength||i;for(;n&&a++<s&&(d=o(n,h),!("html"===d||a>1&&c+r.length*u+d.length>=p));)r.push(d),c+=d.length,n=n.parentNode;return r.reverse().join(l)}catch(n){return"<unknown>"}}function o(t,e){const n=t,r=[];let i,a,o,c,l;if(!n||!n.tagName)return"";r.push(n.tagName.toLowerCase());const u=e&&e.length?e.filter((t=>n.getAttribute(t))).map((t=>[t,n.getAttribute(t)])):null;if(u&&u.length)u.forEach((t=>{r.push(`[${t[0]}="${t[1]}"]`)}));else if(n.id&&r.push(`#${n.id}`),i=n.className,i&&(0,s.HD)(i))for(a=i.split(/\s+/),l=0;l<a.length;l++)r.push(`.${a[l]}`);const d=["aria-label","type","name","title","alt"];for(l=0;l<d.length;l++)o=d[l],c=n.getAttribute(o),c&&r.push(`[${o}="${c}"]`);return r.join("")}function c(){try{return r.document.location.href}catch(t){return""}}function l(t){return r.document&&r.document.querySelector?r.document.querySelector(t):null}},15126:function(t,e,n){n.d(e,{RA:function(){return i},vK:function(){return o}});var s=n(1182);const r=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function i(t,e=!1){const{host:n,path:s,pass:r,port:i,projectId:a,protocol:o,publicKey:c}=t;return`${o}://${c}${e&&r?`:${r}`:""}@${n}${i?`:${i}`:""}/${s?`${s}/`:s}${a}`}function a(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function o(t){const e="string"===typeof t?function(t){const e=r.exec(t);if(!e)return void console.error(`Invalid Sentry Dsn: ${t}`);const[n,s,i="",o,c="",l]=e.slice(1);let u="",d=l;const h=d.split("/");if(h.length>1&&(u=h.slice(0,-1).join("/"),d=h.pop()),d){const t=d.match(/^\d+/);t&&(d=t[0])}return a({host:o,pass:i,path:u,projectId:d,port:c,protocol:n,publicKey:s})}(t):a(t);if(e&&function(t){if("undefined"!==typeof __SENTRY_DEBUG__&&!__SENTRY_DEBUG__)return!0;const{port:e,projectId:n,protocol:r}=t;return!["protocol","publicKey","host","projectId"].find((e=>!t[e]&&(s.kg.error(`Invalid Sentry Dsn: ${e} missing`),!0)))&&(n.match(/^\d+$/)?function(t){return"http"===t||"https"===t}(r)?!e||!isNaN(parseInt(e,10))||(s.kg.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):(s.kg.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(s.kg.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1))}(e))return e}},52661:function(t,e,n){function s(){return"undefined"!==typeof __SENTRY_BROWSER_BUNDLE__&&!!__SENTRY_BROWSER_BUNDLE__}function r(){return"npm"}n.d(e,{S:function(){return r},n:function(){return s}})},97366:function(t,e,n){n.d(e,{BO:function(){return o},zQ:function(){return d},Jd:function(){return a},Cd:function(){return m},mL:function(){return p},gv:function(){return c},HY:function(){return f},V$:function(){return u}});var s=n(15126),r=n(78643),i=n(7221);function a(t,e=[]){return[t,e]}function o(t,e){const[n,s]=t;return[n,[...s,e]]}function c(t,e){const n=t[1];for(const s of n){if(e(s,s[0].type))return!0}return!1}function l(t,e){return(e||new TextEncoder).encode(t)}function u(t,e){const[n,s]=t;let i=JSON.stringify(n);function a(t){"string"===typeof i?i="string"===typeof t?i+t:[l(i,e),t]:i.push("string"===typeof t?l(t,e):t)}for(const c of s){const[t,e]=c;if(a(`\n${JSON.stringify(t)}\n`),"string"===typeof e||e instanceof Uint8Array)a(e);else{let t;try{t=JSON.stringify(e)}catch(o){t=JSON.stringify((0,r.Fv)(e))}a(t)}}return"string"===typeof i?i:function(t){const e=t.reduce(((t,e)=>t+e.length),0),n=new Uint8Array(e);let s=0;for(const r of t)n.set(r,s),s+=r.length;return n}(i)}function d(t,e){const n="string"===typeof t.data?l(t.data,e):t.data;return[(0,i.Jr)({type:"attachment",length:n.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),n]}const h={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor"};function p(t){return h[t]}function f(t){if(!t||!t.sdk)return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function m(t,e,n,r){const a=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:(0,s.RA)(r)},...a&&{trace:(0,i.Jr)({...a})}}}},29722:function(t,e,n){n.d(e,{xU:function(){return d},oq:function(){return m}});var s=n(66353),r=n(1182),i=n(7221),a=n(56813),o=n(21939),c=n(1846);const l=(0,c.Rf)();const u=(0,c.Rf)(),d="__sentry_xhr_v2__",h={},p={};function f(t){if(!p[t])switch(p[t]=!0,t){case"console":!function(){if(!("console"in c.n2))return;r.RU.forEach((function(t){t in c.n2.console&&(0,i.hl)(c.n2.console,t,(function(e){return r.LD[t]=e,function(...e){_("console",{args:e,level:t});const n=r.LD[t];n&&n.apply(c.n2.console,e)}}))}))}();break;case"dom":!function(){if(!u.document)return;const t=_.bind(null,"dom"),e=w(t,!0);u.document.addEventListener("click",e,!1),u.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach((e=>{const n=u[e]&&u[e].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&((0,i.hl)(n,"addEventListener",(function(e){return function(n,s,r){if("click"===n||"keypress"==n)try{const s=this,i=s.__sentry_instrumentation_handlers__=s.__sentry_instrumentation_handlers__||{},a=i[n]=i[n]||{refCount:0};if(!a.handler){const s=w(t);a.handler=s,e.call(this,n,s,r)}a.refCount++}catch(i){}return e.call(this,n,s,r)}})),(0,i.hl)(n,"removeEventListener",(function(t){return function(e,n,s){if("click"===e||"keypress"==e)try{const n=this,r=n.__sentry_instrumentation_handlers__||{},i=r[e];i&&(i.refCount--,i.refCount<=0&&(t.call(this,e,i.handler,s),i.handler=void 0,delete r[e]),0===Object.keys(r).length&&delete n.__sentry_instrumentation_handlers__)}catch(r){}return t.call(this,e,n,s)}})))}))}();break;case"xhr":!function(){if(!u.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;(0,i.hl)(t,"open",(function(t){return function(...e){const n=e[1],r=this[d]={method:(0,s.HD)(e[0])?e[0].toUpperCase():e[0],url:e[1],request_headers:{}};(0,s.HD)(n)&&"POST"===r.method&&n.match(/sentry_key/)&&(this.__sentry_own_request__=!0);const a=()=>{const t=this[d];if(t&&4===this.readyState){try{t.status_code=this.status}catch(n){}_("xhr",{args:e,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:this})}};return"onreadystatechange"in this&&"function"===typeof this.onreadystatechange?(0,i.hl)(this,"onreadystatechange",(function(t){return function(...e){return a(),t.apply(this,e)}})):this.addEventListener("readystatechange",a),(0,i.hl)(this,"setRequestHeader",(function(t){return function(...e){const[n,s]=e,r=this[d];return r&&(r.request_headers[n.toLowerCase()]=s),t.apply(this,e)}})),t.apply(this,e)}})),(0,i.hl)(t,"send",(function(t){return function(...e){const n=this[d];return n&&void 0!==e[0]&&(n.body=e[0]),_("xhr",{args:e,startTimestamp:Date.now(),xhr:this}),t.apply(this,e)}}))}();break;case"fetch":!function(){if(!(0,o.t$)())return;(0,i.hl)(c.n2,"fetch",(function(t){return function(...e){const{method:n,url:s}=function(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){const[e,n]=t;return{url:y(e),method:g(n,"method")?String(n.method).toUpperCase():"GET"}}const e=t[0];return{url:y(e),method:g(e,"method")?String(e.method).toUpperCase():"GET"}}(e),r={args:e,fetchData:{method:n,url:s},startTimestamp:Date.now()};return _("fetch",{...r}),t.apply(c.n2,e).then((t=>(_("fetch",{...r,endTimestamp:Date.now(),response:t}),t)),(t=>{throw _("fetch",{...r,endTimestamp:Date.now(),error:t}),t}))}}))}();break;case"history":!function(){if(!function(){const t=l.chrome,e=t&&t.app&&t.app.runtime,n="history"in l&&!!l.history.pushState&&!!l.history.replaceState;return!e&&n}())return;const t=u.onpopstate;function e(t){return function(...e){const n=e.length>2?e[2]:void 0;if(n){const t=b,e=String(n);b=e,_("history",{from:t,to:e})}return t.apply(this,e)}}u.onpopstate=function(...e){const n=u.location.href,s=b;if(b=n,_("history",{from:s,to:n}),t)try{return t.apply(this,e)}catch(r){}},(0,i.hl)(u.history,"pushState",e),(0,i.hl)(u.history,"replaceState",e)}();break;case"error":E=u.onerror,u.onerror=function(t,e,n,s,r){return _("error",{column:s,error:r,line:n,msg:t,url:e}),!(!E||E.__SENTRY_LOADER__)&&E.apply(this,arguments)},u.onerror.__SENTRY_INSTRUMENTED__=!0;break;case"unhandledrejection":I=u.onunhandledrejection,u.onunhandledrejection=function(t){return _("unhandledrejection",t),!(I&&!I.__SENTRY_LOADER__)||I.apply(this,arguments)},u.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0;break;default:return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.warn("unknown instrumentation type:",t))}}function m(t,e){h[t]=h[t]||[],h[t].push(e),f(t)}function _(t,e){if(t&&h[t])for(const s of h[t]||[])try{s(e)}catch(n){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&r.kg.error(`Error while triggering instrumentation handler.\nType: ${t}\nName: ${(0,a.$P)(s)}\nError:`,n)}}function g(t,e){return!!t&&"object"===typeof t&&!!t[e]}function y(t){return"string"===typeof t?t:t?g(t,"url")?t.url:t.toString?t.toString():"":""}let b;const v=1e3;let S,k;function w(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;if(function(t){if("keypress"!==t.type)return!1;try{const e=t.target;if(!e||!e.tagName)return!0;if("INPUT"===e.tagName||"TEXTAREA"===e.tagName||e.isContentEditable)return!1}catch(e){}return!0}(n))return;(0,i.xp)(n,"_sentryCaptured",!0);const s="keypress"===n.type?"input":n.type;void 0!==k&&function(t,e){if(t.type!==e.type)return!1;try{if(t.target!==e.target)return!1}catch(n){}return!0}(k,n)||(t({event:n,name:s,global:e}),k=n),clearTimeout(S),S=u.setTimeout((()=>{k=void 0}),v)}}let E=null;let I=null},66353:function(t,e,n){n.d(e,{TX:function(){return o},fm:function(){return c},kK:function(){return p},VZ:function(){return r},VW:function(){return a},cO:function(){return h},V9:function(){return y},i2:function(){return g},PO:function(){return d},pt:function(){return u},Kj:function(){return f},HD:function(){return l},Cy:function(){return _},J8:function(){return m},y1:function(){return b}});const s=Object.prototype.toString;function r(t){switch(s.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return y(t,Error)}}function i(t,e){return s.call(t)===`[object ${e}]`}function a(t){return i(t,"ErrorEvent")}function o(t){return i(t,"DOMError")}function c(t){return i(t,"DOMException")}function l(t){return i(t,"String")}function u(t){return null===t||"object"!==typeof t&&"function"!==typeof t}function d(t){return i(t,"Object")}function h(t){return"undefined"!==typeof Event&&y(t,Event)}function p(t){return"undefined"!==typeof Element&&y(t,Element)}function f(t){return i(t,"RegExp")}function m(t){return Boolean(t&&t.then&&"function"===typeof t.then)}function _(t){return d(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function g(t){return"number"===typeof t&&t!==t}function y(t,e){try{return t instanceof e}catch(n){return!1}}function b(t){return!("object"!==typeof t||null===t||!t.__isVue&&!t._isVue)}},1182:function(t,e,n){n.d(e,{RU:function(){return r},Cf:function(){return a},kg:function(){return o},LD:function(){return i}});var s=n(1846);const r=["debug","info","warn","error","log","assert","trace"],i={};function a(t){if(!("console"in s.n2))return t();const e=s.n2.console,n={},r=Object.keys(i);r.forEach((t=>{const s=i[t];n[t]=e[t],e[t]=s}));try{return t()}finally{r.forEach((t=>{e[t]=n[t]}))}}const o=function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return"undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__?r.forEach((n=>{e[n]=(...e)=>{t&&a((()=>{s.n2.console[n](`Sentry Logger [${n}]:`,...e)}))}})):r.forEach((t=>{e[t]=()=>{}})),e}()},74379:function(t,e,n){n.d(e,{EG:function(){return l},Db:function(){return c},lE:function(){return d},YO:function(){return u},jH:function(){return o},DM:function(){return i}});var s=n(7221),r=n(1846);function i(){const t=r.n2,e=t.crypto||t.msCrypto;let n=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>e.getRandomValues(new Uint8Array(1))[0])}catch(s){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&n())>>t/4).toString(16)))}function a(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function o(t){const{message:e,event_id:n}=t;if(e)return e;const s=a(t);return s?s.type&&s.value?`${s.type}: ${s.value}`:s.type||s.value||n||"<unknown>":n||"<unknown>"}function c(t,e,n){const s=t.exception=t.exception||{},r=s.values=s.values||[],i=r[0]=r[0]||{};i.value||(i.value=e||""),i.type||(i.type=n||"Error")}function l(t,e){const n=a(t);if(!n)return;const s=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...s,...e},e&&"data"in e){const t={...s&&s.data,...e.data};n.mechanism.data=t}}function u(t){if(t&&t.__sentry_captured__)return!0;try{(0,s.xp)(t,"__sentry_captured__",!0)}catch(e){}return!1}function d(t){return Array.isArray(t)?t:[t]}},64305:function(t,e,n){n.d(e,{l$:function(){return i},KV:function(){return r}});var s=n(52661);function r(){return!(0,s.n)()&&"[object process]"===Object.prototype.toString.call("undefined"!==typeof process?process:0)}function i(t,e){return t.require(e)}t=n.hmd(t)},78643:function(t,e,n){n.d(e,{Fv:function(){return a},Qy:function(){return o}});var s=n(66353);var r=n(7221),i=n(56813);function a(t,e=100,n=1/0){try{return c("",t,e,n)}catch(s){return{ERROR:`**non-serializable** (${s})`}}}function o(t,e=3,n=102400){const s=a(t,e);return r=s,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(r))>n?o(t,e-1,n):s;var r}function c(t,e,a=1/0,o=1/0,l=function(){const t="function"===typeof WeakSet,e=t?new WeakSet:[];return[function(n){if(t)return!!e.has(n)||(e.add(n),!1);for(let t=0;t<e.length;t++)if(e[t]===n)return!0;return e.push(n),!1},function(n){if(t)e.delete(n);else for(let t=0;t<e.length;t++)if(e[t]===n){e.splice(t,1);break}}]}()){const[u,d]=l;if(null==e||["number","boolean","string"].includes(typeof e)&&!(0,s.i2)(e))return e;const h=function(t,e){try{if("domain"===t&&e&&"object"===typeof e&&e._events)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!==typeof n.g&&e===n.g)return"[Global]";if("undefined"!==typeof window&&e===window)return"[Window]";if("undefined"!==typeof document&&e===document)return"[Document]";if((0,s.y1)(e))return"[VueViewModel]";if((0,s.Cy)(e))return"[SyntheticEvent]";if("number"===typeof e&&e!==e)return"[NaN]";if("function"===typeof e)return`[Function: ${(0,i.$P)(e)}]`;if("symbol"===typeof e)return`[${String(e)}]`;if("bigint"===typeof e)return`[BigInt: ${String(e)}]`;const r=function(t){const e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}(e);return/^HTML(\w*)Element$/.test(r)?`[HTMLElement: ${r}]`:`[object ${r}]`}catch(r){return`**non-serializable** (${r})`}}(t,e);if(!h.startsWith("[object "))return h;if(e.__sentry_skip_normalization__)return e;const p="number"===typeof e.__sentry_override_normalization_depth__?e.__sentry_override_normalization_depth__:a;if(0===p)return h.replace("object ","");if(u(e))return"[Circular ~]";const f=e;if(f&&"function"===typeof f.toJSON)try{return c("",f.toJSON(),p-1,o,l)}catch(y){}const m=Array.isArray(e)?[]:{};let _=0;const g=(0,r.Sh)(e);for(const n in g){if(!Object.prototype.hasOwnProperty.call(g,n))continue;if(_>=o){m[n]="[MaxProperties ~]";break}const t=g[n];m[n]=c(n,t,p-1,o,l),_++}return d(e),m}},7221:function(t,e,n){n.d(e,{xp:function(){return c},Sh:function(){return h},Jr:function(){return _},zf:function(){return m},hl:function(){return o},HK:function(){return u},$Q:function(){return l},_j:function(){return d}});var s=n(84236),r=n(66353),i=n(1182),a=n(57291);function o(t,e,n){if(!(e in t))return;const s=t[e],r=n(s);"function"===typeof r&&l(r,s),t[e]=r}function c(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch(s){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&i.kg.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function l(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,c(t,"__sentry_original__",e)}catch(n){}}function u(t){return t.__sentry_original__}function d(t){return Object.keys(t).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`)).join("&")}function h(t){if((0,r.VZ)(t))return{message:t.message,name:t.name,stack:t.stack,...f(t)};if((0,r.cO)(t)){const e={type:t.type,target:p(t.target),currentTarget:p(t.currentTarget),...f(t)};return"undefined"!==typeof CustomEvent&&(0,r.V9)(t,CustomEvent)&&(e.detail=t.detail),e}return t}function p(t){try{return(0,r.kK)(t)?(0,s.Rt)(t):Object.prototype.toString.call(t)}catch(e){return"<unknown>"}}function f(t){if("object"===typeof t&&null!==t){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function m(t,e=40){const n=Object.keys(h(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return(0,a.$G)(n[0],e);for(let s=n.length;s>0;s--){const t=n.slice(0,s).join(", ");if(!(t.length>e))return s===n.length?t:(0,a.$G)(t,e)}return""}function _(t){return g(t,new Map)}function g(t,e){if((0,r.PO)(t)){const n=e.get(t);if(void 0!==n)return n;const s={};e.set(t,s);for(const r of Object.keys(t))"undefined"!==typeof t[r]&&(s[r]=g(t[r],e));return s}if(Array.isArray(t)){const n=e.get(t);if(void 0!==n)return n;const s=[];return e.set(t,s),t.forEach((t=>{s.push(g(t,e))})),s}return t}},56813:function(t,e,n){n.d(e,{pE:function(){return a},$P:function(){return l},Sq:function(){return o}});const s=50,r=/\(error: (.*)\)/,i=/captureMessage|captureException/;function a(...t){const e=t.sort(((t,e)=>t[0]-e[0])).map((t=>t[1]));return(t,n=0)=>{const a=[],o=t.split("\n");for(let i=n;i<o.length;i++){const t=o[i];if(t.length>1024)continue;const n=r.test(t)?t.replace(r,"$1"):t;if(!n.match(/\S*Error: /)){for(const t of e){const e=t(n);if(e){a.push(e);break}}if(a.length>=s)break}}return function(t){if(!t.length)return[];const e=Array.from(t);/sentryWrapped/.test(e[e.length-1].function||"")&&e.pop();e.reverse(),i.test(e[e.length-1].function||"")&&(e.pop(),i.test(e[e.length-1].function||"")&&e.pop());return e.slice(0,s).map((t=>({...t,filename:t.filename||e[e.length-1].filename,function:t.function||"?"})))}(a)}}function o(t){return Array.isArray(t)?a(...t):t}const c="<anonymous>";function l(t){try{return t&&"function"===typeof t&&t.name||c}catch(e){return c}}},57291:function(t,e,n){n.d(e,{nK:function(){return i},U0:function(){return a},$G:function(){return r}});var s=n(66353);function r(t,e=0){return"string"!==typeof t||0===e||t.length<=e?t:`${t.slice(0,e)}...`}function i(t,e){if(!Array.isArray(t))return"";const n=[];for(let i=0;i<t.length;i++){const e=t[i];try{(0,s.y1)(e)?n.push("[VueViewModel]"):n.push(String(e))}catch(r){n.push("[value cannot be serialized]")}}return n.join(e)}function a(t,e=[],n=!1){return e.some((e=>function(t,e,n=!1){return!!(0,s.HD)(t)&&((0,s.Kj)(e)?e.test(t):!!(0,s.HD)(e)&&(n?t===e:t.includes(e)))}(t,e,n)))}},21939:function(t,e,n){n.d(e,{Du:function(){return a},Ak:function(){return i},t$:function(){return o}});var s=n(1182);const r=(0,n(1846).Rf)();function i(){if(!("fetch"in r))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}function a(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function o(){if(!i())return!1;if(a(r.fetch))return!0;let t=!1;const e=r.document;if(e&&"function"===typeof e.createElement)try{const n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=a(n.contentWindow.fetch)),e.head.removeChild(n)}catch(n){("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&s.kg.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return t}},82048:function(t,e,n){n.d(e,{cW:function(){return o},$2:function(){return a},WD:function(){return i}});var s,r=n(66353);function i(t){return new o((e=>{e(t)}))}function a(t){return new o(((e,n)=>{n(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(s||(s={}));class o{constructor(t){o.prototype.__init.call(this),o.prototype.__init2.call(this),o.prototype.__init3.call(this),o.prototype.__init4.call(this),this._state=s.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(e){this._reject(e)}}then(t,e){return new o(((n,s)=>{this._handlers.push([!1,e=>{if(t)try{n(t(e))}catch(r){s(r)}else n(e)},t=>{if(e)try{n(e(t))}catch(r){s(r)}else s(t)}]),this._executeHandlers()}))}catch(t){return this.then((t=>t),t)}finally(t){return new o(((e,n)=>{let s,r;return this.then((e=>{r=!1,s=e,t&&t()}),(e=>{r=!0,s=e,t&&t()})).then((()=>{r?n(s):e(s)}))}))}__init(){this._resolve=t=>{this._setResult(s.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(s.REJECTED,t)}}__init3(){this._setResult=(t,e)=>{this._state===s.PENDING&&((0,r.J8)(e)?e.then(this._resolve,this._reject):(this._state=t,this._value=e,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===s.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach((t=>{t[0]||(this._state===s.RESOLVED&&t[1](this._value),this._state===s.REJECTED&&t[2](this._value),t[0]=!0)}))}}}},96400:function(t,e,n){n.d(e,{Z1:function(){return h},yW:function(){return l},ph:function(){return u}});var s=n(64305),r=n(1846);t=n.hmd(t);const i=(0,r.Rf)(),a={nowSeconds:()=>Date.now()/1e3};const o=(0,s.KV)()?function(){try{return(0,s.l$)(t,"perf_hooks").performance}catch(e){return}}():function(){const{performance:t}=i;if(!t||!t.now)return;return{now:()=>t.now(),timeOrigin:Date.now()-t.now()}}(),c=void 0===o?a:{nowSeconds:()=>(o.timeOrigin+o.now())/1e3},l=a.nowSeconds.bind(a),u=c.nowSeconds.bind(c);let d;const h=(()=>{const{performance:t}=i;if(!t||!t.now)return void(d="none");const e=36e5,n=t.now(),s=Date.now(),r=t.timeOrigin?Math.abs(t.timeOrigin+n-s):e,a=r<e,o=t.timing&&t.timing.navigationStart,c="number"===typeof o?Math.abs(o+n-s):e;return a||c<e?r<=c?(d="timeOrigin",t.timeOrigin):(d="navigationStart",o):(d="dateNow",s)})()},86118:function(t,e,n){n.d(e,{$p:function(){return o},KA:function(){return a}});var s=n(2543),r=n(74379);const i=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function a(t,e){const n=function(t){if(!t)return;const e=t.match(i);if(!e)return;let n;return"1"===e[3]?n=!0:"0"===e[3]&&(n=!1),{traceId:e[1],parentSampled:n,parentSpanId:e[2]}}(t),a=(0,s.EN)(e),{traceId:o,parentSpanId:c,parentSampled:l}=n||{},u={traceId:o||(0,r.DM)(),spanId:(0,r.DM)().substring(16),sampled:l};return c&&(u.parentSpanId=c),a&&(u.dsc=a),{traceparentData:n,dynamicSamplingContext:a,propagationContext:u}}function o(t=(0,r.DM)(),e=(0,r.DM)().substring(16),n){let s="";return void 0!==n&&(s=n?"-1":"-0"),`${t}-${e}${s}`}},1846:function(t,e,n){function s(t){return t&&t.Math==Math?t:void 0}n.d(e,{n2:function(){return r},Rf:function(){return i},YO:function(){return a}});const r="object"==typeof globalThis&&s(globalThis)||"object"==typeof window&&s(window)||"object"==typeof self&&s(self)||"object"==typeof n.g&&s(n.g)||function(){return this}()||{};function i(){return r}function a(t,e,n){const s=n||r,i=s.__SENTRY__=s.__SENTRY__||{};return i[t]||(i[t]=e())}}}]);
//# sourceMappingURL=@sentry.67e22007ef7eb950321c2500d6b39596.js.map