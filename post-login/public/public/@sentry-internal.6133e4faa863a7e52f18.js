"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@sentry-internal"],{74975:function(e,t,n){n.d(t,{gE:function(){return V}});var r=n(40923),a=n(17119),i=n(12373),o=n(1182),s=n(86118),c=n(84236);const d=n(1846).n2;var u=n(96400);const p=(e,t,n)=>{let r,a;return i=>{t.value>=0&&(i||n)&&(a=t.value-(r||0),(a||void 0===r)&&(r=t.value,t.delta=a,e(t)))}},_=()=>d.__WEB_VITALS_POLYFILL__?d.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||(()=>{const e=d.performance.timing,t=d.performance.navigation.type,n={entryType:"navigation",startTime:0,type:2==t?"back_forward":1===t?"reload":"navigate"};for(const r in e)"navigationStart"!==r&&"toJSON"!==r&&(n[r]=Math.max(e[r]-e.navigationStart,0));return n})()):d.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0],g=()=>{const e=_();return e&&e.activationStart||0},l=(e,t)=>{const n=_();let r="navigate";return n&&(r=d.document.prerendering||g()>0?"prerender":n.type.replace(/_/g,"-")),{name:e,value:"undefined"===typeof t?-1:t,rating:"good",delta:0,entries:[],id:`v3-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},m=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver((e=>{t(e.getEntries())}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(r){}},f=(e,t)=>{const n=r=>{"pagehide"!==r.type&&"hidden"!==d.document.visibilityState||(e(r),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},T=e=>{const t=l("CLS",0);let n,r=0,a=[];const i=e=>{e.forEach((e=>{if(!e.hadRecentInput){const i=a[0],o=a[a.length-1];r&&0!==a.length&&e.startTime-o.startTime<1e3&&e.startTime-i.startTime<5e3?(r+=e.value,a.push(e)):(r=e.value,a=[e]),r>t.value&&(t.value=r,t.entries=a,n&&n())}}))},o=m("layout-shift",i);if(o){n=p(e,t);const r=()=>{i(o.takeRecords()),n(!0)};return f(r),r}};let h=-1;const E=()=>(h<0&&(h="hidden"!==d.document.visibilityState||d.document.prerendering?1/0:0,f((({timeStamp:e})=>{h=e}),!0)),{get firstHiddenTime(){return h}}),v=e=>{const t=E(),n=l("FID");let r;const a=e=>{e.startTime<t.firstHiddenTime&&(n.value=e.processingStart-e.startTime,n.entries.push(e),r(!0))},i=e=>{e.forEach(a)},o=m("first-input",i);r=p(e,n),o&&f((()=>{i(o.takeRecords()),o.disconnect()}),!0)},y={},S=e=>{const t=E(),n=l("LCP");let r;const a=e=>{const a=e[e.length-1];if(a){const e=Math.max(a.startTime-g(),0);e<t.firstHiddenTime&&(n.value=e,n.entries=[a],r())}},i=m("largest-contentful-paint",a);if(i){r=p(e,n);const t=()=>{y[n.id]||(a(i.takeRecords()),i.disconnect(),y[n.id]=!0,r(!0))};return["keydown","click"].forEach((e=>{addEventListener(e,t,{once:!0,capture:!0})})),f(t,!0),t}};function b(e){return"number"===typeof e&&isFinite(e)}function R(e,{startTimestamp:t,...n}){return t&&e.startTimestamp>t&&(e.startTimestamp=t),e.startChild({startTimestamp:t,...n})}function k(e){return e/1e3}function D(){return d&&d.addEventListener&&d.performance}let B,N,U=0,C={};function G(){const e=D();if(e&&u.Z1){e.mark&&d.performance.mark("sentry-tracing-init"),v((e=>{const t=e.entries.pop();if(!t)return;const n=k(u.Z1),r=k(t.startTime);("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding FID"),C.fid={value:e.value,unit:"millisecond"},C["mark.fid"]={value:n+r,unit:"second"}}));const t=T((e=>{const t=e.entries.pop();t&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding CLS"),C.cls={value:e.value,unit:""},N=t)})),n=S((e=>{const t=e.entries.pop();t&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding LCP"),C.lcp={value:e.value,unit:"millisecond"},B=t)}));return()=>{t&&t(),n&&n()}}return()=>{}}function w(e){const t=D();if(!t||!d.performance.getEntries||!u.Z1)return;("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Tracing] Adding & adjusting spans using Performance API");const n=k(u.Z1),r=t.getEntries();let a,i;if(r.slice(U).forEach((t=>{const r=k(t.startTime),s=k(t.duration);if(!("navigation"===e.op&&n+r<e.startTimestamp))switch(t.entryType){case"navigation":!function(e,t,n){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((r=>{Y(e,t,r,n)})),Y(e,t,"secureConnection",n,"TLS/SSL","connectEnd"),Y(e,t,"fetch",n,"cache","domainLookupStart"),Y(e,t,"domainLookup",n,"DNS"),function(e,t,n){R(e,{op:"browser",origin:"auto.browser.browser.metrics",description:"request",startTimestamp:n+k(t.requestStart),endTimestamp:n+k(t.responseEnd)}),R(e,{op:"browser",origin:"auto.browser.browser.metrics",description:"response",startTimestamp:n+k(t.responseStart),endTimestamp:n+k(t.responseEnd)})}(e,t,n)}(e,t,n),a=n+k(t.responseStart),i=n+k(t.requestStart);break;case"mark":case"paint":case"measure":{!function(e,t,n,r,a){const i=a+n,o=i+r;R(e,{description:t.name,endTimestamp:o,op:t.entryType,origin:"auto.resource.browser.metrics",startTimestamp:i})}(e,t,r,s,n);const a=E(),i=t.startTime<a.firstHiddenTime;"first-paint"===t.name&&i&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding FP"),C.fp={value:t.startTime,unit:"millisecond"}),"first-contentful-paint"===t.name&&i&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding FCP"),C.fcp={value:t.startTime,unit:"millisecond"});break}case"resource":{const a=t.name.replace(d.location.origin,"");!function(e,t,n,r,a,i){if("xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType)return;const o={};"transferSize"in t&&(o["http.response_transfer_size"]=t.transferSize);"encodedBodySize"in t&&(o["http.response_content_length"]=t.encodedBodySize);"decodedBodySize"in t&&(o["http.decoded_response_content_length"]=t.decodedBodySize);"renderBlockingStatus"in t&&(o["resource.render_blocking_status"]=t.renderBlockingStatus);const s=i+r,c=s+a;R(e,{description:n,endTimestamp:c,op:t.initiatorType?`resource.${t.initiatorType}`:"resource.other",origin:"auto.resource.browser.metrics",startTimestamp:s,data:o})}(e,t,a,r,s,n);break}}})),U=Math.max(r.length-1,0),function(e){const t=d.navigator;if(!t)return;const n=t.connection;n&&(n.effectiveType&&e.setTag("effectiveConnectionType",n.effectiveType),n.type&&e.setTag("connectionType",n.type),b(n.rtt)&&(C["connection.rtt"]={value:n.rtt,unit:"millisecond"}));b(t.deviceMemory)&&e.setTag("deviceMemory",`${t.deviceMemory} GB`);b(t.hardwareConcurrency)&&e.setTag("hardwareConcurrency",String(t.hardwareConcurrency))}(e),"pageload"===e.op){"number"===typeof a&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding TTFB"),C.ttfb={value:1e3*(a-e.startTimestamp),unit:"millisecond"},"number"===typeof i&&i<=a&&(C["ttfb.requestTime"]={value:1e3*(a-i),unit:"millisecond"})),["fcp","fp","lcp"].forEach((t=>{if(!C[t]||n>=e.startTimestamp)return;const r=C[t].value,a=n+k(r),i=Math.abs(1e3*(a-e.startTimestamp)),s=i-r;("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log(`[Measurements] Normalized ${t} from ${r} to ${i} (${s})`),C[t].value=i}));const t=C["mark.fid"];t&&C.fid&&(R(e,{description:"first input delay",endTimestamp:t.value+k(C.fid.value),op:"ui.action",origin:"auto.ui.browser.metrics",startTimestamp:t.value}),delete C["mark.fid"]),"fcp"in C||delete C.cls,Object.keys(C).forEach((t=>{e.setMeasurement(t,C[t].value,C[t].unit)})),function(e){B&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding LCP Data"),B.element&&e.setTag("lcp.element",(0,c.Rt)(B.element)),B.id&&e.setTag("lcp.id",B.id),B.url&&e.setTag("lcp.url",B.url.trim().slice(0,200)),e.setTag("lcp.size",B.size));N&&N.sources&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log("[Measurements] Adding CLS Data"),N.sources.forEach(((t,n)=>e.setTag(`cls.source.${n+1}`,(0,c.Rt)(t.node)))))}(e)}B=void 0,N=void 0,C={}}function Y(e,t,n,r,a,i){const o=i?t[i]:t[`${n}End`],s=t[`${n}Start`];s&&o&&R(e,{op:"browser",origin:"auto.browser.browser.metrics",description:a||n,startTimestamp:r+k(s),endTimestamp:r+k(o)})}var L=n(11805),x=n(3886),P=n(8330),q=n(29722),H=n(57291),I=n(2543),$=n(66353);const M=["localhost",/^\/(?!\/)/],O={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,tracingOrigins:M,tracePropagationTargets:M};function F(e){const{traceFetch:t,traceXHR:n,tracePropagationTargets:r,tracingOrigins:a,shouldCreateSpanForRequest:i,enableHTTPTimings:o}={traceFetch:O.traceFetch,traceXHR:O.traceXHR,...e},c="function"===typeof i?i:e=>!0,d=e=>function(e,t){return(0,H.U0)(e,t||M)}(e,r||a),u={};t&&(0,q.oq)("fetch",(e=>{const t=function(e,t,n,r){if(!(0,L.z)()||!e.fetchData)return;const a=t(e.fetchData.url);if(e.endTimestamp&&a){const t=e.fetchData.__span;if(!t)return;const n=r[t];if(n){if(e.response){n.setHttpStatus(e.response.status);const t=e.response&&e.response.headers&&e.response.headers.get("content-length"),r=parseInt(t);r>0&&n.setData("http.response_content_length",r)}else e.error&&n.setStatus("internal_error");n.finish(),delete r[t]}return}const i=(0,x.Gd)(),o=i.getScope(),c=i.getClient(),d=o.getSpan(),{method:u,url:p}=e.fetchData,_=a&&d?d.startChild({data:{url:p,type:"fetch","http.method":u},description:`${u} ${p}`,op:"http.client",origin:"auto.http.browser"}):void 0;_&&(e.fetchData.__span=_.spanId,r[_.spanId]=_);if(n(e.fetchData.url)&&c){const t=e.args[0];e.args[1]=e.args[1]||{};const n=e.args[1];n.headers=function(e,t,n,r,a){const i=a||n.getSpan(),o=i&&i.transaction,{traceId:c,sampled:d,dsc:u}=n.getPropagationContext(),p=i?i.toTraceparent():(0,s.$p)(c,void 0,d),_=o?o.getDynamicSamplingContext():u||(0,P._)(c,t,n),g=(0,I.IQ)(_),l="undefined"!==typeof Request&&(0,$.V9)(e,Request)?e.headers:r.headers;if(l){if("undefined"!==typeof Headers&&(0,$.V9)(l,Headers)){const e=new Headers(l);return e.append("sentry-trace",p),g&&e.append(I.bU,g),e}if(Array.isArray(l)){const e=[...l,["sentry-trace",p]];return g&&e.push([I.bU,g]),e}{const e="baggage"in l?l.baggage:void 0,t=[];return Array.isArray(e)?t.push(...e):e&&t.push(e),g&&t.push(g),{...l,"sentry-trace":p,baggage:t.length>0?t.join(","):void 0}}}return{"sentry-trace":p,baggage:g}}(t,c,o,n,_)}return _}(e,c,d,u);o&&t&&A(t)})),n&&(0,q.oq)("xhr",(e=>{const t=function(e,t,n,r){const a=e.xhr,i=a&&a[q.xU];if(!(0,L.z)()||a&&a.__sentry_own_request__||!a||!i)return;const o=t(i.url);if(e.endTimestamp&&o){const e=a.__sentry_xhr_span_id__;if(!e)return;const t=r[e];return void(t&&(t.setHttpStatus(i.status_code),t.finish(),delete r[e]))}const c=(0,x.Gd)(),d=c.getScope(),u=d.getSpan(),p=o&&u?u.startChild({data:{...i.data,type:"xhr","http.method":i.method,url:i.url},description:`${i.method} ${i.url}`,op:"http.client",origin:"auto.http.browser"}):void 0;p&&(a.__sentry_xhr_span_id__=p.spanId,r[a.__sentry_xhr_span_id__]=p);if(a.setRequestHeader&&n(i.url))if(p){const e=p&&p.transaction,t=e&&e.getDynamicSamplingContext(),n=(0,I.IQ)(t);Z(a,p.toTraceparent(),n)}else{const e=c.getClient(),{traceId:t,sampled:n,dsc:r}=d.getPropagationContext(),i=(0,s.$p)(t,void 0,n),o=r||(e?(0,P._)(t,e,d):void 0);Z(a,i,(0,I.IQ)(o))}return p}(e,c,d,u);o&&t&&A(t)}))}function A(e){const t=e.data.url,n=new PerformanceObserver((r=>{r.getEntries().forEach((r=>{if(function(e){return"resource"===e.entryType&&"initiatorType"in e&&"string"===typeof e.nextHopProtocol&&("fetch"===e.initiatorType||"xmlhttprequest"===e.initiatorType)}(r)&&r.name.endsWith(t)){(function(e){const{name:t,version:n}=function(e){let t="unknown",n="unknown",r="";for(const a of e){if("/"===a){[t,n]=e.split("/");break}if(!isNaN(Number(a))){t="h"===r?"http":r,n=e.split(r)[1];break}r+=a}r===e&&(t=r);return{name:t,version:n}}(e.nextHopProtocol),r=[];if(r.push(["network.protocol.version",n],["network.protocol.name",t]),!u.Z1)return r;return[...r,["http.request.redirect_start",z(e.redirectStart)],["http.request.fetch_start",z(e.fetchStart)],["http.request.domain_lookup_start",z(e.domainLookupStart)],["http.request.domain_lookup_end",z(e.domainLookupEnd)],["http.request.connect_start",z(e.connectStart)],["http.request.secure_connection_start",z(e.secureConnectionStart)],["http.request.connection_end",z(e.connectEnd)],["http.request.request_start",z(e.requestStart)],["http.request.response_start",z(e.responseStart)],["http.request.response_end",z(e.responseEnd)]]})(r).forEach((t=>e.setData(...t))),n.disconnect()}}))}));n.observe({entryTypes:["resource"]})}function z(e=0){return((u.Z1||performance.timeOrigin)+e)/1e3}function Z(e,t,n){try{e.setRequestHeader("sentry-trace",t),n&&e.setRequestHeader(I.bU,n)}catch(r){}}const X={...r.AT,markBackgroundTransactions:!0,routingInstrumentation:function(e,t=!0,n=!0){if(!d||!d.location)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.warn("Could not initialize routing instrumentation due to invalid location"));let r,a=d.location.href;t&&(r=e({name:d.location.pathname,startTimestamp:u.Z1?u.Z1/1e3:void 0,op:"pageload",origin:"auto.pageload.browser",metadata:{source:"url"}})),n&&(0,q.oq)("history",(({to:t,from:n})=>{void 0===n&&a&&-1!==a.indexOf(t)?a=void 0:n!==t&&(a=void 0,r&&(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log(`[Tracing] Finishing current transaction with op: ${r.op}`),r.finish()),r=e({name:d.location.pathname,op:"navigation",origin:"auto.navigation.browser",metadata:{source:"url"}}))}))},startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,enableLongTask:!0,_experiments:{},...O};class V{constructor(e){this.name="BrowserTracing",this._hasSetTracePropagationTargets=!1,(0,a.T)(),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&(this._hasSetTracePropagationTargets=!(!e||!e.tracePropagationTargets&&!e.tracingOrigins)),this.options={...X,...e},void 0!==this.options._experiments.enableLongTask&&(this.options.enableLongTask=this.options._experiments.enableLongTask),e&&!e.tracePropagationTargets&&e.tracingOrigins&&(this.options.tracePropagationTargets=e.tracingOrigins),this._collectWebVitals=G(),this.options.enableLongTask&&m("longtask",(e=>{for(const t of e){const e=(0,i.x1)();if(!e)return;const n=k(u.Z1+t.startTime),r=k(t.duration);e.startChild({description:"Main UI thread blocked",op:"ui.long-task",origin:"auto.ui.browser.metrics",startTimestamp:n,endTimestamp:n+r})}})),this.options._experiments.enableInteractions&&m("event",(e=>{for(const t of e){const e=(0,i.x1)();if(!e)return;if("click"===t.name){const n=k(u.Z1+t.startTime),r=k(t.duration);e.startChild({description:(0,c.Rt)(t.target),op:`ui.interaction.${t.name}`,origin:"auto.ui.browser.metrics",startTimestamp:n,endTimestamp:n+r})}}}),{durationThreshold:0})}setupOnce(e,t){this._getCurrentHub=t;const n=t().getClient(),r=n&&n.getOptions(),{routingInstrumentation:a,startTransactionOnLocationChange:s,startTransactionOnPageLoad:c,markBackgroundTransactions:u,traceFetch:p,traceXHR:_,shouldCreateSpanForRequest:g,enableHTTPTimings:l,_experiments:m}=this.options,f=r&&r.tracePropagationTargets,T=f||this.options.tracePropagationTargets;("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&this._hasSetTracePropagationTargets&&f&&o.kg.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used."),a((e=>{const n=this._createRouteTransaction(e);return this.options._experiments.onStartRouteTransaction&&this.options._experiments.onStartRouteTransaction(n,e,t),n}),c,s),u&&(d&&d.document?d.document.addEventListener("visibilitychange",(()=>{const e=(0,i.x1)();if(d.document.hidden&&e){const t="cancelled";("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log(`[Tracing] Transaction: ${t} -> since tab moved to the background, op: ${e.op}`),e.status||e.setStatus(t),e.setTag("visibilitychange","document.hidden"),e.finish()}})):("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.warn("[Tracing] Could not set up background tab detection due to lack of global document")),m.enableInteractions&&this._registerInteractionListener(),F({traceFetch:p,traceXHR:_,tracePropagationTargets:T,shouldCreateSpanForRequest:g,enableHTTPTimings:l})}_createRouteTransaction(e){if(!this._getCurrentHub)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.warn(`[Tracing] Did not create ${e.op} transaction because _getCurrentHub is invalid.`));const t=this._getCurrentHub(),{beforeNavigate:n,idleTimeout:r,finalTimeout:i,heartbeatInterval:c}=this.options,u="pageload"===e.op,p=u?W("sentry-trace"):"",_=u?W("baggage"):"",{traceparentData:g,dynamicSamplingContext:l,propagationContext:m}=(0,s.KA)(p,_),f={...e,...g,metadata:{...e.metadata,dynamicSamplingContext:g&&!l?{}:l},trimEnd:!0},T="function"===typeof n?n(f):f,h=void 0===T?{...f,sampled:!1}:T;h.metadata=h.name!==f.name?{...h.metadata,source:"custom"}:h.metadata,this._latestRouteName=h.name,this._latestRouteSource=h.metadata&&h.metadata.source,!1===h.sampled&&("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log(`[Tracing] Will not send ${h.op} transaction because of beforeNavigate.`),("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.log(`[Tracing] Starting ${h.op} transaction on scope`);const{location:E}=d,v=(0,a.l)(t,h,r,i,!0,{location:E},c),y=t.getScope();return u&&g?y.setPropagationContext(m):y.setPropagationContext({traceId:v.traceId,spanId:v.spanId,parentSpanId:v.parentSpanId,sampled:v.sampled}),v.registerBeforeFinishCallback((e=>{this._collectWebVitals(),w(e)})),v}_registerInteractionListener(){let e;const t=()=>{const{idleTimeout:t,finalTimeout:n,heartbeatInterval:r}=this.options,s="ui.action.click",c=(0,i.x1)();if(c&&c.op&&["navigation","pageload"].includes(c.op))return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.warn(`[Tracing] Did not create ${s} transaction because a pageload or navigation transaction is in progress.`));if(e&&(e.setFinishReason("interactionInterrupted"),e.finish(),e=void 0),!this._getCurrentHub)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.warn(`[Tracing] Did not create ${s} transaction because _getCurrentHub is invalid.`));if(!this._latestRouteName)return void(("undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&o.kg.warn(`[Tracing] Did not create ${s} transaction because _latestRouteName is missing.`));const u=this._getCurrentHub(),{location:p}=d,_={name:this._latestRouteName,op:s,trimEnd:!0,metadata:{source:this._latestRouteSource||"url"}};e=(0,a.l)(u,_,t,n,!0,{location:p},r)};["click"].forEach((e=>{addEventListener(e,t,{once:!1,capture:!0})}))}}function W(e){const t=(0,c.qT)(`meta[name=${e}]`);return t?t.getAttribute("content"):void 0}}}]);
//# sourceMappingURL=@sentry-internal.280e351f35ac51373836d824e9d00c67.js.map