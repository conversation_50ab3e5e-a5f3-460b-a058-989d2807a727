"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@sentry"],{50198:function(t,e,n){var r=n(338),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function c(t){return r.isMemo(t)?i:a[t.$$typeof]||o}a[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[r.Memo]=i;var u=Object.defineProperty,p=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,l=Object.getPrototypeOf,h=Object.prototype;t.exports=function t(e,n,r){if("string"!==typeof n){if(h){var o=l(n);o&&o!==h&&t(e,o,r)}var i=p(n);d&&(i=i.concat(d(n)));for(var a=c(e),m=c(n),g=0;g<i.length;++g){var _=i[g];if(!s[_]&&(!r||!r[_])&&(!m||!m[_])&&(!a||!a[_])){var v=f(n,_);try{u(e,_,v)}catch(y){}}}}return e}},59802:function(t,e,n){n.d(e,{X:function(){return r}});const r="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__},42917:function(t,e,n){n.d(e,{m9:function(){return c},Wz:function(){return p},re:function(){return f}});var r=n(50087),o=n(35506),s=n(55793),i=n(88545),a=n(34100);const c=s.n;let u=0;function p(){return u>0}function d(){u++,setTimeout((()=>{u--}))}function f(t,e={},n){if("function"!==typeof t)return t;try{const e=t.__sentry_wrapped__;if(e)return e;if((0,i.HK)(t))return t}catch(c){return t}const s=function(){const s=Array.prototype.slice.call(arguments);try{n&&"function"===typeof n&&n.apply(this,arguments);const r=s.map((t=>f(t,e)));return t.apply(this,r)}catch(i){throw d(),(0,r.$e)((t=>{t.addEventProcessor((t=>(e.mechanism&&((0,a.Db)(t,void 0,void 0),(0,a.EG)(t,e.mechanism)),t.extra={...t.extra,arguments:s},t))),(0,o.Tb)(i)})),i}};try{for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(s[e]=t[e])}catch(u){}(0,i.$Q)(s,t),(0,i.xp)(t,"__sentry_wrapped__",s);try{Object.getOwnPropertyDescriptor(s,"name").configurable&&Object.defineProperty(s,"name",{get:()=>t.name})}catch(u){}return s}},41856:function(t,e,n){n.d(e,{S1:function(){return Bt},jp:function(){return Kt}});var r=n(46616),o=n(34100),s=n(50712),i=n(33994);const a=[];function c(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;e.forEach((t=>{t.isDefaultInstance=!0})),r=Array.isArray(n)?[...e,...n]:"function"===typeof n?(0,o.lE)(n(e)):e;const s=function(t){const e={};return t.forEach((t=>{const{name:n}=t,r=e[n];r&&!r.isDefaultInstance&&t.isDefaultInstance||(e[n]=t)})),Object.keys(e).map((t=>e[t]))}(r),i=function(t,e){for(let n=0;n<t.length;n++)if(!0===e(t[n]))return n;return-1}(s,(t=>"Debug"===t.name));if(-1!==i){const[t]=s.splice(i,1);s.push(t)}return s}function u(t,e){for(const n of e)n&&n.afterAllSetup&&n.afterAllSetup(t)}function p(t,e,n){if(n[e.name])i.X&&r.kg.log(`Integration skipped because it was already installed: ${e.name}`);else{if(n[e.name]=e,-1===a.indexOf(e.name)&&"function"===typeof e.setupOnce&&(e.setupOnce(),a.push(e.name)),e.setup&&"function"===typeof e.setup&&e.setup(t),"function"===typeof e.preprocessEvent){const n=e.preprocessEvent.bind(e);t.on("preprocessEvent",((e,r)=>n(e,r,t)))}if("function"===typeof e.processEvent){const n=e.processEvent.bind(e),r=Object.assign(((e,r)=>n(e,r,t)),{id:e.name});t.addEventProcessor(r)}i.X&&r.kg.log(`Integration installed: ${e.name}`)}}const d=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/],f=(t={})=>({name:"InboundFilters",processEvent(e,n,a){const c=a.getOptions(),u=function(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:d],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(t,c);return function(t,e){if(e.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(e){}return!1}(t))return i.X&&r.kg.warn(`Event dropped due to being internal Sentry Error.\nEvent: ${(0,o.jH)(t)}`),!0;if(function(t,e){if(t.type||!e||!e.length)return!1;return function(t){const e=[];t.message&&e.push(t.message);let n;try{n=t.exception.values[t.exception.values.length-1]}catch(r){}n&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`));return e}(t).some((t=>(0,s.U0)(t,e)))}(t,e.ignoreErrors))return i.X&&r.kg.warn(`Event dropped due to being matched by \`ignoreErrors\` option.\nEvent: ${(0,o.jH)(t)}`),!0;if(function(t,e){if("transaction"!==t.type||!e||!e.length)return!1;const n=t.transaction;return!!n&&(0,s.U0)(n,e)}(t,e.ignoreTransactions))return i.X&&r.kg.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.\nEvent: ${(0,o.jH)(t)}`),!0;if(function(t,e){if(!e||!e.length)return!1;const n=l(t);return!!n&&(0,s.U0)(n,e)}(t,e.denyUrls))return i.X&&r.kg.warn(`Event dropped due to being matched by \`denyUrls\` option.\nEvent: ${(0,o.jH)(t)}.\nUrl: ${l(t)}`),!0;if(!function(t,e){if(!e||!e.length)return!0;const n=l(t);return!n||(0,s.U0)(n,e)}(t,e.allowUrls))return i.X&&r.kg.warn(`Event dropped due to not being matched by \`allowUrls\` option.\nEvent: ${(0,o.jH)(t)}.\nUrl: ${l(t)}`),!0;return!1}(e,u)?null:e}});function l(t){try{let n;try{n=t.exception.values[0].stacktrace.frames}catch(e){}return n?function(t=[]){for(let e=t.length-1;e>=0;e--){const n=t[e];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(n):null}catch(n){return i.X&&r.kg.error(`Cannot extract url for event ${(0,o.jH)(t)}`),null}}var h=n(88545),m=n(50087);let g;const _=new WeakMap,v=()=>({name:"FunctionToString",setupOnce(){g=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=(0,h.HK)(this),n=_.has((0,m.s3)())&&void 0!==e?e:this;return g.apply(n,t)}}catch(t){}},setup(t){_.set(t,!0)}}),y=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(t,e){if(!e)return!1;if(function(t,e){const n=t.message,r=e.message;if(!n&&!r)return!1;if(n&&!r||!n&&r)return!1;if(n!==r)return!1;if(!S(t,e))return!1;if(!b(t,e))return!1;return!0}(t,e))return!0;if(function(t,e){const n=E(e),r=E(t);if(!n||!r)return!1;if(n.type!==r.type||n.value!==r.value)return!1;if(!S(t,e))return!1;if(!b(t,e))return!1;return!0}(t,e))return!0;return!1}(e,t))return i.X&&r.kg.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(n){}return t=e}}};function b(t,e){let n=k(t),r=k(e);if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;if(n=n,r=r,r.length!==n.length)return!1;for(let o=0;o<r.length;o++){const t=r[o],e=n[o];if(t.filename!==e.filename||t.lineno!==e.lineno||t.colno!==e.colno||t.function!==e.function)return!1}return!0}function S(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;n=n,r=r;try{return!(n.join("")!==r.join(""))}catch(o){return!1}}function E(t){return t.exception&&t.exception.values&&t.exception.values[0]}function k(t){const e=t.exception;if(e)try{return e.values[0].stacktrace.frames}catch(n){return}}var x=n(97992);function w(t,e){!0===e.debug&&(i.X?r.kg.enable():(0,r.Cf)((()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})));(0,m.nZ)().update(e.initialScope);const n=new t(e);!function(t){(0,m.nZ)().setClient(t),function(t){const e=(0,x.q)((0,x.c)());e.hub&&"function"===typeof e.hub.getStackTop&&(e.hub.getStackTop().client=t)}(t)}(n),n.init()}var T=n(86630);function D(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function $(t,e,n){return e||`${function(t){return`${D(t)}${t.projectId}/envelope/`}(t)}?${function(t,e){return(0,h._j)({sentry_key:t.publicKey,sentry_version:"7",...e&&{sentry_client:`${e.name}/${e.version}`}})}(t,n)}`}var I=n(35506),O=n(38794),C=n(26930),j=n(52472),R=n(47701),N=n(10300),P=n(68571);class A extends Error{constructor(t,e="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=e}}var M=n(11540),U=n(7933),L=n(349),X=n(18057),H=n(43130);const F="Not capturing exception because it's already been captured.";function q(t){return void 0===t.type}function J(t){return"transaction"===t.type}var G=n(70829),W=n(9896),Y=n(22519);var Z=n(59802),z=n(2615);function V(t,e){const n=Q(t,e),r={type:e&&e.name,value:et(e)};return n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function B(t,e,n,r){const o=(0,m.s3)(),s=o&&o.getOptions().normalizeDepth,i=function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const n=t[e];if(n instanceof Error)return n}return}(e),a={__serialized__:(0,z.Qy)(e,s)};if(i)return{exception:{values:[V(t,i)]},extra:a};const c={exception:{values:[{type:(0,R.cO)(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:ot(e,{isUnhandledRejection:r})}]},extra:a};if(n){const e=Q(t,n);e.length&&(c.exception.values[0].stacktrace={frames:e})}return c}function K(t,e){return{exception:{values:[V(t,e)]}}}function Q(t,e){const n=e.stacktrace||e.stack||"",r=function(t){if(t&&tt.test(t.message))return 1;return 0}(e),o=function(t){if("number"===typeof t.framesToPop)return t.framesToPop;return 0}(e);try{return t(n,r,o)}catch(s){}return[]}const tt=/Minified React error #\d+;/i;function et(t){const e=t&&t.message;return e?e.error&&"string"===typeof e.error.message?e.error.message:e:"No error message"}function nt(t,e,n,r,s){let i;if((0,R.VW)(e)&&e.error){return K(t,e.error)}if((0,R.TX)(e)||(0,R.fm)(e)){const s=e;if("stack"in e)i=K(t,e);else{const e=s.name||((0,R.TX)(s)?"DOMError":"DOMException"),a=s.message?`${e}: ${s.message}`:e;i=rt(t,a,n,r),(0,o.Db)(i,a)}return"code"in s&&(i.tags={...i.tags,"DOMException.code":`${s.code}`}),i}if((0,R.VZ)(e))return K(t,e);if((0,R.PO)(e)||(0,R.cO)(e)){return i=B(t,e,n,s),(0,o.EG)(i,{synthetic:!0}),i}return i=rt(t,e,n,r),(0,o.Db)(i,`${e}`,void 0),(0,o.EG)(i,{synthetic:!0}),i}function rt(t,e,n,r){const o={};if(r&&n){const r=Q(t,n);r.length&&(o.exception={values:[{value:e,stacktrace:{frames:r}}]})}if((0,R.Le)(e)){const{__sentry_template_string__:t,__sentry_template_values__:n}=e;return o.logentry={message:t,params:n},o}return o.message=e,o}function ot(t,{isUnhandledRejection:e}){const n=(0,h.zf)(t),r=e?"promise rejection":"exception";if((0,R.VW)(t))return`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``;if((0,R.cO)(t)){return`Event \`${function(t){try{const e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch(e){}}(t)}\` (type=${t.type}) captured as ${r}`}return`Object captured as ${r} with keys: ${n}`}var st=n(42917);class it extends class{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=(0,T.vK)(t.dsn):i.X&&r.kg.warn("No DSN provided, client will not send events."),this._dsn){const e=$(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:e})}}captureException(t,e,n){const s=(0,o.DM)();if((0,o.YO)(t))return i.X&&r.kg.log(F),s;const a={event_id:s,...e};return this._process(this.eventFromException(t,a).then((t=>this._captureEvent(t,a,n)))),a.event_id}captureMessage(t,e,n,r){const s={event_id:(0,o.DM)(),...n},i=(0,R.Le)(t)?t:String(t),a=(0,R.pt)(t)?this.eventFromMessage(i,e,s):this.eventFromException(t,s);return this._process(a.then((t=>this._captureEvent(t,s,r)))),s.event_id}captureEvent(t,e,n){const s=(0,o.DM)();if(e&&e.originalException&&(0,o.YO)(e.originalException))return i.X&&r.kg.log(F),s;const a={event_id:s,...e},c=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,a,c||n)),a.event_id}captureSession(t){"string"!==typeof t.release?i.X&&r.kg.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),(0,U.CT)(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const e=this._transport;return e?(this.emit("flush"),this._isClientDoneProcessing(t).then((n=>e.flush(t).then((t=>n&&t))))):(0,N.WD)(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,this.emit("close"),t)))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){this._isEnabled()&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const e=this._integrations[t.name];p(this,t,this._integrations),e||u(this,[t])}sendEvent(t,e={}){this.emit("beforeSendEvent",t,e);let n=(0,M.Mq)(t,this._dsn,this._options._metadata,this._options.tunnel);for(const o of e.attachments||[])n=(0,P.BO)(n,(0,P.zQ)(o));const r=this.sendEnvelope(n);r&&r.then((e=>this.emit("afterSendEvent",t,e)),null)}sendSession(t){const e=(0,M.Q3)(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(e)}recordDroppedEvent(t,e,n){if(this._options.sendClientReports){const n=`${t}:${e}`;i.X&&r.kg.log(`Adding outcome: "${n}"`),this._outcomes[n]=this._outcomes[n]+1||1}}on(t,e){this._hooks[t]||(this._hooks[t]=[]),this._hooks[t].push(e)}emit(t,...e){this._hooks[t]&&this._hooks[t].forEach((t=>t(...e)))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,(t=>(i.X&&r.kg.error("Error while sending event:",t),t))):(i.X&&r.kg.error("Transport disabled"),(0,N.WD)({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=function(t,e){const n={};return e.forEach((e=>{e&&p(t,e,n)})),n}(this,t),u(this,t)}_updateSessionFromEvent(t,e){let n=!1,r=!1;const o=e.exception&&e.exception.values;if(o){r=!0;for(const t of o){const e=t.mechanism;if(e&&!1===e.handled){n=!0;break}}}const s="ok"===t.status;(s&&0===t.errors||s&&n)&&((0,U.CT)(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new N.cW((e=>{let n=0;const r=setInterval((()=>{0==this._numProcessing?(clearInterval(r),e(!0)):(n+=1,t&&n>=t&&(clearInterval(r),e(!1)))}),1)}))}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(t,e,n,r=(0,m.aF)()){const o=this.getOptions(),s=Object.keys(this._integrations);return!e.integrations&&s.length>0&&(e.integrations=s),this.emit("preprocessEvent",t,e),t.type||r.setLastEventId(t.event_id||e.event_id),(0,H.R)(o,t,e,n,this,r).then((t=>{if(null===t)return t;const e={...r.getPropagationContext(),...n?n.getPropagationContext():void 0};if(!(t.contexts&&t.contexts.trace)&&e){const{traceId:n,spanId:r,parentSpanId:o,dsc:s}=e;t.contexts={trace:(0,h.Jr)({trace_id:n,span_id:r,parent_span_id:o}),...t.contexts};const i=s||(0,L._l)(n,this);t.sdkProcessingMetadata={dynamicSamplingContext:i,...t.sdkProcessingMetadata}}return t}))}_captureEvent(t,e={},n){return this._processEvent(t,e,n).then((t=>t.event_id),(t=>{if(i.X){const e=t;"log"===e.logLevel?r.kg.log(e.message):r.kg.warn(e)}}))}_processEvent(t,e,n){const r=this.getOptions(),{sampleRate:o}=r,s=J(t),i=q(t),a=t.type||"error",c=`before send for type \`${a}\``,u="undefined"===typeof o?void 0:(0,X.o)(o);if(i&&"number"===typeof u&&Math.random()>u)return this.recordDroppedEvent("sample_rate","error",t),(0,N.$2)(new A(`Discarding event because it's not included in the random sample (sampling rate = ${o})`,"log"));const p="replay_event"===a?"replay":a,d=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,e,n,d).then((n=>{if(null===n)throw this.recordDroppedEvent("event_processor",p,t),new A("An event processor returned `null`, will not send event.","log");if(e.data&&!0===e.data.__sentry__)return n;const o=function(t,e,n){const{beforeSend:r,beforeSendTransaction:o}=t;if(q(e)&&r)return r(e,n);if(J(e)&&o)return o(e,n);return e}(r,n,e);return function(t,e){const n=`${e} must return \`null\` or a valid event.`;if((0,R.J8)(t))return t.then((t=>{if(!(0,R.PO)(t)&&null!==t)throw new A(n);return t}),(t=>{throw new A(`${e} rejected with ${t}`)}));if(!(0,R.PO)(t)&&null!==t)throw new A(n);return t}(o,c)})).then((r=>{if(null===r)throw this.recordDroppedEvent("before_send",p,t),new A(`${c} returned \`null\`, will not send event.`,"log");const o=n&&n.getSession();!s&&o&&this._updateSessionFromEvent(o,r);const i=r.transaction_info;if(s&&i&&r.transaction!==t.transaction){const t="custom";r.transaction_info={...i,source:t}}return this.sendEvent(r,e),r})).then(null,(t=>{if(t instanceof A)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new A(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}_process(t){this._numProcessing++,t.then((t=>(this._numProcessing--,t)),(t=>(this._numProcessing--,t)))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.keys(t).map((e=>{const[n,r]=e.split(":");return{reason:n,category:r,quantity:t[e]}}))}}{constructor(t){const e={parentSpanIsAlwaysRootSpan:!0,...t},n=st.m9.SENTRY_SDK_SOURCE||(0,W.S)();(0,G.V)(e,"browser",["browser"],n),super(e),e.sendClientReports&&st.m9.document&&st.m9.document.addEventListener("visibilitychange",(()=>{"hidden"===st.m9.document.visibilityState&&this._flushOutcomes()}))}eventFromException(t,e){return function(t,e,n,r){const s=nt(t,e,n&&n.syntheticException||void 0,r);return(0,o.EG)(s),s.level="error",n&&n.event_id&&(s.event_id=n.event_id),(0,N.WD)(s)}(this._options.stackParser,t,e,this._options.attachStacktrace)}eventFromMessage(t,e="info",n){return function(t,e,n="info",r,o){const s=rt(t,e,r&&r.syntheticException||void 0,o);return s.level=n,r&&r.event_id&&(s.event_id=r.event_id),(0,N.WD)(s)}(this._options.stackParser,t,e,n,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled())return void(Z.X&&r.kg.warn("SDK not enabled, will not capture user feedback."));const e=function(t,{metadata:e,tunnel:n,dsn:r}){const o={event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&e.sdk&&{sdk:{name:e.sdk.name,version:e.sdk.version}},...!!n&&!!r&&{dsn:(0,T.RA)(r)}},s=function(t){return[{type:"user_report"},t]}(t);return(0,P.Jd)(o,[s])}(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(e)}_prepareEvent(t,e,n){return t.platform=t.platform||"javascript",super._prepareEvent(t,e,n)}_flushOutcomes(){const t=this._clearOutcomes();if(0===t.length)return void(Z.X&&r.kg.log("No outcomes to send"));if(!this._dsn)return void(Z.X&&r.kg.log("No dsn provided, will not send outcomes"));Z.X&&r.kg.log("Sending outcomes:",t);const e=function(t,e,n){const r=[{type:"client_report"},{timestamp:n||(0,Y.yW)(),discarded_events:t}];return(0,P.Jd)(e?{dsn:e}:{},[r])}(t,this._options.tunnel&&(0,T.RA)(this._dsn));this.sendEnvelope(e)}}var at=n(43218),ct=n(41048),ut=n(34124),pt=n(55793),dt=n(63224);function ft(){"console"in pt.n&&r.RU.forEach((function(t){t in pt.n.console&&(0,h.hl)(pt.n.console,t,(function(e){return r.LD[t]=e,function(...e){const n={args:e,level:t};(0,dt.rK)("console",n);const o=r.LD[t];o&&o.apply(pt.n.console,e)}}))}))}var lt=n(67567),ht=n(28169);const mt=["fatal","error","warning","log","info","debug"];function gt(t){return"warn"===t?"warning":mt.includes(t)?t:"log"}var _t=n(486);const vt=1024,yt=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(t){e.console&&function(t){const e="console";(0,dt.Hj)(e,t),(0,dt.D2)(e,ft)}(function(t){return function(e){if((0,m.s3)()!==t)return;const n={category:"console",data:{arguments:e.args,logger:"console"},level:gt(e.level),message:(0,s.nK)(e.args," ")};if("assert"===e.level){if(!1!==e.args[0])return;n.message=`Assertion failed: ${(0,s.nK)(e.args.slice(1)," ")||"console.assert"}`,n.data.arguments=e.args.slice(1)}(0,ut.n)(n,{input:e.args,level:e.level})}}(t)),e.dom&&(0,at.O)(function(t,e){return function(n){if((0,m.s3)()!==t)return;let o,s,i="object"===typeof e?e.serializeAttribute:void 0,a="object"===typeof e&&"number"===typeof e.maxStringLength?e.maxStringLength:void 0;a&&a>vt&&(Z.X&&r.kg.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${a} was configured. Sentry will use 1024 instead.`),a=vt),"string"===typeof i&&(i=[i]);try{const t=n.event,e=function(t){return!!t&&!!t.target}(t)?t.target:t;o=(0,ht.Rt)(e,{keyAttrs:i,maxStringLength:a}),s=(0,ht.iY)(e)}catch(u){o="<unknown>"}if(0===o.length)return;const c={category:`ui.${n.name}`,message:o};s&&(c.data={"ui.component_name":s}),(0,ut.n)(c,{event:n.event,name:n.name,global:n.global})}}(t,e.dom)),e.xhr&&(0,ct.UK)(function(t){return function(e){if((0,m.s3)()!==t)return;const{startTimestamp:n,endTimestamp:r}=e,o=e.xhr[ct.xU];if(!n||!r||!o)return;const{method:s,url:i,status_code:a,body:c}=o,u={method:s,url:i,status_code:a},p={xhr:e.xhr,input:c,startTimestamp:n,endTimestamp:r};(0,ut.n)({category:"xhr",data:u,type:"http"},p)}}(t)),e.fetch&&(0,lt.U)(function(t){return function(e){if((0,m.s3)()!==t)return;const{startTimestamp:n,endTimestamp:r}=e;if(r&&(!e.fetchData.url.match(/sentry_key/)||"POST"!==e.fetchData.method))if(e.error){const t=e.fetchData,o={data:e.error,input:e.args,startTimestamp:n,endTimestamp:r};(0,ut.n)({category:"fetch",data:t,level:"error",type:"http"},o)}else{const t=e.response,o={...e.fetchData,status_code:t&&t.status},s={input:e.args,response:t,startTimestamp:n,endTimestamp:r};(0,ut.n)({category:"fetch",data:o,type:"http"},s)}}}(t)),e.history&&(0,j.a)(function(t){return function(e){if((0,m.s3)()!==t)return;let n=e.from,r=e.to;const o=(0,_t.en)(st.m9.location.href);let s=n?(0,_t.en)(n):void 0;const i=(0,_t.en)(r);s&&s.path||(s=o),o.protocol===i.protocol&&o.host===i.host&&(r=i.relative),o.protocol===s.protocol&&o.host===s.host&&(n=s.relative),(0,ut.n)({category:"navigation",data:{from:n,to:r}})}}(t)),e.sentry&&t.on("beforeSendEvent",function(t){return function(e){(0,m.s3)()===t&&(0,ut.n)({category:"sentry."+("transaction"===e.type?"transaction":"event"),event_id:e.event_id,level:e.level,message:(0,o.jH)(e)},{event:e})}}(t))}}};const bt=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],St=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&(0,h.hl)(st.m9,"setTimeout",Et),e.setInterval&&(0,h.hl)(st.m9,"setInterval",Et),e.requestAnimationFrame&&(0,h.hl)(st.m9,"requestAnimationFrame",kt),e.XMLHttpRequest&&"XMLHttpRequest"in st.m9&&(0,h.hl)(XMLHttpRequest.prototype,"send",xt);const t=e.eventTarget;if(t){(Array.isArray(t)?t:bt).forEach(wt)}}}};function Et(t){return function(...e){const n=e[0];return e[0]=(0,st.re)(n,{mechanism:{data:{function:(0,C.$P)(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function kt(t){return function(e){return t.apply(this,[(0,st.re)(e,{mechanism:{data:{function:"requestAnimationFrame",handler:(0,C.$P)(t)},handled:!1,type:"instrument"}})])}}function xt(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in n&&"function"===typeof n[t]&&(0,h.hl)(n,t,(function(e){const n={mechanism:{data:{function:t,handler:(0,C.$P)(e)},handled:!1,type:"instrument"}},r=(0,h.HK)(e);return r&&(n.mechanism.data.handler=(0,C.$P)(r)),(0,st.re)(e,n)}))})),t.apply(this,e)}}function wt(t){const e=st.m9,n=e[t]&&e[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&((0,h.hl)(n,"addEventListener",(function(e){return function(n,r,o){try{"function"===typeof r.handleEvent&&(r.handleEvent=(0,st.re)(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:(0,C.$P)(r),target:t},handled:!1,type:"instrument"}}))}catch(s){}return e.apply(this,[n,(0,st.re)(r,{mechanism:{data:{function:"addEventListener",handler:(0,C.$P)(r),target:t},handled:!1,type:"instrument"}}),o])}})),(0,h.hl)(n,"removeEventListener",(function(t){return function(e,n,r){const o=n;try{const n=o&&o.__sentry_wrapped__;n&&t.call(this,e,n,r)}catch(s){}return t.call(this,e,o,r)}})))}var Tt=n(98522),Dt=n(51176);const $t=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(t){e.onerror&&(!function(t){(0,Tt.V)((e=>{const{stackParser:n,attachStacktrace:r}=Ot();if((0,m.s3)()!==t||(0,st.Wz)())return;const{msg:o,url:s,line:i,column:a,error:c}=e,u=function(t,e,n,r){const o=t.exception=t.exception||{},s=o.values=o.values||[],i=s[0]=s[0]||{},a=i.stacktrace=i.stacktrace||{},c=a.frames=a.frames||[],u=isNaN(parseInt(r,10))?void 0:r,p=isNaN(parseInt(n,10))?void 0:n,d=(0,R.HD)(e)&&e.length>0?e:(0,ht.l4)();0===c.length&&c.push({colno:u,filename:d,function:C.Fi,in_app:!0,lineno:p});return t}(nt(n,c||o,void 0,r,!1),s,i,a);u.level="error",(0,I.eN)(u,{originalException:c,mechanism:{handled:!1,type:"onerror"}})}))}(t),It("onerror")),e.onunhandledrejection&&(!function(t){(0,Dt.h)((e=>{const{stackParser:n,attachStacktrace:r}=Ot();if((0,m.s3)()!==t||(0,st.Wz)())return;const o=function(t){if((0,R.pt)(t))return t;try{if("reason"in t)return t.reason;if("detail"in t&&"reason"in t.detail)return t.detail.reason}catch(e){}return t}(e),s=(0,R.pt)(o)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(o)}`}]}}:nt(n,o,void 0,r,!0);s.level="error",(0,I.eN)(s,{originalException:o,mechanism:{handled:!1,type:"onunhandledrejection"}})}))}(t),It("onunhandledrejection"))}}};function It(t){Z.X&&r.kg.log(`Global Handler attached: ${t}`)}function Ot(){const t=(0,m.s3)();return t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const Ct=()=>({name:"HttpContext",preprocessEvent(t){if(!st.m9.navigator&&!st.m9.location&&!st.m9.document)return;const e=t.request&&t.request.url||st.m9.location&&st.m9.location.href,{referrer:n}=st.m9.document||{},{userAgent:r}=st.m9.navigator||{},o={...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},s={...t.request,...e&&{url:e},headers:o};t.request=s}});function jt(t,e,n=250,r,o,i,a){if(!i.exception||!i.exception.values||!a||!(0,R.V9)(a.originalException,Error))return;const c=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;var u,p;c&&(i.exception.values=(u=Rt(t,e,o,a.originalException,r,i.exception.values,c,0),p=n,u.map((t=>(t.value&&(t.value=(0,s.$G)(t.value,p)),t)))))}function Rt(t,e,n,r,o,s,i,a){if(s.length>=n+1)return s;let c=[...s];if((0,R.V9)(r[o],Error)){Nt(i,a);const s=t(e,r[o]),u=c.length;Pt(s,o,u,a),c=Rt(t,e,n,r[o],o,[s,...c],s,u)}return Array.isArray(r.errors)&&r.errors.forEach(((r,s)=>{if((0,R.V9)(r,Error)){Nt(i,a);const u=t(e,r),p=c.length;Pt(u,`errors[${s}]`,p,a),c=Rt(t,e,n,r,o,[u,...c],u,p)}})),c}function Nt(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,..."AggregateError"===t.type&&{is_exception_group:!0},exception_id:e}}function Pt(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}const At=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(t,r,o){const s=o.getOptions();jt(V,s.stackParser,s.maxValueLength,n,e,t,r)}}};function Mt(t,e,n,r){const o={filename:t,function:"<anonymous>"===e?C.Fi:e,in_app:!0};return void 0!==n&&(o.lineno=n),void 0!==r&&(o.colno=r),o}const Ut=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Lt=/\((\S*)(?::(\d+))(?::(\d+))\)/,Xt=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Ht=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Ft=[[30,t=>{const e=Ut.exec(t);if(e){if(e[2]&&0===e[2].indexOf("eval")){const t=Lt.exec(e[2]);t&&(e[2]=t[1],e[3]=t[2],e[4]=t[3])}const[t,n]=Jt(e[1]||C.Fi,e[2]);return Mt(n,t,e[3]?+e[3]:void 0,e[4]?+e[4]:void 0)}}],[50,t=>{const e=Xt.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const t=Ht.exec(e[3]);t&&(e[1]=e[1]||"eval",e[3]=t[1],e[4]=t[2],e[5]="")}let t=e[3],n=e[1]||C.Fi;return[n,t]=Jt(n,t),Mt(t,n,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]],qt=(0,C.pE)(...Ft),Jt=(t,e)=>{const n=-1!==t.indexOf("safari-extension"),r=-1!==t.indexOf("safari-web-extension");return n||r?[-1!==t.indexOf("@")?t.split("@")[0]:C.Fi,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]};function Gt(t){const e=[];function n(t){return e.splice(e.indexOf(t),1)[0]}return{$:e,add:function(r){if(!(void 0===t||e.length<t))return(0,N.$2)(new A("Not adding Promise because buffer limit was reached."));const o=r();return-1===e.indexOf(o)&&e.push(o),o.then((()=>n(o))).then(null,(()=>n(o).then(null,(()=>{})))),o},drain:function(t){return new N.cW(((n,r)=>{let o=e.length;if(!o)return n(!0);const s=setTimeout((()=>{t&&t>0&&n(!1)}),t);e.forEach((t=>{(0,N.WD)(t).then((()=>{--o||(clearTimeout(s),n(!0))}),r)}))}))}}}var Wt=n(29796);function Yt(t,e){if("event"===e||"transaction"===e)return Array.isArray(t)?t[1]:void 0}let Zt;function zt(){Zt=void 0}function Vt(t,e=function(){if(Zt)return Zt;if((0,O.Du)(st.m9.fetch))return Zt=st.m9.fetch.bind(st.m9);const t=st.m9.document;let e=st.m9.fetch;if(t&&"function"===typeof t.createElement)try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n);const r=n.contentWindow;r&&r.fetch&&(e=r.fetch),t.head.removeChild(n)}catch(n){Z.X&&r.kg.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}try{return Zt=e.bind(st.m9)}catch(n){}}()){let n=0,o=0;return function(t,e,n=Gt(t.bufferSize||64)){let o={};return{send:function(s){const a=[];if((0,P.gv)(s,((e,n)=>{const r=(0,P.mL)(n);if((0,Wt.Q)(o,r)){const o=Yt(e,n);t.recordDroppedEvent("ratelimit_backoff",r,o)}else a.push(e)})),0===a.length)return(0,N.WD)({});const c=(0,P.Jd)(s[0],a),u=e=>{(0,P.gv)(c,((n,r)=>{const o=Yt(n,r);t.recordDroppedEvent(e,(0,P.mL)(r),o)}))};return n.add((()=>e({body:(0,P.V$)(c)}).then((t=>(void 0!==t.statusCode&&(t.statusCode<200||t.statusCode>=300)&&i.X&&r.kg.warn(`Sentry responded with status code ${t.statusCode} to sent event.`),o=(0,Wt.WG)(o,t),t)),(t=>{throw u("network_error"),t})))).then((t=>t),(t=>{if(t instanceof A)return i.X&&r.kg.error("Skipped sending event because buffer is full."),u("queue_overflow"),(0,N.WD)({});throw t}))},flush:t=>n.drain(t)}}(t,(function(r){const s=r.body.length;n+=s,o++;const i={body:r.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:n<=6e4&&o<15,...t.fetchOptions};if(!e)return zt(),(0,N.$2)("No fetch implementation available");try{return e(t.url,i).then((t=>(n-=s,o--,{statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}catch(a){return zt(),n-=s,o--,(0,N.$2)(a)}}))}function Bt(t={}){const e=function(t={}){return{defaultIntegrations:[f(),v(),St(),yt(),$t(),At(),y(),Ct()],release:"string"===typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:st.m9.SENTRY_RELEASE&&st.m9.SENTRY_RELEASE.id?st.m9.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0,...t}}(t);if(function(){const t=st.m9,e=t&&t.chrome&&t.chrome.runtime&&t.chrome.runtime.id,n=st.m9;return!!(n&&n.browser&&n.browser.runtime&&n.browser.runtime.id)||!!e}())return void(0,r.Cf)((()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));Z.X&&((0,O.Ak)()||r.kg.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...e,stackParser:(0,C.Sq)(e.stackParser||qt),integrations:c(e),transport:e.transport||Vt};w(it,n),e.autoSessionTracking&&function(){if("undefined"===typeof st.m9.document)return void(Z.X&&r.kg.warn("Session tracking in non-browser environment with @sentry/browser is not supported."));(0,I.yj)({ignoreDuration:!0}),(0,I.cg)(),(0,j.a)((({from:t,to:e})=>{void 0!==t&&t!==e&&((0,I.yj)({ignoreDuration:!0}),(0,I.cg)())}))}()}function Kt(t={}){if(!st.m9.document)return void(Z.X&&r.kg.error("Global document not defined in showReportDialog call"));const e=(0,m.nZ)(),n=e.getClient(),o=n&&n.getDsn();if(!o)return void(Z.X&&r.kg.error("DSN not configured for showReportDialog call"));if(e&&(t.user={...e.getUser(),...t.user}),!t.eventId){const e=(0,I.eW)();e&&(t.eventId=e)}const s=st.m9.document.createElement("script");s.async=!0,s.crossOrigin="anonymous",s.src=function(t,e){const n=(0,T.vK)(t);if(!n)return"";const r=`${D(n)}embed/error-page/`;let o=`dsn=${(0,T.RA)(n)}`;for(const s in e)if("dsn"!==s&&"onClose"!==s)if("user"===s){const t=e.user;if(!t)continue;t.name&&(o+=`&name=${encodeURIComponent(t.name)}`),t.email&&(o+=`&email=${encodeURIComponent(t.email)}`)}else o+=`&${encodeURIComponent(s)}=${encodeURIComponent(e[s])}`;return`${r}?${o}`}(o,t),t.onLoad&&(s.onload=t.onLoad);const{onClose:i}=t;if(i){const t=e=>{if("__sentry_reportdialog_closed__"===e.data)try{i()}finally{st.m9.removeEventListener("message",t)}};st.m9.addEventListener("message",t)}const a=st.m9.document.head||st.m9.document.body;a?a.appendChild(s):Z.X&&r.kg.error("Not injecting report dialog. No injection point found in HTML")}},40103:function(t,e,n){n.d(e,{E8:function(){return q}});var r=n(48434),o=n(52550),s=n(52472),i=n(22519),a=n(46616),c=n(50087),u=n(33994),p=n(17670),d=n(28772),f=n(34454),l=n(74042),h=n(62439),m=n(78061),g=n(49839);const _={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3};function v(t,e={}){const n=new Map;let r,o=!1,s="externalFinish",v=!e.disableAutoFinish;const{idleTimeout:y=_.idleTimeout,finalTimeout:b=_.finalTimeout,childSpanTimeout:S=_.childSpanTimeout,beforeSpanEnd:E}=e,k=(0,c.s3)();if(!k||!(0,d.z)())return new h.b;const x=(0,c.nZ)(),w=(0,l.HN)(),T=function(t){const e=(0,g.qp)(t);return(0,f.D)((0,c.nZ)(),e),u.X&&a.kg.log("[Tracing] Started span is an idle span"),e}(t);function D(t=(0,i.ph)()){const e=(0,l.Dp)(T).filter((t=>t!==T));if(!e.length)return void T.end(t);const n=e.map((t=>(0,l.XU)(t).timestamp)).filter((t=>!!t)),r=n.length?Math.max(...n):void 0,o=(0,l.$k)(t),s=(0,l.XU)(T).start_timestamp,a=Math.max(s||-1/0,Math.min(o,r||1/0));T.end(a)}function $(){r&&(clearTimeout(r),r=void 0)}function I(t){$(),r=setTimeout((()=>{!o&&0===n.size&&v&&(s="idleTimeout",D(t))}),y)}function O(t){r=setTimeout((()=>{!o&&v&&(s="heartbeatFailed",D(t))}),S)}return k.on("spanStart",(t=>{if(o||t===T||(0,l.XU)(t).timestamp)return;var e;(0,l.Dp)(T).includes(t)&&(e=t.spanContext().spanId,$(),n.set(e,!0),O((0,i.ph)()+S/1e3))})),k.on("spanEnd",(t=>{var e;o||(e=t.spanContext().spanId,n.has(e)&&n.delete(e),0===n.size&&I((0,i.ph)()+y/1e3),t===T&&function(){o=!0,n.clear(),E&&E(T),(0,f.D)(x,w);const t=(0,l.XU)(T),{timestamp:e,start_timestamp:r}=t;if(!e||!r)return;const i=t.data||{};"ui.action.click"!==t.op||i[p.ju]||T.setAttribute(p.ju,s),a.kg.log(`[Tracing] Idle span "${t.op}" finished`),(0,l.Dp)(T).filter((t=>t!==T)).forEach((t=>{t.isRecording()&&(t.setStatus({code:m.jt,message:"cancelled"}),t.end(e),u.X&&a.kg.log("[Tracing] Cancelling span since span ended early",JSON.stringify(t,void 0,2)));const n=(0,l.XU)(t),{timestamp:r=0,start_timestamp:o=0}=n,s=o<=e,i=r-o<(b+y)/1e3;if(u.X){const e=JSON.stringify(t,void 0,2);s?i||a.kg.log("[Tracing] Discarding span since it finished after idle span final timeout",e):a.kg.log("[Tracing] Discarding span since it happened after idle span was finished",e)}i&&s||(0,l.ed)(T,t)}))}())})),k.on("idleSpanEnableAutoFinish",(t=>{t===T&&(v=!0,I(),n.size&&O())})),e.disableAutoFinish||I(),setTimeout((()=>{o||(T.setStatus({code:m.jt,message:"deadline_exceeded"}),s="finalTimeout",D())}),b),T}var y=n(98522),b=n(51176);let S=!1;function E(){const t=(0,l.HN)(),e=t&&(0,l.Gx)(t);if(e){const t="internal_error";u.X&&a.kg.log(`[Tracing] Root span: ${t} -> Global error occured`),e.setStatus({code:m.jt,message:t})}}E.tag="sentry_tracingErrorCallback";var k=n(349),x=n(16847),w=n(28169),T=n(34100),D=n(59802),$=n(42917);var I=n(41048),O=n(48312),C=n(486),j=n(63426),R=n(47701);function N(t,e,n,r,o="auto.http.browser"){if(!t.fetchData)return;const s=(0,d.z)()&&e(t.fetchData.url);if(t.endTimestamp&&s){const e=t.fetchData.__span;if(!e)return;const n=r[e];return void(n&&(!function(t,e){if(e.response){(0,m.Q0)(t,e.response.status);const n=e.response&&e.response.headers&&e.response.headers.get("content-length");if(n){const e=parseInt(n);e>0&&t.setAttribute("http.response_content_length",e)}}else e.error&&t.setStatus({code:m.jt,message:"internal_error"});t.end()}(n,t),delete r[e]))}const i=(0,c.nZ)(),a=(0,c.s3)(),{method:u,url:f}=t.fetchData,_=function(t){try{return new URL(t).href}catch(e){return}}(f),v=_?(0,C.en)(_).host:void 0,y=!!(0,l.HN)(),b=s&&y?(0,g.qp)({name:`${u} ${f}`,attributes:{url:f,type:"fetch","http.method":u,"http.url":_,"server.address":v,[p.S3]:o,[p.$J]:"http.client"}}):new h.b;if(t.fetchData.__span=b.spanContext().spanId,r[b.spanContext().spanId]=b,n(t.fetchData.url)&&a){const e=t.args[0];t.args[1]=t.args[1]||{};const n=t.args[1];n.headers=function(t,e,n,r,o){const s=(0,c.aF)(),{traceId:i,spanId:a,sampled:u,dsc:p}={...s.getPropagationContext(),...n.getPropagationContext()},d=o?(0,l.Hb)(o):(0,x.$p)(i,a,u),f=(0,j.IQ)(p||(o?(0,k.jC)(o):(0,k._l)(i,e))),h=r.headers||("undefined"!==typeof Request&&(0,R.V9)(t,Request)?t.headers:void 0);if(h){if("undefined"!==typeof Headers&&(0,R.V9)(h,Headers)){const t=new Headers(h);return t.append("sentry-trace",d),f&&t.append(j.bU,f),t}if(Array.isArray(h)){const t=[...h,["sentry-trace",d]];return f&&t.push([j.bU,f]),t}{const t="baggage"in h?h.baggage:void 0,e=[];return Array.isArray(t)?e.push(...t):t&&e.push(t),f&&e.push(f),{...h,"sentry-trace":d,baggage:e.length>0?e.join(","):void 0}}}return{"sentry-trace":d,baggage:f}}(e,a,i,n,(0,d.z)()&&y?b:void 0)}return b}var P=n(67567),A=n(50712);const M={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0};function U(t){const{traceFetch:e,traceXHR:n,shouldCreateSpanForRequest:r,enableHTTPTimings:o,tracePropagationTargets:s}={traceFetch:M.traceFetch,traceXHR:M.traceXHR,...t},i="function"===typeof r?r:t=>!0,a=t=>function(t,e){const n=$.m9.location&&$.m9.location.href;if(n){let o,s;try{o=new URL(t,n),s=new URL(n).origin}catch(r){return!1}const i=o.origin===s;return e?(0,A.U0)(o.toString(),e)||i&&(0,A.U0)(o.pathname,e):i}{const n=!!t.match(/^\/(?!\/)/);return e?(0,A.U0)(t,e):n}}(t,s),u={};e&&(0,P.U)((t=>{const e=N(t,i,a,u);if(e){const n=H(t.fetchData.url),r=n?(0,C.en)(n).host:void 0;e.setAttributes({"http.url":n,"server.address":r})}o&&e&&L(e)})),n&&(0,I.UK)((t=>{const e=function(t,e,n,r){const o=t.xhr,s=o&&o[I.xU];if(!o||o.__sentry_own_request__||!s)return;const i=(0,d.z)()&&e(s.url);if(t.endTimestamp&&i){const t=o.__sentry_xhr_span_id__;if(!t)return;const e=r[t];return void(e&&void 0!==s.status_code&&((0,m.Q0)(e,s.status_code),e.end(),delete r[t]))}const a=H(s.url),u=a?(0,C.en)(a).host:void 0,f=!!(0,l.HN)(),_=i&&f?(0,g.qp)({name:`${s.method} ${s.url}`,attributes:{type:"xhr","http.method":s.method,"http.url":a,url:s.url,"server.address":u,[p.S3]:"auto.http.browser",[p.$J]:"http.client"}}):new h.b;o.__sentry_xhr_span_id__=_.spanContext().spanId,r[o.__sentry_xhr_span_id__]=_;const v=(0,c.s3)();o.setRequestHeader&&n(s.url)&&v&&function(t,e,n){const r=(0,c.nZ)(),o=(0,c.aF)(),{traceId:s,spanId:i,sampled:a,dsc:u}={...o.getPropagationContext(),...r.getPropagationContext()},p=n&&(0,d.z)()?(0,l.Hb)(n):(0,x.$p)(s,i,a),f=(0,j.IQ)(u||(n?(0,k.jC)(n):(0,k._l)(s,e)));!function(t,e,n){try{t.setRequestHeader("sentry-trace",e),n&&t.setRequestHeader(j.bU,n)}catch(r){}}(t,p,f)}(o,v,(0,d.z)()&&f?_:void 0);return _}(t,i,a,u);o&&e&&L(e)}))}function L(t){const{url:e}=(0,l.XU)(t).data||{};if(!e||"string"!==typeof e)return;const n=(0,O._j)("resource",(({entries:r})=>{r.forEach((r=>{if(function(t){return"resource"===t.entryType&&"initiatorType"in t&&"string"===typeof t.nextHopProtocol&&("fetch"===t.initiatorType||"xmlhttprequest"===t.initiatorType)}(r)&&r.name.endsWith(e)){(function(t){const{name:e,version:n}=function(t){let e="unknown",n="unknown",r="";for(const o of t){if("/"===o){[e,n]=t.split("/");break}if(!isNaN(Number(o))){e="h"===r?"http":r,n=t.split(r)[1];break}r+=o}r===t&&(e=r);return{name:e,version:n}}(t.nextHopProtocol),r=[];if(r.push(["network.protocol.version",n],["network.protocol.name",e]),!i.Z1)return r;return[...r,["http.request.redirect_start",X(t.redirectStart)],["http.request.fetch_start",X(t.fetchStart)],["http.request.domain_lookup_start",X(t.domainLookupStart)],["http.request.domain_lookup_end",X(t.domainLookupEnd)],["http.request.connect_start",X(t.connectStart)],["http.request.secure_connection_start",X(t.secureConnectionStart)],["http.request.connection_end",X(t.connectEnd)],["http.request.request_start",X(t.requestStart)],["http.request.response_start",X(t.responseStart)],["http.request.response_end",X(t.responseEnd)]]})(r).forEach((e=>t.setAttribute(...e))),setTimeout(n)}}))}))}function X(t=0){return((i.Z1||performance.timeOrigin)+t)/1e3}function H(t){try{return new URL(t,$.m9.location.origin).href}catch(e){return}}const F={..._,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableInp:!0,_experiments:{},...M},q=(t={})=>{S||(S=!0,(0,y.V)(E),(0,b.h)(E));const{enableInp:e,enableLongTask:n,_experiments:{enableInteractions:u},beforeStartSpan:d,idleTimeout:f,finalTimeout:h,childSpanTimeout:g,markBackgroundSpan:_,traceFetch:w,traceXHR:T,shouldCreateSpanForRequest:I,enableHTTPTimings:O,instrumentPageLoad:C,instrumentNavigation:j}={...F,...t},R=(0,r.PR)();e&&(0,o.N)(),n&&(0,r.Fv)(),u&&(0,r.sn)();const N={name:void 0,source:void 0};function P(t,e){const n="pageload"===e.op,o=d?d(e):e,s=o.attributes||{};e.name!==o.name&&(s[p.Zj]="custom",o.attributes=s),N.name=o.name,N.source=s[p.Zj];const i=v(o,{idleTimeout:f,finalTimeout:h,childSpanTimeout:g,disableAutoFinish:n,beforeSpanEnd:t=>{R(),(0,r.f7)(t)}});function a(){["interactive","complete"].includes($.m9.document.readyState)&&t.emit("idleSpanEnableAutoFinish",i)}return n&&$.m9.document&&($.m9.document.addEventListener("readystatechange",(()=>{a()})),a()),i}return{name:"BrowserTracing",afterAllSetup(t){let e,n=$.m9.location&&$.m9.location.href;t.on("startNavigationSpan",(n=>{(0,c.s3)()===t&&(e&&(D.X&&a.kg.log(`[Tracing] Finishing current root span with op: ${(0,l.XU)(e).op}`),e.end()),e=P(t,{op:"navigation",...n}))})),t.on("startPageLoadSpan",((n,r={})=>{if((0,c.s3)()!==t)return;e&&(D.X&&a.kg.log(`[Tracing] Finishing current root span with op: ${(0,l.XU)(e).op}`),e.end());const o=r.sentryTrace||J("sentry-trace"),s=r.baggage||J("baggage"),i=(0,x.pT)(o,s);(0,c.nZ)().setPropagationContext(i),e=P(t,{op:"pageload",...n})})),t.on("spanEnd",(t=>{const e=(0,l.XU)(t).op;if(t!==(0,l.Gx)(t)||"navigation"!==e&&"pageload"!==e)return;const n=(0,c.nZ)(),r=n.getPropagationContext();n.setPropagationContext({...r,sampled:void 0!==r.sampled?r.sampled:(0,l.Tt)(t),dsc:r.dsc||(0,k.jC)(t)})})),$.m9.location&&(C&&function(t,e,n){t.emit("startPageLoadSpan",e,n),(0,c.nZ)().setTransactionName(e.name);const r=(0,l.HN)();r&&(0,l.XU)(r).op}(t,{name:$.m9.location.pathname,startTime:i.Z1?i.Z1/1e3:void 0,attributes:{[p.Zj]:"url",[p.S3]:"auto.pageload.browser"}}),j&&(0,s.a)((({to:e,from:r})=>{void 0===r&&n&&-1!==n.indexOf(e)?n=void 0:r!==e&&(n=void 0,function(t,e){(0,c.nZ)().setPropagationContext(G()),(0,c.aF)().setPropagationContext(G()),t.emit("startNavigationSpan",e),(0,c.nZ)().setTransactionName(e.name);const n=(0,l.HN)();n&&(0,l.XU)(n).op}(t,{name:$.m9.location.pathname,attributes:{[p.Zj]:"url",[p.S3]:"auto.navigation.browser"}}))}))),_&&($.m9&&$.m9.document?$.m9.document.addEventListener("visibilitychange",(()=>{const t=(0,l.HN)();if(!t)return;const e=(0,l.Gx)(t);if($.m9.document.hidden&&e){const t="cancelled",{op:n,status:r}=(0,l.XU)(e);D.X&&a.kg.log(`[Tracing] Transaction: ${t} -> since tab moved to the background, op: ${n}`),r||e.setStatus({code:m.jt,message:t}),e.setAttribute("sentry.cancellation_reason","document.hidden"),e.end()}})):D.X&&a.kg.warn("[Tracing] Could not set up background tab detection due to lack of global document")),u&&function(t,e,n,r){let o;const s=()=>{const s="ui.action.click",i=(0,l.HN)(),c=i&&(0,l.Gx)(i);if(c){const t=(0,l.XU)(c).op;if(["navigation","pageload"].includes(t))return void(D.X&&a.kg.warn(`[Tracing] Did not create ${s} span because a pageload or navigation span is in progress.`))}o&&(o.setAttribute(p.ju,"interactionInterrupted"),o.end(),o=void 0),r.name?o=v({name:r.name,op:s,attributes:{[p.Zj]:r.source||"url"}},{idleTimeout:t,finalTimeout:e,childSpanTimeout:n}):D.X&&a.kg.warn(`[Tracing] Did not create ${s} transaction because _latestRouteName is missing.`)};$.m9.document&&addEventListener("click",s,{once:!1,capture:!0})}(f,h,g,N),U({traceFetch:w,traceXHR:T,tracePropagationTargets:t.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:I,enableHTTPTimings:O})}}};function J(t){const e=(0,w.qT)(`meta[name=${t}]`);return e?e.getAttribute("content"):void 0}function G(){return{traceId:(0,T.DM)(),spanId:(0,T.DM)().substring(16)}}},29396:function(t,e,n){n.d(e,{G:function(){return f}});var r=n(97992),o=n(47701),s=n(50087),i=n(88422);class a{constructor(t,e){let n,r;n=t||new i.s,r=e||new i.s,this._stack=[{scope:n}],this._isolationScope=r}withScope(t){const e=this._pushScope();let n;try{n=t(e)}catch(r){throw this._popScope(),r}return(0,o.J8)(n)?n.then((t=>(this._popScope(),t)),(t=>{throw this._popScope(),t})):(this._popScope(),n)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this.getStack().push({client:this.getClient(),scope:t}),t}_popScope(){return!(this.getStack().length<=1)&&!!this.getStack().pop()}}function c(){const t=(0,r.c)(),e=(0,r.q)(t);return e.hub||(e.hub=new a((0,s.Xl)(),(0,s.Tv)())),e.hub}function u(t){return c().withScope(t)}function p(t,e){const n=c();return n.withScope((()=>(n.getStackTop().scope=t,e(t))))}function d(t){return c().withScope((()=>t(c().getIsolationScope())))}function f(t){const e=(0,r.q)(t);return e.acs?e.acs:{withIsolationScope:d,withScope:u,withSetScope:p,withSetIsolationScope:(t,e)=>d(e),getCurrentScope:()=>c().getScope(),getIsolationScope:()=>c().getIsolationScope()}}},34124:function(t,e,n){n.d(e,{n:function(){return a}});var r=n(22519),o=n(46616),s=n(50087);const i=100;function a(t,e){const n=(0,s.s3)(),a=(0,s.aF)();if(!n)return;const{beforeBreadcrumb:c=null,maxBreadcrumbs:u=i}=n.getOptions();if(u<=0)return;const p={timestamp:(0,r.yW)(),...t},d=c?(0,o.Cf)((()=>c(p,e))):p;null!==d&&(n.emit&&n.emit("beforeAddBreadcrumb",d,e),a.addBreadcrumb(d,u))}},97992:function(t,e,n){n.d(e,{c:function(){return o},q:function(){return s}});var r=n(55793);function o(){return s(r.n),r.n}function s(t){return t.__SENTRY__||(t.__SENTRY__={extensions:{}}),t.__SENTRY__}},9685:function(t,e,n){n.d(e,{J:function(){return r}});const r="production"},50087:function(t,e,n){n.d(e,{s3:function(){return l},nZ:function(){return u},Xl:function(){return a},Tv:function(){return c},lW:function(){return d},aF:function(){return p},$e:function(){return f}});var r=n(55793),o=n(29396),s=n(97992),i=n(88422);function a(){return(0,r.Y)("defaultCurrentScope",(()=>new i.s))}function c(){return(0,r.Y)("defaultIsolationScope",(()=>new i.s))}function u(){const t=(0,s.c)();return(0,o.G)(t).getCurrentScope()}function p(){const t=(0,s.c)();return(0,o.G)(t).getIsolationScope()}function d(){return(0,r.Y)("globalScope",(()=>new i.s))}function f(...t){const e=(0,s.c)(),n=(0,o.G)(e);if(2===t.length){const[e,r]=t;return e?n.withSetScope(e,r):n.withScope(r)}return n.withScope(t[0])}function l(){return u().getClient()}},33994:function(t,e,n){n.d(e,{X:function(){return r}});const r="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__},11540:function(t,e,n){n.d(e,{Mq:function(){return c},Q3:function(){return a},uE:function(){return u}});var r=n(68571),o=n(86630),s=n(74042),i=n(349);function a(t,e,n,s){const i=(0,r.HY)(n),a={sent_at:(new Date).toISOString(),...i&&{sdk:i},...!!s&&e&&{dsn:(0,o.RA)(e)}},c="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()];return(0,r.Jd)(a,[c])}function c(t,e,n,o){const s=(0,r.HY)(n),i=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,e){e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]])}(t,n&&n.sdk);const a=(0,r.Cd)(t,s,o,e);delete t.sdkProcessingMetadata;const c=[{type:i},t];return(0,r.Jd)(a,[c])}function u(t){const e=(0,i.jC)(t[0]),n={sent_at:(new Date).toISOString(),...function(t){return!!t.trace_id&&!!t.public_key}(e)&&{trace:e}},o=t.map((t=>(0,r.KQ)((0,s.XU)(t))));return(0,r.Jd)(n,o)}},35506:function(t,e,n){n.d(e,{Qy:function(){return f},eN:function(){return u},Tb:function(){return c},cg:function(){return g},eW:function(){return d},v:function(){return p},yj:function(){return l}});var r=n(55793),o=n(9685),s=n(50087),i=n(7933),a=n(43130);function c(t,e){return(0,s.nZ)().captureException(t,(0,a.U0)(e))}function u(t,e){return(0,s.nZ)().captureEvent(t,e)}function p(t,e){(0,s.aF)().setContext(t,e)}function d(){return(0,s.aF)().lastEventId()}function f(t){(0,s.aF)().addEventProcessor(t)}function l(t){const e=(0,s.s3)(),n=(0,s.aF)(),a=(0,s.nZ)(),{release:c,environment:u=o.J}=e&&e.getOptions()||{},{userAgent:p}=r.n.navigator||{},d=(0,i.Hv)({release:c,environment:u,user:a.getUser()||n.getUser(),...p&&{userAgent:p},...t}),f=n.getSession();return f&&"ok"===f.status&&(0,i.CT)(f,{status:"exited"}),h(),n.setSession(d),a.setSession(d),d}function h(){const t=(0,s.aF)(),e=(0,s.nZ)(),n=e.getSession()||t.getSession();n&&(0,i.RJ)(n),m(),t.setSession(),e.setSession()}function m(){const t=(0,s.aF)(),e=(0,s.nZ)(),n=(0,s.s3)(),r=e.getSession()||t.getSession();r&&n&&n.captureSession(r)}function g(t=!1){t?h():m()}},77341:function(t,e,n){n.d(e,{y:function(){return i}});var r=n(88545);let o;function s(t){return o?o.get(t):void 0}function i(t){const e=s(t);if(!e)return;const n={};for(const[,[o,s]]of e)n[o]||(n[o]=[]),n[o].push((0,r.Jr)(s));return n}},88422:function(t,e,n){n.d(e,{s:function(){return u}});var r=n(47701),o=n(22519),s=n(34100),i=n(46616),a=n(7933),c=n(34454);class u{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=p()}clone(){const t=new u;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,(0,c.D)(t,(0,c.Y)(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&(0,a.CT)(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,e){return this._tags={...this._tags,[t]:e},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,e){return this._extra={...this._extra,[t]:e},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,e){return null===e?delete this._contexts[t]:this._contexts[t]=e,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const e="function"===typeof t?t(this):t,[n,o]=e instanceof u?[e.getScopeData(),e.getRequestSession()]:(0,r.PO)(e)?[t,t.requestSession]:[],{tags:s,extra:i,user:a,contexts:c,level:p,fingerprint:d=[],propagationContext:f}=n||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...i},this._contexts={...this._contexts,...c},a&&Object.keys(a).length&&(this._user=a),p&&(this._level=p),d.length&&(this._fingerprint=d),f&&(this._propagationContext=f),o&&(this._requestSession=o),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,(0,c.D)(this,void 0),this._attachments=[],this._propagationContext=p(),this._notifyScopeListeners(),this}addBreadcrumb(t,e){const n="number"===typeof e?e:100;if(n<=0)return this;const r={timestamp:(0,o.yW)(),...t},s=this._breadcrumbs;return s.push(r),this._breadcrumbs=s.length>n?s.slice(-n):s,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:(0,c.Y)(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,e){const n=e&&e.event_id?e.event_id:(0,s.DM)();if(!this._client)return i.kg.warn("No client configured on scope - will not capture exception!"),n;const r=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:r,...e,event_id:n},this),n}captureMessage(t,e,n){const r=n&&n.event_id?n.event_id:(0,s.DM)();if(!this._client)return i.kg.warn("No client configured on scope - will not capture message!"),r;const o=new Error(t);return this._client.captureMessage(t,e,{originalException:t,syntheticException:o,...n,event_id:r},this),r}captureEvent(t,e){const n=e&&e.event_id?e.event_id:(0,s.DM)();return this._client?(this._client.captureEvent(t,{...e,event_id:n},this),n):(i.kg.warn("No client configured on scope - will not capture event!"),n)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((t=>{t(this)})),this._notifyingListeners=!1)}}function p(){return{traceId:(0,s.DM)(),spanId:(0,s.DM)().substring(16)}}},17670:function(t,e,n){n.d(e,{JQ:function(){return d},p6:function(){return p},ju:function(){return a},E1:function(){return c},Wb:function(){return u},$J:function(){return s},S3:function(){return i},TE:function(){return o},Zj:function(){return r}});const r="sentry.source",o="sentry.sample_rate",s="sentry.op",i="sentry.origin",a="sentry.idle_span_finish_reason",c="sentry.measurement_unit",u="sentry.measurement_value",p="sentry.profile_id",d="sentry.exclusive_time"},7933:function(t,e,n){n.d(e,{RJ:function(){return c},Hv:function(){return i},CT:function(){return a}});var r=n(22519),o=n(34100),s=n(88545);function i(t){const e=(0,r.ph)(),n={sid:(0,o.DM)(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return(0,s.Jr)({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"===typeof t.did||"string"===typeof t.did?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(n)};return t&&a(n,t),n}function a(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||(0,r.ph)(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:(0,o.DM)()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"===typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"===typeof e.duration)t.duration=e.duration;else{const e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"===typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}function c(t,e){let n={};e?n={status:e}:"ok"===t.status&&(n={status:"exited"}),a(t,n)}},349:function(t,e,n){n.d(e,{Lh:function(){return u},_l:function(){return p},jC:function(){return d}});var r=n(88545),o=n(9685),s=n(50087),i=n(17670),a=n(74042);const c="_frozenDsc";function u(t,e){const n=t;(0,r.xp)(n,c,e)}function p(t,e){const n=e.getOptions(),{publicKey:s}=e.getDsn()||{},i=(0,r.Jr)({environment:n.environment||o.J,release:n.release,public_key:s,trace_id:t});return e.emit("createDsc",i),i}function d(t){const e=(0,s.s3)();if(!e)return{};const n=p((0,a.XU)(t).trace_id||"",e),r=(0,a.Gx)(t);if(!r)return n;const o=r._frozenDsc;if(o)return o;const c=(0,a.XU)(r),u=c.data||{},d=u[i.TE];null!=d&&(n.sample_rate=`${d}`);const f=u[i.Zj];return f&&"url"!==f&&(n.transaction=c.description),n.sampled=String((0,a.Tt)(r)),e.emit("createDsc",n),n}},2511:function(t,e,n){n.d(e,{o:function(){return s},l:function(){return i}});var r=n(17670),o=n(74042);function s(t,e,n){const s=(0,o.HN)(),i=s&&(0,o.Gx)(s);i&&i.addEvent(t,{[r.Wb]:e,[r.E1]:n})}function i(t){if(!t||0===t.length)return;const e={};return t.forEach((t=>{const n=t.attributes||{},o=n[r.E1],s=n[r.Wb];"string"===typeof o&&"number"===typeof s&&(e[t.name]={value:s,unit:o})})),e}},62439:function(t,e,n){n.d(e,{b:function(){return s}});var r=n(34100),o=n(74042);class s{constructor(t={}){this._traceId=t.traceId||(0,r.DM)(),this._spanId=t.spanId||(0,r.DM)().substring(16)}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:o.ve}}end(t){}setAttribute(t,e){return this}setAttributes(t){return this}setStatus(t){return this}updateName(t){return this}isRecording(){return!1}addEvent(t,e,n){return this}}},78061:function(t,e,n){n.d(e,{jt:function(){return s},OP:function(){return o},pq:function(){return r},Q0:function(){return i}});const r=0,o=1,s=2;function i(t,e){t.setAttribute("http.response.status_code",e);const n=function(t){if(t<400&&t>=100)return{code:o};if(t>=400&&t<500)switch(t){case 401:return{code:s,message:"unauthenticated"};case 403:return{code:s,message:"permission_denied"};case 404:return{code:s,message:"not_found"};case 409:return{code:s,message:"already_exists"};case 413:return{code:s,message:"failed_precondition"};case 429:return{code:s,message:"resource_exhausted"};case 499:return{code:s,message:"cancelled"};default:return{code:s,message:"invalid_argument"}}if(t>=500&&t<600)switch(t){case 501:return{code:s,message:"unimplemented"};case 503:return{code:s,message:"unavailable"};case 504:return{code:s,message:"deadline_exceeded"};default:return{code:s,message:"internal_error"}}return{code:s,message:"unknown_error"}}(e);"unknown_error"!==n.message&&t.setStatus(n)}},49839:function(t,e,n){n.d(e,{qp:function(){return $},_d:function(){return I}});var r=n(97992),o=n(50087),s=n(29396),i=n(17670),a=n(28772),c=n(34454),u=n(74042),p=n(349),d=n(46616),f=n(33994);var l=n(18057);var h=n(62439),m=n(34100),g=n(22519),_=n(88545),v=n(11540),y=n(77341),b=n(2511);const S="_sentryScope",E="_sentryIsolationScope";function k(t){return{scope:t._sentryScope,isolationScope:t._sentryIsolationScope}}class x{constructor(t={}){this._traceId=t.traceId||(0,m.DM)(),this._spanId=t.spanId||(0,m.DM)().substring(16),this._startTime=t.startTimestamp||(0,g.ph)(),this._attributes={},this.setAttributes({[i.S3]:"manual",[i.$J]:t.op,...t.attributes}),this._name=t.name,t.parentSpanId&&(this._parentSpanId=t.parentSpanId),"sampled"in t&&(this._sampled=t.sampled),t.endTimestamp&&(this._endTime=t.endTimestamp),this._events=[],this._endTime&&this._onSpanEnded(),this._isStandaloneSpan=t.isStandalone}spanContext(){const{_spanId:t,_traceId:e,_sampled:n}=this;return{spanId:t,traceId:e,traceFlags:n?u.i0:u.ve}}setAttribute(t,e){void 0===e?delete this._attributes[t]:this._attributes[t]=e}setAttributes(t){Object.keys(t).forEach((e=>this.setAttribute(e,t[e])))}updateStartTime(t){this._startTime=(0,u.$k)(t)}setStatus(t){return this._status=t,this}updateName(t){return this._name=t,this}end(t){this._endTime||(this._endTime=(0,u.$k)(t),function(t){if(!f.X)return;const{description:e="< unknown name >",op:n="< unknown op >"}=(0,u.XU)(t),{spanId:r}=t.spanContext(),o=`[Tracing] Finishing "${n}" ${(0,u.Gx)(t)===t?"root ":""}span "${e}" with ID ${r}`;d.kg.log(o)}(this),this._onSpanEnded())}getSpanJSON(){return(0,_.Jr)({data:this._attributes,description:this._name,op:this._attributes[i.$J],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:(0,u._4)(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[i.S3],_metrics_summary:(0,y.y)(this),profile_id:this._attributes[i.p6],exclusive_time:this._attributes[i.JQ],measurements:(0,b.l)(this._events),is_segment:this._isStandaloneSpan&&(0,u.Gx)(this)===this||void 0,segment_id:this._isStandaloneSpan?(0,u.Gx)(this).spanContext().spanId:void 0})}isRecording(){return!this._endTime&&!!this._sampled}addEvent(t,e,n){f.X&&d.kg.log("[Tracing] Adding an event to span:",t);const r=w(e)?e:n||(0,g.ph)(),o=w(e)?{}:e||{},s={name:t,time:(0,u.$k)(r),attributes:o};return this._events.push(s),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const t=(0,o.s3)();t&&t.emit("spanEnd",this);if(!(this._isStandaloneSpan||this===(0,u.Gx)(this)))return;if(this._isStandaloneSpan)return void function(t){const e=(0,o.s3)();if(!e)return;const n=e.getTransport();n&&n.send(t).then(null,(t=>{f.X&&d.kg.error("Error while sending span:",t)}))}((0,v.uE)([this]));const e=this._convertSpanToTransaction();if(e){(k(this).scope||(0,o.nZ)()).captureEvent(e)}}_convertSpanToTransaction(){if(!T((0,u.XU)(this)))return;this._name||(f.X&&d.kg.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:t,isolationScope:e}=k(this),n=(t||(0,o.nZ)()).getClient()||(0,o.s3)();if(!0!==this._sampled)return f.X&&d.kg.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),void(n&&n.recordDroppedEvent("sample_rate","transaction"));const r=(0,u.Dp)(this).filter((t=>t!==this&&!function(t){return t instanceof x&&t.isStandaloneSpan()}(t))).map((t=>(0,u.XU)(t))).filter(T),s=this._attributes[i.Zj],a={contexts:{trace:(0,u.HR)(this)},spans:r,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:t,capturedSpanIsolationScope:e,...(0,_.Jr)({dynamicSamplingContext:(0,p.jC)(this)})},_metrics_summary:(0,y.y)(this),...s&&{transaction_info:{source:s}}},c=(0,b.l)(this._events);return c&&Object.keys(c).length&&(f.X&&d.kg.log("[Measurements] Adding measurements to transaction",JSON.stringify(c,void 0,2)),a.measurements=c),a}}function w(t){return t&&"number"===typeof t||t instanceof Date||Array.isArray(t)}function T(t){return!!t.start_timestamp&&!!t.timestamp&&!!t.span_id&&!!t.trace_id}const D="__SENTRY_SUPPRESS_TRACING__";function $(t){const e=j();if(e.startInactiveSpan)return e.startInactiveSpan(t);const n=C(t),r=t.scope||(0,o.nZ)(),s=N(r);return t.onlyIfParent&&!s?new h.b:O({parentSpan:s,spanContext:n,forceTransaction:t.forceTransaction,scope:r})}function I(t,e){const n=j();return n.withActiveSpan?n.withActiveSpan(t,e):(0,o.$e)((n=>((0,c.D)(n,t||void 0),e(n))))}function O({parentSpan:t,spanContext:e,forceTransaction:n,scope:r}){if(!(0,a.z)())return new h.b;const s=(0,o.aF)();let i;if(t&&!n)i=function(t,e,n){const{spanId:r,traceId:s}=t.spanContext(),i=!e.getScopeData().sdkProcessingMetadata[D]&&(0,u.Tt)(t),a=i?new x({...n,parentSpanId:r,traceId:s,sampled:i}):new h.b({traceId:s});(0,u.j5)(t,a);const c=(0,o.s3)();c&&(c.emit("spanStart",a),n.endTimestamp&&c.emit("spanEnd",a));return a}(t,r,e),(0,u.j5)(t,i);else if(t){const n=(0,p.jC)(t),{traceId:o,spanId:s}=t.spanContext(),a=(0,u.Tt)(t);i=R({traceId:o,parentSpanId:s,...e},r,a),(0,p.Lh)(i,n)}else{const{traceId:t,dsc:n,parentSpanId:o,sampled:a}={...s.getPropagationContext(),...r.getPropagationContext()};i=R({traceId:t,parentSpanId:o,...e},r,a),n&&(0,p.Lh)(i,n)}return function(t){if(!f.X)return;const{description:e="< unknown name >",op:n="< unknown op >",parent_span_id:r}=(0,u.XU)(t),{spanId:o}=t.spanContext(),s=(0,u.Tt)(t),i=(0,u.Gx)(t),a=i===t,c=`[Tracing] Starting ${s?"sampled":"unsampled"} ${a?"root ":""}span`,p=[`op: ${n}`,`name: ${e}`,`ID: ${o}`];if(r&&p.push(`parent ID: ${r}`),!a){const{op:t,description:e}=(0,u.XU)(i);p.push(`root ID: ${i.spanContext().spanId}`),t&&p.push(`root op: ${t}`),e&&p.push(`root description: ${e}`)}d.kg.log(`${c}\n  ${p.join("\n  ")}`)}(i),function(t,e,n){t&&((0,_.xp)(t,E,n),(0,_.xp)(t,S,e))}(i,r,s),i}function C(t){const e={isStandalone:(t.experimental||{}).standalone,...t};if(t.startTime){const n={...e};return n.startTimestamp=(0,u.$k)(t.startTime),delete n.startTime,n}return e}function j(){const t=(0,r.c)();return(0,s.G)(t)}function R(t,e,n){const r=(0,o.s3)(),s=r&&r.getOptions()||{},{name:c="",attributes:u}=t,[p,h]=e.getScopeData().sdkProcessingMetadata[D]?[!1]:function(t,e){if(!(0,a.z)(t))return[!1];let n;n="function"===typeof t.tracesSampler?t.tracesSampler(e):void 0!==e.parentSampled?e.parentSampled:"undefined"!==typeof t.tracesSampleRate?t.tracesSampleRate:1;const r=(0,l.o)(n);return void 0===r?(f.X&&d.kg.warn("[Tracing] Discarding transaction because of invalid sample rate."),[!1]):r?Math.random()<r?[!0,r]:(f.X&&d.kg.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(n)})`),[!1,r]):(f.X&&d.kg.log("[Tracing] Discarding transaction because "+("function"===typeof t.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,r])}(s,{name:c,parentSampled:n,attributes:u,transactionContext:{name:c,parentSampled:n}}),m=new x({...t,attributes:{[i.Zj]:"custom",...t.attributes},sampled:p});return void 0!==h&&m.setAttribute(i.TE,h),r&&r.emit("spanStart",m),m}function N(t){const e=(0,c.Y)(t);if(!e)return;const n=(0,o.s3)();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?(0,u.Gx)(e):e}},28772:function(t,e,n){n.d(e,{z:function(){return o}});var r=n(50087);function o(t){if("boolean"===typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const e=(0,r.s3)(),n=t||e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}},19896:function(t,e,n){function r(t,e){const n=e&&e.getDsn(),r=e&&e.getOptions().tunnel;return function(t,e){return!!e&&t.includes(e.host)}(t,n)||function(t,e){if(!e)return!1;return o(t)===o(e)}(t,r)}function o(t){return"/"===t[t.length-1]?t.slice(0,-1):t}n.d(e,{W:function(){return r}})},18057:function(t,e,n){n.d(e,{o:function(){return s}});var r=n(46616),o=n(33994);function s(t){if("boolean"===typeof t)return Number(t);const e="string"===typeof t?parseFloat(t):t;if(!("number"!==typeof e||isNaN(e)||e<0||e>1))return e;o.X&&r.kg.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(t)} of type ${JSON.stringify(typeof t)}.`)}},43130:function(t,e,n){n.d(e,{U0:function(){return x},R:function(){return E}});var r=n(34100),o=n(22519),s=n(50712),i=n(55793),a=n(2615),c=n(9685),u=n(50087),p=n(10300),d=n(46616),f=n(47701),l=n(33994);function h(t,e,n,r=0){return new p.cW(((o,s)=>{const i=t[r];if(null===e||"function"!==typeof i)o(e);else{const a=i({...e},n);l.X&&i.id&&null===a&&d.kg.log(`Event processor "${i.id}" dropped event`),(0,f.J8)(a)?a.then((e=>h(t,e,n,r+1).then(o))).then(null,s):h(t,a,n,r+1).then(o).then(null,s)}}))}var m=n(88422),g=n(88545),_=n(349),v=n(74042);function y(t,e){const{fingerprint:n,span:o,breadcrumbs:s,sdkProcessingMetadata:i}=e;!function(t,e){const{extra:n,tags:r,user:o,contexts:s,level:i,transactionName:a}=e,c=(0,g.Jr)(n);c&&Object.keys(c).length&&(t.extra={...c,...t.extra});const u=(0,g.Jr)(r);u&&Object.keys(u).length&&(t.tags={...u,...t.tags});const p=(0,g.Jr)(o);p&&Object.keys(p).length&&(t.user={...p,...t.user});const d=(0,g.Jr)(s);d&&Object.keys(d).length&&(t.contexts={...d,...t.contexts});i&&(t.level=i);a&&"transaction"!==t.type&&(t.transaction=a)}(t,e),o&&function(t,e){t.contexts={trace:(0,v.wy)(e),...t.contexts},t.sdkProcessingMetadata={dynamicSamplingContext:(0,_.jC)(e),...t.sdkProcessingMetadata};const n=(0,v.Gx)(e),r=(0,v.XU)(n).description;r&&!t.transaction&&"transaction"===t.type&&(t.transaction=r)}(t,o),function(t,e){t.fingerprint=t.fingerprint?(0,r.lE)(t.fingerprint):[],e&&(t.fingerprint=t.fingerprint.concat(e));t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}(t,n),function(t,e){const n=[...t.breadcrumbs||[],...e];t.breadcrumbs=n.length?n:void 0}(t,s),function(t,e){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...e}}(t,i)}function b(t,e){const{extra:n,tags:r,user:o,contexts:s,level:i,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:p,attachments:d,propagationContext:f,transactionName:l,span:h}=e;S(t,"extra",n),S(t,"tags",r),S(t,"user",o),S(t,"contexts",s),S(t,"sdkProcessingMetadata",a),i&&(t.level=i),l&&(t.transactionName=l),h&&(t.span=h),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),p.length&&(t.eventProcessors=[...t.eventProcessors,...p]),d.length&&(t.attachments=[...t.attachments,...d]),t.propagationContext={...t.propagationContext,...f}}function S(t,e,n){if(n&&Object.keys(n).length){t[e]={...t[e]};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[e][r]=n[r])}}function E(t,e,n,p,d,f){const{normalizeDepth:l=3,normalizeMaxBreadth:g=1e3}=t,_={...e,event_id:e.event_id||n.event_id||(0,r.DM)(),timestamp:e.timestamp||(0,o.yW)()},v=n.integrations||t.integrations.map((t=>t.name));!function(t,e){const{environment:n,release:r,dist:o,maxValueLength:i=250}=e;"environment"in t||(t.environment="environment"in e?n:c.J);void 0===t.release&&void 0!==r&&(t.release=r);void 0===t.dist&&void 0!==o&&(t.dist=o);t.message&&(t.message=(0,s.$G)(t.message,i));const a=t.exception&&t.exception.values&&t.exception.values[0];a&&a.value&&(a.value=(0,s.$G)(a.value,i));const u=t.request;u&&u.url&&(u.url=(0,s.$G)(u.url,i))}(_,t),function(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}(_,v),void 0===e.type&&function(t,e){const n=i.n._sentryDebugIds;if(!n)return;let r;const o=k.get(e);o?r=o:(r=new Map,k.set(e,r));const s=Object.keys(n).reduce(((t,o)=>{let s;const i=r.get(o);i?s=i:(s=e(o),r.set(o,s));for(let e=s.length-1;e>=0;e--){const r=s[e];if(r.filename){t[r.filename]=n[o];break}}return t}),{});try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.filename&&(t.debug_id=s[t.filename])}))}))}catch(a){}}(_,t.stackParser);const S=function(t,e){if(!e)return t;const n=t?t.clone():new m.s;return n.update(e),n}(p,n.captureContext);n.mechanism&&(0,r.EG)(_,n.mechanism);const E=d?d.getEventProcessors():[],x=(0,u.lW)().getScopeData();if(f){b(x,f.getScopeData())}if(S){b(x,S.getScopeData())}const w=[...n.attachments||[],...x.attachments];w.length&&(n.attachments=w),y(_,x);return h([...E,...x.eventProcessors],_,n).then((t=>(t&&function(t){const e={};try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.debug_id&&(t.abs_path?e[t.abs_path]=t.debug_id:t.filename&&(e[t.filename]=t.debug_id),delete t.debug_id)}))}))}catch(r){}if(0===Object.keys(e).length)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const n=t.debug_meta.images;Object.keys(e).forEach((t=>{n.push({type:"sourcemap",code_file:t,debug_id:e[t]})}))}(t),"number"===typeof l&&l>0?function(t,e,n){if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:(0,a.Fv)(t.data,e,n)}})))},...t.user&&{user:(0,a.Fv)(t.user,e,n)},...t.contexts&&{contexts:(0,a.Fv)(t.contexts,e,n)},...t.extra&&{extra:(0,a.Fv)(t.extra,e,n)}};t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=(0,a.Fv)(t.contexts.trace.data,e,n)));t.spans&&(r.spans=t.spans.map((t=>({...t,...t.data&&{data:(0,a.Fv)(t.data,e,n)}}))));return r}(t,l,g):t)))}const k=new WeakMap;function x(t){if(t)return function(t){return t instanceof m.s||"function"===typeof t}(t)||function(t){return Object.keys(t).some((t=>w.includes(t)))}(t)?{captureContext:t}:t}const w=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"]},70829:function(t,e,n){n.d(e,{V:function(){return o}});const r="8.1.0";function o(t,e,n=[e],o="npm"){const s=t._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${e}`,packages:n.map((t=>({name:`${o}:@sentry/${t}`,version:r}))),version:r}),t._metadata=s}},34454:function(t,e,n){n.d(e,{Y:function(){return i},D:function(){return s}});var r=n(88545);const o="_sentrySpan";function s(t,e){e?(0,r.xp)(t,o,e):delete t._sentrySpan}function i(t){return t._sentrySpan}},74042:function(t,e,n){n.d(e,{ve:function(){return l},i0:function(){return h},j5:function(){return w},HN:function(){return I},Gx:function(){return $},Dp:function(){return D},_4:function(){return E},ed:function(){return T},Tt:function(){return S},$k:function(){return v},XU:function(){return b},wy:function(){return g},Hb:function(){return _},HR:function(){return m}});var r=n(88545),o=n(16847),s=n(22519),i=n(29396),a=n(97992),c=n(50087),u=n(77341),p=n(17670),d=n(34454),f=n(78061);const l=0,h=1;function m(t){const{spanId:e,traceId:n}=t.spanContext(),{data:o,op:s,parent_span_id:i,status:a,origin:c}=b(t);return(0,r.Jr)({parent_span_id:i,span_id:e,trace_id:n,data:o,op:s,status:a,origin:c})}function g(t){const{spanId:e,traceId:n}=t.spanContext(),{parent_span_id:o}=b(t);return(0,r.Jr)({parent_span_id:o,span_id:e,trace_id:n})}function _(t){const{traceId:e,spanId:n}=t.spanContext(),r=S(t);return(0,o.$p)(e,n,r)}function v(t){return"number"===typeof t?y(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?y(t.getTime()):(0,s.ph)()}function y(t){return t>9999999999?t/1e3:t}function b(t){if(function(t){return"function"===typeof t.getSpanJSON}(t))return t.getSpanJSON();try{const{spanId:e,traceId:n}=t.spanContext();if(function(t){const e=t;return!!e.attributes&&!!e.startTime&&!!e.name&&!!e.endTime&&!!e.status}(t)){const{attributes:o,startTime:s,name:i,endTime:a,parentSpanId:c,status:d}=t;return(0,r.Jr)({span_id:e,trace_id:n,data:o,description:i,parent_span_id:c,start_timestamp:v(s),timestamp:v(a)||void 0,status:E(d),op:o[p.$J],origin:o[p.S3],_metrics_summary:(0,u.y)(t)})}return{span_id:e,trace_id:n}}catch(e){return{}}}function S(t){const{traceFlags:e}=t.spanContext();return e===h}function E(t){if(t&&t.code!==f.pq)return t.code===f.OP?"ok":t.message||"unknown_error"}const k="_sentryChildSpans",x="_sentryRootSpan";function w(t,e){const n=t._sentryRootSpan||t;(0,r.xp)(e,x,n),t._sentryChildSpans&&t._sentryChildSpans.size<1e3?t._sentryChildSpans.add(e):(0,r.xp)(t,k,new Set([e]))}function T(t,e){t._sentryChildSpans&&t._sentryChildSpans.delete(e)}function D(t){const e=new Set;return function t(n){if(!e.has(n)&&S(n)){e.add(n);const r=n._sentryChildSpans?Array.from(n._sentryChildSpans):[];for(const e of r)t(e)}}(t),Array.from(e)}function $(t){return t._sentryRootSpan||t}function I(){const t=(0,a.c)(),e=(0,i.G)(t);return e.getActiveSpan?e.getActiveSpan():(0,d.Y)((0,c.nZ)())}},72971:function(t,e,n){n.d(e,{SV:function(){return d}});var r=n(50087),o=n(41856),s=n(35506),i=n(47701),a=n(46616),c=(n(50198),n(89526));const u="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;const p={componentStack:null,error:null,eventId:null};class d extends c.Component{constructor(t){super(t),d.prototype.__init.call(this),this.state=p,this._openFallbackReportDialog=!0;const e=(0,r.s3)();e&&t.showDialog&&(this._openFallbackReportDialog=!1,e.on("afterSendEvent",(e=>{!e.type&&this._lastEventId&&e.event_id===this._lastEventId&&(0,o.jp)({...t.dialogOptions,eventId:this._lastEventId})})))}componentDidCatch(t,{componentStack:e}){const{beforeCapture:n,onError:a,showDialog:u,dialogOptions:p}=this.props;(0,r.$e)((r=>{if(function(t){const e=t.match(/^([^.]+)/);return null!==e&&parseInt(e[0])>=17}(c.version)&&(0,i.VZ)(t)){const n=new Error(t.message);n.name=`React ErrorBoundary ${t.name}`,n.stack=e,function(t,e){const n=new WeakMap;!function t(e,r){if(!n.has(e))return e.cause?(n.set(e,!0),t(e.cause,r)):void(e.cause=r)}(t,e)}(t,n)}n&&n(r,t,e);const d=(0,s.Tb)(t,{captureContext:{contexts:{react:{componentStack:e}}},mechanism:{handled:!!this.props.fallback}});a&&a(t,e,d),u&&(this._lastEventId=d,this._openFallbackReportDialog&&(0,o.jp)({...p,eventId:d})),this.setState({error:t,componentStack:e,eventId:d})}))}componentDidMount(){const{onMount:t}=this.props;t&&t()}componentWillUnmount(){const{error:t,componentStack:e,eventId:n}=this.state,{onUnmount:r}=this.props;r&&r(t,e,n)}__init(){this.resetErrorBoundary=()=>{const{onReset:t}=this.props,{error:e,componentStack:n,eventId:r}=this.state;t&&t(e,n,r),this.setState(p)}}render(){const{fallback:t,children:e}=this.props,n=this.state;if(n.error){let e;return e="function"===typeof t?c.createElement(t,{error:n.error,componentStack:n.componentStack,resetError:this.resetErrorBoundary,eventId:n.eventId}):t,c.isValidElement(e)?e:(t&&u&&a.kg.warn("fallback did not produce a valid ReactElement"),null)}return"function"===typeof e?e():e}}},10367:function(t,e,n){n.d(e,{S:function(){return s}});var r=n(41856),o=n(70829);function s(t){const e={...t};(0,o.V)(e,"react"),(0,r.S1)(e)}},63426:function(t,e,n){n.d(e,{bU:function(){return i},EN:function(){return u},IQ:function(){return p}});var r=n(20570),o=n(47701),s=n(46616);const i="baggage",a="sentry-",c=/^sentry-/;function u(t){const e=function(t){if(!t||!(0,o.HD)(t)&&!Array.isArray(t))return;if(Array.isArray(t))return t.reduce(((t,e)=>{const n=d(e);for(const r of Object.keys(n))t[r]=n[r];return t}),{});return d(t)}(t);if(!e)return;const n=Object.entries(e).reduce(((t,[e,n])=>{if(e.match(c)){t[e.slice(a.length)]=n}return t}),{});return Object.keys(n).length>0?n:void 0}function p(t){if(!t)return;return function(t){if(0===Object.keys(t).length)return;return Object.entries(t).reduce(((t,[e,n],o)=>{const i=`${encodeURIComponent(e)}=${encodeURIComponent(n)}`,a=0===o?i:`${t},${i}`;return a.length>8192?(r.X&&s.kg.warn(`Not adding key: ${e} with val: ${n} to baggage header due to exceeding baggage size limits.`),t):a}),"")}(Object.entries(t).reduce(((t,[e,n])=>(n&&(t[`sentry-${e}`]=n),t)),{}))}function d(t){return t.split(",").map((t=>t.split("=").map((t=>decodeURIComponent(t.trim()))))).reduce(((t,[e,n])=>(t[e]=n,t)),{})}},28169:function(t,e,n){n.d(e,{iY:function(){return u},qT:function(){return c},l4:function(){return a},Rt:function(){return s}});var r=n(47701);const o=n(55793).n;function s(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,o=[];let s=0,a=0;const c=" > ",u=c.length;let p;const d=Array.isArray(e)?e:e.keyAttrs,f=!Array.isArray(e)&&e.maxStringLength||80;for(;n&&s++<r&&(p=i(n,d),!("html"===p||s>1&&a+o.length*u+p.length>=f));)o.push(p),a+=p.length,n=n.parentNode;return o.reverse().join(c)}catch(n){return"<unknown>"}}function i(t,e){const n=t,s=[];let i,a,c,u,p;if(!n||!n.tagName)return"";if(o.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}s.push(n.tagName.toLowerCase());const d=e&&e.length?e.filter((t=>n.getAttribute(t))).map((t=>[t,n.getAttribute(t)])):null;if(d&&d.length)d.forEach((t=>{s.push(`[${t[0]}="${t[1]}"]`)}));else if(n.id&&s.push(`#${n.id}`),i=n.className,i&&(0,r.HD)(i))for(a=i.split(/\s+/),p=0;p<a.length;p++)s.push(`.${a[p]}`);const f=["aria-label","type","name","title","alt"];for(p=0;p<f.length;p++)c=f[p],u=n.getAttribute(c),u&&s.push(`[${c}="${u}"]`);return s.join("")}function a(){try{return o.document.location.href}catch(t){return""}}function c(t){return o.document&&o.document.querySelector?o.document.querySelector(t):null}function u(t){if(!o.HTMLElement)return null;let e=t;for(let n=0;n<5;n++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}},98643:function(t,e,n){function r(t,e){return null!=t?t:e()}n.d(e,{h:function(){return r}})},42174:function(t,e,n){function r(t){let e,n=t[0],r=1;for(;r<t.length;){const o=t[r],s=t[r+1];if(r+=2,("optionalAccess"===o||"optionalCall"===o)&&null==n)return;"access"===o||"optionalAccess"===o?(e=n,n=s(n)):"call"!==o&&"optionalCall"!==o||(n=s(((...t)=>n.call(e,...t))),e=void 0)}return n}n.d(e,{x:function(){return r}})},20570:function(t,e,n){n.d(e,{X:function(){return r}});const r="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__},86630:function(t,e,n){n.d(e,{RA:function(){return i},vK:function(){return c}});var r=n(20570),o=n(46616);const s=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function i(t,e=!1){const{host:n,path:r,pass:o,port:s,projectId:i,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&o?`:${o}`:""}@${n}${s?`:${s}`:""}/${r?`${r}/`:r}${i}`}function a(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function c(t){const e="string"===typeof t?function(t){const e=s.exec(t);if(!e)return void(0,o.Cf)((()=>{console.error(`Invalid Sentry Dsn: ${t}`)}));const[n,r,i="",c,u="",p]=e.slice(1);let d="",f=p;const l=f.split("/");if(l.length>1&&(d=l.slice(0,-1).join("/"),f=l.pop()),f){const t=f.match(/^\d+/);t&&(f=t[0])}return a({host:c,pass:i,path:d,projectId:f,port:u,protocol:n,publicKey:r})}(t):a(t);if(e&&function(t){if(!r.X)return!0;const{port:e,projectId:n,protocol:s}=t;return!["protocol","publicKey","host","projectId"].find((e=>!t[e]&&(o.kg.error(`Invalid Sentry Dsn: ${e} missing`),!0)))&&(n.match(/^\d+$/)?function(t){return"http"===t||"https"===t}(s)?!e||!isNaN(parseInt(e,10))||(o.kg.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):(o.kg.error(`Invalid Sentry Dsn: Invalid protocol ${s}`),!1):(o.kg.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1))}(e))return e}},9896:function(t,e,n){function r(){return"undefined"!==typeof __SENTRY_BROWSER_BUNDLE__&&!!__SENTRY_BROWSER_BUNDLE__}function o(){return"npm"}n.d(e,{S:function(){return o},n:function(){return r}})},68571:function(t,e,n){n.d(e,{BO:function(){return c},zQ:function(){return l},Jd:function(){return a},Cd:function(){return _},KQ:function(){return f},mL:function(){return m},gv:function(){return u},HY:function(){return g},V$:function(){return d}});var r=n(86630),o=n(2615),s=n(88545),i=n(55793);function a(t,e=[]){return[t,e]}function c(t,e){const[n,r]=t;return[n,[...r,e]]}function u(t,e){const n=t[1];for(const r of n){if(e(r,r[0].type))return!0}return!1}function p(t){return i.n.__SENTRY__&&i.n.__SENTRY__.encodePolyfill?i.n.__SENTRY__.encodePolyfill(t):(new TextEncoder).encode(t)}function d(t){const[e,n]=t;let r=JSON.stringify(e);function s(t){"string"===typeof r?r="string"===typeof t?r+t:[p(r),t]:r.push("string"===typeof t?p(t):t)}for(const a of n){const[t,e]=a;if(s(`\n${JSON.stringify(t)}\n`),"string"===typeof e||e instanceof Uint8Array)s(e);else{let t;try{t=JSON.stringify(e)}catch(i){t=JSON.stringify((0,o.Fv)(e))}s(t)}}return"string"===typeof r?r:function(t){const e=t.reduce(((t,e)=>t+e.length),0),n=new Uint8Array(e);let r=0;for(const o of t)n.set(o,r),r+=o.length;return n}(r)}function f(t){return[{type:"span"},t]}function l(t){const e="string"===typeof t.data?p(t.data):t.data;return[(0,s.Jr)({type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),e]}const h={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function m(t){return h[t]}function g(t){if(!t||!t.sdk)return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function _(t,e,n,o){const i=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&{sdk:e},...!!n&&o&&{dsn:(0,r.RA)(o)},...i&&{trace:(0,s.Jr)({...i})}}}},67567:function(t,e,n){n.d(e,{U:function(){return a}});var r=n(88545),o=n(38794),s=n(55793),i=n(63224);function a(t){const e="fetch";(0,i.Hj)(e,t),(0,i.D2)(e,c)}function c(){(0,o.t$)()&&(0,r.hl)(s.n,"fetch",(function(t){return function(...e){const{method:n,url:r}=function(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){const[e,n]=t;return{url:p(e),method:u(n,"method")?String(n.method).toUpperCase():"GET"}}const e=t[0];return{url:p(e),method:u(e,"method")?String(e.method).toUpperCase():"GET"}}(e),o={args:e,fetchData:{method:n,url:r},startTimestamp:Date.now()};return(0,i.rK)("fetch",{...o}),t.apply(s.n,e).then((t=>{const e={...o,endTimestamp:Date.now(),response:t};return(0,i.rK)("fetch",e),t}),(t=>{const e={...o,endTimestamp:Date.now(),error:t};throw(0,i.rK)("fetch",e),t}))}}))}function u(t,e){return!!t&&"object"===typeof t&&!!t[e]}function p(t){return"string"===typeof t?t:t?u(t,"url")?t.url:t.toString?t.toString():"":""}},98522:function(t,e,n){n.d(e,{V:function(){return i}});var r=n(55793),o=n(63224);let s=null;function i(t){const e="error";(0,o.Hj)(e,t),(0,o.D2)(e,a)}function a(){s=r.n.onerror,r.n.onerror=function(t,e,n,r,i){const a={column:r,error:i,line:n,msg:t,url:e};return(0,o.rK)("error",a),!(!s||s.__SENTRY_LOADER__)&&s.apply(this,arguments)},r.n.onerror.__SENTRY_INSTRUMENTED__=!0}},51176:function(t,e,n){n.d(e,{h:function(){return i}});var r=n(55793),o=n(63224);let s=null;function i(t){const e="unhandledrejection";(0,o.Hj)(e,t),(0,o.D2)(e,a)}function a(){s=r.n.onunhandledrejection,r.n.onunhandledrejection=function(t){const e=t;return(0,o.rK)("unhandledrejection",e),!(s&&!s.__SENTRY_LOADER__)||s.apply(this,arguments)},r.n.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}},63224:function(t,e,n){n.d(e,{Hj:function(){return c},D2:function(){return u},rK:function(){return p}});var r=n(20570),o=n(46616),s=n(26930);const i={},a={};function c(t,e){i[t]=i[t]||[],i[t].push(e)}function u(t,e){a[t]||(e(),a[t]=!0)}function p(t,e){const n=t&&i[t];if(n)for(const i of n)try{i(e)}catch(a){r.X&&o.kg.error(`Error while triggering instrumentation handler.\nType: ${t}\nName: ${(0,s.$P)(i)}\nError:`,a)}}},47701:function(t,e,n){n.d(e,{TX:function(){return a},fm:function(){return c},kK:function(){return h},VZ:function(){return o},VW:function(){return i},cO:function(){return l},V9:function(){return v},Le:function(){return p},PO:function(){return f},pt:function(){return d},Kj:function(){return m},HD:function(){return u},Cy:function(){return _},J8:function(){return g},y1:function(){return y}});const r=Object.prototype.toString;function o(t){switch(r.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return v(t,Error)}}function s(t,e){return r.call(t)===`[object ${e}]`}function i(t){return s(t,"ErrorEvent")}function a(t){return s(t,"DOMError")}function c(t){return s(t,"DOMException")}function u(t){return s(t,"String")}function p(t){return"object"===typeof t&&null!==t&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function d(t){return null===t||p(t)||"object"!==typeof t&&"function"!==typeof t}function f(t){return s(t,"Object")}function l(t){return"undefined"!==typeof Event&&v(t,Event)}function h(t){return"undefined"!==typeof Element&&v(t,Element)}function m(t){return s(t,"RegExp")}function g(t){return Boolean(t&&t.then&&"function"===typeof t.then)}function _(t){return f(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function v(t,e){try{return t instanceof e}catch(n){return!1}}function y(t){return!("object"!==typeof t||null===t||!t.__isVue&&!t._isVue)}},99987:function(t,e,n){n.d(e,{j:function(){return s}});var r=n(9896);var o=n(55793);function s(){return"undefined"!==typeof window&&(!(!(0,r.n)()&&"[object process]"===Object.prototype.toString.call("undefined"!==typeof process?process:0))||void 0!==o.n.process&&"renderer"===o.n.process.type)}},46616:function(t,e,n){n.d(e,{RU:function(){return s},Cf:function(){return a},kg:function(){return c},LD:function(){return i}});var r=n(20570),o=n(55793);const s=["debug","info","warn","error","log","assert","trace"],i={};function a(t){if(!("console"in o.n))return t();const e=o.n.console,n={},r=Object.keys(i);r.forEach((t=>{const r=i[t];n[t]=e[t],e[t]=r}));try{return t()}finally{r.forEach((t=>{e[t]=n[t]}))}}const c=function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return r.X?s.forEach((n=>{e[n]=(...e)=>{t&&a((()=>{o.n.console[n](`Sentry Logger [${n}]:`,...e)}))}})):s.forEach((t=>{e[t]=()=>{}})),e}()},34100:function(t,e,n){n.d(e,{EG:function(){return u},Db:function(){return c},lE:function(){return d},YO:function(){return p},jH:function(){return a},DM:function(){return s}});var r=n(88545),o=n(55793);function s(){const t=o.n,e=t.crypto||t.msCrypto;let n=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>{const t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(r){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&n())>>t/4).toString(16)))}function i(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function a(t){const{message:e,event_id:n}=t;if(e)return e;const r=i(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function c(t,e,n){const r=t.exception=t.exception||{},o=r.values=r.values||[],s=o[0]=o[0]||{};s.value||(s.value=e||""),s.type||(s.type=n||"Error")}function u(t,e){const n=i(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const t={...r&&r.data,...e.data};n.mechanism.data=t}}function p(t){if(t&&t.__sentry_captured__)return!0;try{(0,r.xp)(t,"__sentry_captured__",!0)}catch(e){}return!1}function d(t){return Array.isArray(t)?t:[t]}},2615:function(t,e,n){n.d(e,{Fv:function(){return i},Qy:function(){return a}});var r=n(47701);var o=n(88545),s=n(26930);function i(t,e=100,n=1/0){try{return c("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function a(t,e=3,n=102400){const r=i(t,e);return o=r,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(o))>n?a(t,e-1,n):r;var o}function c(t,e,n=1/0,i=1/0,a=function(){const t="function"===typeof WeakSet,e=t?new WeakSet:[];return[function(n){if(t)return!!e.has(n)||(e.add(n),!1);for(let t=0;t<e.length;t++)if(e[t]===n)return!0;return e.push(n),!1},function(n){if(t)e.delete(n);else for(let t=0;t<e.length;t++)if(e[t]===n){e.splice(t,1);break}}]}()){const[u,p]=a;if(null==e||["number","boolean","string"].includes(typeof e)&&!Number.isNaN(e))return e;const d=function(t,e){try{if("domain"===t&&e&&"object"===typeof e&&e._events)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!==typeof global&&e===global)return"[Global]";if("undefined"!==typeof window&&e===window)return"[Window]";if("undefined"!==typeof document&&e===document)return"[Document]";if((0,r.y1)(e))return"[VueViewModel]";if((0,r.Cy)(e))return"[SyntheticEvent]";if("number"===typeof e&&e!==e)return"[NaN]";if("function"===typeof e)return`[Function: ${(0,s.$P)(e)}]`;if("symbol"===typeof e)return`[${String(e)}]`;if("bigint"===typeof e)return`[BigInt: ${String(e)}]`;const n=function(t){const e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}(e);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}(t,e);if(!d.startsWith("[object "))return d;if(e.__sentry_skip_normalization__)return e;const f="number"===typeof e.__sentry_override_normalization_depth__?e.__sentry_override_normalization_depth__:n;if(0===f)return d.replace("object ","");if(u(e))return"[Circular ~]";const l=e;if(l&&"function"===typeof l.toJSON)try{return c("",l.toJSON(),f-1,i,a)}catch(_){}const h=Array.isArray(e)?[]:{};let m=0;const g=(0,o.Sh)(e);for(const r in g){if(!Object.prototype.hasOwnProperty.call(g,r))continue;if(m>=i){h[r]="[MaxProperties ~]";break}const t=g[r];h[r]=c(r,t,f-1,i,a),m++}return p(e),h}},88545:function(t,e,n){n.d(e,{xp:function(){return u},Sh:function(){return l},Jr:function(){return _},zf:function(){return g},hl:function(){return c},HK:function(){return d},$Q:function(){return p},_j:function(){return f}});var r=n(28169),o=n(20570),s=n(47701),i=n(46616),a=n(50712);function c(t,e,n){if(!(e in t))return;const r=t[e],o=n(r);"function"===typeof o&&p(o,r),t[e]=o}function u(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch(r){o.X&&i.kg.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function p(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,u(t,"__sentry_original__",e)}catch(n){}}function d(t){return t.__sentry_original__}function f(t){return Object.keys(t).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`)).join("&")}function l(t){if((0,s.VZ)(t))return{message:t.message,name:t.name,stack:t.stack,...m(t)};if((0,s.cO)(t)){const e={type:t.type,target:h(t.target),currentTarget:h(t.currentTarget),...m(t)};return"undefined"!==typeof CustomEvent&&(0,s.V9)(t,CustomEvent)&&(e.detail=t.detail),e}return t}function h(t){try{return(0,s.kK)(t)?(0,r.Rt)(t):Object.prototype.toString.call(t)}catch(e){return"<unknown>"}}function m(t){if("object"===typeof t&&null!==t){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function g(t,e=40){const n=Object.keys(l(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return(0,a.$G)(n[0],e);for(let r=n.length;r>0;r--){const t=n.slice(0,r).join(", ");if(!(t.length>e))return r===n.length?t:(0,a.$G)(t,e)}return""}function _(t){return v(t,new Map)}function v(t,e){if(function(t){if(!(0,s.PO)(t))return!1;try{const e=Object.getPrototypeOf(t).constructor.name;return!e||"Object"===e}catch(e){return!0}}(t)){const n=e.get(t);if(void 0!==n)return n;const r={};e.set(t,r);for(const o of Object.keys(t))"undefined"!==typeof t[o]&&(r[o]=v(t[o],e));return r}if(Array.isArray(t)){const n=e.get(t);if(void 0!==n)return n;const r=[];return e.set(t,r),t.forEach((t=>{r.push(v(t,e))})),r}return t}},29796:function(t,e,n){n.d(e,{Q:function(){return r},WG:function(){return o}});function r(t,e,n=Date.now()){return function(t,e){return t[e]||t.all||0}(t,e)>n}function o(t,{statusCode:e,headers:n},r=Date.now()){const o={...t},s=n&&n["x-sentry-rate-limits"],i=n&&n["retry-after"];if(s)for(const a of s.trim().split(",")){const[t,e,,,n]=a.split(":",5),s=parseInt(t,10),i=1e3*(isNaN(s)?60:s);if(e)for(const a of e.split(";"))"metric_bucket"===a&&n&&!n.split(";").includes("custom")||(o[a]=r+i);else o.all=r+i}else i?o.all=r+function(t,e=Date.now()){const n=parseInt(`${t}`,10);if(!isNaN(n))return 1e3*n;const r=Date.parse(`${t}`);return isNaN(r)?6e4:r-e}(i,r):429===e&&(o.all=r+6e4);return o}},26930:function(t,e,n){n.d(e,{Fi:function(){return r},pE:function(){return i},$P:function(){return u},Sq:function(){return a}});const r="?",o=/\(error: (.*)\)/,s=/captureMessage|captureException/;function i(...t){const e=t.sort(((t,e)=>t[0]-e[0])).map((t=>t[1]));return(t,n=0,i=0)=>{const a=[],c=t.split("\n");for(let r=n;r<c.length;r++){const t=c[r];if(t.length>1024)continue;const n=o.test(t)?t.replace(o,"$1"):t;if(!n.match(/\S*Error: /)){for(const t of e){const e=t(n);if(e){a.push(e);break}}if(a.length>=50+i)break}}return function(t){if(!t.length)return[];const e=Array.from(t);/sentryWrapped/.test(e[e.length-1].function||"")&&e.pop();e.reverse(),s.test(e[e.length-1].function||"")&&(e.pop(),s.test(e[e.length-1].function||"")&&e.pop());return e.slice(0,50).map((t=>({...t,filename:t.filename||e[e.length-1].filename,function:t.function||r})))}(a.slice(i))}}function a(t){return Array.isArray(t)?i(...t):t}const c="<anonymous>";function u(t){try{return t&&"function"===typeof t&&t.name||c}catch(e){return c}}},50712:function(t,e,n){n.d(e,{nK:function(){return s},U0:function(){return i},$G:function(){return o}});var r=n(47701);function o(t,e=0){return"string"!==typeof t||0===e||t.length<=e?t:`${t.slice(0,e)}...`}function s(t,e){if(!Array.isArray(t))return"";const n=[];for(let s=0;s<t.length;s++){const e=t[s];try{(0,r.y1)(e)?n.push("[VueViewModel]"):n.push(String(e))}catch(o){n.push("[value cannot be serialized]")}}return n.join(e)}function i(t,e=[],n=!1){return e.some((e=>function(t,e,n=!1){return!!(0,r.HD)(t)&&((0,r.Kj)(e)?e.test(t):!!(0,r.HD)(e)&&(n?t===e:t.includes(e)))}(t,e,n)))}},38794:function(t,e,n){n.d(e,{Du:function(){return a},Ak:function(){return i},t$:function(){return c}});var r=n(20570),o=n(46616);const s=n(55793).n;function i(){if(!("fetch"in s))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}function a(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function c(){if("string"===typeof EdgeRuntime)return!0;if(!i())return!1;if(a(s.fetch))return!0;let t=!1;const e=s.document;if(e&&"function"===typeof e.createElement)try{const n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=a(n.contentWindow.fetch)),e.head.removeChild(n)}catch(n){r.X&&o.kg.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return t}},10300:function(t,e,n){n.d(e,{cW:function(){return a},$2:function(){return i},WD:function(){return s}});var r,o=n(47701);function s(t){return new a((e=>{e(t)}))}function i(t){return new a(((e,n)=>{n(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(r||(r={}));class a{constructor(t){a.prototype.__init.call(this),a.prototype.__init2.call(this),a.prototype.__init3.call(this),a.prototype.__init4.call(this),this._state=r.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(e){this._reject(e)}}then(t,e){return new a(((n,r)=>{this._handlers.push([!1,e=>{if(t)try{n(t(e))}catch(o){r(o)}else n(e)},t=>{if(e)try{n(e(t))}catch(o){r(o)}else r(t)}]),this._executeHandlers()}))}catch(t){return this.then((t=>t),t)}finally(t){return new a(((e,n)=>{let r,o;return this.then((e=>{o=!1,r=e,t&&t()}),(e=>{o=!0,r=e,t&&t()})).then((()=>{o?n(r):e(r)}))}))}__init(){this._resolve=t=>{this._setResult(r.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(r.REJECTED,t)}}__init3(){this._setResult=(t,e)=>{this._state===r.PENDING&&((0,o.J8)(e)?e.then(this._resolve,this._reject):(this._state=t,this._value=e,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===r.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach((t=>{t[0]||(this._state===r.RESOLVED&&t[1](this._value),this._state===r.REJECTED&&t[2](this._value),t[0]=!0)}))}}}},22519:function(t,e,n){n.d(e,{Z1:function(){return a},yW:function(){return o},ph:function(){return s}});var r=n(55793);function o(){return Date.now()/1e3}const s=function(){const{performance:t}=r.n;if(!t||!t.now)return o;const e=Date.now()-t.now(),n=void 0==t.timeOrigin?e:t.timeOrigin;return()=>(n+t.now())/1e3}();let i;const a=(()=>{const{performance:t}=r.n;if(!t||!t.now)return void(i="none");const e=36e5,n=t.now(),o=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-o):e,a=s<e,c=t.timing&&t.timing.navigationStart,u="number"===typeof c?Math.abs(c+n-o):e;return a||u<e?s<=u?(i="timeOrigin",t.timeOrigin):(i="navigationStart",c):(i="dateNow",o)})()},16847:function(t,e,n){n.d(e,{$p:function(){return a},pT:function(){return i}});var r=n(63426),o=n(34100);const s=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function i(t,e){const n=function(t){if(!t)return;const e=t.match(s);if(!e)return;let n;return"1"===e[3]?n=!0:"0"===e[3]&&(n=!1),{traceId:e[1],parentSampled:n,parentSpanId:e[2]}}(t),i=(0,r.EN)(e),{traceId:a,parentSpanId:c,parentSampled:u}=n||{};return n?{traceId:a||(0,o.DM)(),parentSpanId:c||(0,o.DM)().substring(16),spanId:(0,o.DM)().substring(16),sampled:u,dsc:i||{}}:{traceId:a||(0,o.DM)(),spanId:(0,o.DM)().substring(16)}}function a(t=(0,o.DM)(),e=(0,o.DM)().substring(16),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${t}-${e}${r}`}},486:function(t,e,n){function r(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}n.d(e,{en:function(){return r}})},55192:function(t,e,n){n.d(e,{B:function(){return o}});const r=n(55793).n;function o(){const t=r.chrome,e=t&&t.app&&t.app.runtime,n="history"in r&&!!r.history.pushState&&!!r.history.replaceState;return!e&&n}},55793:function(t,e,n){n.d(e,{n:function(){return r},Y:function(){return o}});const r=globalThis;function o(t,e,n){const o=n||r,s=o.__SENTRY__=o.__SENTRY__||{};return s[t]||(s[t]=e())}}}]);
//# sourceMappingURL=@sentry.a602d18403fc99fe3c0cf034ade6b129.js.map