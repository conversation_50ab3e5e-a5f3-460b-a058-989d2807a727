"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@sentry-internal"],{8946:function(e,t,n){n.d(t,{X:function(){return r}});const r="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__},43218:function(e,t,n){n.d(t,{O:function(){return u}});var r=n(63224),s=n(88545),o=n(34100),i=n(89273);let a,c,l;function u(e){(0,r.Hj)("dom",e),(0,r.D2)("dom",d)}function d(){if(!i.m.document)return;const e=r.rK.bind(null,"dom"),t=h(e,!0);i.m.document.addEventListener("click",t,!1),i.m.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach((t=>{const n=i.m[t]&&i.m[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&((0,s.hl)(n,"addEventListener",(function(t){return function(n,r,s){if("click"===n||"keypress"==n)try{const r=this,o=r.__sentry_instrumentation_handlers__=r.__sentry_instrumentation_handlers__||{},i=o[n]=o[n]||{refCount:0};if(!i.handler){const r=h(e);i.handler=r,t.call(this,n,r,s)}i.refCount++}catch(o){}return t.call(this,n,r,s)}})),(0,s.hl)(n,"removeEventListener",(function(e){return function(t,n,r){if("click"===t||"keypress"==t)try{const n=this,s=n.__sentry_instrumentation_handlers__||{},o=s[t];o&&(o.refCount--,o.refCount<=0&&(e.call(this,t,o.handler,r),o.handler=void 0,delete s[t]),0===Object.keys(s).length&&delete n.__sentry_instrumentation_handlers__)}catch(s){}return e.call(this,t,n,r)}})))}))}function h(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(e){try{return e.target}catch(t){return null}}(n);if(function(e,t){return"keypress"===e&&(!t||!t.tagName||"INPUT"!==t.tagName&&"TEXTAREA"!==t.tagName&&!t.isContentEditable)}(n.type,r))return;(0,s.xp)(n,"_sentryCaptured",!0),r&&!r._sentryId&&(0,s.xp)(r,"_sentryId",(0,o.DM)());const u="keypress"===n.type?"input":n.type;if(!function(e){if(e.type!==c)return!1;try{if(!e.target||e.target._sentryId!==l)return!1}catch(t){}return!0}(n)){e({event:n,name:u,global:t}),c=n.type,l=r?r._sentryId:void 0}clearTimeout(a),a=i.m.setTimeout((()=>{l=void 0,c=void 0}),1e3)}}},52472:function(e,t,n){n.d(t,{a:function(){return c}});var r=n(63224),s=n(55192),o=n(88545),i=n(89273);let a;function c(e){const t="history";(0,r.Hj)(t,e),(0,r.D2)(t,l)}function l(){if(!(0,s.B)())return;const e=i.m.onpopstate;function t(e){return function(...t){const n=t.length>2?t[2]:void 0;if(n){const e=a,t=String(n);a=t;const s={from:e,to:t};(0,r.rK)("history",s)}return e.apply(this,t)}}i.m.onpopstate=function(...t){const n=i.m.location.href,s=a;a=n;const o={from:s,to:n};if((0,r.rK)("history",o),e)try{return e.apply(this,t)}catch(c){}},(0,o.hl)(i.m.history,"pushState",t),(0,o.hl)(i.m.history,"replaceState",t)}},41048:function(e,t,n){n.d(t,{xU:function(){return c},UK:function(){return l}});var r=n(63224),s=n(88545),o=n(22519),i=n(47701),a=n(89273);const c="__sentry_xhr_v3__";function l(e){(0,r.Hj)("xhr",e),(0,r.D2)("xhr",u)}function u(){if(!a.m.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;(0,s.hl)(e,"open",(function(e){return function(...t){const n=1e3*(0,o.ph)(),a=(0,i.HD)(t[0])?t[0].toUpperCase():void 0,l=function(e){if((0,i.HD)(e))return e;try{return e.toString()}catch(t){}return}(t[1]);if(!a||!l)return e.apply(this,t);this[c]={method:a,url:l,request_headers:{}},"POST"===a&&l.match(/sentry_key/)&&(this.__sentry_own_request__=!0);const u=()=>{const e=this[c];if(e&&4===this.readyState){try{e.status_code=this.status}catch(t){}const s={endTimestamp:1e3*(0,o.ph)(),startTimestamp:n,xhr:this};(0,r.rK)("xhr",s)}};return"onreadystatechange"in this&&"function"===typeof this.onreadystatechange?(0,s.hl)(this,"onreadystatechange",(function(e){return function(...t){return u(),e.apply(this,t)}})):this.addEventListener("readystatechange",u),(0,s.hl)(this,"setRequestHeader",(function(e){return function(...t){const[n,r]=t,s=this[c];return s&&(0,i.HD)(n)&&(0,i.HD)(r)&&(s.request_headers[n.toLowerCase()]=r),e.apply(this,t)}})),e.apply(this,t)}})),(0,s.hl)(e,"send",(function(e){return function(...t){const n=this[c];if(!n)return e.apply(this,t);void 0!==t[0]&&(n.body=t[0]);const s={startTimestamp:1e3*(0,o.ph)(),xhr:this};return(0,r.rK)("xhr",s),e.apply(this,t)}}))}},48434:function(e,t,n){n.d(t,{f7:function(){return I},sn:function(){return w},Fv:function(){return b},PR:function(){return _}});var r=n(74042),s=n(49839),o=n(17670),i=n(2511),a=n(22519),c=n(28169),l=n(46616),u=n(486),d=n(8946),h=n(48312),p=n(89273),m=n(40461),f=n(95923),y=n(84633);let g,k,v=0,S={};function _(){const e=(0,m.QV)();if(e&&a.Z1){e.mark&&p.m.performance.mark("sentry-tracing-init");const t=(0,h.to)((({metric:e})=>{const t=e.entries[e.entries.length-1];if(!t)return;const n=(0,m.XL)(a.Z1),r=(0,m.XL)(t.startTime);d.X&&l.kg.log("[Measurements] Adding FID"),S.fid={value:e.value,unit:"millisecond"},S["mark.fid"]={value:n+r,unit:"second"}})),n=(0,h.PR)((({metric:e})=>{const t=e.entries[e.entries.length-1];t&&(d.X&&l.kg.log("[Measurements] Adding CLS"),S.cls={value:e.value,unit:""},k=t)}),!0),r=(0,h.$A)((({metric:e})=>{const t=e.entries[e.entries.length-1];t&&(d.X&&l.kg.log("[Measurements] Adding LCP"),S.lcp={value:e.value,unit:"millisecond"},g=t)}),!0),s=(0,h._4)((({metric:e})=>{e.entries[e.entries.length-1]&&(d.X&&l.kg.log("[Measurements] Adding TTFB"),S.ttfb={value:e.value,unit:"millisecond"})}));return()=>{t(),n(),r(),s()}}return()=>{}}function b(){(0,h._j)("longtask",(({entries:e})=>{for(const t of e){if(!(0,r.HN)())return;const e=(0,m.XL)(a.Z1+t.startTime),n=(0,m.XL)(t.duration),i=(0,s.qp)({name:"Main UI thread blocked",op:"ui.long-task",startTime:e,attributes:{[o.S3]:"auto.ui.browser.metrics"}});i&&i.end(e+n)}}))}function w(){(0,h._j)("event",(({entries:e})=>{for(const t of e){if(!(0,r.HN)())return;if("click"===t.name){const e=(0,m.XL)(a.Z1+t.startTime),n=(0,m.XL)(t.duration),r={name:(0,c.Rt)(t.target),op:`ui.interaction.${t.name}`,startTime:e,attributes:{[o.S3]:"auto.ui.browser.metrics"}},i=(0,c.iY)(t.target);i&&(r.attributes["ui.component_name"]=i);const l=(0,s.qp)(r);l&&l.end(e+n)}}}))}function I(e){const t=(0,m.QV)();if(!t||!p.m.performance.getEntries||!a.Z1)return;d.X&&l.kg.log("[Tracing] Adding & adjusting spans using Performance API");const n=(0,m.XL)(a.Z1),s=t.getEntries(),{op:h,start_timestamp:_}=(0,r.XU)(e);if(s.slice(v).forEach((t=>{const r=(0,m.XL)(t.startTime),s=(0,m.XL)(t.duration);if(!("navigation"===h&&_&&n+r<_))switch(t.entryType){case"navigation":!function(e,t,n){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((r=>{T(e,t,r,n)})),T(e,t,"secureConnection",n,"TLS/SSL","connectEnd"),T(e,t,"fetch",n,"cache","domainLookupStart"),T(e,t,"domainLookup",n,"DNS"),function(e,t,n){t.responseEnd&&((0,m.Y)(e,n+(0,m.XL)(t.requestStart),n+(0,m.XL)(t.responseEnd),{op:"browser",name:"request",attributes:{[o.S3]:"auto.ui.browser.metrics"}}),(0,m.Y)(e,n+(0,m.XL)(t.responseStart),n+(0,m.XL)(t.responseEnd),{op:"browser",name:"response",attributes:{[o.S3]:"auto.ui.browser.metrics"}}))}(e,t,n)}(e,t,n);break;case"mark":case"paint":case"measure":{!function(e,t,n,r,s){const i=s+n,a=i+r;(0,m.Y)(e,i,a,{name:t.name,op:t.entryType,attributes:{[o.S3]:"auto.resource.browser.metrics"}})}(e,t,r,s,n);const i=(0,y.Y)(),a=t.startTime<i.firstHiddenTime;"first-paint"===t.name&&a&&(d.X&&l.kg.log("[Measurements] Adding FP"),S.fp={value:t.startTime,unit:"millisecond"}),"first-contentful-paint"===t.name&&a&&(d.X&&l.kg.log("[Measurements] Adding FCP"),S.fcp={value:t.startTime,unit:"millisecond"});break}case"resource":!function(e,t,n,r,s,i){if("xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType)return;const a=(0,u.en)(n),c={[o.S3]:"auto.resource.browser.metrics"};C(c,t,"transferSize","http.response_transfer_size"),C(c,t,"encodedBodySize","http.response_content_length"),C(c,t,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in t&&(c["resource.render_blocking_status"]=t.renderBlockingStatus);a.protocol&&(c["url.scheme"]=a.protocol.split(":").pop());a.host&&(c["server.address"]=a.host);c["url.same_origin"]=n.includes(p.m.location.origin);const l=i+r,d=l+s;(0,m.Y)(e,l,d,{name:n.replace(p.m.location.origin,""),op:t.initiatorType?`resource.${t.initiatorType}`:"resource.other",attributes:c})}(e,t,t.name,r,s,n)}})),v=Math.max(s.length-1,0),function(e){const t=p.m.navigator;if(!t)return;const n=t.connection;n&&(n.effectiveType&&e.setAttribute("effectiveConnectionType",n.effectiveType),n.type&&e.setAttribute("connectionType",n.type),(0,m.nl)(n.rtt)&&(S["connection.rtt"]={value:n.rtt,unit:"millisecond"}));(0,m.nl)(t.deviceMemory)&&e.setAttribute("deviceMemory",`${t.deviceMemory} GB`);(0,m.nl)(t.hardwareConcurrency)&&e.setAttribute("hardwareConcurrency",String(t.hardwareConcurrency))}(e),"pageload"===h){!function(e){const t=(0,f.W)();if(!t)return;const{responseStart:n,requestStart:r}=t;r<=n&&(d.X&&l.kg.log("[Measurements] Adding TTFB Request Time"),e["ttfb.requestTime"]={value:n-r,unit:"millisecond"})}(S),["fcp","fp","lcp"].forEach((e=>{if(!S[e]||!_||n>=_)return;const t=S[e].value,r=n+(0,m.XL)(t),s=Math.abs(1e3*(r-_)),o=s-t;d.X&&l.kg.log(`[Measurements] Normalized ${e} from ${t} to ${s} (${o})`),S[e].value=s}));const t=S["mark.fid"];t&&S.fid&&((0,m.Y)(e,t.value,t.value+(0,m.XL)(S.fid.value),{name:"first input delay",op:"ui.action",attributes:{[o.S3]:"auto.ui.browser.metrics"}}),delete S["mark.fid"]),"fcp"in S||delete S.cls,Object.keys(S).forEach((e=>{(0,i.o)(e,S[e].value,S[e].unit)})),function(e){g&&(d.X&&l.kg.log("[Measurements] Adding LCP Data"),g.element&&e.setAttribute("lcp.element",(0,c.Rt)(g.element)),g.id&&e.setAttribute("lcp.id",g.id),g.url&&e.setAttribute("lcp.url",g.url.trim().slice(0,200)),e.setAttribute("lcp.size",g.size));k&&k.sources&&(d.X&&l.kg.log("[Measurements] Adding CLS Data"),k.sources.forEach(((t,n)=>e.setAttribute(`cls.source.${n+1}`,(0,c.Rt)(t.node)))))}(e)}g=void 0,k=void 0,S={}}function T(e,t,n,r,s,i){const a=i?t[i]:t[`${n}End`],c=t[`${n}Start`];c&&a&&(0,m.Y)(e,r+(0,m.XL)(c),r+(0,m.XL)(a),{op:"browser",name:s||n,attributes:{[o.S3]:"auto.ui.browser.metrics"}})}function C(e,t,n,r){const s=t[n];null!=s&&s<2147483647&&(e[r]=s)}},52550:function(e,t,n){n.d(t,{N:function(){return p}});var r=n(42174),s=n(50087),o=n(74042),i=n(17670),a=n(49839),c=n(22519),l=n(28169),u=n(88545),d=n(48312),h=n(40461);function p(){if((0,h.QV)()&&c.Z1){const e=(0,d.YF)((({metric:e})=>{const t=(0,s.s3)();if(!t||void 0==e.value)return;const n=e.entries.find((t=>t.duration===e.value&&m[t.name]));if(!n)return;const d=m[n.name],p=t.getOptions(),f=(0,h.XL)(c.Z1+n.startTime),y=(0,h.XL)(e.value),g=(0,s.nZ)(),k=(0,o.HN)(),v=k?(0,o.Gx)(k):void 0,S=v?(0,o.XU)(v).description:void 0,_=g.getUser(),b=t.getIntegrationByName("Replay"),w=b&&b.getReplayId(),I=void 0!==_?_.email||_.id||_.ip_address:void 0,T=(0,r.x)([g,"access",e=>e.getScopeData,"call",e=>e(),"access",e=>e.contexts,"optionalAccess",e=>e.profile,"optionalAccess",e=>e.profile_id]),C=(0,l.Rt)(n.target),E=(0,u.Jr)({release:p.release,environment:p.environment,transaction:S,[i.JQ]:e.value,user:I||void 0,profile_id:T||void 0,replay_id:w||void 0}),x=(0,a.qp)({name:C,op:`ui.interaction.${d}`,attributes:E,startTime:f,experimental:{standalone:!0}});x.addEvent("inp",{[i.E1]:"millisecond",[i.Wb]:e.value}),x.end(f+y)}));return()=>{e()}}return()=>{}}const m={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"}},48312:function(e,t,n){n.d(t,{PR:function(){return j},to:function(){return q},YF:function(){return K},$A:function(){return $},_j:function(){return V},_4:function(){return X}});var r=n(46616),s=n(26930),o=n(8946);const i=(e,t,n,r)=>{let s,o;return i=>{t.value>=0&&(i||r)&&(o=t.value-(s||0),(o||void 0===s)&&(s=t.value,t.delta=o,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,n),e(t)))}};var a=n(89273);var c=n(95923);const l=()=>{const e=(0,c.W)();return e&&e.activationStart||0},u=(e,t)=>{const n=(0,c.W)();let r="navigate";n&&(a.m.document&&a.m.document.prerendering||l()>0?r="prerender":a.m.document&&a.m.document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-")));return{name:e,value:"undefined"===typeof t?-1:t,rating:"good",delta:0,entries:[],id:`v3-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},d=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(r){}},h=e=>{const t=t=>{("pagehide"===t.type||a.m.document&&"hidden"===a.m.document.visibilityState)&&e(t)};a.m.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},p=e=>{let t=!1;return n=>{t||(e(n),t=!0)}};var m=n(84633);const f=e=>{a.m.document&&a.m.document.prerendering?addEventListener("prerenderingchange",(()=>e()),!0):e()},y=[1800,3e3],g=[.1,.25],k=(e,t={})=>{((e,t={})=>{f((()=>{const n=(0,m.Y)(),r=u("FCP");let s;const o=d("paint",(e=>{e.forEach((e=>{"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<n.firstHiddenTime&&(r.value=Math.max(e.startTime-l(),0),r.entries.push(e),s(!0)))}))}));o&&(s=i(e,r,y,t.reportAllChanges))}))})(p((()=>{const n=u("CLS",0);let r,s=0,o=[];const a=e=>{e.forEach((e=>{if(!e.hadRecentInput){const t=o[0],n=o[o.length-1];s&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(s+=e.value,o.push(e)):(s=e.value,o=[e])}})),s>n.value&&(n.value=s,n.entries=o,r())},c=d("layout-shift",a);c&&(r=i(e,n,g,t.reportAllChanges),h((()=>{a(c.takeRecords()),r(!0)})),setTimeout(r,0))})))},v=[100,300];let S=0,_=1/0,b=0;const w=e=>{e.forEach((e=>{e.interactionId&&(_=Math.min(_,e.interactionId),b=Math.max(b,e.interactionId),S=b?(b-_)/7+1:0)}))};let I;const T=()=>{"interactionCount"in performance||I||(I=d("event",w,{type:"event",buffered:!0,durationThreshold:0}))},C=[200,500],E=()=>(I?S:performance.interactionCount||0)-0,x=[],M={},R=e=>{const t=x[x.length-1],n=M[e.interactionId];if(n||x.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{const t={id:e.interactionId,latency:e.duration,entries:[e]};M[t.id]=t,x.push(t)}x.sort(((e,t)=>t.latency-e.latency)),x.splice(10).forEach((e=>{delete M[e.id]}))}},A=(e,t={})=>{f((()=>{T();const n=u("INP");let r;const s=e=>{e.forEach((e=>{if(e.interactionId&&R(e),"first-input"===e.entryType){!x.some((t=>t.entries.some((t=>e.duration===t.duration&&e.startTime===t.startTime))))&&R(e)}}));const t=(()=>{const e=Math.min(x.length-1,Math.floor(E()/50));return x[e]})();t&&t.latency!==n.value&&(n.value=t.latency,n.entries=t.entries,r())},o=d("event",s,{durationThreshold:null!=t.durationThreshold?t.durationThreshold:40});r=i(e,n,C,t.reportAllChanges),o&&("PerformanceEventTiming"in a.m&&"interactionId"in PerformanceEventTiming.prototype&&o.observe({type:"first-input",buffered:!0}),h((()=>{s(o.takeRecords()),n.value<0&&E()>0&&(n.value=0,n.entries=[]),r(!0)})))}))},D=[2500,4e3],O={},L=[800,1800],N=e=>{a.m.document&&a.m.document.prerendering?f((()=>N(e))):a.m.document&&"complete"!==a.m.document.readyState?addEventListener("load",(()=>N(e)),!0):setTimeout(e,0)},F={},B={};let P,U,z,H,W;function j(e,t=!1){return te("cls",e,J,P,t)}function $(e,t=!1){return te("lcp",e,Q,z,t)}function q(e){return te("fid",e,G,U)}function X(e){return te("ttfb",e,Z,H)}function K(e){return te("inp",e,ee,W)}function V(e,t){return ne(e,t),B[e]||(!function(e){const t={};"event"===e&&(t.durationThreshold=0);d(e,(t=>{Y(e,{entries:t})}),t)}(e),B[e]=!0),re(e,t)}function Y(e,t){const n=F[e];if(n&&n.length)for(const a of n)try{a(t)}catch(i){o.X&&r.kg.error(`Error while triggering instrumentation handler.\nType: ${e}\nName: ${(0,s.$P)(a)}\nError:`,i)}}function J(){return k((e=>{Y("cls",{metric:e}),P=e}),{reportAllChanges:!0})}function G(){return((e,t={})=>{f((()=>{const n=(0,m.Y)(),r=u("FID");let s;const o=e=>{e.startTime<n.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),s(!0))},a=e=>{e.forEach(o)},c=d("first-input",a);s=i(e,r,v,t.reportAllChanges),c&&h(p((()=>{a(c.takeRecords()),c.disconnect()})))}))})((e=>{Y("fid",{metric:e}),U=e}))}function Q(){return((e,t={})=>{f((()=>{const n=(0,m.Y)(),r=u("LCP");let s;const o=e=>{const t=e[e.length-1];t&&t.startTime<n.firstHiddenTime&&(r.value=Math.max(t.startTime-l(),0),r.entries=[t],s())},c=d("largest-contentful-paint",o);if(c){s=i(e,r,D,t.reportAllChanges);const n=p((()=>{O[r.id]||(o(c.takeRecords()),c.disconnect(),O[r.id]=!0,s(!0))}));["keydown","click"].forEach((e=>{a.m.document&&addEventListener(e,(()=>setTimeout(n,0)),!0)})),h(n)}}))})((e=>{Y("lcp",{metric:e}),z=e}))}function Z(){return((e,t={})=>{const n=u("TTFB"),r=i(e,n,L,t.reportAllChanges);N((()=>{const e=(0,c.W)();if(e){const t=e.responseStart;if(t<=0||t>performance.now())return;n.value=Math.max(t-l(),0),n.entries=[e],r(!0)}}))})((e=>{Y("ttfb",{metric:e}),H=e}))}function ee(){return A((e=>{Y("inp",{metric:e}),W=e}))}function te(e,t,n,r,s=!1){let o;return ne(e,t),B[e]||(o=n(),B[e]=!0),r&&t({metric:r}),re(e,t,s?o:void 0)}function ne(e,t){F[e]=F[e]||[],F[e].push(t)}function re(e,t,n){return()=>{n&&n();const r=F[e];if(!r)return;const s=r.indexOf(t);-1!==s&&r.splice(s,1)}}},89273:function(e,t,n){n.d(t,{m:function(){return r}});const r=n(55793).n},40461:function(e,t,n){n.d(t,{QV:function(){return c},nl:function(){return i},XL:function(){return l},Y:function(){return a}});var r=n(74042),s=n(49839),o=n(89273);function i(e){return"number"===typeof e&&isFinite(e)}function a(e,t,n,{...o}){const i=(0,r.XU)(e).start_timestamp;return i&&i>t&&"function"===typeof e.updateStartTime&&e.updateStartTime(t),(0,s._d)(e,(()=>{const e=(0,s.qp)({startTime:t,...o});return e&&e.end(n),e}))}function c(){return o.m&&o.m.addEventListener&&o.m.performance}function l(e){return e/1e3}},95923:function(e,t,n){n.d(t,{W:function(){return s}});var r=n(89273);const s=()=>r.m.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},84633:function(e,t,n){n.d(t,{Y:function(){return i}});var r=n(89273);let s=-1;const o=e=>{"hidden"===r.m.document.visibilityState&&s>-1&&(s="visibilitychange"===e.type?e.timeStamp:0,removeEventListener("visibilitychange",o,!0),removeEventListener("prerenderingchange",o,!0))},i=()=>(r.m.document&&s<0&&(s="hidden"!==r.m.document.visibilityState||r.m.document.prerendering?1/0:0,addEventListener("visibilitychange",o,!0),addEventListener("prerenderingchange",o,!0)),{get firstHiddenTime(){return s}})},94082:function(e,t,n){n.d(t,{G:function(){return gr}});var r=n(98643),s=n(42174),o=n(34124),i=n(50087),a=n(19896),c=n(35506),l=n(43130),u=n(74042),d=n(17670),h=n(18057),p=n(55793),m=n(2615),f=n(88545),y=n(28169),g=n(22519),k=n(46616),v=n(34100),S=n(50712),_=n(68571),b=n(10300),w=n(29796),I=n(99987),T=n(48312),C=n(41048),E=n(43218),x=n(52472);const M=p.n,R="sentryReplaySession",A="Unable to send Replay",D=15e4,O=5e3,L=2e7,N=36e5;function F(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}var B;function P(e){const t=F([e,"optionalAccess",e=>e.host]);return Boolean(F([t,"optionalAccess",e=>e.shadowRoot])===e)}function U(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function z(e){try{const n=e.rules||e.cssRules;return n?((t=Array.from(n,H).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),t):null}catch(n){return null}var t}function H(e){let t;if(function(e){return"styleSheet"in e}(e))try{t=z(e.styleSheet)||function(e){const{cssText:t}=e;if(t.split('"').length<3)return t;const n=["@import",`url(${JSON.stringify(e.href)})`];return""===e.layerName?n.push("layer"):e.layerName&&n.push(`layer(${e.layerName})`),e.supportsText&&n.push(`supports(${e.supportsText})`),e.media.length&&n.push(e.media.mediaText),n.join(" ")+";"}(e)}catch(n){}else if(function(e){return"selectorText"in e}(e)&&e.selectorText.includes(":"))return function(e){const t=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm;return e.replace(t,"$1\\$2")}(e.cssText);return t||e.cssText}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(B||(B={}));class W{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){if(!e)return-1;const t=F([this,"access",e=>e.getMeta,"call",t=>t(e),"optionalAccess",e=>e.id]);return r=()=>-1,null!=(n=t)?n:r();var n,r}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){const n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)}replace(e,t){const n=this.getNode(e);if(n){const e=this.nodeMetaMap.get(n);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function j({maskInputOptions:e,tagName:t,type:n}){return"OPTION"===t&&(t="SELECT"),Boolean(e[t.toLowerCase()]||n&&e[n]||"password"===n||"INPUT"===t&&!n&&e.text)}function $({isMasked:e,element:t,value:n,maskInputFn:r}){let s=n||"";return e?(r&&(s=r(s,t)),"*".repeat(s.length)):s}function q(e){return e.toLowerCase()}function X(e){return e.toUpperCase()}const K="__rrweb_original__";function V(e){const t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?q(t):null}function Y(e,t,n){return"INPUT"!==t||"radio"!==n&&"checkbox"!==n?e.value:e.getAttribute("value")||""}let J=1;const G=new RegExp("[^a-z0-9-_:]");function Q(){return J++}let Z,ee;const te=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,ne=/^(?:[a-z+]+:)?\/\//i,re=/^www\..*/i,se=/^(data:)([^,]*),(.*)/i;function oe(e,t){return(e||"").replace(te,((e,n,r,s,o,i)=>{const a=r||o||i,c=n||s||"";if(!a)return e;if(ne.test(a)||re.test(a))return`url(${c}${a}${c})`;if(se.test(a))return`url(${c}${a}${c})`;if("/"===a[0])return`url(${c}${function(e){let t="";return t=e.indexOf("//")>-1?e.split("/").slice(0,3).join("/"):e.split("/")[0],t=t.split("?")[0],t}(t)+a}${c})`;const l=t.split("/"),u=a.split("/");l.pop();for(const t of u)"."!==t&&(".."===t?l.pop():l.push(t));return`url(${c}${l.join("/")}${c})`}))}const ie=/^[^ \t\n\r\u000c]+/,ae=/^[, \t\n\r\u000c]+/;function ce(e,t){if(!t||""===t.trim())return t;const n=e.createElement("a");return n.href=t,n.href}function le(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function ue(){const e=document.createElement("a");return e.href="",e.href}function de(e,t,n,r,s,o){return r?"src"===n||"href"===n&&("use"!==t||"#"!==r[0])||"xlink:href"===n&&"#"!==r[0]?ce(e,r):"background"!==n||"table"!==t&&"td"!==t&&"th"!==t?"srcset"===n?function(e,t){if(""===t.trim())return t;let n=0;function r(e){let r;const s=e.exec(t.substring(n));return s?(r=s[0],n+=r.length,r):""}const s=[];for(;r(ae),!(n>=t.length);){let o=r(ie);if(","===o.slice(-1))o=ce(e,o.substring(0,o.length-1)),s.push(o);else{let r="";o=ce(e,o);let i=!1;for(;;){const e=t.charAt(n);if(""===e){s.push((o+r).trim());break}if(i)")"===e&&(i=!1);else{if(","===e){n+=1,s.push((o+r).trim());break}"("===e&&(i=!0)}r+=e,n+=1}}}return s.join(", ")}(e,r):"style"===n?oe(r,ue()):"object"===t&&"data"===n?ce(e,r):"function"===typeof o?o(n,r,s):r:ce(e,r):r}function he(e,t,n){return("video"===e||"audio"===e)&&"autoplay"===t}function pe(e,t,n=1/0,r=0){return e?e.nodeType!==e.ELEMENT_NODE||r>n?-1:t(e)?r:pe(e.parentNode,t,n,r+1):-1}function me(e,t){return n=>{const r=n;if(null===r)return!1;try{if(e)if("string"===typeof e){if(r.matches(`.${e}`))return!0}else if(function(e,t){for(let n=e.classList.length;n--;){const r=e.classList[n];if(t.test(r))return!0}return!1}(r,e))return!0;return!(!t||!r.matches(t))}catch(s){return!1}}}function fe(e,t,n,r,s,o){try{const i=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===i)return!1;if("INPUT"===i.tagName){const e=i.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(e))return!0}let a=-1,c=-1;if(o){if(c=pe(i,me(r,s)),c<0)return!0;a=pe(i,me(t,n),c>=0?c:1/0)}else{if(a=pe(i,me(t,n)),a<0)return!1;c=pe(i,me(r,s),a>=0?a:1/0)}return a>=0?!(c>=0)||a<=c:!(c>=0)&&!!o}catch(i){}return!!o}function ye(e,t){const{doc:n,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskAttributeFn:c,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,inlineStylesheet:p,maskInputOptions:m={},maskTextFn:f,maskInputFn:y,dataURLOptions:g={},inlineImages:k,recordCanvas:v,keepIframeSrcFn:S,newlyAddedElement:_=!1}=t,b=function(e,t){if(!t.hasNode(e))return;const n=t.getId(e);return 1===n?void 0:n}(n,r);switch(e.nodeType){case e.DOCUMENT_NODE:return"CSS1Compat"!==e.compatMode?{type:B.Document,childNodes:[],compatMode:e.compatMode}:{type:B.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:B.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:b};case e.ELEMENT_NODE:return function(e,t){const{doc:n,blockClass:r,blockSelector:s,unblockSelector:o,inlineStylesheet:i,maskInputOptions:a={},maskAttributeFn:c,maskInputFn:l,dataURLOptions:u={},inlineImages:d,recordCanvas:h,keepIframeSrcFn:p,newlyAddedElement:m=!1,rootId:f,maskAllText:y,maskTextClass:g,unmaskTextClass:k,maskTextSelector:v,unmaskTextSelector:S}=t,_=function(e,t,n,r){try{if(r&&e.matches(r))return!1;if("string"===typeof t){if(e.classList.contains(t))return!0}else for(let n=e.classList.length;n--;){const r=e.classList[n];if(t.test(r))return!0}if(n)return e.matches(n)}catch(s){}return!1}(e,r,s,o),b=function(e){if(e instanceof HTMLFormElement)return"form";const t=q(e.tagName);return G.test(t)?"div":t}(e);let w={};const I=e.attributes.length;for(let E=0;E<I;E++){const t=e.attributes[E];t.name&&!he(b,t.name,t.value)&&(w[t.name]=de(n,b,q(t.name),t.value,e,c))}if("link"===b&&i){const t=Array.from(n.styleSheets).find((t=>t.href===e.href));let r=null;t&&(r=z(t)),r&&(delete w.rel,delete w.href,w._cssText=oe(r,t.href))}if("style"===b&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){const t=z(e.sheet);t&&(w._cssText=oe(t,ue()))}if("input"===b||"textarea"===b||"select"===b||"option"===b){const t=e,n=V(t),r=Y(t,X(b),n),s=t.checked;if("submit"!==n&&"button"!==n&&r){const e=fe(t,g,v,k,S,j({type:n,tagName:X(b),maskInputOptions:a}));w.value=$({isMasked:e,element:t,value:r,maskInputFn:l})}s&&(w.checked=s)}"option"===b&&(e.selected&&!a.select?w.selected=!0:delete w.selected);if("canvas"===b&&h)if("2d"===e.__context)(function(e){const t=e.getContext("2d");if(!t)return!0;for(let n=0;n<e.width;n+=50)for(let r=0;r<e.height;r+=50){const s=t.getImageData,o=K in s?s.__rrweb_original__:s;if(new Uint32Array(o.call(t,n,r,Math.min(50,e.width-n),Math.min(50,e.height-r)).data.buffer).some((e=>0!==e)))return!1}return!0})(e)||(w.rr_dataURL=e.toDataURL(u.type,u.quality));else if(!("__context"in e)){const t=e.toDataURL(u.type,u.quality),n=document.createElement("canvas");n.width=e.width,n.height=e.height;t!==n.toDataURL(u.type,u.quality)&&(w.rr_dataURL=t)}if("img"===b&&d){Z||(Z=n.createElement("canvas"),ee=Z.getContext("2d"));const t=e,r=t.crossOrigin;t.crossOrigin="anonymous";const s=()=>{t.removeEventListener("load",s);try{Z.width=t.naturalWidth,Z.height=t.naturalHeight,ee.drawImage(t,0,0),w.rr_dataURL=Z.toDataURL(u.type,u.quality)}catch(e){console.warn(`Cannot inline img src=${t.currentSrc}! Error: ${e}`)}r?w.crossOrigin=r:t.removeAttribute("crossorigin")};t.complete&&0!==t.naturalWidth?s():t.addEventListener("load",s)}"audio"!==b&&"video"!==b||(w.rr_mediaState=e.paused?"paused":"played",w.rr_mediaCurrentTime=e.currentTime);m||(e.scrollLeft&&(w.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(w.rr_scrollTop=e.scrollTop));if(_){const{width:t,height:n}=e.getBoundingClientRect();w={class:w.class,rr_width:`${t}px`,rr_height:`${n}px`}}"iframe"!==b||p(w.src)||(e.contentDocument||(w.rr_src=w.src),delete w.src);let T;try{customElements.get(b)&&(T=!0)}catch(C){}return{type:B.Element,tagName:b,attributes:w,childNodes:[],isSVG:le(e)||void 0,needBlock:_,rootId:f,isCustom:T}}(e,{doc:n,blockClass:s,blockSelector:o,unblockSelector:i,inlineStylesheet:p,maskAttributeFn:c,maskInputOptions:m,maskInputFn:y,dataURLOptions:g,inlineImages:k,recordCanvas:v,keepIframeSrcFn:S,newlyAddedElement:_,rootId:b,maskAllText:a,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h});case e.TEXT_NODE:return function(e,t){const{maskAllText:n,maskTextClass:r,unmaskTextClass:s,maskTextSelector:o,unmaskTextSelector:i,maskTextFn:a,maskInputOptions:c,maskInputFn:l,rootId:u}=t,d=e.parentNode&&e.parentNode.tagName;let h=e.textContent;const p="STYLE"===d||void 0,m="SCRIPT"===d||void 0,f="TEXTAREA"===d||void 0;if(p&&h){try{e.nextSibling||e.previousSibling||F([e,"access",e=>e.parentNode,"access",e=>e.sheet,"optionalAccess",e=>e.cssRules])&&(h=z(e.parentNode.sheet))}catch(g){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${g}`,e)}h=oe(h,ue())}m&&(h="SCRIPT_PLACEHOLDER");const y=fe(e,r,o,s,i,n);p||m||f||!h||!y||(h=a?a(h,e.parentElement):h.replace(/[\S]/g,"*"));f&&h&&(c.textarea||y)&&(h=l?l(h,e.parentNode):h.replace(/[\S]/g,"*"));if("OPTION"===d&&h){h=$({isMasked:fe(e,r,o,s,i,j({type:null,tagName:d,maskInputOptions:c})),element:e,value:h,maskInputFn:l})}return{type:B.Text,textContent:h||"",isStyle:p,rootId:u}}(e,{maskAllText:a,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,maskTextFn:f,maskInputOptions:m,maskInputFn:y,rootId:b});case e.CDATA_SECTION_NODE:return{type:B.CDATA,textContent:"",rootId:b};case e.COMMENT_NODE:return{type:B.Comment,textContent:e.textContent||"",rootId:b};default:return!1}}function ge(e){return void 0===e||null===e?"":e.toLowerCase()}function ke(e,t){const{doc:n,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:h=!1,inlineStylesheet:p=!0,maskInputOptions:m={},maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:k,dataURLOptions:v={},inlineImages:S=!1,recordCanvas:_=!1,onSerialize:b,onIframeLoad:w,iframeLoadTimeout:I=5e3,onStylesheetLoad:T,stylesheetLoadTimeout:C=5e3,keepIframeSrcFn:E=(()=>!1),newlyAddedElement:x=!1}=t;let{preserveWhiteSpace:M=!0}=t;const R=ye(e,{doc:n,mirror:r,blockClass:s,blockSelector:o,maskAllText:a,unblockSelector:i,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,dataURLOptions:v,inlineImages:S,recordCanvas:_,keepIframeSrcFn:E,newlyAddedElement:x});if(!R)return console.warn(e,"not serialized"),null;let A;A=r.hasNode(e)?r.getId(e):!function(e,t){if(t.comment&&e.type===B.Comment)return!0;if(e.type===B.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)&&"script"===e.attributes.as||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"===typeof e.attributes.href&&e.attributes.href.endsWith(".js")))return!0;if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(ge(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===ge(e.attributes.name)||"icon"===ge(e.attributes.rel)||"apple-touch-icon"===ge(e.attributes.rel)||"shortcut icon"===ge(e.attributes.rel))))return!0;if("meta"===e.tagName){if(t.headMetaDescKeywords&&ge(e.attributes.name).match(/^description|keywords$/))return!0;if(t.headMetaSocial&&(ge(e.attributes.property).match(/^(og|twitter|fb):/)||ge(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===ge(e.attributes.name)))return!0;if(t.headMetaRobots&&("robots"===ge(e.attributes.name)||"googlebot"===ge(e.attributes.name)||"bingbot"===ge(e.attributes.name)))return!0;if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;if(t.headMetaAuthorship&&("author"===ge(e.attributes.name)||"generator"===ge(e.attributes.name)||"framework"===ge(e.attributes.name)||"publisher"===ge(e.attributes.name)||"progid"===ge(e.attributes.name)||ge(e.attributes.property).match(/^article:/)||ge(e.attributes.property).match(/^product:/)))return!0;if(t.headMetaVerification&&("google-site-verification"===ge(e.attributes.name)||"yandex-verification"===ge(e.attributes.name)||"csrf-token"===ge(e.attributes.name)||"p:domain_verify"===ge(e.attributes.name)||"verify-v1"===ge(e.attributes.name)||"verification"===ge(e.attributes.name)||"shopify-checkout-api-token"===ge(e.attributes.name)))return!0}}return!1}(R,k)&&(M||R.type!==B.Text||R.isStyle||R.textContent.replace(/^\s+|\s+$/gm,"").length)?Q():-2;const D=Object.assign(R,{id:A});if(r.add(e,D),-2===A)return null;b&&b(e);let O=!h;if(D.type===B.Element){O=O&&!D.needBlock,delete D.needBlock;const t=e.shadowRoot;t&&U(t)&&(D.isShadowHost=!0)}if((D.type===B.Document||D.type===B.Element)&&O){k.headWhitespace&&D.type===B.Element&&"head"===D.tagName&&(M=!1);const t={doc:n,mirror:r,blockClass:s,blockSelector:o,maskAllText:a,unblockSelector:i,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:h,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:k,dataURLOptions:v,inlineImages:S,recordCanvas:_,preserveWhiteSpace:M,onSerialize:b,onIframeLoad:w,iframeLoadTimeout:I,onStylesheetLoad:T,stylesheetLoadTimeout:C,keepIframeSrcFn:E};for(const n of Array.from(e.childNodes)){const e=ke(n,t);e&&D.childNodes.push(e)}if(function(e){return e.nodeType===e.ELEMENT_NODE}(e)&&e.shadowRoot)for(const n of Array.from(e.shadowRoot.childNodes)){const r=ke(n,t);r&&(U(e.shadowRoot)&&(r.isShadow=!0),D.childNodes.push(r))}}return e.parentNode&&P(e.parentNode)&&U(e.parentNode)&&(D.isShadow=!0),D.type===B.Element&&"iframe"===D.tagName&&function(e,t,n){const r=e.contentWindow;if(!r)return;let s,o=!1;try{s=r.document.readyState}catch(a){return}if("complete"!==s){const r=setTimeout((()=>{o||(t(),o=!0)}),n);return void e.addEventListener("load",(()=>{clearTimeout(r),o=!0,t()}))}const i="about:blank";if(r.location.href!==i||e.src===i||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}(e,(()=>{const t=e.contentDocument;if(t&&w){const n=ke(t,{doc:t,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:k,dataURLOptions:v,inlineImages:S,recordCanvas:_,preserveWhiteSpace:M,onSerialize:b,onIframeLoad:w,iframeLoadTimeout:I,onStylesheetLoad:T,stylesheetLoadTimeout:C,keepIframeSrcFn:E});n&&w(e,n)}}),I),D.type===B.Element&&"link"===D.tagName&&"stylesheet"===D.attributes.rel&&function(e,t,n){let r,s=!1;try{r=e.sheet}catch(i){return}if(r)return;const o=setTimeout((()=>{s||(t(),s=!0)}),n);e.addEventListener("load",(()=>{clearTimeout(o),s=!0,t()}))}(e,(()=>{if(T){const t=ke(e,{doc:n,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:k,dataURLOptions:v,inlineImages:S,recordCanvas:_,preserveWhiteSpace:M,onSerialize:b,onIframeLoad:w,iframeLoadTimeout:I,onStylesheetLoad:T,stylesheetLoadTimeout:C,keepIframeSrcFn:E});t&&T(e,t)}}),C),D}function ve(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}function Se(e,t,n=document){const r={capture:!0,passive:!0};return n.addEventListener(e,t,r),()=>n.removeEventListener(e,t,r)}const _e="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let be={map:{},getId:()=>(console.error(_e),-1),getNode:()=>(console.error(_e),null),removeNodeFromMap(){console.error(_e)},has:()=>(console.error(_e),!1),reset(){console.error(_e)}};function we(e,t,n={}){let r=null,s=0;return function(...o){const i=Date.now();s||!1!==n.leading||(s=i);const a=t-(i-s),c=this;a<=0||a>t?(r&&(!function(...e){je("clearTimeout")(...e)}(r),r=null),s=i,e.apply(c,o)):r||!1===n.trailing||(r=$e((()=>{s=!1===n.leading?0:Date.now(),r=null,e.apply(c,o)}),a))}}function Ie(e,t,n,r,s=window){const o=s.Object.getOwnPropertyDescriptor(e,t);return s.Object.defineProperty(e,t,r?n:{set(e){$e((()=>{n.set.call(this,e)}),0),o&&o.set&&o.set.call(this,e)}}),()=>Ie(e,t,o||{},!0)}function Te(e,t,n){try{if(!(t in e))return()=>{};const r=e[t],s=n(r);return"function"===typeof s&&(s.prototype=s.prototype||{},Object.defineProperties(s,{__rrweb_original__:{enumerable:!1,value:r}})),e[t]=s,()=>{e[t]=r}}catch(r){return()=>{}}}"undefined"!==typeof window&&window.Proxy&&window.Reflect&&(be=new Proxy(be,{get:(e,t,n)=>("map"===t&&console.error(_e),Reflect.get(e,t,n))}));let Ce=Date.now;function Ee(e){const t=e.document;return{left:t.scrollingElement?t.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:ve([t,"optionalAccess",e=>e.documentElement,"access",e=>e.scrollLeft])||ve([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.parentElement,"optionalAccess",e=>e.scrollLeft])||ve([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.scrollLeft])||0,top:t.scrollingElement?t.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:ve([t,"optionalAccess",e=>e.documentElement,"access",e=>e.scrollTop])||ve([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.parentElement,"optionalAccess",e=>e.scrollTop])||ve([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.scrollTop])||0}}function xe(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function Me(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function Re(e){if(!e)return null;return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}function Ae(e,t,n,r,s){if(!e)return!1;const o=Re(e);if(!o)return!1;const i=me(t,n);if(!s){const e=r&&o.matches(r);return i(o)&&!e}const a=pe(o,i);let c=-1;return!(a<0)&&(r&&(c=pe(o,me(null,r))),a>-1&&c<0||a<c)}function De(e,t){return-2===t.getId(e)}function Oe(e,t){if(P(e))return!1;const n=t.getId(e);return!t.has(n)||(!e.parentNode||e.parentNode.nodeType!==e.DOCUMENT_NODE)&&(!e.parentNode||Oe(e.parentNode,t))}function Le(e){return Boolean(e.changedTouches)}function Ne(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))}function Fe(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function Be(e){return Boolean(ve([e,"optionalAccess",e=>e.shadowRoot]))}/[1-9][0-9]{12}/.test(Date.now().toString())||(Ce=()=>(new Date).getTime());class Pe{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){return(0,r.h)(this.styleIDMap.get(e),(()=>-1))}has(e){return this.styleIDMap.has(e)}add(e,t){if(this.has(e))return this.getId(e);let n;return n=void 0===t?this.id++:t,this.styleIDMap.set(e,n),this.idStyleMap.set(n,e),n}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function Ue(e){let t=null;return ve([e,"access",e=>e.getRootNode,"optionalCall",e=>e(),"optionalAccess",e=>e.nodeType])===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(t=e.getRootNode().host),t}function ze(e){const t=e.ownerDocument;if(!t)return!1;const n=function(e){let t,n=e;for(;t=Ue(n);)n=t;return n}(e);return t.contains(n)}function He(e){const t=e.ownerDocument;return!!t&&(t.contains(e)||ze(e))}const We={};function je(e){const t=We[e];if(t)return t;const n=window.document;let r=window[e];if(n&&"function"===typeof n.createElement)try{const t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);const s=t.contentWindow;s&&s[e]&&(r=s[e]),n.head.removeChild(t)}catch(s){}return We[e]=r.bind(window)}function $e(...e){return je("setTimeout")(...e)}var qe=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(qe||{}),Xe=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(Xe||{}),Ke=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(Ke||{}),Ve=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(Ve||{});function Ye(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}function Je(e){return"__ln"in e}class Ge{constructor(){this.length=0,this.head=null,this.tail=null}get(e){if(e>=this.length)throw new Error("Position outside of list range");let t=this.head;for(let n=0;n<e;n++)t=Ye([t,"optionalAccess",e=>e.next])||null;return t}addNode(e){const t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&Je(e.previousSibling)){const n=e.previousSibling.__ln.next;t.next=n,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,n&&(n.previous=t)}else if(e.nextSibling&&Je(e.nextSibling)&&e.nextSibling.__ln.previous){const n=e.nextSibling.__ln.previous;t.previous=n,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,n&&(n.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){const t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}const Qe=(e,t)=>`${e}@${t}`;class Ze{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const e=[],t=new Set,n=new Ge,r=e=>{let t=e,n=-2;for(;-2===n;)t=t&&t.nextSibling,n=t&&this.mirror.getId(t);return n},s=s=>{if(!s.parentNode||!He(s))return;const o=P(s.parentNode)?this.mirror.getId(Ue(s)):this.mirror.getId(s.parentNode),i=r(s);if(-1===o||-1===i)return n.addNode(s);const a=ke(s,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{Ne(e,this.mirror)&&this.iframeManager.addIframe(e),Fe(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),Be(s)&&this.shadowDomManager.addShadowRoot(s.shadowRoot,this.doc)},onIframeLoad:(e,t)=>{this.iframeManager.attachIframe(e,t),this.shadowDomManager.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});a&&(e.push({parentId:o,nextId:i,node:a}),t.add(a.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const a of this.movedSet)tt(this.removes,a,this.mirror)&&!this.movedSet.has(a.parentNode)||s(a);for(const a of this.addedSet)rt(this.droppedSet,a)||tt(this.removes,a,this.mirror)?rt(this.movedSet,a)?s(a):this.droppedSet.add(a):s(a);let o=null;for(;n.length;){let e=null;if(o){const t=this.mirror.getId(o.value.parentNode),n=r(o.value);-1!==t&&-1!==n&&(e=o)}if(!e){let t=n.tail;for(;t;){const n=t;if(t=t.previous,n){const t=this.mirror.getId(n.value.parentNode);if(-1===r(n.value))continue;if(-1!==t){e=n;break}{const t=n.value;if(t.parentNode&&t.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const r=t.parentNode.host;if(-1!==this.mirror.getId(r)){e=n;break}}}}}}if(!e){for(;n.head;)n.removeNode(n.head.value);break}o=e.previous,n.removeNode(e.value),s(e.value)}const i={texts:this.texts.map((e=>({id:this.mirror.getId(e.node),value:e.value}))).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),attributes:this.attributes.map((e=>{const{attributes:t}=e;if("string"===typeof t.style){const n=JSON.stringify(e.styleDiff),r=JSON.stringify(e._unchangedStyles);n.length<t.style.length&&(n+r).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),removes:this.removes,adds:e};(i.texts.length||i.attributes.length||i.removes.length||i.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(i))},this.processMutation=e=>{if(!De(e.target,this.mirror))switch(e.type){case"characterData":{const t=e.target.textContent;Ae(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||t===e.oldValue||this.texts.push({value:fe(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&t?this.maskTextFn?this.maskTextFn(t,Re(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break}case"attributes":{const n=e.target;let r=e.attributeName,s=e.target.getAttribute(r);if("value"===r){const t=V(n),r=n.tagName;s=Y(n,r,t);const o=j({maskInputOptions:this.maskInputOptions,tagName:r,type:t});s=$({isMasked:fe(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,o),element:n,value:s,maskInputFn:this.maskInputFn})}if(Ae(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||s===e.oldValue)return;let o=this.attributeMap.get(e.target);if("IFRAME"===n.tagName&&"src"===r&&!this.keepIframeSrcFn(s)){if(n.contentDocument)return;r="rr_src"}if(o||(o={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(o),this.attributeMap.set(e.target,o)),"type"===r&&"INPUT"===n.tagName&&"password"===(e.oldValue||"").toLowerCase()&&n.setAttribute("data-rr-is-password","true"),!he(n.tagName,r)&&(o.attributes[r]=de(this.doc,q(n.tagName),q(r),s,n,this.maskAttributeFn),"style"===r)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(t){this.unattachedDoc=this.doc}const r=this.unattachedDoc.createElement("span");e.oldValue&&r.setAttribute("style",e.oldValue);for(const e of Array.from(n.style)){const t=n.style.getPropertyValue(e),s=n.style.getPropertyPriority(e);t!==r.style.getPropertyValue(e)||s!==r.style.getPropertyPriority(e)?o.styleDiff[e]=""===s?t:[t,s]:o._unchangedStyles[e]=[t,s]}for(const e of Array.from(r.style))""===n.style.getPropertyValue(e)&&(o.styleDiff[e]=!1)}break}case"childList":if(Ae(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;e.addedNodes.forEach((t=>this.genAdds(t,e.target))),e.removedNodes.forEach((t=>{const n=this.mirror.getId(t),r=P(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);Ae(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||De(t,this.mirror)||!function(e,t){return-1!==t.getId(e)}(t,this.mirror)||(this.addedSet.has(t)?(et(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===n||Oe(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[Qe(n,r)]?et(this.movedSet,t):this.removes.push({parentId:r,id:n,isShadow:!(!P(e.target)||!U(e.target))||void 0})),this.mapRemoves.push(t))}))}},this.genAdds=(e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!this.addedSet.has(e)&&!this.movedSet.has(e)){if(this.mirror.hasNode(e)){if(De(e,this.mirror))return;this.movedSet.add(e);let n=null;t&&this.mirror.hasNode(t)&&(n=this.mirror.getId(t)),n&&-1!==n&&(this.movedMap[Qe(this.mirror.getId(e),n)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);Ae(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(e.childNodes.forEach((e=>this.genAdds(e))),Be(e)&&e.shadowRoot.childNodes.forEach((t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)})))}}}init(e){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((t=>{this[t]=e[t]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function et(e,t){e.delete(t),t.childNodes.forEach((t=>et(e,t)))}function tt(e,t,n){return 0!==e.length&&nt(e,t,n)}function nt(e,t,n){const{parentNode:r}=t;if(!r)return!1;const s=n.getId(r);return!!e.some((e=>e.id===s))||nt(e,r,n)}function rt(e,t){return 0!==e.size&&st(e,t)}function st(e,t){const{parentNode:n}=t;return!!n&&(!!e.has(n)||st(e,n))}let ot;function it(e){ot=e}function at(){ot=void 0}const ct=e=>{if(!ot)return e;return(...t)=>{try{return e(...t)}catch(n){if(ot&&!0===ot(n))return()=>{};throw n}}};function lt(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}const ut=[];function dt(e){try{if("composedPath"in e){const t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch(t){}return e&&e.target}function ht(e,t){const n=new Ze;ut.push(n),n.init(e);let r=window.MutationObserver||window.__rrMutationObserver;const s=lt([window,"optionalAccess",e=>e.Zone,"optionalAccess",e=>e.__symbol__,"optionalCall",e=>e("MutationObserver")]);s&&window[s]&&(r=window[s]);const o=new r(ct((t=>{e.onMutation&&!1===e.onMutation(t)||n.processMutations.bind(n)(t)})));return o.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),o}function pt({mouseInteractionCb:e,doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,sampling:i}){if(!1===i.mouseInteraction)return()=>{};const a=!0===i.mouseInteraction||void 0===i.mouseInteraction?{}:i.mouseInteraction,c=[];let l=null;return Object.keys(Ke).filter((e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==a[e])).forEach((i=>{let a=q(i);const u=(t=>i=>{const a=dt(i);if(Ae(a,r,s,o,!0))return;let c=null,u=t;if("pointerType"in i){switch(i.pointerType){case"mouse":c=Ve.Mouse;break;case"touch":c=Ve.Touch;break;case"pen":c=Ve.Pen}c===Ve.Touch?Ke[t]===Ke.MouseDown?u="TouchStart":Ke[t]===Ke.MouseUp&&(u="TouchEnd"):Ve.Pen}else Le(i)&&(c=Ve.Touch);null!==c?(l=c,(u.startsWith("Touch")&&c===Ve.Touch||u.startsWith("Mouse")&&c===Ve.Mouse)&&(c=null)):Ke[t]===Ke.Click&&(c=l,l=null);const d=Le(i)?i.changedTouches[0]:i;if(!d)return;const h=n.getId(a),{clientX:p,clientY:m}=d;ct(e)({type:Ke[u],id:h,x:p,y:m,...null!==c&&{pointerType:c}})})(i);if(window.PointerEvent)switch(Ke[i]){case Ke.MouseDown:case Ke.MouseUp:a=a.replace("mouse","pointer");break;case Ke.TouchStart:case Ke.TouchEnd:return}c.push(Se(a,u,t))})),ct((()=>{c.forEach((e=>e()))}))}function mt({scrollCb:e,doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,sampling:i}){return Se("scroll",ct(we(ct((i=>{const a=dt(i);if(!a||Ae(a,r,s,o,!0))return;const c=n.getId(a);if(a===t&&t.defaultView){const n=Ee(t.defaultView);e({id:c,x:n.left,y:n.top})}else e({id:c,x:a.scrollLeft,y:a.scrollTop})})),i.scroll||100)),t)}const ft=["INPUT","TEXTAREA","SELECT"],yt=new WeakMap;function gt({inputCb:e,doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,ignoreClass:i,ignoreSelector:a,maskInputOptions:c,maskInputFn:l,sampling:u,userTriggeredOnInput:d,maskTextClass:h,unmaskTextClass:p,maskTextSelector:m,unmaskTextSelector:f}){function y(e){let n=dt(e);const u=e.isTrusted,y=n&&X(n.tagName);if("OPTION"===y&&(n=n.parentElement),!n||!y||ft.indexOf(y)<0||Ae(n,r,s,o,!0))return;const k=n;if(k.classList.contains(i)||a&&k.matches(a))return;const v=V(n);let S=Y(k,y,v),_=!1;const b=j({maskInputOptions:c,tagName:y,type:v}),w=fe(n,h,m,p,f,b);"radio"!==v&&"checkbox"!==v||(_=n.checked),S=$({isMasked:w,element:n,value:S,maskInputFn:l}),g(n,d?{text:S,isChecked:_,userTriggered:u}:{text:S,isChecked:_});const I=n.name;"radio"===v&&I&&_&&t.querySelectorAll(`input[type="radio"][name="${I}"]`).forEach((e=>{if(e!==n){const t=$({isMasked:w,element:e,value:Y(e,y,v),maskInputFn:l});g(e,d?{text:t,isChecked:!_,userTriggered:!1}:{text:t,isChecked:!_})}}))}function g(t,r){const s=yt.get(t);if(!s||s.text!==r.text||s.isChecked!==r.isChecked){yt.set(t,r);const s=n.getId(t);ct(e)({...r,id:s})}}const k=("last"===u.input?["change"]:["input","change"]).map((e=>Se(e,ct(y),t))),v=t.defaultView;if(!v)return()=>{k.forEach((e=>e()))};const S=v.Object.getOwnPropertyDescriptor(v.HTMLInputElement.prototype,"value"),_=[[v.HTMLInputElement.prototype,"value"],[v.HTMLInputElement.prototype,"checked"],[v.HTMLSelectElement.prototype,"value"],[v.HTMLTextAreaElement.prototype,"value"],[v.HTMLSelectElement.prototype,"selectedIndex"],[v.HTMLOptionElement.prototype,"selected"]];return S&&S.set&&k.push(..._.map((e=>Ie(e[0],e[1],{set(){ct(y)({target:this,isTrusted:!1})}},!1,v)))),ct((()=>{k.forEach((e=>e()))}))}function kt(e){return function(e,t){if(bt("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||bt("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||bt("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||bt("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){const n=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(n)}else if(e.parentStyleSheet){const n=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(n)}return t}(e,[])}function vt(e,t,n){let r,s;return e?(e.ownerNode?r=t.getId(e.ownerNode):s=n.getId(e),{styleId:s,id:r}):{}}function St({mirror:e,stylesheetManager:t},n){let r=null;r="#document"===n.nodeName?e.getId(n):e.getId(n.host);const s="#document"===n.nodeName?lt([n,"access",e=>e.defaultView,"optionalAccess",e=>e.Document]):lt([n,"access",e=>e.ownerDocument,"optionalAccess",e=>e.defaultView,"optionalAccess",e=>e.ShadowRoot]),o=lt([s,"optionalAccess",e=>e.prototype])?Object.getOwnPropertyDescriptor(lt([s,"optionalAccess",e=>e.prototype]),"adoptedStyleSheets"):void 0;return null!==r&&-1!==r&&s&&o?(Object.defineProperty(n,"adoptedStyleSheets",{configurable:o.configurable,enumerable:o.enumerable,get(){return lt([o,"access",e=>e.get,"optionalAccess",e=>e.call,"call",e=>e(this)])},set(e){const n=lt([o,"access",e=>e.set,"optionalAccess",e=>e.call,"call",t=>t(this,e)]);if(null!==r&&-1!==r)try{t.adoptStyleSheets(e,r)}catch(s){}return n}}),ct((()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:o.configurable,enumerable:o.enumerable,get:o.get,set:o.set})}))):()=>{}}function _t(e,t={}){const n=e.doc.defaultView;if(!n)return()=>{};const r=ht(e,e.doc),s=function({mousemoveCb:e,sampling:t,doc:n,mirror:r}){if(!1===t.mousemove)return()=>{};const s="number"===typeof t.mousemove?t.mousemove:50,o="number"===typeof t.mousemoveCallback?t.mousemoveCallback:500;let i,a=[];const c=we(ct((t=>{const n=Date.now()-i;e(a.map((e=>(e.timeOffset-=n,e))),t),a=[],i=null})),o),l=ct(we(ct((e=>{const t=dt(e),{clientX:n,clientY:s}=Le(e)?e.changedTouches[0]:e;i||(i=Ce()),a.push({x:n,y:s,id:r.getId(t),timeOffset:Ce()-i}),c("undefined"!==typeof DragEvent&&e instanceof DragEvent?Xe.Drag:e instanceof MouseEvent?Xe.MouseMove:Xe.TouchMove)})),s,{trailing:!1})),u=[Se("mousemove",l,n),Se("touchmove",l,n),Se("drag",l,n)];return ct((()=>{u.forEach((e=>e()))}))}(e),o=pt(e),i=mt(e),a=function({viewportResizeCb:e},{win:t}){let n=-1,r=-1;return Se("resize",ct(we(ct((()=>{const t=xe(),s=Me();n===t&&r===s||(e({width:Number(s),height:Number(t)}),n=t,r=s)})),200)),t)}(e,{win:n}),c=gt(e),l=function({mediaInteractionCb:e,blockClass:t,blockSelector:n,unblockSelector:r,mirror:s,sampling:o,doc:i}){const a=ct((i=>we(ct((o=>{const a=dt(o);if(!a||Ae(a,t,n,r,!0))return;const{currentTime:c,volume:l,muted:u,playbackRate:d}=a;e({type:i,id:s.getId(a),currentTime:c,volume:l,muted:u,playbackRate:d})})),o.media||500))),c=[Se("play",a(0),i),Se("pause",a(1),i),Se("seeked",a(2),i),Se("volumechange",a(3),i),Se("ratechange",a(4),i)];return ct((()=>{c.forEach((e=>e()))}))}(e),u=function({styleSheetRuleCb:e,mirror:t,stylesheetManager:n},{win:r}){if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype)return()=>{};const s=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(s,{apply:ct(((r,s,o)=>{const[i,a]=o,{id:c,styleId:l}=vt(s,t,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&e({id:c,styleId:l,adds:[{rule:i,index:a}]}),r.apply(s,o)}))});const o=r.CSSStyleSheet.prototype.deleteRule;let i,a;r.CSSStyleSheet.prototype.deleteRule=new Proxy(o,{apply:ct(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=vt(s,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,removes:[{index:i}]}),r.apply(s,o)}))}),r.CSSStyleSheet.prototype.replace&&(i=r.CSSStyleSheet.prototype.replace,r.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:ct(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=vt(s,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,replace:i}),r.apply(s,o)}))})),r.CSSStyleSheet.prototype.replaceSync&&(a=r.CSSStyleSheet.prototype.replaceSync,r.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:ct(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=vt(s,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,replaceSync:i}),r.apply(s,o)}))}));const c={};wt("CSSGroupingRule")?c.CSSGroupingRule=r.CSSGroupingRule:(wt("CSSMediaRule")&&(c.CSSMediaRule=r.CSSMediaRule),wt("CSSConditionRule")&&(c.CSSConditionRule=r.CSSConditionRule),wt("CSSSupportsRule")&&(c.CSSSupportsRule=r.CSSSupportsRule));const l={};return Object.entries(c).forEach((([r,s])=>{l[r]={insertRule:s.prototype.insertRule,deleteRule:s.prototype.deleteRule},s.prototype.insertRule=new Proxy(l[r].insertRule,{apply:ct(((r,s,o)=>{const[i,a]=o,{id:c,styleId:l}=vt(s.parentStyleSheet,t,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&e({id:c,styleId:l,adds:[{rule:i,index:[...kt(s),a||0]}]}),r.apply(s,o)}))}),s.prototype.deleteRule=new Proxy(l[r].deleteRule,{apply:ct(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=vt(s.parentStyleSheet,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,removes:[{index:[...kt(s),i]}]}),r.apply(s,o)}))})})),ct((()=>{r.CSSStyleSheet.prototype.insertRule=s,r.CSSStyleSheet.prototype.deleteRule=o,i&&(r.CSSStyleSheet.prototype.replace=i),a&&(r.CSSStyleSheet.prototype.replaceSync=a),Object.entries(c).forEach((([e,t])=>{t.prototype.insertRule=l[e].insertRule,t.prototype.deleteRule=l[e].deleteRule}))}))}(e,{win:n}),d=St(e,e.doc),h=function({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:n,stylesheetManager:r},{win:s}){const o=s.CSSStyleDeclaration.prototype.setProperty;s.CSSStyleDeclaration.prototype.setProperty=new Proxy(o,{apply:ct(((s,i,a)=>{const[c,l,u]=a;if(n.has(c))return o.apply(i,[c,l,u]);const{id:d,styleId:h}=vt(lt([i,"access",e=>e.parentRule,"optionalAccess",e=>e.parentStyleSheet]),t,r.styleMirror);return(d&&-1!==d||h&&-1!==h)&&e({id:d,styleId:h,set:{property:c,value:l,priority:u},index:kt(i.parentRule)}),s.apply(i,a)}))});const i=s.CSSStyleDeclaration.prototype.removeProperty;return s.CSSStyleDeclaration.prototype.removeProperty=new Proxy(i,{apply:ct(((s,o,a)=>{const[c]=a;if(n.has(c))return i.apply(o,[c]);const{id:l,styleId:u}=vt(lt([o,"access",e=>e.parentRule,"optionalAccess",e=>e.parentStyleSheet]),t,r.styleMirror);return(l&&-1!==l||u&&-1!==u)&&e({id:l,styleId:u,remove:{property:c},index:kt(o.parentRule)}),s.apply(o,a)}))}),ct((()=>{s.CSSStyleDeclaration.prototype.setProperty=o,s.CSSStyleDeclaration.prototype.removeProperty=i}))}(e,{win:n}),p=e.collectFonts?function({fontCb:e,doc:t}){const n=t.defaultView;if(!n)return()=>{};const r=[],s=new WeakMap,o=n.FontFace;n.FontFace=function(e,t,n){const r=new o(e,t,n);return s.set(r,{family:e,buffer:"string"!==typeof t,descriptors:n,fontSource:"string"===typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),r};const i=Te(t.fonts,"add",(function(t){return function(n){return $e(ct((()=>{const t=s.get(n);t&&(e(t),s.delete(n))})),0),t.apply(this,[n])}}));return r.push((()=>{n.FontFace=o})),r.push(i),ct((()=>{r.forEach((e=>e()))}))}(e):()=>{},m=function(e){const{doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,selectionCb:i}=e;let a=!0;const c=ct((()=>{const e=t.getSelection();if(!e||a&&lt([e,"optionalAccess",e=>e.isCollapsed]))return;a=e.isCollapsed||!1;const c=[],l=e.rangeCount||0;for(let t=0;t<l;t++){const i=e.getRangeAt(t),{startContainer:a,startOffset:l,endContainer:u,endOffset:d}=i;Ae(a,r,s,o,!0)||Ae(u,r,s,o,!0)||c.push({start:n.getId(a),startOffset:l,end:n.getId(u),endOffset:d})}i({ranges:c})}));return c(),Se("selectionchange",c)}(e),f=function({doc:e,customElementCb:t}){const n=e.defaultView;return n&&n.customElements?Te(n.customElements,"define",(function(e){return function(n,r,s){try{t({define:{name:n}})}catch(o){}return e.apply(this,[n,r,s])}})):()=>{}}(e),y=[];for(const g of e.plugins)y.push(g.observer(g.callback,n,g.options));return ct((()=>{ut.forEach((e=>e.reset())),r.disconnect(),s(),o(),i(),a(),c(),l(),u(),d(),h(),p(),m(),f(),y.forEach((e=>e()))}))}function bt(e){return"undefined"!==typeof window[e]}function wt(e){return Boolean("undefined"!==typeof window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class It{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,t,n,r){const s=n||this.getIdToRemoteIdMap(e),o=r||this.getRemoteIdToIdMap(e);let i=s.get(t);return i||(i=this.generateIdFn(),s.set(t,i),o.set(i,t)),i}getIds(e,t){const n=this.getIdToRemoteIdMap(e),r=this.getRemoteIdToIdMap(e);return t.map((t=>this.getId(e,t,n,r)))}getRemoteId(e,t,n){const r=n||this.getRemoteIdToIdMap(e);if("number"!==typeof t)return t;const s=r.get(t);return s||-1}getRemoteIds(e,t){const n=this.getRemoteIdToIdMap(e);return t.map((t=>this.getRemoteId(e,t,n)))}reset(e){if(!e)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){let t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}function Tt(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}class Ct{constructor(){this.crossOriginIframeMirror=new It(Q),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}}class Et{constructor(e){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new It(Q),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new It(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),Tt([this,"access",e=>e.loadListener,"optionalCall",t=>t(e)]),e.contentDocument&&e.contentDocument.adoptedStyleSheets&&e.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(e.contentDocument.adoptedStyleSheets,this.mirror.getId(e.contentDocument))}handleMessage(e){const t=e;if("rrweb"!==t.data.type||t.origin!==t.data.origin)return;if(!e.source)return;const n=this.crossOriginIframeMap.get(e.source);if(!n)return;const r=this.transformCrossOriginEvent(n,t.data.event);r&&this.wrappedEmit(r,t.data.isCheckout)}transformCrossOriginEvent(e,t){switch(t.type){case qe.FullSnapshot:{this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);const n=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,n),this.patchRootIdOnNode(t.data.node,n),{timestamp:t.timestamp,type:qe.IncrementalSnapshot,data:{source:Xe.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case qe.Meta:case qe.Load:case qe.DomContentLoaded:return!1;case qe.Plugin:return t;case qe.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case qe.IncrementalSnapshot:switch(t.data.source){case Xe.Mutation:return t.data.adds.forEach((t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);const n=this.crossOriginIframeRootIdMap.get(e);n&&this.patchRootIdOnNode(t.node,n)})),t.data.removes.forEach((t=>{this.replaceIds(t,e,["parentId","id"])})),t.data.attributes.forEach((t=>{this.replaceIds(t,e,["id"])})),t.data.texts.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case Xe.Drag:case Xe.TouchMove:case Xe.MouseMove:return t.data.positions.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case Xe.ViewportResize:return!1;case Xe.MediaInteraction:case Xe.MouseInteraction:case Xe.Scroll:case Xe.CanvasMutation:case Xe.Input:return this.replaceIds(t.data,e,["id"]),t;case Xe.StyleSheetRule:case Xe.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case Xe.Font:return t;case Xe.Selection:return t.data.ranges.forEach((t=>{this.replaceIds(t,e,["start","end"])})),t;case Xe.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),Tt([t,"access",e=>e.data,"access",e=>e.styles,"optionalAccess",e=>e.forEach,"call",t=>t((t=>{this.replaceStyleIds(t,e,["styleId"])}))]),t}}return!1}replace(e,t,n,r){for(const s of r)(Array.isArray(t[s])||"number"===typeof t[s])&&(Array.isArray(t[s])?t[s]=e.getIds(n,t[s]):t[s]=e.getId(n,t[s]));return t}replaceIds(e,t,n){return this.replace(this.crossOriginIframeMirror,e,t,n)}replaceStyleIds(e,t,n){return this.replace(this.crossOriginIframeStyleMirror,e,t,n)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach((e=>{this.replaceIdOnNode(e,t)}))}patchRootIdOnNode(e,t){e.type===B.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach((e=>{this.patchRootIdOnNode(e,t)}))}}class xt{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}}class Mt{constructor(e){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(!U(e))return;if(this.shadowDoms.has(e))return;this.shadowDoms.add(e);const n=ht({...this.bypassOptions,doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},e);this.restoreHandlers.push((()=>n.disconnect())),this.restoreHandlers.push(mt({...this.bypassOptions,scrollCb:this.scrollCb,doc:e,mirror:this.mirror})),$e((()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(e.host)),this.restoreHandlers.push(St({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))}),0)}observeAttachShadow(e){e.contentWindow&&e.contentDocument&&this.patchAttachShadow(e.contentWindow.Element,e.contentDocument)}patchAttachShadow(e,t){const n=this;this.restoreHandlers.push(Te(e.prototype,"attachShadow",(function(e){return function(r){const s=e.call(this,r);return this.shadowRoot&&He(this)&&n.addShadowRoot(this.shadowRoot,t),s}})))}reset(){this.restoreHandlers.forEach((e=>{try{e()}catch(t){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}class Rt{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}}class At{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new Pe,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){if(0===e.length)return;const n={id:t,styleIds:[]},r=[];for(const s of e){let e;this.styleMirror.has(s)?e=this.styleMirror.getId(s):(e=this.styleMirror.add(s),r.push({styleId:e,rules:Array.from(s.rules||CSSRule,((e,t)=>({rule:H(e),index:t})))})),n.styleIds.push(e)}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class Dt{constructor(){this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}periodicallyClear(){!function(...e){je("requestAnimationFrame")(...e)}((()=>{this.clear(),this.loop&&this.periodicallyClear()}))}inOtherBuffer(e,t){const n=this.nodeMap.get(e);return n&&Array.from(n).some((e=>e!==t))}add(e,t){this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}clear(){this.nodeMap=new WeakMap}destroy(){this.loop=!1}}let Ot,Lt;const Nt=new W;function Ft(e={}){const{emit:t,checkoutEveryNms:n,checkoutEveryNth:r,blockClass:o="rr-block",blockSelector:i=null,unblockSelector:a=null,ignoreClass:c="rr-ignore",ignoreSelector:l=null,maskAllText:u=!1,maskTextClass:d="rr-mask",unmaskTextClass:h=null,maskTextSelector:p=null,unmaskTextSelector:m=null,inlineStylesheet:f=!0,maskAllInputs:y,maskInputOptions:g,slimDOMOptions:k,maskAttributeFn:v,maskInputFn:S,maskTextFn:_,maxCanvasSize:b=null,packFn:w,sampling:I={},dataURLOptions:T={},mousemoveWait:C,recordCanvas:E=!1,recordCrossOriginIframes:x=!1,recordAfter:M=("DOMContentLoaded"===e.recordAfter?e.recordAfter:"load"),userTriggeredOnInput:R=!1,collectFonts:A=!1,inlineImages:D=!1,plugins:O,keepIframeSrcFn:L=(()=>!1),ignoreCSSAttributes:N=new Set([]),errorHandler:F,onMutation:B,getCanvasManager:P}=e;it(F);const U=!x||window.parent===window;let z=!1;if(!U)try{window.parent.document&&(z=!1)}catch(ne){z=!0}if(U&&!t)throw new Error("emit function is required");void 0!==C&&void 0===I.mousemove&&(I.mousemove=C),Nt.reset();const H=!0===y?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==g?g:{},j=!0===k||"all"===k?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===k,headMetaDescKeywords:"all"===k}:k||{};let $;!function(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let t=e[0];if(!(0 in e))throw new TypeError("1 argument is required");do{if(this===t)return!0}while(t=t&&t.parentNode);return!1})}();let q=0;const X=e=>{for(const t of O||[])t.eventProcessor&&(e=t.eventProcessor(e));return w&&!z&&(e=w(e)),e};Ot=(e,o)=>{const i=e;if(i.timestamp=Ce(),!(0,s.x)([ut,"access",e=>e[0],"optionalAccess",e=>e.isFrozen,"call",e=>e()])||i.type===qe.FullSnapshot||i.type===qe.IncrementalSnapshot&&i.data.source===Xe.Mutation||ut.forEach((e=>e.unfreeze())),U)(0,s.x)([t,"optionalCall",e=>e(X(i),o)]);else if(z){const e={type:"rrweb",event:X(i),origin:window.location.origin,isCheckout:o};window.parent.postMessage(e,"*")}if(i.type===qe.FullSnapshot)$=i,q=0;else if(i.type===qe.IncrementalSnapshot){if(i.data.source===Xe.Mutation&&i.data.isAttachIframe)return;q++;const e=r&&q>=r,t=n&&$&&i.timestamp-$.timestamp>n;(e||t)&&te(!0)}};const K=e=>{Ot({type:qe.IncrementalSnapshot,data:{source:Xe.Mutation,...e}})},V=e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.Scroll,...e}}),Y=e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.CanvasMutation,...e}}),J=new At({mutationCb:K,adoptedStyleSheetCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.AdoptedStyleSheet,...e}})}),G="boolean"===typeof __RRWEB_EXCLUDE_IFRAME__&&__RRWEB_EXCLUDE_IFRAME__?new Ct:new Et({mirror:Nt,mutationCb:K,stylesheetManager:J,recordCrossOriginIframes:x,wrappedEmit:Ot});for(const s of O||[])s.getMirror&&s.getMirror({nodeMirror:Nt,crossOriginIframeMirror:G.crossOriginIframeMirror,crossOriginIframeStyleMirror:G.crossOriginIframeStyleMirror});const Q=new Dt,Z=function(e,t){try{return e?e(t):new Rt}catch(n){return console.warn("Unable to initialize CanvasManager"),new Rt}}(P,{mirror:Nt,win:window,mutationCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.CanvasMutation,...e}}),recordCanvas:E,blockClass:o,blockSelector:i,unblockSelector:a,maxCanvasSize:b,sampling:I.canvas,dataURLOptions:T,errorHandler:F}),ee="boolean"===typeof __RRWEB_EXCLUDE_SHADOW_DOM__&&__RRWEB_EXCLUDE_SHADOW_DOM__?new xt:new Mt({mutationCb:K,scrollCb:V,bypassOptions:{onMutation:B,blockClass:o,blockSelector:i,unblockSelector:a,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:m,inlineStylesheet:f,maskInputOptions:H,dataURLOptions:T,maskAttributeFn:v,maskTextFn:_,maskInputFn:S,recordCanvas:E,inlineImages:D,sampling:I,slimDOMOptions:j,iframeManager:G,stylesheetManager:J,canvasManager:Z,keepIframeSrcFn:L,processedNodeManager:Q},mirror:Nt}),te=(e=!1)=>{Ot({type:qe.Meta,data:{href:window.location.href,width:Me(),height:xe()}},e),J.reset(),ee.init(),ut.forEach((e=>e.lock()));const t=function(e,t){const{mirror:n=new W,blockClass:r="rr-block",blockSelector:s=null,unblockSelector:o=null,maskAllText:i=!1,maskTextClass:a="rr-mask",unmaskTextClass:c=null,maskTextSelector:l=null,unmaskTextSelector:u=null,inlineStylesheet:d=!0,inlineImages:h=!1,recordCanvas:p=!1,maskAllInputs:m=!1,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOM:k=!1,dataURLOptions:v,preserveWhiteSpace:S,onSerialize:_,onIframeLoad:b,iframeLoadTimeout:w,onStylesheetLoad:I,stylesheetLoadTimeout:T,keepIframeSrcFn:C=(()=>!1)}=t||{};return ke(e,{doc:e,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,maskAllText:i,maskTextClass:a,unmaskTextClass:c,maskTextSelector:l,unmaskTextSelector:u,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===m?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===m?{}:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:!0===k||"all"===k?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===k,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===k?{}:k,dataURLOptions:v,inlineImages:h,recordCanvas:p,preserveWhiteSpace:S,onSerialize:_,onIframeLoad:b,iframeLoadTimeout:w,onStylesheetLoad:I,stylesheetLoadTimeout:T,keepIframeSrcFn:C,newlyAddedElement:!1})}(document,{mirror:Nt,blockClass:o,blockSelector:i,unblockSelector:a,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:m,inlineStylesheet:f,maskAllInputs:H,maskAttributeFn:v,maskInputFn:S,maskTextFn:_,slimDOM:j,dataURLOptions:T,recordCanvas:E,inlineImages:D,onSerialize:e=>{Ne(e,Nt)&&G.addIframe(e),Fe(e,Nt)&&J.trackLinkElement(e),Be(e)&&ee.addShadowRoot(e.shadowRoot,document)},onIframeLoad:(e,t)=>{G.attachIframe(e,t),ee.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{J.attachLinkElement(e,t)},keepIframeSrcFn:L});if(!t)return console.warn("Failed to snapshot the document");Ot({type:qe.FullSnapshot,data:{node:t,initialOffset:Ee(window)}}),ut.forEach((e=>e.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&J.adoptStyleSheets(document.adoptedStyleSheets,Nt.getId(document))};Lt=te;try{const e=[],t=e=>ct(_t)({onMutation:B,mutationCb:K,mousemoveCb:(e,t)=>Ot({type:qe.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.MouseInteraction,...e}}),scrollCb:V,viewportResizeCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.ViewportResize,...e}}),inputCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.Input,...e}}),mediaInteractionCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.MediaInteraction,...e}}),styleSheetRuleCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.StyleSheetRule,...e}}),styleDeclarationCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.StyleDeclaration,...e}}),canvasMutationCb:Y,fontCb:e=>Ot({type:qe.IncrementalSnapshot,data:{source:Xe.Font,...e}}),selectionCb:e=>{Ot({type:qe.IncrementalSnapshot,data:{source:Xe.Selection,...e}})},customElementCb:e=>{Ot({type:qe.IncrementalSnapshot,data:{source:Xe.CustomElement,...e}})},blockClass:o,ignoreClass:c,ignoreSelector:l,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:m,maskInputOptions:H,inlineStylesheet:f,sampling:I,recordCanvas:E,inlineImages:D,userTriggeredOnInput:R,collectFonts:A,doc:e,maskAttributeFn:v,maskInputFn:S,maskTextFn:_,keepIframeSrcFn:L,blockSelector:i,unblockSelector:a,slimDOMOptions:j,dataURLOptions:T,mirror:Nt,iframeManager:G,stylesheetManager:J,shadowDomManager:ee,processedNodeManager:Q,canvasManager:Z,ignoreCSSAttributes:N,plugins:(0,s.x)([O,"optionalAccess",e=>e.filter,"call",e=>e((e=>e.observer)),"optionalAccess",e=>e.map,"call",e=>e((e=>({observer:e.observer,options:e.options,callback:t=>Ot({type:qe.Plugin,data:{plugin:e.name,payload:t}})})))])||[]},{});G.addLoadListener((n=>{try{e.push(t(n.contentDocument))}catch(r){console.warn(r)}}));const n=()=>{te(),e.push(t(document))};return"interactive"===document.readyState||"complete"===document.readyState?n():(e.push(Se("DOMContentLoaded",(()=>{Ot({type:qe.DomContentLoaded,data:{}}),"DOMContentLoaded"===M&&n()}))),e.push(Se("load",(()=>{Ot({type:qe.Load,data:{}}),"load"===M&&n()}),window))),()=>{e.forEach((e=>e())),Q.destroy(),Lt=void 0,at()}}catch(re){console.warn(re)}}Ft.mirror=Nt,Ft.takeFullSnapshot=function(e){if(!Lt)throw new Error("please take full snapshot after start recording");Lt(e)};function Bt(e){return e>9999999999?e:1e3*e}function Pt(e){return e>9999999999?e/1e3:e}function Ut(e,t){"sentry.transaction"!==t.category&&(["ui.click","ui.input"].includes(t.category)?e.triggerUserActivity():e.checkAndHandleExpiredSession(),e.addUpdate((()=>(e.throttledAddEvent({type:qe.Custom,timestamp:1e3*(t.timestamp||0),data:{tag:"breadcrumb",payload:(0,m.Fv)(t,10,1e3)}}),"console"===t.category))))}function zt(e){return e.closest("button,a")||e}function Ht(e){const t=Wt(e);return t&&t instanceof Element?zt(t):t}function Wt(e){return function(e){return"object"===typeof e&&!!e&&"target"in e}(e)?e.target:e}let jt;function $t(e){return jt||(jt=[],(0,f.hl)(M,"open",(function(e){return function(...t){if(jt)try{jt.forEach((e=>e()))}catch(n){}return e.apply(M,t)}}))),jt.push(e),()=>{const t=jt?jt.indexOf(e):-1;t>-1&&jt.splice(t,1)}}class qt{constructor(e,t,n=Ut){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=t.timeout/1e3,this._threshold=t.threshold/1e3,this._scollTimeout=t.scrollTimeout/1e3,this._replay=e,this._ignoreSelector=t.ignoreSelector,this._addBreadcrumbEvent=n}addListeners(){const e=$t((()=>{this._lastMutation=Kt()}));this._teardown=()=>{e(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(e,t){if(function(e,t){if(!Xt.includes(e.tagName))return!0;if("INPUT"===e.tagName&&!["submit","button"].includes(e.getAttribute("type")||""))return!0;if("A"===e.tagName&&(e.hasAttribute("download")||e.hasAttribute("target")&&"_self"!==e.getAttribute("target")))return!0;if(t&&e.matches(t))return!0;return!1}(t,this._ignoreSelector)||!function(e){return!(!e.data||"number"!==typeof e.data.nodeId||!e.timestamp)}(e))return;const n={timestamp:Pt(e.timestamp),clickBreadcrumb:e,clickCount:0,node:t};this._clicks.some((e=>e.node===n.node&&Math.abs(e.timestamp-n.timestamp)<1))||(this._clicks.push(n),1===this._clicks.length&&this._scheduleCheckClicks())}registerMutation(e=Date.now()){this._lastMutation=Pt(e)}registerScroll(e=Date.now()){this._lastScroll=Pt(e)}registerClick(e){const t=zt(e);this._handleMultiClick(t)}_handleMultiClick(e){this._getClicks(e).forEach((e=>{e.clickCount++}))}_getClicks(e){return this._clicks.filter((t=>t.node===e))}_checkClicks(){const e=[],t=Kt();this._clicks.forEach((n=>{!n.mutationAfter&&this._lastMutation&&(n.mutationAfter=n.timestamp<=this._lastMutation?this._lastMutation-n.timestamp:void 0),!n.scrollAfter&&this._lastScroll&&(n.scrollAfter=n.timestamp<=this._lastScroll?this._lastScroll-n.timestamp:void 0),n.timestamp+this._timeout<=t&&e.push(n)}));for(const n of e){const e=this._clicks.indexOf(n);e>-1&&(this._generateBreadcrumbs(n),this._clicks.splice(e,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(e){const t=this._replay,n=e.scrollAfter&&e.scrollAfter<=this._scollTimeout,r=e.mutationAfter&&e.mutationAfter<=this._threshold,s=!n&&!r,{clickCount:o,clickBreadcrumb:i}=e;if(s){const n=1e3*Math.min(e.mutationAfter||this._timeout,this._timeout),r=n<1e3*this._timeout?"mutation":"timeout",s={type:"default",message:i.message,timestamp:i.timestamp,category:"ui.slowClickDetected",data:{...i.data,url:M.location.href,route:t.getCurrentRoute(),timeAfterClickMs:n,endReason:r,clickCount:o||1}};this._addBreadcrumbEvent(t,s)}else if(o>1){const e={type:"default",message:i.message,timestamp:i.timestamp,category:"ui.multiClick",data:{...i.data,url:M.location.href,route:t.getCurrentRoute(),clickCount:o,metric:!0}};this._addBreadcrumbEvent(t,e)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=setTimeout((()=>this._checkClicks()),1e3)}}const Xt=["A","BUTTON","INPUT"];function Kt(){return Date.now()/1e3}function Vt(e,t){try{if(!function(e){return 3===e.type}(t))return;const{source:n}=t.data;if(n===Xe.Mutation&&e.registerMutation(t.timestamp),n===Xe.Scroll&&e.registerScroll(t.timestamp),function(e){return e.data.source===Xe.MouseInteraction}(t)){const{type:n,id:r}=t.data,s=Ft.mirror.getNode(r);s instanceof HTMLElement&&n===Ke.Click&&e.registerClick(s)}}catch(n){}}function Yt(e){return{timestamp:Date.now()/1e3,type:"default",...e}}var Jt;!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(Jt||(Jt={}));const Gt=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]);function Qt(e){const t={};!e["data-sentry-component"]&&e["data-sentry-element"]&&(e["data-sentry-component"]=e["data-sentry-element"]);for(const n in e)if(Gt.has(n)){let r=n;"data-testid"!==n&&"data-test-id"!==n||(r="testId"),t[r]=e[n]}return t}const Zt=e=>t=>{if(!e.isEnabled())return;const n=function(e){const{target:t,message:n}=function(e){const t="click"===e.name;let n,r=null;try{r=t?Ht(e.event):Wt(e.event),n=(0,y.Rt)(r,{maxStringLength:200})||"<unknown>"}catch(s){n="<unknown>"}return{target:r,message:n}}(e);return Yt({category:`ui.${e.name}`,...en(t,n)})}(t);if(!n)return;const r="click"===t.name,s=r?t.event:void 0;var o,i,a;!(r&&e.clickDetector&&s&&s.target)||s.altKey||s.metaKey||s.ctrlKey||s.shiftKey||(o=e.clickDetector,i=n,a=Ht(t.event),o.handleClick(i,a)),Ut(e,n)};function en(e,t){const n=Ft.mirror.getId(e),r=n&&Ft.mirror.getNode(n),s=r&&Ft.mirror.getMeta(r),o=s&&function(e){return e.type===Jt.Element}(s)?s:null;return{message:t,data:o?{nodeId:n,node:{id:n,tagName:o.tagName,textContent:Array.from(o.childNodes).map((e=>e.type===Jt.Text&&e.textContent)).filter(Boolean).map((e=>e.trim())).join(""),attributes:Qt(o.attributes)}}:{}}}function tn(e,t){if(!e.isEnabled())return;e.updateUserActivity();const n=function(e){const{metaKey:t,shiftKey:n,ctrlKey:r,altKey:s,key:o,target:i}=e;if(!i||function(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||e.isContentEditable}(i)||!o)return null;const a=t||r||s,c=1===o.length;if(!a&&c)return null;const l=(0,y.Rt)(i,{maxStringLength:200})||"<unknown>",u=en(i,l);return Yt({category:"ui.keyDown",message:l,data:{...u.data,metaKey:t,shiftKey:n,ctrlKey:r,altKey:s,key:o}})}(t);n&&Ut(e,n)}const nn={resource:function(e){const{entryType:t,initiatorType:n,name:r,responseEnd:s,startTime:o,decodedBodySize:i,encodedBodySize:a,responseStatus:c,transferSize:l}=e;if(["fetch","xmlhttprequest"].includes(n))return null;return{type:`${t}.${n}`,start:sn(o),end:sn(s),name:r,data:{size:l,statusCode:c,decodedBodySize:i,encodedBodySize:a}}},paint:function(e){const{duration:t,entryType:n,name:r,startTime:s}=e,o=sn(s);return{type:n,name:r,start:o,end:o+t,data:void 0}},navigation:function(e){const{entryType:t,name:n,decodedBodySize:r,duration:s,domComplete:o,encodedBodySize:i,domContentLoadedEventStart:a,domContentLoadedEventEnd:c,domInteractive:l,loadEventStart:u,loadEventEnd:d,redirectCount:h,startTime:p,transferSize:m,type:f}=e;if(0===s)return null;return{type:`${t}.${f}`,start:sn(p),end:sn(o),name:n,data:{size:m,decodedBodySize:r,encodedBodySize:i,duration:s,domInteractive:l,domContentLoadedEventStart:a,domContentLoadedEventEnd:c,loadEventStart:u,loadEventEnd:d,domComplete:o,redirectCount:h}}}};function rn(e){return nn[e.entryType]?nn[e.entryType](e):null}function sn(e){return((g.Z1||M.performance.timeOrigin)+e)/1e3}function on(e){function t(t){e.performanceEntries.includes(t)||e.performanceEntries.push(t)}function n({entries:e}){e.forEach(t)}const r=[];return["navigation","paint","resource"].forEach((e=>{r.push((0,T._j)(e,n))})),r.push((0,T.$A)((({metric:t})=>{e.replayPerformanceEntries.push(function(e){const t=e.entries,n=t[t.length-1],r=n?n.element:void 0,s=e.value,o=sn(s);return{type:"largest-contentful-paint",name:"largest-contentful-paint",start:o,end:o,data:{value:s,size:s,nodeId:r?Ft.mirror.getId(r):void 0}}}(t))}))),()=>{r.forEach((e=>e()))}}const an="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function cn(e,t){an&&(k.kg.info(e),t&&un(e))}function ln(e,t){an&&(k.kg.info(e),t&&setTimeout((()=>{un(e)}),0))}function un(e){(0,o.n)({category:"console",data:{logger:"replay"},level:"info",message:e},{level:"info"})}class dn extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class hn{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(e){const t=JSON.stringify(e).length;if(this._totalSize+=t,this._totalSize>L)throw new dn;this.events.push(e)}finish(){return new Promise((e=>{const t=this.events;this.clear(),e(JSON.stringify(t))}))}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){const e=this.events.map((e=>e.timestamp)).sort()[0];return e?Bt(e):null}}class pn{constructor(e){this._worker=e,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise(((e,t)=>{this._worker.addEventListener("message",(({data:n})=>{n.success?e():t()}),{once:!0}),this._worker.addEventListener("error",(e=>{t(e)}),{once:!0})}))),this._ensureReadyPromise}destroy(){cn("[Replay] Destroying compression worker"),this._worker.terminate()}postMessage(e,t){const n=this._getAndIncrementId();return new Promise(((r,s)=>{const o=({data:t})=>{const i=t;if(i.method===e&&i.id===n){if(this._worker.removeEventListener("message",o),!i.success)return an&&k.kg.error("[Replay]",i.response),void s(new Error("Error in compression worker"));r(i.response)}};this._worker.addEventListener("message",o),this._worker.postMessage({id:n,method:e,arg:t})}))}_getAndIncrementId(){return this._id++}}class mn{constructor(e){this._worker=new pn(e),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(e){const t=Bt(e.timestamp);(!this._earliestTimestamp||t<this._earliestTimestamp)&&(this._earliestTimestamp=t);const n=JSON.stringify(e);return this._totalSize+=n.length,this._totalSize>L?Promise.reject(new dn):this._sendEventToWorker(n)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,(e=>{an&&k.kg.warn('[Replay] Sending "clear" message to worker failed',e)}))}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(e){return this._worker.postMessage("addEvent",e)}async _finishRequest(){const e=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,e}}class fn{constructor(e){this._fallback=new hn,this._compression=new mn(e),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(e){this._used.hasCheckout=e}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(e){return this._used.addEvent(e)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(e){return void cn("[Replay] Failed to load the compression worker, falling back to simple buffer")}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){const{events:e,hasCheckout:t}=this._fallback,n=[];for(const s of e)n.push(this._compression.addEvent(s));this._compression.hasCheckout=t,this._used=this._compression;try{await Promise.all(n)}catch(r){an&&k.kg.warn("[Replay] Failed to add events when switching buffers.",r)}}}function yn({useCompression:e,workerUrl:t}){if(e&&window.Worker){const e=function(e){try{const t=e||function(){if("undefined"===typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__)return function(){const e=new Blob(['var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,e){for(var i=new n(31),a=0;a<31;++a)i[a]=e+=1<<t[a-1];var s=new r(i[30]);for(a=1;a<30;++a)for(var o=i[a];o<i[a+1];++o)s[o]=o-i[a]<<5|a;return{b:i,r:s}},o=s(e,2),f=o.b,h=o.r;f[28]=258,h[258]=28;for(var l=s(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,a=0,s=new n(r);a<i;++a)t[a]&&++s[t[a]-1];var o,f=new n(r);for(a=1;a<r;++a)f[a]=f[a-1]+s[a-1]<<1;if(e){o=new n(1<<r);var h=15-r;for(a=0;a<i;++a)if(t[a])for(var l=a<<4|t[a],c=r-t[a],v=f[t[a]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>h]=l}else for(o=new n(i),a=0;a<i;++a)t[a]&&(o[a]=u[f[t[a]-1]++]>>15-t[a]);return o},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var w=new t(32);for(c=0;c<32;++c)w[c]=5;var p=d(g,9,0),y=d(w,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==r||r<0)&&(r=0),(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},A=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},_=function(r,e){for(var i=[],a=0;a<r.length;++a)r[a]&&i.push({s:a,f:r[a]});var s=i.length,o=i.slice();if(!s)return{t:F,l:0};if(1==s){var f=new t(i[0].s+1);return f[i[0].s]=1,{t:f,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var h=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:h.f+l.f,l:h,r:l};c!=s-1;)h=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:h.f+l.f,l:h,r:l};var d=o[0].s;for(a=1;a<s;++a)o[a].s>d&&(d=o[a].s);var g=new n(d+1),w=x(i[c-1],g,0);if(w>e){a=0;var p=0,y=w-e,m=1<<y;for(o.sort((function(t,n){return g[n.s]-g[t.s]||t.f-n.f}));a<s;++a){var b=o[a].s;if(!(g[b]>e))break;p+=m-(1<<w-g[b]),g[b]=e}for(p>>=y;p>0;){var M=o[a].s;g[M]<e?p-=1<<e-g[M]++-1:++a}for(;a>=0&&p;--a){var E=o[a].s;g[E]==e&&(--g[E],++p)}w=e}return{t:new t(g),l:w}},x=function(t,n,r){return-1==t.s?Math.max(x(t.l,n,r+1),x(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,a=t[0],s=1,o=function(t){e[i++]=t},f=1;f<=r;++f)if(t[f]==a&&f!=r)++s;else{if(!a&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(a),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(a);s=1,a=t[f]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<e;++a)t[i+a+4]=r[a];return 8*(i+4+e)},C=function(t,r,s,o,f,h,l,u,c,v,m){z(r,m++,s),++f[256];for(var b=_(f,15),M=b.t,E=b.l,x=_(h,15),C=x.t,U=x.l,F=D(M),I=F.c,S=F.n,L=D(C),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=_(q,7),H=G.t,J=G.l,K=19;K>4&&!H[a[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(f,g)+T(h,w)+l,X=T(f,M)+T(h,C)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(C,U,0),R=C;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[a[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=p,P=g,Q=y,R=w;for(B=0;B<u;++B){var rt=o[B];if(rt>255){A(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;A(r,m,Q[et]),m+=R[et],et>3&&(A(r,m,rt>>5&8191),m+=i[et])}else A(r,m,N[rt]),m+=P[rt]}return A(r,m,N[256]),m+P[256]},U=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}},L=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,a=0|r.length,s=0;s!=a;){for(var o=Math.min(s+2655,a);s<o;++s)i+=e+=r[s];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},O=function(a,s,o,f,u){if(!u&&(u={l:1},s.dictionary)){var c=s.dictionary.subarray(-32768),v=new t(c.length+a.length);v.set(c),v.set(a,c.length),a=v,u.w=c.length}return function(a,s,o,f,u,c){var v=c.z||a.length,d=new t(f+v+5*(1+Math.ceil(v/7e3))+u),g=d.subarray(f,d.length-u),w=c.l,p=7&(c.r||0);if(s){p&&(g[0]=c.r>>3);for(var y=U[s-1],M=y>>13,E=8191&y,z=(1<<o)-1,A=c.p||new n(32768),_=c.h||new n(z+1),x=Math.ceil(o/3),D=2*x,T=function(t){return(a[t]^a[t+1]<<x^a[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=_[H];if(A[J]=K,_[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!w)){p=C(a,g,0,F,I,S,O,q,G,j-G,p),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(a[j+Q]==a[j+Q-W]){for(var $=0;$<Z&&a[j+$]==a[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-A[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=A[J])&32767}if(R){F[q++]=268435456|h[Q]<<18|l[R];var it=31&h[Q],at=31&l[R];O+=e[it]+i[at],++I[257+it],++S[at],B=j+Q,++L}else F[q++]=a[j],++I[a[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=a[j],++I[a[j]];p=C(a,g,w,F,I,S,O,q,G,j-G,p),w||(c.r=7&p|g[p/8|0]<<3,p-=7,c.h=_,c.p=A,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+w;j+=65535){var st=j+65535;st>=v&&(g[p/8|0]=w,st=v),p=k(g,p+1,a.subarray(j,st))}c.i=v}return b(d,0,f+m(p)+u)}(a,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):12+s.mem,o,f,u)},j=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},q=function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&j(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}},B=function(t){return 10+(t.filename?t.filename.length+1:0)},G=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(O(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;a&&(this.b.set(n.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(a),32768),this.s.z=n.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n}();var H=function(){function t(t,n){this.c=L(),this.v=1,G.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),G.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=O(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=L();i.p(n.dictionary),j(t,2,i.d())}}(r,this.o),this.v=0),n&&j(r,r.length-4,this.c.d()),this.ondata(r,n)},t}(),J="undefined"!=typeof TextEncoder&&new TextEncoder,K="undefined"!=typeof TextDecoder&&new TextDecoder;try{K.decode(F,{stream:!0})}catch(t){}var N=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(P(t),this.d=n||!1)},t}();function P(n,r){if(r){for(var e=new t(n.length),i=0;i<n.length;++i)e[i]=n.charCodeAt(i);return e}if(J)return J.encode(n);var a=n.length,s=new t(n.length+(n.length>>1)),o=0,f=function(t){s[o++]=t};for(i=0;i<a;++i){if(o+5>s.length){var h=new t(o+8+(a-i<<1));h.set(s),s=h}var l=n.charCodeAt(i);l<128||r?f(l):l<2048?(f(192|l>>6),f(128|63&l)):l>55295&&l<57344?(f(240|(l=65536+(1047552&l)|1023&n.charCodeAt(++i))>>18),f(128|l>>12&63),f(128|l>>6&63),f(128|63&l)):(f(224|l>>12),f(128|l>>6&63),f(128|63&l))}return b(s,0,o)}function Q(t){return function(t,n){n||(n={});var r=S(),e=t.length;r.p(t);var i=O(t,n,B(n),8),a=i.length;return q(i,n),j(i,a-8,r.d()),j(i,a-4,e),i}(P(t))}const R=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(let r=0,e=t.length;r<e;r++)n+=t[r].length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new H,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new N(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},V={clear:()=>{R.clear()},addEvent:t=>R.addEvent(t),finish:()=>R.finish(),compress:t=>Q(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in V&&"function"==typeof V[n])try{const t=V[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});']);return URL.createObjectURL(e)}();return""}();if(!t)return;cn("[Replay] Using compression worker"+(e?` from ${e}`:""));const n=new Worker(t);return new fn(n)}catch(t){cn("[Replay] Failed to create compression worker")}}(t);if(e)return e}return cn("[Replay] Using simple buffer"),new hn}function gn(){try{return"sessionStorage"in M&&!!M.sessionStorage}catch(e){return!1}}function kn(e){!function(){if(!gn())return;try{M.sessionStorage.removeItem(R)}catch(e){}}(),e.session=void 0}function vn(e){return void 0!==e&&Math.random()<e}function Sn(e){const t=Date.now();return{id:e.id||(0,v.DM)(),started:e.started||t,lastActivity:e.lastActivity||t,segmentId:e.segmentId||0,sampled:e.sampled,previousSessionId:e.previousSessionId}}function _n(e){if(gn())try{M.sessionStorage.setItem(R,JSON.stringify(e))}catch(t){}}function bn({sessionSampleRate:e,allowBuffering:t,stickySession:n=!1},{previousSessionId:r}={}){const s=function(e,t){return vn(e)?"session":!!t&&"buffer"}(e,t),o=Sn({sampled:s,previousSessionId:r});return n&&_n(o),o}function wn(e,t,n=+new Date){return null===e||void 0===t||t<0||0!==t&&e+t<=n}function In(e,{maxReplayDuration:t,sessionIdleExpire:n,targetTime:r=Date.now()}){return wn(e.started,t,r)||wn(e.lastActivity,n,r)}function Tn(e,{sessionIdleExpire:t,maxReplayDuration:n}){return!!In(e,{sessionIdleExpire:t,maxReplayDuration:n})&&("buffer"!==e.sampled||0!==e.segmentId)}function Cn({traceInternals:e,sessionIdleExpire:t,maxReplayDuration:n,previousSessionId:r},s){const o=s.stickySession&&function(e){if(!gn())return null;try{const t=M.sessionStorage.getItem(R);if(!t)return null;const n=JSON.parse(t);return ln("[Replay] Loading existing session",e),Sn(n)}catch(t){return null}}(e);return o?Tn(o,{sessionIdleExpire:t,maxReplayDuration:n})?(ln("[Replay] Session in sessionStorage is expired, creating new one..."),bn(s,{previousSessionId:o.id})):o:(ln("[Replay] Creating new session",e),bn(s,{previousSessionId:r}))}function En(e,t,n){return!!Mn(e,t)&&(xn(e,t,n),!0)}async function xn(e,t,n){if(!e.eventBuffer)return null;try{n&&"buffer"===e.recordingMode&&e.eventBuffer.clear(),n&&(e.eventBuffer.hasCheckout=!0);const r=function(e,t){try{if("function"===typeof t&&function(e){return e.type===qe.Custom}(e))return t(e)}catch(n){return an&&k.kg.error("[Replay] An error occured in the `beforeAddRecordingEvent` callback, skipping the event...",n),null}return e}(t,e.getOptions().beforeAddRecordingEvent);if(!r)return;return await e.eventBuffer.addEvent(r)}catch(r){const t=r&&r instanceof dn?"addEventSizeExceeded":"addEvent";an&&k.kg.error(r),await e.stop({reason:t});const n=(0,i.s3)();n&&n.recordDroppedEvent("internal_sdk_error","replay")}}function Mn(e,t){if(!e.eventBuffer||e.isPaused()||!e.isEnabled())return!1;const n=Bt(t.timestamp);return!(n+e.timeouts.sessionIdlePause<Date.now())&&(!(n>e.getContext().initialTimestamp+e.getOptions().maxReplayDuration)||(cn(`[Replay] Skipping event with timestamp ${n} because it is after maxReplayDuration`,e.getOptions()._experiments.traceInternals),!1))}function Rn(e){return!e.type}function An(e){return"transaction"===e.type}function Dn(e){return"feedback"===e.type}function On(e){return(t,n)=>{if(!e.isEnabled()||!Rn(t)&&!An(t))return;const r=n&&n.statusCode;!r||r<200||r>=300||(An(t)?function(e,t){const n=e.getContext();t.contexts&&t.contexts.trace&&t.contexts.trace.trace_id&&n.traceIds.size<100&&n.traceIds.add(t.contexts.trace.trace_id)}(e,t):function(e,t){const n=e.getContext();t.event_id&&n.errorIds.size<100&&n.errorIds.add(t.event_id);if("buffer"!==e.recordingMode||!t.tags||!t.tags.replayId)return;const{beforeErrorSampling:r}=e.getOptions();if("function"===typeof r&&!r(t))return;setTimeout((()=>{e.sendBufferedReplayOrFlush()}))}(e,t))}}function Ln(e){return t=>{e.isEnabled()&&Rn(t)&&function(e,t){const n=t.exception&&t.exception.values&&t.exception.values[0].value;if("string"!==typeof n)return;if(n.match(/reactjs\.org\/docs\/error-decoder\.html\?invariant=(418|419|422|423|425)/)||n.match(/(does not match server-rendered HTML|Hydration failed because)/i)){Ut(e,Yt({category:"replay.hydrate-error"}))}}(e,t)}}function Nn(e){const t=(0,i.s3)();t&&t.on("beforeAddBreadcrumb",(t=>function(e,t){if(!e.isEnabled()||!Fn(t))return;const n=function(e){if(!Fn(e)||["fetch","xhr","sentry.event","sentry.transaction"].includes(e.category)||e.category.startsWith("ui."))return null;if("console"===e.category)return function(e){const t=e.data&&e.data.arguments;if(!Array.isArray(t)||0===t.length)return Yt(e);let n=!1;const r=t.map((e=>{if(!e)return e;if("string"===typeof e)return e.length>O?(n=!0,`${e.slice(0,O)}\u2026`):e;if("object"===typeof e)try{const t=(0,m.Fv)(e,7);return JSON.stringify(t).length>O?(n=!0,`${JSON.stringify(t,null,2).slice(0,O)}\u2026`):t}catch(t){}return e}));return Yt({...e,data:{...e.data,arguments:r,...n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(e);return Yt(e)}(t);n&&Ut(e,n)}(e,t)))}function Fn(e){return!!e.category}function Bn(e){return Object.assign(((t,n)=>{if(!e.isEnabled())return t;if(function(e){return"replay_event"===e.type}(t))return delete t.breadcrumbs,t;if(!Rn(t)&&!An(t)&&!Dn(t))return t;if(!e.checkAndHandleExpiredSession())return t;if(Dn(t))return e.flush(),t.contexts.feedback.replay_id=e.getSessionId(),function(e,t){e.triggerUserActivity(),e.addUpdate((()=>!t.timestamp||(e.throttledAddEvent({type:qe.Custom,timestamp:1e3*t.timestamp,data:{tag:"breadcrumb",payload:{timestamp:t.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:t.event_id}}}}),!1)))}(e,t),t;if(function(e,t){return!(e.type||!e.exception||!e.exception.values||!e.exception.values.length)&&!(!t.originalException||!t.originalException.__rrweb__)}(t,n)&&!e.getOptions()._experiments.captureExceptions)return an&&k.kg.log("[Replay] Ignoring error from rrweb internals",t),null;const r=function(e,t){return"buffer"===e.recordingMode&&t.message!==A&&!(!t.exception||t.type)&&vn(e.getOptions().errorSampleRate)}(e,t);return(r||"session"===e.recordingMode)&&(t.tags={...t.tags,replayId:e.getSessionId()}),t}),{id:"Replay"})}function Pn(e,t){return t.map((({type:t,start:n,end:r,name:s,data:o})=>{const i=e.throttledAddEvent({type:qe.Custom,timestamp:n,data:{tag:"performanceSpan",payload:{op:t,description:s,startTimestamp:n,endTimestamp:r,data:o}}});return"string"===typeof i?Promise.resolve(null):i}))}function Un(e){return t=>{if(!e.isEnabled())return;const n=function(e){const{from:t,to:n}=e,r=Date.now()/1e3;return{type:"navigation.push",start:r,end:r,name:n,data:{previous:t}}}(t);null!==n&&(e.getContext().urls.push(n.name),e.triggerUserActivity(),e.addUpdate((()=>(Pn(e,[n]),!1))))}}function zn(e,t){e.isEnabled()&&null!==t&&(function(e,t){return(!an||!e.getOptions()._experiments.traceInternals)&&(0,a.W)(t,(0,i.s3)())}(e,t.name)||e.addUpdate((()=>(Pn(e,[t]),!0))))}function Hn(e){if(!e)return;const t=new TextEncoder;try{if("string"===typeof e)return t.encode(e).length;if(e instanceof URLSearchParams)return t.encode(e.toString()).length;if(e instanceof FormData){const n=Yn(e);return t.encode(n).length}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength}catch(n){}}function Wn(e){if(!e)return;const t=parseInt(e,10);return isNaN(t)?void 0:t}function jn(e){try{if("string"===typeof e)return[e];if(e instanceof URLSearchParams)return[e.toString()];if(e instanceof FormData)return[Yn(e)];if(!e)return[void 0]}catch(t){return an&&k.kg.warn("[Replay] Failed to serialize body",e),[void 0,"BODY_PARSE_ERROR"]}return an&&k.kg.info("[Replay] Skipping network body because of body type",e),[void 0,"UNPARSEABLE_BODY_TYPE"]}function $n(e,t){if(!e)return{headers:{},size:void 0,_meta:{warnings:[t]}};const n={...e._meta},r=n.warnings||[];return n.warnings=[...r,t],e._meta=n,e}function qn(e,t){if(!t)return null;const{startTimestamp:n,endTimestamp:r,url:s,method:o,statusCode:i,request:a,response:c}=t;return{type:e,start:n/1e3,end:r/1e3,name:s,data:(0,f.Jr)({method:o,statusCode:i,request:a,response:c})}}function Xn(e){return{headers:{},size:e,_meta:{warnings:["URL_SKIPPED"]}}}function Kn(e,t,n){if(!t&&0===Object.keys(e).length)return;if(!t)return{headers:e};if(!n)return{headers:e,size:t};const r={headers:e,size:t},{body:s,warnings:o}=function(e){if(!e||"string"!==typeof e)return{body:e};const t=e.length>D,n=function(e){const t=e[0],n=e[e.length-1];return"["===t&&"]"===n||"{"===t&&"}"===n}(e);if(t){const t=e.slice(0,D);return n?{body:t,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${t}\u2026`,warnings:["TEXT_TRUNCATED"]}}if(n)try{return{body:JSON.parse(e)}}catch(r){}return{body:e}}(n);return r.body=s,o&&o.length>0&&(r._meta={warnings:o}),r}function Vn(e,t){return Object.keys(e).reduce(((n,r)=>{const s=r.toLowerCase();return t.includes(s)&&e[r]&&(n[s]=e[r]),n}),{})}function Yn(e){return new URLSearchParams(e).toString()}function Jn(e,t){const n=function(e,t=M.document.baseURI){if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith(M.location.origin))return e;const n=new URL(e,t);if(n.origin!==new URL(t).origin)return e;const r=n.href;if(!e.endsWith("/")&&r.endsWith("/"))return r.slice(0,-1);return r}(e);return(0,S.U0)(n,t)}async function Gn(e,t,n){try{const r=await async function(e,t,n){const r=Date.now(),{startTimestamp:s=r,endTimestamp:o=r}=t,{url:i,method:a,status_code:c=0,request_body_size:l,response_body_size:u}=e.data,d=Jn(i,n.networkDetailAllowUrls)&&!Jn(i,n.networkDetailDenyUrls),h=d?function({networkCaptureBodies:e,networkRequestHeaders:t},n,r){const s=n?function(e,t){if(1===e.length&&"string"!==typeof e[0])return er(e[0],t);if(2===e.length)return er(e[1],t);return{}}(n,t):{};if(!e)return Kn(s,r,void 0);const o=Qn(n),[i,a]=jn(o),c=Kn(s,r,i);if(a)return $n(c,a);return c}(n,t.input,l):Xn(l),p=await async function(e,{networkCaptureBodies:t,networkResponseHeaders:n},r,s){if(!e&&void 0!==s)return Xn(s);const o=r?Zn(r.headers,n):{};if(!r||!t&&void 0!==s)return Kn(o,s,void 0);const[i,a]=await async function(e){const t=function(e){try{return e.clone()}catch(t){an&&k.kg.warn("[Replay] Failed to clone response body",t)}}(e);if(!t)return[void 0,"BODY_PARSE_ERROR"];try{const e=await function(e){return new Promise(((t,n)=>{const r=setTimeout((()=>n(new Error("Timeout while trying to read response body"))),500);(async function(e){return await e.text()})(e).then((e=>t(e)),(e=>n(e))).finally((()=>clearTimeout(r)))}))}(t);return[e]}catch(n){return an&&k.kg.warn("[Replay] Failed to get text body from response",n),[void 0,"BODY_PARSE_ERROR"]}}(r),c=function(e,{networkCaptureBodies:t,responseBodySize:n,captureDetails:r,headers:s}){try{const o=e&&e.length&&void 0===n?Hn(e):n;return r?Kn(s,o,t?e:void 0):Xn(o)}catch(o){return an&&k.kg.warn("[Replay] Failed to serialize response body",o),Kn(s,n,void 0)}}(i,{networkCaptureBodies:t,responseBodySize:s,captureDetails:e,headers:o});if(a)return $n(c,a);return c}(d,n,t.response,u);return{startTimestamp:s,endTimestamp:o,url:i,method:a,statusCode:c,request:h,response:p}}(e,t,n),s=qn("resource.fetch",r);zn(n.replay,s)}catch(r){an&&k.kg.error("[Replay] Failed to capture fetch breadcrumb",r)}}function Qn(e=[]){if(2===e.length&&"object"===typeof e[1])return e[1].body}function Zn(e,t){const n={};return t.forEach((t=>{e.get(t)&&(n[t]=e.get(t))})),n}function er(e,t){if(!e)return{};const n=e.headers;return n?n instanceof Headers?Zn(n,t):Array.isArray(n)?{}:Vn(n,t):{}}async function tr(e,t,n){try{const r=function(e,t,n){const r=Date.now(),{startTimestamp:s=r,endTimestamp:o=r,input:i,xhr:a}=t,{url:c,method:l,status_code:u=0,request_body_size:d,response_body_size:h}=e.data;if(!c)return null;if(!a||!Jn(c,n.networkDetailAllowUrls)||Jn(c,n.networkDetailDenyUrls)){return{startTimestamp:s,endTimestamp:o,url:c,method:l,statusCode:u,request:Xn(d),response:Xn(h)}}const p=a[C.xU],m=p?Vn(p.request_headers,n.networkRequestHeaders):{},f=Vn(function(e){const t=e.getAllResponseHeaders();if(!t)return{};return t.split("\r\n").reduce(((e,t)=>{const[n,r]=t.split(": ");return e[n.toLowerCase()]=r,e}),{})}(a),n.networkResponseHeaders),[y,g]=n.networkCaptureBodies?jn(i):[void 0],[v,S]=n.networkCaptureBodies?function(e){const t=[];try{return[e.responseText]}catch(n){t.push(n)}try{return function(e,t){try{if("string"===typeof e)return[e];if(e instanceof Document)return[e.body.outerHTML];if("json"===t&&e&&"object"===typeof e)return[JSON.stringify(e)];if(!e)return[void 0]}catch(n){return an&&k.kg.warn("[Replay] Failed to serialize body",e),[void 0,"BODY_PARSE_ERROR"]}return an&&k.kg.info("[Replay] Skipping network body because of body type",e),[void 0,"UNPARSEABLE_BODY_TYPE"]}(e.response,e.responseType)}catch(n){t.push(n)}return an&&k.kg.warn("[Replay] Failed to get xhr response body",...t),[void 0]}(a):[void 0],_=Kn(m,d,y),b=Kn(f,h,v);return{startTimestamp:s,endTimestamp:o,url:c,method:l,statusCode:u,request:g?$n(_,g):_,response:S?$n(b,S):b}}(e,t,n),s=qn("resource.xhr",r);zn(n.replay,s)}catch(r){an&&k.kg.error("[Replay] Failed to capture xhr breadcrumb",r)}}function nr(e,t){const{xhr:n,input:r}=t;if(!n)return;const s=Hn(r),o=n.getResponseHeader("content-length")?Wn(n.getResponseHeader("content-length")):function(e,t){try{return Hn("json"===t&&e&&"object"===typeof e?JSON.stringify(e):e)}catch(n){return}}(n.response,n.responseType);void 0!==s&&(e.data.request_body_size=s),void 0!==o&&(e.data.response_body_size=o)}function rr(e){const t=(0,i.s3)();try{const{networkDetailAllowUrls:n,networkDetailDenyUrls:r,networkCaptureBodies:s,networkRequestHeaders:o,networkResponseHeaders:i}=e.getOptions(),a={replay:e,networkDetailAllowUrls:n,networkDetailDenyUrls:r,networkCaptureBodies:s,networkRequestHeaders:o,networkResponseHeaders:i};t&&t.on("beforeAddBreadcrumb",((e,t)=>function(e,t,n){if(!t.data)return;try{(function(e){return"xhr"===e.category})(t)&&function(e){return e&&e.xhr}(n)&&(nr(t,n),tr(t,n,e)),function(e){return"fetch"===e.category}(t)&&function(e){return e&&e.response}(n)&&(!function(e,t){const{input:n,response:r}=t,s=Hn(n?Qn(n):void 0),o=r?Wn(r.headers.get("content-length")):void 0;void 0!==s&&(e.data.request_body_size=s),void 0!==o&&(e.data.response_body_size=o)}(t,n),Gn(t,n,e))}catch(r){an&&k.kg.warn("Error when enriching network breadcrumb")}}(a,e,t)))}catch(n){}}function sr(e){const{jsHeapSizeLimit:t,totalJSHeapSize:n,usedJSHeapSize:r}=e,s=Date.now()/1e3;return{type:"memory",name:"memory",start:s,end:s,data:{memory:{jsHeapSizeLimit:t,totalJSHeapSize:n,usedJSHeapSize:r}}}}function or(e){let t=!1;return(n,r)=>{if(!e.checkAndHandleExpiredSession())return void(an&&k.kg.warn("[Replay] Received replay event after session expired."));const s=r||!t;t=!0,e.clickDetector&&Vt(e.clickDetector,n),e.addUpdate((()=>{if("buffer"===e.recordingMode&&s&&e.setInitialState(),!En(e,n,s))return!0;if(!s)return!1;if(function(e,t){if(!t||!e.session||0!==e.session.segmentId)return;En(e,function(e){const t=e.getOptions();return{type:qe.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:e.isRecordingCanvas(),sessionSampleRate:t.sessionSampleRate,errorSampleRate:t.errorSampleRate,useCompressionOption:t.useCompression,blockAllMedia:t.blockAllMedia,maskAllText:t.maskAllText,maskAllInputs:t.maskAllInputs,useCompression:!!e.eventBuffer&&"worker"===e.eventBuffer.type,networkDetailHasUrls:t.networkDetailAllowUrls.length>0,networkCaptureBodies:t.networkCaptureBodies,networkRequestHasHeaders:t.networkRequestHeaders.length>0,networkResponseHasHeaders:t.networkResponseHeaders.length>0}}}}(e),!1)}(e,s),e.session&&e.session.previousSessionId)return!0;if("buffer"===e.recordingMode&&e.session&&e.eventBuffer){const t=e.eventBuffer.getEarliestTimestamp();t&&(cn(`[Replay] Updating session start time to earliest event in buffer to ${new Date(t)}`,e.getOptions()._experiments.traceInternals),e.session.started=t,e.getOptions().stickySession&&_n(e.session))}return"session"===e.recordingMode&&e.flush(),!0}))}}async function ir({recordingData:e,replayId:t,segmentId:n,eventContext:r,timestamp:s,session:o}){const a=function({recordingData:e,headers:t}){let n;const r=`${JSON.stringify(t)}\n`;if("string"===typeof e)n=`${r}${e}`;else{const t=(new TextEncoder).encode(r);n=new Uint8Array(t.length+e.length),n.set(t),n.set(e,t.length)}return n}({recordingData:e,headers:{segment_id:n}}),{urls:c,errorIds:u,traceIds:d,initialTimestamp:h}=r,p=(0,i.s3)(),m=(0,i.nZ)(),f=p&&p.getTransport(),y=p&&p.getDsn();if(!p||!f||!y||!o.sampled)return(0,b.WD)({});const g={type:"replay_event",replay_start_timestamp:h/1e3,timestamp:s/1e3,error_ids:u,trace_ids:d,urls:c,replay_id:t,segment_id:n,replay_type:o.sampled},k=await async function({client:e,scope:t,replayId:n,event:r}){const s={event_id:n,integrations:"object"!==typeof e._integrations||null===e._integrations||Array.isArray(e._integrations)?void 0:Object.keys(e._integrations)};e.emit("preprocessEvent",r,s);const o=await(0,l.R)(e.getOptions(),r,s,t,e,(0,i.aF)());if(!o)return null;o.platform=o.platform||"javascript";const a=e.getSdkMetadata(),{name:c,version:u}=a&&a.sdk||{};return o.sdk={...o.sdk,name:c||"sentry.javascript.unknown",version:u||"0.0.0"},o}({scope:m,client:p,replayId:t,event:g});if(!k)return p.recordDroppedEvent("event_processor","replay",g),cn("An event processor returned `null`, will not send event."),(0,b.WD)({});delete k.sdkProcessingMetadata;const v=function(e,t,n,r){return(0,_.Jd)((0,_.Cd)(e,(0,_.HY)(e),r,n),[[{type:"replay_event"},e],[{type:"replay_recording",length:"string"===typeof t?(new TextEncoder).encode(t).length:t.length},t]])}(k,a,y,p.getOptions().tunnel);let S;try{S=await f.send(v)}catch(T){const e=new Error(A);try{e.cause=T}catch(C){}throw e}if("number"===typeof S.statusCode&&(S.statusCode<200||S.statusCode>=300))throw new ar(S.statusCode);const I=(0,w.WG)({},S);if((0,w.Q)(I,"replay"))throw new cr(I);return S}class ar extends Error{constructor(e){super(`Transport returned status code ${e}`)}}class cr extends Error{constructor(e){super("Rate limit hit"),this.rateLimits=e}}async function lr(e,t={count:0,interval:5e3}){const{recordingData:n,options:r}=e;if(n.length)try{return await ir(e),!0}catch(s){if(s instanceof ar||s instanceof cr)throw s;if((0,c.v)("Replays",{_retryCount:t.count}),an&&r._experiments&&r._experiments.captureExceptions&&(0,c.Tb)(s),t.count>=3){const e=new Error("Unable to send Replay - max retries exceeded");try{e.cause=s}catch(o){}throw e}return t.interval*=++t.count,new Promise(((n,r)=>{setTimeout((async()=>{try{await lr(e,t),n(!0)}catch(s){r(s)}}),t.interval)}))}}const ur="__THROTTLED";function dr(e,t,n){const r=new Map;let s=!1;return(...o)=>{const i=Math.floor(Date.now()/1e3);if((e=>{const t=e-n;r.forEach(((e,n)=>{n<t&&r.delete(n)}))})(i),[...r.values()].reduce(((e,t)=>e+t),0)>=t){const e=s;return s=!0,e?"__SKIPPED":ur}s=!1;const a=r.get(i)||0;return r.set(i,a+1),e(...o)}}class hr{constructor({options:e,recordingOptions:t}){hr.prototype.__init.call(this),hr.prototype.__init2.call(this),hr.prototype.__init3.call(this),hr.prototype.__init4.call(this),hr.prototype.__init5.call(this),hr.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=t,this._options=e,this._debouncedFlush=function(e,t,n){let r,s,o;const i=n&&n.maxWait?Math.max(n.maxWait,t):0;function a(){return c(),r=e(),r}function c(){void 0!==s&&clearTimeout(s),void 0!==o&&clearTimeout(o),s=o=void 0}function l(){return s&&clearTimeout(s),s=setTimeout(a,t),i&&void 0===o&&(o=setTimeout(a,i)),r}return l.cancel=c,l.flush=function(){return void 0!==s||void 0!==o?a():r},l}((()=>this._flush()),this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=dr(((e,t)=>function(e,t,n){return Mn(e,t)?xn(e,t,n):Promise.resolve(null)}(this,e,t)),300,5);const{slowClickTimeout:n,slowClickIgnoreSelectors:r}=this.getOptions(),s=n?{threshold:Math.min(3e3,n),timeout:n,scrollTimeout:300,ignoreSelector:r?r.join(","):""}:void 0;s&&(this.clickDetector=new qt(this,s))}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return Boolean(this._canvas)}getOptions(){return this._options}initializeSampling(e){const{errorSampleRate:t,sessionSampleRate:n}=this._options;t<=0&&n<=0||(this._initializeSessionForSampling(e),this.session?!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",ln(`[Replay] Starting replay in ${this.recordingMode} mode`,this._options._experiments.traceInternals),this._initializeRecording()):this._handleException(new Error("Unable to initialize and create session")))}start(){if(this._isEnabled&&"session"===this.recordingMode)throw new Error("Replay recording is already in progress");if(this._isEnabled&&"buffer"===this.recordingMode)throw new Error("Replay buffering is in progress, call `flush()` to save the replay");ln("[Replay] Starting replay in session mode",this._options._experiments.traceInternals),this._updateUserActivity();const e=Cn({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this._initializeRecording()}startBuffering(){if(this._isEnabled)throw new Error("Replay recording is already in progress");ln("[Replay] Starting replay in buffer mode",this._options._experiments.traceInternals);const e=Cn({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{const e=this._canvas;this._stopRecording=Ft({...this._recordingOptions,..."buffer"===this.recordingMode&&{checkoutEveryNms:6e4},emit:or(this),onMutation:this._onMutationHandler,...e?{recordCanvas:e.recordCanvas,getCanvasManager:e.getCanvasManager,sampling:e.sampling,dataURLOptions:e.dataURLOptions}:{}})}catch(e){this._handleException(e)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this._handleException(e),!1}}async stop({forceFlush:e=!1,reason:t}={}){if(this._isEnabled){this._isEnabled=!1;try{cn("[Replay] Stopping Replay"+(t?` triggered by ${t}`:""),this._options._experiments.traceInternals),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),e&&await this._flush({force:!0}),this.eventBuffer&&this.eventBuffer.destroy(),this.eventBuffer=null,kn(this)}catch(n){this._handleException(n)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording(),cn("[Replay] Pausing replay",this._options._experiments.traceInternals))}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording(),cn("[Replay] Resuming replay",this._options._experiments.traceInternals))}async sendBufferedReplayOrFlush({continueRecording:e=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();const t=Date.now();cn("[Replay] Converting buffer to session",this._options._experiments.traceInternals),await this.flushImmediate();const n=this.stopRecording();e&&n&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(t),this._updateSessionActivity(t),this._maybeSaveSession()),this.startRecording())}addUpdate(e){const t=e();"buffer"!==this.recordingMode&&!0!==t&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),this._stopRecording)this.checkAndHandleExpiredSession(),this._updateSessionActivity();else{if(!this._checkSession())return;this.resume()}}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session&&this.session.id}checkAndHandleExpiredSession(){if(!(this._lastActivity&&wn(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled))return!!this._checkSession();this.pause()}setInitialState(){const e=`${M.location.pathname}${M.location.hash}${M.location.search}`,t=`${M.location.origin}${e}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=t,this._context.initialTimestamp=Date.now(),this._context.urls.push(t)}throttledAddEvent(e,t){const n=this._throttledAddEvent(e,t);if(n===ur){const e=Yt({category:"replay.throttled"});this.addUpdate((()=>!En(this,{type:5,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e,metric:!0}})))}return n}getCurrentRoute(){const e=this.lastActiveSpan||(0,u.HN)(),t=e&&(0,u.Gx)(e),n=(t&&(0,u.XU)(t).data||{})[d.Zj];if(t&&n&&["route","custom"].includes(n))return(0,u.XU)(t).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=yn({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_handleException(e){an&&k.kg.error("[Replay]",e),an&&this._options._experiments&&this._options._experiments.captureExceptions&&(0,c.Tb)(e)}_initializeSessionForSampling(e){const t=this._options.errorSampleRate>0,n=Cn({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:t});this.session=n}_checkSession(){if(!this.session)return!1;const e=this.session;return!Tn(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(e),!1)}async _refreshSession(e){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(e.id))}_addListeners(){try{M.document.addEventListener("visibilitychange",this._handleVisibilityChange),M.addEventListener("blur",this._handleWindowBlur),M.addEventListener("focus",this._handleWindowFocus),M.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(!function(e){const t=(0,i.s3)();(0,E.O)(Zt(e)),(0,x.a)(Un(e)),Nn(e),rr(e);const n=Bn(e);(0,c.Qy)(n),t&&(t.on("beforeSendEvent",Ln(e)),t.on("afterSendEvent",On(e)),t.on("createDsc",(t=>{const n=e.getSessionId();n&&e.isEnabled()&&"session"===e.recordingMode&&e.checkAndHandleExpiredSession()&&(t.replay_id=n)})),t.on("spanStart",(t=>{e.lastActiveSpan=t})),t.on("spanEnd",(t=>{e.lastActiveSpan=t})),t.on("beforeSendFeedback",((t,n)=>{const r=e.getSessionId();n&&n.includeReplay&&e.isEnabled()&&r&&t.contexts&&t.contexts.feedback&&(t.contexts.feedback.replay_id=r)})))}(this),this._hasInitializedCoreListeners=!0)}catch(e){this._handleException(e)}this._performanceCleanupCallback=on(this)}_removeListeners(){try{M.document.removeEventListener("visibilitychange",this._handleVisibilityChange),M.removeEventListener("blur",this._handleWindowBlur),M.removeEventListener("focus",this._handleWindowFocus),M.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this._handleException(e)}}__init(){this._handleVisibilityChange=()=>{"visible"===M.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()}}__init2(){this._handleWindowBlur=()=>{const e=Yt({category:"ui.blur"});this._doChangeToBackgroundTasks(e)}}__init3(){this._handleWindowFocus=()=>{const e=Yt({category:"ui.focus"});this._doChangeToForegroundTasks(e)}}__init4(){this._handleKeyboardEvent=e=>{tn(this,e)}}_doChangeToBackgroundTasks(e){if(!this.session)return;In(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush())}_doChangeToForegroundTasks(e){if(!this.session)return;this.checkAndHandleExpiredSession()?e&&this._createCustomBreadcrumb(e):cn("[Replay] Document has become active, but session has expired")}_updateUserActivity(e=Date.now()){this._lastActivity=e}_updateSessionActivity(e=Date.now()){this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}_createCustomBreadcrumb(e){this.addUpdate((()=>{this.throttledAddEvent({type:qe.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})}))}_addPerformanceEntries(){const e=(t=this.performanceEntries,t.map(rn).filter(Boolean)).concat(this.replayPerformanceEntries);var t;return this.performanceEntries=[],this.replayPerformanceEntries=[],Promise.all(Pn(this,e))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){const{session:e,eventBuffer:t}=this;if(!e||!t)return;if(e.segmentId)return;const n=t.getEarliestTimestamp();n&&n<this._context.initialTimestamp&&(this._context.initialTimestamp=n)}_popEventContext(){const e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}async _runFlush(){const e=this.getSessionId();if(this.session&&this.eventBuffer&&e){if(await this._addPerformanceEntries(),this.eventBuffer&&this.eventBuffer.hasEvents&&(await async function(e){try{return Promise.all(Pn(e,[sr(M.performance.memory)]))}catch(t){return[]}}(this),this.eventBuffer&&e===this.getSessionId()))try{this._updateInitialTimestampFromEventBuffer();const t=Date.now();if(t-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw new Error("Session is too long, not sending replay");const n=this._popEventContext(),r=this.session.segmentId++;this._maybeSaveSession();const s=await this.eventBuffer.finish();await lr({replayId:e,recordingData:s,segmentId:r,eventContext:n,session:this.session,options:this.getOptions(),timestamp:t})}catch(t){this._handleException(t),this.stop({reason:"sendReplay"});const e=(0,i.s3)();e&&e.recordDroppedEvent("send_error","replay")}}else an&&k.kg.error("[Replay] No session or eventBuffer found to flush.")}__init5(){this._flush=async({force:e=!1}={})=>{if(!this._isEnabled&&!e)return;if(!this.checkAndHandleExpiredSession())return void(an&&k.kg.error("[Replay] Attempting to finish replay event after session expired."));if(!this.session)return;const t=this.session.started,n=Date.now()-t;this._debouncedFlush.cancel();const r=n<this._options.minReplayDuration,s=n>this._options.maxReplayDuration+5e3;if(r||s)return cn(`[Replay] Session duration (${Math.floor(n/1e3)}s) is too ${r?"short":"long"}, not sending replay.`,this._options._experiments.traceInternals),void(r&&this._debouncedFlush());const o=this.eventBuffer;if(o&&0===this.session.segmentId&&!o.hasCheckout&&cn("[Replay] Flushing initial segment without checkout.",this._options._experiments.traceInternals),!this._flushLock)return this._flushLock=this._runFlush(),await this._flushLock,void(this._flushLock=void 0);try{await this._flushLock}catch(i){an&&k.kg.error(i)}finally{this._debouncedFlush()}}}_maybeSaveSession(){this.session&&this._options.stickySession&&_n(this.session)}__init6(){this._onMutationHandler=e=>{const t=e.length,n=this._options.mutationLimit,r=n&&t>n;if(t>this._options.mutationBreadcrumbLimit||r){const e=Yt({category:"replay.mutations",data:{count:t,limit:r}});this._createCustomBreadcrumb(e)}return!r||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}}function pr(e,t){return[...e,...t].join(",")}const mr='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',fr=["content-length","content-type","accept"];let yr=!1;const gr=e=>new kr(e);class kr{static __initStatic(){this.id="Replay"}constructor({flushMinDelay:e=5e3,flushMaxDelay:t=5500,minReplayDuration:n=4999,maxReplayDuration:r=36e5,stickySession:s=!0,useCompression:o=!0,workerUrl:i,_experiments:a={},maskAllText:c=!0,maskAllInputs:l=!0,blockAllMedia:u=!0,mutationBreadcrumbLimit:d=750,mutationLimit:h=1e4,slowClickTimeout:p=7e3,slowClickIgnoreSelectors:m=[],networkDetailAllowUrls:f=[],networkDetailDenyUrls:y=[],networkCaptureBodies:g=!0,networkRequestHeaders:k=[],networkResponseHeaders:v=[],mask:S=[],maskAttributes:_=["title","placeholder"],unmask:b=[],block:w=[],unblock:T=[],ignore:C=[],maskFn:E,beforeAddRecordingEvent:x,beforeErrorSampling:M}={}){this.name=kr.id;const R=function({mask:e,unmask:t,block:n,unblock:r,ignore:s}){return{maskTextSelector:pr(e,[".sentry-mask","[data-sentry-mask]"]),unmaskTextSelector:pr(t,[]),blockSelector:pr(n,[".sentry-block","[data-sentry-block]",'base[href="/"]']),unblockSelector:pr(r,[]),ignoreSelector:pr(s,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'])}}({mask:S,unmask:b,block:w,unblock:T,ignore:C});if(this._recordingOptions={maskAllInputs:l,maskAllText:c,maskInputOptions:{password:!0},maskTextFn:E,maskInputFn:E,maskAttributeFn:(e,t,n)=>function({el:e,key:t,maskAttributes:n,maskAllText:r,privacyOptions:s,value:o}){return r?s.unmaskTextSelector&&e.matches(s.unmaskTextSelector)?o:n.includes(t)||"value"===t&&"INPUT"===e.tagName&&["submit","button"].includes(e.getAttribute("type")||"")?o.replace(/[\S]/g,"*"):o:o}({maskAttributes:_,maskAllText:c,privacyOptions:R,key:e,value:t,el:n}),...R,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:e=>{try{e.__rrweb__=!0}catch(t){}}},this._initialOptions={flushMinDelay:e,flushMaxDelay:t,minReplayDuration:Math.min(n,15e3),maxReplayDuration:Math.min(r,N),stickySession:s,useCompression:o,workerUrl:i,blockAllMedia:u,maskAllInputs:l,maskAllText:c,mutationBreadcrumbLimit:d,mutationLimit:h,slowClickTimeout:p,slowClickIgnoreSelectors:m,networkDetailAllowUrls:f,networkDetailDenyUrls:y,networkCaptureBodies:g,networkRequestHeaders:vr(k),networkResponseHeaders:vr(v),beforeAddRecordingEvent:x,beforeErrorSampling:M,_experiments:a},this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${mr}`:mr),this._isInitialized&&(0,I.j)())throw new Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return yr}set _isInitialized(e){yr=e}setupOnce(){(0,I.j)()&&(this._setup(),setTimeout((()=>this._initialize())))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(e){return this._replay&&this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(e):Promise.resolve()}getReplayId(){if(this._replay&&this._replay.isEnabled())return this._replay.getSessionId()}_initialize(){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(),this._replay.initializeSampling())}_setup(){const e=function(e){const t=(0,i.s3)(),n=t&&t.getOptions(),r={sessionSampleRate:0,errorSampleRate:0,...(0,f.Jr)(e)};if(!n)return(0,k.Cf)((()=>{console.warn("SDK client is not available.")})),r;const s=(0,h.o)(n.replaysSessionSampleRate),o=(0,h.o)(n.replaysOnErrorSampleRate);null==s&&null==o&&(0,k.Cf)((()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}));null!=s&&(r.sessionSampleRate=s);null!=o&&(r.errorSampleRate=o);return r}(this._initialOptions);this._replay=new hr({options:e,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(){try{const e=(0,i.s3)().getIntegrationByName("ReplayCanvas");if(!e)return;this._replay._canvas=e.getOptions()}catch(e){}}}function vr(e){return[...fr,...e.map((e=>e.toLowerCase()))]}kr.__initStatic()}}]);
//# sourceMappingURL=@sentry-internal.f0f8b676d57cd191c481d8111078d7a7.js.map