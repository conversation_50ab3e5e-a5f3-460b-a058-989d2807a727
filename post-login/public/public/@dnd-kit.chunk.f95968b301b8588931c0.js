"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@dnd-kit"],{11522:function(e,t,n){n.d(t,{LB:function(){return He},y9:function(){return st},g4:function(){return ee},Lg:function(){return ie},uN:function(){return me},we:function(){return ue},ey:function(){return E},VK:function(){return T},_8:function(){return D},hI:function(){return j},Cj:function(){return _e},O1:function(){return qe},Zj:function(){return Ve},VT:function(){return p},Dy:function(){return b}});var r=n(89526),o=n(73961),i=n(16750);const a={display:"none"};function l(e){let{id:t,value:n}=e;return r.createElement("div",{id:t,style:a},n)}const s={position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};function c(e){let{id:t,announcement:n}=e;return r.createElement("div",{id:t,style:s,role:"status","aria-live":"assertive","aria-atomic":!0},n)}const u=(0,r.createContext)(null);const d={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},f={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function g(e){let{announcements:t=f,container:n,hiddenTextDescribedById:a,screenReaderInstructions:s=d}=e;const{announce:g,announcement:v}=function(){const[e,t]=(0,r.useState)("");return{announce:(0,r.useCallback)((e=>{null!=e&&t(e)}),[]),announcement:e}}(),h=(0,i.Ld)("DndLiveRegion"),[p,b]=(0,r.useState)(!1);if((0,r.useEffect)((()=>{b(!0)}),[]),function(e){const t=(0,r.useContext)(u);(0,r.useEffect)((()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)}),[e,t])}((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;g(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&g(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;g(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;g(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;g(t.onDragCancel({active:n,over:r}))}})),[g,t])),!p)return null;const m=r.createElement(r.Fragment,null,r.createElement(l,{id:a,value:s.draggable}),r.createElement(c,{id:h,announcement:v}));return n?(0,o.createPortal)(m,n):m}var v;function h(){}function p(e,t){return(0,r.useMemo)((()=>({sensor:e,options:null!=t?t:{}})),[e,t])}function b(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>[...t].filter((e=>null!=e))),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(v||(v={}));const m=Object.freeze({x:0,y:0});function y(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function w(e,t){const n=(0,i.DC)(e);if(!n)return"0 0";return(n.x-t.left)/t.width*100+"% "+(n.y-t.top)/t.height*100+"%"}function x(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function C(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function R(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function D(e,t){if(!e||0===e.length)return null;const[n]=e;return t?n[t]:n}const E=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=R(t),i=[];for(const a of r){const{id:e}=a,t=n.get(e);if(t){const n=R(t),r=o.reduce(((e,t,r)=>e+y(n[r],t)),0),l=Number((r/4).toFixed(4));i.push({id:e,data:{droppableContainer:a,value:l}})}}return i.sort(x)};function S(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=o-r,l=i-n;if(r<o&&n<i){const n=t.width*t.height,r=e.width*e.height,o=a*l;return Number((o/(n+r-o)).toFixed(4))}return 0}const I=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const i of r){const{id:e}=i,r=n.get(e);if(r){const n=S(r,t);n>0&&o.push({id:e,data:{droppableContainer:i,value:n}})}}return o.sort(C)};function N(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:m}function M(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const O=M(1);function k(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}const L={ignoreTransform:!1};function T(e,t){void 0===t&&(t=L);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=(0,i.Jj)(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=k(t);if(!r)return e;const{scaleX:o,scaleY:i,x:a,y:l}=r,s=e.left-a-(1-o)*parseFloat(n),c=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),u=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:u,height:d,top:c,right:s+u,bottom:c+d,left:s}}(n,t,r))}const{top:r,left:o,width:a,height:l,bottom:s,right:c}=n;return{top:r,left:o,width:a,height:l,bottom:s,right:c}}function A(e){return T(e,{ignoreTransform:!0})}function j(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if((0,i.qk)(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!(0,i.Re)(o)||(0,i.vZ)(o))return n;if(n.includes(o))return n;const a=(0,i.Jj)(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=(0,i.Jj)(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"===typeof r&&n.test(r)}))}(o,a)&&n.push(o),function(e,t){return void 0===t&&(t=(0,i.Jj)(e).getComputedStyle(e)),"fixed"===t.position}(o,a)?n:r(o.parentNode)}(e):n}function B(e){const[t]=j(e,1);return null!=t?t:null}function z(e){return i.Nq&&e?(0,i.FJ)(e)?e:(0,i.UG)(e)?(0,i.qk)(e)||e===(0,i.r3)(e).scrollingElement?window:(0,i.Re)(e)?e:null:null:null}function X(e){return(0,i.FJ)(e)?e.scrollX:e.scrollLeft}function P(e){return(0,i.FJ)(e)?e.scrollY:e.scrollTop}function F(e){return{x:X(e),y:P(e)}}var J;function Y(e){return!(!i.Nq||!e)&&e===document.scrollingElement}function U(e){const t={x:0,y:0},n=Y(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(J||(J={}));const H={x:.2,y:.2};function K(e,t,n,r,o){let{top:i,left:a,right:l,bottom:s}=n;void 0===r&&(r=10),void 0===o&&(o=H);const{isTop:c,isBottom:u,isLeft:d,isRight:f}=U(e),g={x:0,y:0},v={x:0,y:0},h=t.height*o.y,p=t.width*o.x;return!c&&i<=t.top+h?(g.y=J.Backward,v.y=r*Math.abs((t.top+h-i)/h)):!u&&s>=t.bottom-h&&(g.y=J.Forward,v.y=r*Math.abs((t.bottom-h-s)/h)),!f&&l>=t.right-p?(g.x=J.Forward,v.x=r*Math.abs((t.right-p-l)/p)):!d&&a<=t.left+p&&(g.x=J.Backward,v.x=r*Math.abs((t.left+p-a)/p)),{direction:g,speed:v}}function W(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function q(e){return e.reduce(((e,t)=>(0,i.IH)(e,F(t))),m)}function _(e,t){if(void 0===t&&(t=T),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);B(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const G=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+X(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+P(t)),0)}]];class V{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=j(t),r=q(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[o,i,a]of G)for(const e of i)Object.defineProperty(this,e,{get:()=>{const t=a(n),i=r[o]-t;return this.rect[e]+i},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ${constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Z(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"===typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var Q,ee;function te(e){e.preventDefault()}function ne(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Q||(Q={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter"}(ee||(ee={}));const re={start:[ee.Space,ee.Enter],cancel:[ee.Esc],end:[ee.Space,ee.Enter]},oe=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case ee.Right:return{...n,x:n.x+25};case ee.Left:return{...n,x:n.x-25};case ee.Down:return{...n,y:n.y+25};case ee.Up:return{...n,y:n.y-25}}};class ie{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new $((0,i.r3)(t)),this.windowListeners=new $((0,i.Jj)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(Q.Resize,this.handleCancel),this.windowListeners.add(Q.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(Q.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&_(n),t(m)}handleKeyDown(e){if((0,i.vd)(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=re,coordinateGetter:a=oe,scrollBehavior:l="smooth"}=r,{code:s}=e;if(o.end.includes(s))return void this.handleEnd(e);if(o.cancel.includes(s))return void this.handleCancel(e);const{collisionRect:c}=n.current,u=c?{x:c.left,y:c.top}:m;this.referenceCoordinates||(this.referenceCoordinates=u);const d=a(e,{active:t,context:n.current,currentCoordinates:u});if(d){const t=(0,i.$X)(d,u),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:i,isRight:a,isLeft:s,isBottom:c,maxScroll:u,minScroll:f}=U(n),g=W(n),v={x:Math.min(o===ee.Right?g.right-g.width/2:g.right,Math.max(o===ee.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(o===ee.Down?g.bottom-g.height/2:g.bottom,Math.max(o===ee.Down?g.top:g.top+g.height/2,d.y))},h=o===ee.Right&&!a||o===ee.Left&&!s,p=o===ee.Down&&!c||o===ee.Up&&!i;if(h&&v.x!==d.x){const e=n.scrollLeft+t.x,i=o===ee.Right&&e<=u.x||o===ee.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:l});r.x=i?n.scrollLeft-e:o===ee.Right?n.scrollLeft-u.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:l});break}if(p&&v.y!==d.y){const e=n.scrollTop+t.y,i=o===ee.Down&&e<=u.y||o===ee.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:l});r.y=i?n.scrollTop-e:o===ee.Down?n.scrollTop-u.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:l});break}}this.handleMove(e,(0,i.IH)((0,i.$X)(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function ae(e){return Boolean(e&&"distance"in e)}function le(e){return Boolean(e&&"delay"in e)}ie.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=re,onActivation:o}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==o||o({event:e.nativeEvent}),!0)}return!1}}];class se{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=(0,i.Jj)(e);return e instanceof t?e:(0,i.r3)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:a}=o;this.props=e,this.events=t,this.document=(0,i.r3)(a),this.documentListeners=new $(this.document),this.listeners=new $(n),this.windowListeners=new $((0,i.Jj)(a)),this.initialCoordinates=null!=(r=(0,i.DC)(o))?r:m,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(Q.Resize,this.handleCancel),this.windowListeners.add(Q.DragStart,te),this.windowListeners.add(Q.VisibilityChange,this.handleCancel),this.windowListeners.add(Q.ContextMenu,te),this.documentListeners.add(Q.Keydown,this.handleKeydown),t){if(ae(t))return;if(le(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay))}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Q.Click,ne,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Q.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:a,options:{activationConstraint:l}}=o;if(!r)return;const s=null!=(t=(0,i.DC)(e))?t:m,c=(0,i.$X)(r,s);if(!n&&l){if(le(l))return Z(c,l.tolerance)?this.handleCancel():void 0;if(ae(l))return null!=l.tolerance&&Z(c,l.tolerance)?this.handleCancel():Z(c,l.distance)?this.handleStart():void 0}e.cancelable&&e.preventDefault(),a(s)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===ee.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const ce={move:{name:"pointermove"},end:{name:"pointerup"}};class ue extends se{constructor(e){const{event:t}=e,n=(0,i.r3)(t.target);super(e,ce,n)}}ue.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button)&&(null==r||r({event:n}),!0)}}];const de={move:{name:"mousemove"},end:{name:"mouseup"}};var fe;!function(e){e[e.RightClick=2]="RightClick"}(fe||(fe={}));(class extends se{constructor(e){super(e,de,(0,i.r3)(e.event.target))}}).activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==fe.RightClick&&(null==r||r({event:n}),!0)}}];const ge={move:{name:"touchmove"},end:{name:"touchend"}};var ve,he;function pe(e){let{acceleration:t,activator:n=ve.Pointer,canScroll:o,draggingRect:a,enabled:l,interval:s=5,order:c=he.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:f,delta:g,threshold:v}=e;const h=function(e){let{delta:t,disabled:n}=e;const r=(0,i.D9)(t);return(0,i.Gj)((e=>{if(n||!r||!e)return be;const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[J.Backward]:e.x[J.Backward]||-1===o.x,[J.Forward]:e.x[J.Forward]||1===o.x},y:{[J.Backward]:e.y[J.Backward]||-1===o.y,[J.Forward]:e.y[J.Forward]||1===o.y}}}),[n,t,r])}({delta:g,disabled:!l}),[p,b]=(0,i.Yz)(),m=(0,r.useRef)({x:0,y:0}),y=(0,r.useRef)({x:0,y:0}),w=(0,r.useMemo)((()=>{switch(n){case ve.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case ve.DraggableRect:return a}}),[n,a,u]),x=(0,r.useRef)(null),C=(0,r.useCallback)((()=>{const e=x.current;if(!e)return;const t=m.current.x*y.current.x,n=m.current.y*y.current.y;e.scrollBy(t,n)}),[]),R=(0,r.useMemo)((()=>c===he.TreeOrder?[...d].reverse():d),[c,d]);(0,r.useEffect)((()=>{if(l&&d.length&&w){for(const e of R){if(!1===(null==o?void 0:o(e)))continue;const n=d.indexOf(e),r=f[n];if(!r)continue;const{direction:i,speed:a}=K(e,r,w,t,v);for(const e of["x","y"])h[e][i[e]]||(a[e]=0,i[e]=0);if(a.x>0||a.y>0)return b(),x.current=e,p(C,s),m.current=a,void(y.current=i)}m.current={x:0,y:0},y.current={x:0,y:0},b()}else b()}),[t,C,o,b,l,s,JSON.stringify(w),JSON.stringify(h),p,d,R,f,JSON.stringify(v)])}(class extends se{constructor(e){super(e,ge)}static setup(){return window.addEventListener(ge.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ge.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(ve||(ve={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(he||(he={}));const be={x:{[J.Backward]:!1,[J.Forward]:!1},y:{[J.Backward]:!1,[J.Forward]:!1}};var me,ye;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(me||(me={})),function(e){e.Optimized="optimized"}(ye||(ye={}));const we=new Map;function xe(e,t){return(0,i.Gj)((n=>e?n||("function"===typeof t?t(e):e):null),[t,e])}function Ce(e){let{callback:t,disabled:n}=e;const o=(0,i.zX)(t),a=(0,r.useMemo)((()=>{if(n||"undefined"===typeof window||"undefined"===typeof window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(o)}),[n]);return(0,r.useEffect)((()=>()=>null==a?void 0:a.disconnect()),[a]),a}function Re(e){return new V(T(e),e)}function De(e,t,n){void 0===t&&(t=Re);const[o,a]=(0,r.useReducer)((function(r){if(!e)return null;var o;if(!1===e.isConnected)return null!=(o=null!=r?r:n)?o:null;const i=t(e);if(JSON.stringify(r)===JSON.stringify(i))return r;return i}),null),l=function(e){let{callback:t,disabled:n}=e;const o=(0,i.zX)(t),a=(0,r.useMemo)((()=>{if(n||"undefined"===typeof window||"undefined"===typeof window.MutationObserver)return;const{MutationObserver:e}=window;return new e(o)}),[o,n]);return(0,r.useEffect)((()=>()=>null==a?void 0:a.disconnect()),[a]),a}({callback(t){if(e)for(const n of t){const{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){a();break}}}}),s=Ce({callback:a});return(0,i.LI)((()=>{a(),e?(null==s||s.observe(e),null==l||l.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==l||l.disconnect())}),[e]),o}const Ee=[];function Se(e,t){void 0===t&&(t=[]);const n=(0,r.useRef)(null);return(0,r.useEffect)((()=>{n.current=null}),t),(0,r.useEffect)((()=>{const t=e!==m;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)}),[e]),n.current?(0,i.$X)(e,n.current):m}function Ie(e){return(0,r.useMemo)((()=>e?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null),[e])}const Ne=[];function Me(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return(0,i.Re)(t)?t:e}const Oe=[{sensor:ue,options:{}},{sensor:ie,options:{}}],ke={current:{}},Le={draggable:{measure:A},droppable:{measure:A,strategy:me.WhileDragging,frequency:ye.Optimized},dragOverlay:{measure:T}};class Te extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const Ae={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new Te,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:h},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Le,measureDroppableContainers:h,windowRect:null,measuringScheduled:!1},je={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:h,draggableNodes:new Map,over:null,measureDroppableContainers:h},Be=(0,r.createContext)(je),ze=(0,r.createContext)(Ae);function Xe(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new Te}}}function Pe(e,t){switch(t.type){case v.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case v.DragMove:return e.draggable.active?{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}}:e;case v.DragEnd:case v.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case v.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new Te(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case v.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new Te(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case v.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new Te(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function Fe(e){let{disabled:t}=e;const{active:n,activatorEvent:o,draggableNodes:a}=(0,r.useContext)(Be),l=(0,i.D9)(o),s=(0,i.D9)(null==n?void 0:n.id);return(0,r.useEffect)((()=>{if(!t&&!o&&l&&null!=s){if(!(0,i.vd)(l))return;if(document.activeElement===l.target)return;const e=a.get(s);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=(0,i.so)(e);if(t){t.focus();break}}}))}}),[o,t,a,s,l]),null}function Je(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}const Ye=(0,r.createContext)({...m,scaleX:1,scaleY:1});var Ue;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(Ue||(Ue={}));const He=(0,r.memo)((function(e){var t,n,a,l;let{id:s,accessibility:c,autoScroll:d=!0,children:f,sensors:h=Oe,collisionDetection:p=I,measuring:b,modifiers:y,...w}=e;const x=(0,r.useReducer)(Pe,void 0,Xe),[C,R]=x,[E,S]=function(){const[e]=(0,r.useState)((()=>new Set)),t=(0,r.useCallback)((t=>(e.add(t),()=>e.delete(t))),[e]);return[(0,r.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[e]),t]}(),[M,k]=(0,r.useState)(Ue.Uninitialized),L=M===Ue.Initialized,{draggable:{active:A,nodes:X,translate:P},droppable:{containers:J}}=C,U=A?X.get(A):null,H=(0,r.useRef)({initial:null,translated:null}),K=(0,r.useMemo)((()=>{var e;return null!=A?{id:A,data:null!=(e=null==U?void 0:U.data)?e:ke,rect:H}:null}),[A,U]),W=(0,r.useRef)(null),[_,G]=(0,r.useState)(null),[$,Z]=(0,r.useState)(null),Q=(0,i.Ey)(w,Object.values(w)),ee=(0,i.Ld)("DndDescribedBy",s),te=(0,r.useMemo)((()=>J.getEnabled()),[J]),ne=(re=b,(0,r.useMemo)((()=>({draggable:{...Le.draggable,...null==re?void 0:re.draggable},droppable:{...Le.droppable,...null==re?void 0:re.droppable},dragOverlay:{...Le.dragOverlay,...null==re?void 0:re.dragOverlay}})),[null==re?void 0:re.draggable,null==re?void 0:re.droppable,null==re?void 0:re.dragOverlay]));var re;const{droppableRects:oe,measureDroppableContainers:ie,measuringScheduled:ae}=function(e,t){let{dragging:n,dependencies:o,config:a}=t;const[l,s]=(0,r.useState)(null),{frequency:c,measure:u,strategy:d}=a,f=(0,r.useRef)(e),g=function(){switch(d){case me.Always:return!1;case me.BeforeDragging:return n;default:return!n}}(),v=(0,i.Ey)(g),h=(0,r.useCallback)((function(e){void 0===e&&(e=[]),v.current||s((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[v]),p=(0,r.useRef)(null),b=(0,i.Gj)((t=>{if(g&&!n)return we;if(!t||t===we||f.current!==e||null!=l){const t=new Map;for(let n of e){if(!n)continue;if(l&&l.length>0&&!l.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current,r=e?new V(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t}),[e,l,n,g,u]);return(0,r.useEffect)((()=>{f.current=e}),[e]),(0,r.useEffect)((()=>{g||h()}),[n,g]),(0,r.useEffect)((()=>{l&&l.length>0&&s(null)}),[JSON.stringify(l)]),(0,r.useEffect)((()=>{g||"number"!==typeof c||null!==p.current||(p.current=setTimeout((()=>{h(),p.current=null}),c))}),[c,g,h,...o]),{droppableRects:b,measureDroppableContainers:h,measuringScheduled:null!=l}}(te,{dragging:L,dependencies:[P.x,P.y],config:ne.droppable}),le=function(e,t){const n=null!==t?e.get(t):void 0,r=n?n.node.current:null;return(0,i.Gj)((e=>{var n;return null===t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(X,A),se=(0,r.useMemo)((()=>$?(0,i.DC)($):null),[$]),ce=function(){const e=!1===(null==_?void 0:_.autoScrollEnabled),t="object"===typeof d?!1===d.enabled:!1===d,n=L&&!e&&!t;if("object"===typeof d)return{...d,enabled:n};return{enabled:n}}(),ue=function(e,t){return xe(e,t)}(le,ne.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:o,config:a=!0}=e;const l=(0,r.useRef)(!1),{x:s,y:c}="boolean"===typeof a?{x:a,y:a}:a;(0,i.LI)((()=>{if(!s&&!c||!t)return void(l.current=!1);if(l.current||!o)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const r=N(n(e),o);if(s||(r.x=0),c||(r.y=0),l.current=!0,Math.abs(r.x)>0||Math.abs(r.y)>0){const t=B(e);t&&t.scrollBy({top:r.y,left:r.x})}}),[t,s,c,o,n])}({activeNode:A?X.get(A):null,config:ce.layoutShiftCompensation,initialRect:ue,measure:ne.draggable.measure});const de=De(le,ne.draggable.measure,ue),fe=De(le?le.parentElement:null),ge=(0,r.useRef)({activatorEvent:null,active:null,activeNode:le,collisionRect:null,collisions:null,droppableRects:oe,draggableNodes:X,draggingNode:null,draggingNodeRect:null,droppableContainers:J,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ve=J.getNodeFor(null==(t=ge.current.over)?void 0:t.id),he=function(e){let{measure:t}=e;const[n,o]=(0,r.useState)(null),a=(0,r.useCallback)((e=>{for(const{target:n}of e)if((0,i.Re)(n)){o((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}),[t]),l=Ce({callback:a}),s=(0,r.useCallback)((e=>{const n=Me(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),o(n?t(n):null)}),[t,l]),[c,u]=(0,i.wm)(s);return(0,r.useMemo)((()=>({nodeRef:c,rect:n,setRef:u})),[n,c,u])}({measure:ne.dragOverlay.measure}),be=null!=(n=he.nodeRef.current)?n:le,ye=L?null!=(a=he.rect)?a:de:null,Re=Boolean(he.nodeRef.current&&he.rect),Te=N(Ae=Re?null:de,xe(Ae));var Ae;const je=Ie(be?(0,i.Jj)(be):null),He=function(e){const t=(0,r.useRef)(e),n=(0,i.Gj)((n=>e?n&&n!==Ee&&e&&t.current&&e.parentNode===t.current.parentNode?n:j(e):Ee),[e]);return(0,r.useEffect)((()=>{t.current=e}),[e]),n}(L?null!=ve?ve:le:null),Ke=function(e,t){void 0===t&&(t=T);const[n]=e,o=Ie(n?(0,i.Jj)(n):null),[a,l]=(0,r.useReducer)((function(){return e.length?e.map((e=>Y(e)?o:new V(t(e),e))):Ne}),Ne),s=Ce({callback:l});return e.length>0&&a===Ne&&l(),(0,i.LI)((()=>{e.length?e.forEach((e=>null==s?void 0:s.observe(e))):(null==s||s.disconnect(),l())}),[e]),a}(He),We=Je(y,{transform:{x:P.x-Te.x,y:P.y-Te.y,scaleX:1,scaleY:1},activatorEvent:$,active:K,activeNodeRect:de,containerNodeRect:fe,draggingNodeRect:ye,over:ge.current.over,overlayNodeRect:he.rect,scrollableAncestors:He,scrollableAncestorRects:Ke,windowRect:je}),qe=se?(0,i.IH)(se,P):null,_e=function(e){const[t,n]=(0,r.useState)(null),o=(0,r.useRef)(e),a=(0,r.useCallback)((e=>{const t=z(e.target);t&&n((e=>e?(e.set(t,F(t)),new Map(e)):null))}),[]);return(0,r.useEffect)((()=>{const t=o.current;if(e!==t){r(t);const i=e.map((e=>{const t=z(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,F(t)]):null})).filter((e=>null!=e));n(i.length?new Map(i):null),o.current=e}return()=>{r(e),r(t)};function r(e){e.forEach((e=>{const t=z(e);null==t||t.removeEventListener("scroll",a)}))}}),[a,e]),(0,r.useMemo)((()=>e.length?t?Array.from(t.values()).reduce(((e,t)=>(0,i.IH)(e,t)),m):q(e):m),[e,t])}(He),Ge=Se(_e),Ve=Se(_e,[de]),$e=(0,i.IH)(We,Ge),Ze=ye?O(ye,We):null,Qe=K&&Ze?p({active:K,collisionRect:Ze,droppableRects:oe,droppableContainers:te,pointerCoordinates:qe}):null,et=D(Qe,"id"),[tt,nt]=(0,r.useState)(null),rt=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(Re?We:(0,i.IH)(We,Ve),null!=(l=null==tt?void 0:tt.rect)?l:null,de),ot=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(null==W.current)return;const i=X.get(W.current);if(!i)return;const a=e.nativeEvent,l=new n({active:W.current,activeNode:i,event:a,options:r,context:ge,onStart(e){const t=W.current;if(null==t)return;const n=X.get(t);if(!n)return;const{onDragStart:r}=Q.current,i={active:{id:t,data:n.data,rect:H}};(0,o.unstable_batchedUpdates)((()=>{null==r||r(i),k(Ue.Initializing),R({type:v.DragStart,initialCoordinates:e,active:t}),E({type:"onDragStart",event:i})}))},onMove(e){R({type:v.DragMove,coordinates:e})},onEnd:s(v.DragEnd),onCancel:s(v.DragCancel)});function s(e){return async function(){const{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=ge.current;let l=null;if(t&&i){const{cancelDrop:o}=Q.current;if(l={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===v.DragEnd&&"function"===typeof o){await Promise.resolve(o(l))&&(e=v.DragCancel)}}W.current=null,(0,o.unstable_batchedUpdates)((()=>{R({type:e}),k(Ue.Uninitialized),nt(null),G(null),Z(null);const t=e===v.DragEnd?"onDragEnd":"onDragCancel";if(l){const e=Q.current[t];null==e||e(l),E({type:t,event:l})}}))}}(0,o.unstable_batchedUpdates)((()=>{G(l),Z(e.nativeEvent)}))}),[X]),it=(0,r.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent,i=X.get(r);if(null!==W.current||!i||o.dndKit||o.defaultPrevented)return;const a={active:i};!0===e(n,t.options,a)&&(o.dndKit={capturedBy:t.sensor},W.current=r,ot(n,t))}),[X,ot]),at=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})))]}),[])),[e,t])}(h,it);!function(e){(0,r.useEffect)((()=>{if(!i.Nq)return;const t=e.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const e of t)null==e||e()}}),e.map((e=>{let{sensor:t}=e;return t})))}(h),(0,i.LI)((()=>{de&&M===Ue.Initializing&&k(Ue.Initialized)}),[de,M]),(0,r.useEffect)((()=>{const{onDragMove:e}=Q.current,{active:t,activatorEvent:n,collisions:r,over:i}=ge.current;if(!t||!n)return;const a={active:t,activatorEvent:n,collisions:r,delta:{x:$e.x,y:$e.y},over:i};(0,o.unstable_batchedUpdates)((()=>{null==e||e(a),E({type:"onDragMove",event:a})}))}),[$e.x,$e.y]),(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:i}=ge.current;if(!e||null==W.current||!t||!i)return;const{onDragOver:a}=Q.current,l=r.get(et),s=l&&l.rect.current?{id:l.id,rect:l.rect.current,data:l.data,disabled:l.disabled}:null,c={active:e,activatorEvent:t,collisions:n,delta:{x:i.x,y:i.y},over:s};(0,o.unstable_batchedUpdates)((()=>{nt(s),null==a||a(c),E({type:"onDragOver",event:c})}))}),[et]),(0,i.LI)((()=>{ge.current={activatorEvent:$,active:K,activeNode:le,collisionRect:Ze,collisions:Qe,droppableRects:oe,draggableNodes:X,draggingNode:be,draggingNodeRect:ye,droppableContainers:J,over:tt,scrollableAncestors:He,scrollAdjustedTranslate:$e},H.current={initial:ye,translated:Ze}}),[K,le,Qe,Ze,X,be,ye,oe,J,tt,He,$e]),pe({...ce,delta:P,draggingRect:Ze,pointerCoordinates:qe,scrollableAncestors:He,scrollableAncestorRects:Ke});const lt=(0,r.useMemo)((()=>({active:K,activeNode:le,activeNodeRect:de,activatorEvent:$,collisions:Qe,containerNodeRect:fe,dragOverlay:he,draggableNodes:X,droppableContainers:J,droppableRects:oe,over:tt,measureDroppableContainers:ie,scrollableAncestors:He,scrollableAncestorRects:Ke,measuringConfiguration:ne,measuringScheduled:ae,windowRect:je})),[K,le,de,$,Qe,fe,he,X,J,oe,tt,ie,He,Ke,ne,ae,je]),st=(0,r.useMemo)((()=>({activatorEvent:$,activators:at,active:K,activeNodeRect:de,ariaDescribedById:{draggable:ee},dispatch:R,draggableNodes:X,over:tt,measureDroppableContainers:ie})),[$,at,K,de,R,ee,X,tt,ie]);return r.createElement(u.Provider,{value:S},r.createElement(Be.Provider,{value:st},r.createElement(ze.Provider,{value:lt},r.createElement(Ye.Provider,{value:rt},f)),r.createElement(Fe,{disabled:!1===(null==c?void 0:c.restoreFocus)})),r.createElement(g,{...c,hiddenTextDescribedById:ee}))})),Ke=(0,r.createContext)(null),We="button";function qe(e){let{id:t,data:n,disabled:o=!1,attributes:a}=e;const l=(0,i.Ld)("Droppable"),{activators:s,activatorEvent:c,active:u,activeNodeRect:d,ariaDescribedById:f,draggableNodes:g,over:v}=(0,r.useContext)(Be),{role:h=We,roleDescription:p="draggable",tabIndex:b=0}=null!=a?a:{},m=(null==u?void 0:u.id)===t,y=(0,r.useContext)(m?Ye:Ke),[w,x]=(0,i.wm)(),[C,R]=(0,i.wm)(),D=function(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:o}=n;return e[r]=e=>{o(e,t)},e}),{})),[e,t])}(s,t),E=(0,i.Ey)(n);(0,i.LI)((()=>(g.set(t,{id:t,key:l,node:w,activatorNode:C,data:E}),()=>{const e=g.get(t);e&&e.key===l&&g.delete(t)})),[g,t]);return{active:u,activatorEvent:c,activeNodeRect:d,attributes:(0,r.useMemo)((()=>({role:h,tabIndex:b,"aria-disabled":o,"aria-pressed":!(!m||h!==We)||void 0,"aria-roledescription":p,"aria-describedby":f.draggable})),[o,h,b,m,p,f.draggable]),isDragging:m,listeners:o?void 0:D,node:w,over:v,setNodeRef:x,setActivatorNodeRef:R,transform:y}}function _e(){return(0,r.useContext)(ze)}const Ge={timeout:25};function Ve(e){let{data:t,disabled:n=!1,id:o,resizeObserverConfig:a}=e;const l=(0,i.Ld)("Droppable"),{active:s,dispatch:c,over:u,measureDroppableContainers:d}=(0,r.useContext)(Be),f=(0,r.useRef)({disabled:n}),g=(0,r.useRef)(!1),h=(0,r.useRef)(null),p=(0,r.useRef)(null),{disabled:b,updateMeasurementsFor:m,timeout:y}={...Ge,...a},w=(0,i.Ey)(null!=m?m:o),x=Ce({callback:(0,r.useCallback)((()=>{g.current?(null!=p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{d(Array.isArray(w.current)?w.current:[w.current]),p.current=null}),y)):g.current=!0}),[y]),disabled:b||!s}),C=(0,r.useCallback)(((e,t)=>{x&&(t&&(x.unobserve(t),g.current=!1),e&&x.observe(e))}),[x]),[R,D]=(0,i.wm)(C),E=(0,i.Ey)(t);return(0,r.useEffect)((()=>{x&&R.current&&(x.disconnect(),g.current=!1,x.observe(R.current))}),[R,x]),(0,i.LI)((()=>(c({type:v.RegisterDroppable,element:{id:o,key:l,disabled:n,node:R,rect:h,data:E}}),()=>c({type:v.UnregisterDroppable,key:l,id:o}))),[o]),(0,r.useEffect)((()=>{n!==f.current.disabled&&(c({type:v.SetDroppableDisabled,id:o,key:l,disabled:n}),f.current.disabled=n)}),[o,l,n,c]),{active:s,rect:h,isOver:(null==u?void 0:u.id)===o,node:R,over:u,setNodeRef:D}}function $e(e){let{animation:t,children:n}=e;const[o,a]=(0,r.useState)(null),[l,s]=(0,r.useState)(null),c=(0,i.D9)(n);return n||o||!c||a(c),(0,i.LI)((()=>{if(!l)return;const e=null==o?void 0:o.key,n=null==o?void 0:o.props.id;null!=e&&null!=n?Promise.resolve(t(n,l)).then((()=>{a(null)})):a(null)}),[t,o,l]),r.createElement(r.Fragment,null,n,o?(0,r.cloneElement)(o,{ref:s}):null)}const Ze={x:0,y:0,scaleX:1,scaleY:1};function Qe(e){let{children:t}=e;return r.createElement(Be.Provider,{value:je},r.createElement(Ye.Provider,{value:Ze},t))}const et={position:"fixed",touchAction:"none"},tt=e=>(0,i.vd)(e)?"transform 250ms ease":void 0,nt=(0,r.forwardRef)(((e,t)=>{let{as:n,activatorEvent:o,adjustScale:a,children:l,className:s,rect:c,style:u,transform:d,transition:f=tt}=e;if(!c)return null;const g=a?d:{...d,scaleX:1,scaleY:1},v={...et,width:c.width,height:c.height,top:c.top,left:c.left,transform:i.ux.Transform.toString(g),transformOrigin:a&&o?w(o,c):void 0,transition:"function"===typeof f?f(o):f,...u};return r.createElement(n,{className:s,style:v,ref:t},l)})),rt=e=>t=>{let{active:n,dragOverlay:r}=t;const o={},{styles:i,className:a}=e;if(null!=i&&i.active)for(const[e,l]of Object.entries(i.active))void 0!==l&&(o[e]=n.node.style.getPropertyValue(e),n.node.style.setProperty(e,l));if(null!=i&&i.dragOverlay)for(const[e,l]of Object.entries(i.dragOverlay))void 0!==l&&r.node.style.setProperty(e,l);return null!=a&&a.active&&n.node.classList.add(a.active),null!=a&&a.dragOverlay&&r.node.classList.add(a.dragOverlay),function(){for(const[e,t]of Object.entries(o))n.node.style.setProperty(e,t);null!=a&&a.active&&n.node.classList.remove(a.active)}},ot={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:i.ux.Transform.toString(t)},{transform:i.ux.Transform.toString(n)}]},sideEffects:rt({styles:{active:{opacity:"0"}}})};function it(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return(0,i.zX)(((e,a)=>{if(null===t)return;const l=n.get(e);if(!l)return;const s=l.node.current;if(!s)return;const c=Me(a);if(!c)return;const{transform:u}=(0,i.Jj)(a).getComputedStyle(a),d=k(u);if(!d)return;const f="function"===typeof t?t:function(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={...ot,...e};return e=>{let{active:i,dragOverlay:a,transform:l,...s}=e;if(!t)return;const c={x:a.rect.left-i.rect.left,y:a.rect.top-i.rect.top},u={scaleX:1!==l.scaleX?i.rect.width*l.scaleX/a.rect.width:1,scaleY:1!==l.scaleY?i.rect.height*l.scaleY/a.rect.height:1},d={x:l.x-c.x,y:l.y-c.y,...u},f=o({...s,active:i,dragOverlay:a,transform:{initial:l,final:d}}),[g]=f,v=f[f.length-1];if(JSON.stringify(g)===JSON.stringify(v))return;const h=null==r?void 0:r({active:i,dragOverlay:a,...s}),p=a.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise((e=>{p.onfinish=()=>{null==h||h(),e()}}))}}(t);return _(s,o.draggable.measure),f({active:{id:e,data:l.data,node:s,rect:o.draggable.measure(s)},draggableNodes:n,dragOverlay:{node:a,rect:o.dragOverlay.measure(c)},droppableContainers:r,measuringConfiguration:o,transform:d})}))}let at=0;function lt(e){return(0,r.useMemo)((()=>{if(null!=e)return at++,at}),[e])}const st=r.memo((e=>{let{adjustScale:t=!1,children:n,dropAnimation:o,style:i,transition:a,modifiers:l,wrapperElement:s="div",className:c,zIndex:u=999}=e;const{activatorEvent:d,active:f,activeNodeRect:g,containerNodeRect:v,draggableNodes:h,droppableContainers:p,dragOverlay:b,over:m,measuringConfiguration:y,scrollableAncestors:w,scrollableAncestorRects:x,windowRect:C}=_e(),R=(0,r.useContext)(Ye),D=lt(null==f?void 0:f.id),E=Je(l,{activatorEvent:d,active:f,activeNodeRect:g,containerNodeRect:v,draggingNodeRect:b.rect,over:m,overlayNodeRect:b.rect,scrollableAncestors:w,scrollableAncestorRects:x,transform:R,windowRect:C}),S=xe(g),I=it({config:o,draggableNodes:h,droppableContainers:p,measuringConfiguration:y}),N=S?b.setRef:void 0;return r.createElement(Qe,null,r.createElement($e,{animation:I},f&&D?r.createElement(nt,{key:D,id:f.id,ref:N,as:s,activatorEvent:d,adjustScale:t,className:c,transition:a,rect:S,style:{zIndex:u,...i},transform:E},n):null))}))},42609:function(e,t,n){n.d(t,{Fo:function(){return f},Rp:function(){return a},is:function(){return C},nB:function(){return y}});var r=n(89526),o=n(11522),i=n(16750);function a(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function l(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);return o&&(e[r]=o),e}),Array(e.length))}function s(e){return null!==e&&e>=0}const c=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=a(t,r,n),l=t[o],s=i[o];return s&&l?{x:s.left-l.left,y:s.top-l.top,scaleX:s.width/l.width,scaleY:s.height/l.height}:null};const u="Sortable",d=r.createContext({activeIndex:-1,containerId:u,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:c,disabled:{draggable:!1,droppable:!1}});function f(e){let{children:t,id:n,items:a,strategy:s=c,disabled:f=!1}=e;const{active:g,dragOverlay:v,droppableRects:h,over:p,measureDroppableContainers:b}=(0,o.Cj)(),m=(0,i.Ld)(u,n),y=Boolean(null!==v.rect),w=(0,r.useMemo)((()=>a.map((e=>"object"===typeof e&&"id"in e?e.id:e))),[a]),x=null!=g,C=g?w.indexOf(g.id):-1,R=p?w.indexOf(p.id):-1,D=(0,r.useRef)(w),E=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(w,D.current),S=-1!==R&&-1===C||E,I=function(e){return"boolean"===typeof e?{draggable:e,droppable:e}:e}(f);(0,i.LI)((()=>{E&&x&&b(w)}),[E,w,x,b]),(0,r.useEffect)((()=>{D.current=w}),[w]);const N=(0,r.useMemo)((()=>({activeIndex:C,containerId:m,disabled:I,disableTransforms:S,items:w,overIndex:R,useDragOverlay:y,sortedRects:l(w,h),strategy:s})),[C,m,I.draggable,I.droppable,S,w,R,h,y,s]);return r.createElement(d.Provider,{value:N},t)}const g=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return a(n,r,o).indexOf(t)},v=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:a,previousItems:l,previousContainerId:s,transition:c}=e;return!(!c||!r)&&((l===i||o!==a)&&(!!n||a!==o&&t===s))},h={duration:200,easing:"ease"},p="transform",b=i.ux.Transition.toString({property:p,duration:0,easing:"linear"}),m={roleDescription:"sortable"};function y(e){let{animateLayoutChanges:t=v,attributes:n,disabled:a,data:l,getNewIndex:c=g,id:u,strategy:f,resizeObserverConfig:y,transition:w=h}=e;const{items:x,containerId:C,activeIndex:R,disabled:D,disableTransforms:E,sortedRects:S,overIndex:I,useDragOverlay:N,strategy:M}=(0,r.useContext)(d),O=function(e,t){var n,r;if("boolean"===typeof e)return{draggable:e,droppable:!1};return{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(r=null==e?void 0:e.droppable)?r:t.droppable}}(a,D),k=x.indexOf(u),L=(0,r.useMemo)((()=>({sortable:{containerId:C,index:k,items:x},...l})),[C,l,k,x]),T=(0,r.useMemo)((()=>x.slice(x.indexOf(u))),[x,u]),{rect:A,node:j,isOver:B,setNodeRef:z}=(0,o.Zj)({id:u,data:L,disabled:O.droppable,resizeObserverConfig:{updateMeasurementsFor:T,...y}}),{active:X,activatorEvent:P,activeNodeRect:F,attributes:J,setNodeRef:Y,listeners:U,isDragging:H,over:K,setActivatorNodeRef:W,transform:q}=(0,o.O1)({id:u,data:L,attributes:{...m,...n},disabled:O.draggable}),_=(0,i.HB)(z,Y),G=Boolean(X),V=G&&!E&&s(R)&&s(I),$=!N&&H,Z=$&&V?q:null,Q=V?null!=Z?Z:(null!=f?f:M)({rects:S,activeNodeRect:F,activeIndex:R,overIndex:I,index:k}):null,ee=s(R)&&s(I)?c({id:u,items:x,activeIndex:R,overIndex:I}):k,te=null==X?void 0:X.id,ne=(0,r.useRef)({activeId:te,items:x,newIndex:ee,containerId:C}),re=x!==ne.current.items,oe=t({active:X,containerId:C,isDragging:H,isSorting:G,id:u,index:k,items:x,newIndex:ne.current.newIndex,previousItems:ne.current.items,previousContainerId:ne.current.containerId,transition:w,wasDragging:null!=ne.current.activeId}),ie=function(e){let{disabled:t,index:n,node:a,rect:l}=e;const[s,c]=(0,r.useState)(null),u=(0,r.useRef)(n);return(0,i.LI)((()=>{if(!t&&n!==u.current&&a.current){const e=l.current;if(e){const t=(0,o.VK)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&c(n)}}n!==u.current&&(u.current=n)}),[t,n,a,l]),(0,r.useEffect)((()=>{s&&c(null)}),[s]),s}({disabled:!oe,index:k,node:j,rect:A});return(0,r.useEffect)((()=>{G&&ne.current.newIndex!==ee&&(ne.current.newIndex=ee),C!==ne.current.containerId&&(ne.current.containerId=C),x!==ne.current.items&&(ne.current.items=x)}),[G,ee,C,x]),(0,r.useEffect)((()=>{if(te===ne.current.activeId)return;if(te&&!ne.current.activeId)return void(ne.current.activeId=te);const e=setTimeout((()=>{ne.current.activeId=te}),50);return()=>clearTimeout(e)}),[te]),{active:X,activeIndex:R,attributes:J,data:L,rect:A,index:k,newIndex:ee,items:x,isOver:B,isSorting:G,isDragging:H,listeners:U,node:j,overIndex:I,over:K,setNodeRef:_,setActivatorNodeRef:W,setDroppableNodeRef:z,setDraggableNodeRef:Y,transform:null!=ie?ie:Q,transition:function(){if(ie||re&&ne.current.newIndex===k)return b;if($&&!(0,i.vd)(P)||!w)return;if(G||oe)return i.ux.Transition.toString({...w,property:p});return}()}}function w(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&"object"===typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const x=[o.g4.Down,o.g4.Right,o.g4.Up,o.g4.Left],C=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:a,droppableContainers:l,over:s,scrollableAncestors:c}}=t;if(x.includes(e.code)){if(e.preventDefault(),!n||!r)return;const t=[];l.getEnabled().forEach((n=>{if(!n||null!=n&&n.disabled)return;const i=a.get(n.id);if(i)switch(e.code){case o.g4.Down:r.top<i.top&&t.push(n);break;case o.g4.Up:r.top>i.top&&t.push(n);break;case o.g4.Left:r.left>i.left&&t.push(n);break;case o.g4.Right:r.left<i.left&&t.push(n)}}));const u=(0,o.ey)({active:n,collisionRect:r,droppableRects:a,droppableContainers:t,pointerCoordinates:null});let d=(0,o._8)(u,"id");if(d===(null==s?void 0:s.id)&&u.length>1&&(d=u[1].id),null!=d){const e=l.get(n.id),t=l.get(d),s=t?a.get(t.id):null,u=null==t?void 0:t.node.current;if(u&&s&&e&&t){const n=(0,o.hI)(u).some(((e,t)=>c[t]!==e)),a=R(e,t),l=function(e,t){if(!w(e)||!w(t))return!1;if(!R(e,t))return!1;return e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!a?{x:0,y:0}:{x:l?r.width-s.width:0,y:l?r.height-s.height:0},f={x:s.left,y:s.top};return d.x&&d.y?f:(0,i.$X)(f,d)}}}};function R(e,t){return!(!w(e)||!w(t))&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}},16750:function(e,t,n){n.d(t,{ux:function(){return I},IH:function(){return R},Nq:function(){return i},so:function(){return M},DC:function(){return S},r3:function(){return f},Jj:function(){return s},qk:function(){return c},Re:function(){return u},vd:function(){return E},UG:function(){return l},vZ:function(){return d},FJ:function(){return a},$X:function(){return D},HB:function(){return o},zX:function(){return v},Yz:function(){return h},LI:function(){return g},Ey:function(){return p},Gj:function(){return b},wm:function(){return m},D9:function(){return y},Ld:function(){return x}});var r=n(89526);function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}const i="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;function a(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function l(e){return"nodeType"in e}function s(e){var t,n;return e?a(e)?e:l(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function c(e){const{Document:t}=s(e);return e instanceof t}function u(e){return!a(e)&&e instanceof s(e).HTMLElement}function d(e){return e instanceof s(e).SVGElement}function f(e){return e?a(e)?e.document:l(e)?c(e)?e:u(e)?e.ownerDocument:document:document:document}const g=i?r.useLayoutEffect:r.useEffect;function v(e){const t=(0,r.useRef)(e);return g((()=>{t.current=e})),(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function h(){const e=(0,r.useRef)(null);return[(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]),(0,r.useCallback)((()=>{null!==e.current&&(clearInterval(e.current),e.current=null)}),[])]}function p(e,t){void 0===t&&(t=[e]);const n=(0,r.useRef)(e);return g((()=>{n.current!==e&&(n.current=e)}),t),n}function b(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);return n.current=t,t}),[...t])}function m(e){const t=v(e),n=(0,r.useRef)(null),o=(0,r.useCallback)((e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e}),[]);return[n,o]}function y(e){const t=(0,r.useRef)();return(0,r.useEffect)((()=>{t.current=e}),[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)((()=>{if(t)return t;const n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n}),[e,t])}function C(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[o,i]of r){const n=t[o];null!=n&&(t[o]=n+e*i)}return t}),{...t})}}const R=C(1),D=C(-1);function E(e){if(!e)return!1;const{KeyboardEvent:t}=s(e.target);return t&&e instanceof t}function S(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=s(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const I=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[I.Translate.toString(e),I.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),N="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function M(e){return e.matches(N)?e:e.querySelector(N)}}}]);
//# sourceMappingURL=@dnd-kit.0c842f3112e37830435bc7a1912ddd69.js.map