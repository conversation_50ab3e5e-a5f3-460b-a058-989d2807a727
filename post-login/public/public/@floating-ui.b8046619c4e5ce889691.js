"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@floating-ui"],{83009:function(e,t,n){n.d(t,{Me:function(){return T}});const o=Math.min,i=Math.max,r=Math.round,c=Math.floor,u=e=>({x:e,y:e});function s(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function l(e){return d(e)?(e.nodeName||"").toLowerCase():"#document"}function a(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function f(e){var t;return null==(t=(d(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function d(e){return e instanceof Node||e instanceof a(e).Node}function h(e){return e instanceof Element||e instanceof a(e).Element}function m(e){return e instanceof HTMLElement||e instanceof a(e).HTMLElement}function p(e){return"undefined"!==typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof a(e).ShadowRoot)}function v(e){const{overflow:t,overflowX:n,overflowY:o,display:i}=y(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(i)}function w(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function g(e){return["html","body","#document"].includes(l(e))}function y(e){return a(e).getComputedStyle(e)}function b(e){if("html"===l(e))return e;const t=e.assignedSlot||e.parentNode||p(e)&&e.host||f(e);return p(t)?t.host:t}function x(e){const t=b(e);return g(t)?e.ownerDocument?e.ownerDocument.body:e.body:m(t)&&v(t)?t:x(t)}function E(e,t,n){var o;void 0===t&&(t=[]),void 0===n&&(n=!0);const i=x(e),r=i===(null==(o=e.ownerDocument)?void 0:o.body),c=a(i);return r?t.concat(c,c.visualViewport||[],v(i)?i:[],c.frameElement&&n?E(c.frameElement):[]):t.concat(i,E(i,[],n))}function S(e){const t=y(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const i=m(e),c=i?e.offsetWidth:n,u=i?e.offsetHeight:o,s=r(n)!==c||r(o)!==u;return s&&(n=c,o=u),{width:n,height:o,$:s}}function R(e){return h(e)?e:e.contextElement}function C(e){const t=R(e);if(!m(t))return u(1);const n=t.getBoundingClientRect(),{width:o,height:i,$:c}=S(t);let s=(c?r(n.width):n.width)/o,l=(c?r(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}const F=u(0);function L(e){const t=a(e);return w()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:F}function M(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n=!1);const i=e.getBoundingClientRect(),r=R(e);let c=u(1);t&&(o?h(o)&&(c=C(o)):c=C(e));const l=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==a(e))&&t}(r,n,o)?L(r):u(0);let f=(i.left+l.x)/c.x,d=(i.top+l.y)/c.y,m=i.width/c.x,p=i.height/c.y;if(r){const e=a(r),t=o&&h(o)?a(o):o;let n=e.frameElement;for(;n&&o&&t!==e;){const e=C(n),t=n.getBoundingClientRect(),o=y(n),i=t.left+(n.clientLeft+parseFloat(o.paddingLeft))*e.x,r=t.top+(n.clientTop+parseFloat(o.paddingTop))*e.y;f*=e.x,d*=e.y,m*=e.x,p*=e.y,f+=i,d+=r,n=a(n).frameElement}}return s({width:m,height:p,x:f,y:d})}function T(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:u=!0,ancestorResize:s=!0,elementResize:l="function"===typeof ResizeObserver,layoutShift:a="function"===typeof IntersectionObserver,animationFrame:d=!1}=r,h=R(e),m=u||s?[...h?E(h):[],...E(t)]:[];m.forEach((e=>{u&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)}));const p=h&&a?function(e,t){let n,r=null;const u=f(e);function s(){clearTimeout(n),r&&r.disconnect(),r=null}return function l(a,f){void 0===a&&(a=!1),void 0===f&&(f=1),s();const{left:d,top:h,width:m,height:p}=e.getBoundingClientRect();if(a||t(),!m||!p)return;const v={rootMargin:-c(h)+"px "+-c(u.clientWidth-(d+m))+"px "+-c(u.clientHeight-(h+p))+"px "+-c(d)+"px",threshold:i(0,o(1,f))||1};let w=!0;function g(e){const t=e[0].intersectionRatio;if(t!==f){if(!w)return l();t?l(!1,t):n=setTimeout((()=>{l(!1,1e-7)}),100)}w=!1}try{r=new IntersectionObserver(g,{...v,root:u.ownerDocument})}catch(y){r=new IntersectionObserver(g,v)}r.observe(e)}(!0),s}(h,n):null;let v,w=-1,g=null;l&&(g=new ResizeObserver((e=>{let[o]=e;o&&o.target===h&&g&&(g.unobserve(t),cancelAnimationFrame(w),w=requestAnimationFrame((()=>{g&&g.observe(t)}))),n()})),h&&!d&&g.observe(h),g.observe(t));let y=d?M(e):null;return d&&function t(){const o=M(e);!y||o.x===y.x&&o.y===y.y&&o.width===y.width&&o.height===y.height||n();y=o,v=requestAnimationFrame(t)}(),n(),()=>{m.forEach((e=>{u&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)})),p&&p(),g&&g.disconnect(),g=null,d&&cancelAnimationFrame(v)}}}}]);
//# sourceMappingURL=@floating-ui.9e685a3e66daf5f5e6663ccd215b18c0.js.map