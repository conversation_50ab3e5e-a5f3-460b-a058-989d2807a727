"use strict";(self.webpackChunkheaplabs_coldemail_app=self.webpackChunkheaplabs_coldemail_app||[]).push([["@sentry"],{50198:function(t,e,n){var s=n(338),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},r={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function c(t){return s.isMemo(t)?i:a[t.$$typeof]||o}a[s.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[s.Memo]=i;var p=Object.defineProperty,u=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,l=Object.getOwnPropertyDescriptor,d=Object.getPrototypeOf,_=Object.prototype;t.exports=function t(e,n,s){if("string"!==typeof n){if(_){var o=d(n);o&&o!==_&&t(e,o,s)}var i=u(n);h&&(i=i.concat(h(n)));for(var a=c(e),f=c(n),g=0;g<i.length;++g){var m=i[g];if(!r[m]&&(!s||!s[m])&&(!f||!f[m])&&(!a||!a[m])){var S=l(n,m);try{p(e,m,S)}catch(y){}}}}return e}},45051:function(t,e,n){n.d(e,{SV:function(){return W}});const s=globalThis;function o(t,e,n){const o=n||s,r=o.__SENTRY__=o.__SENTRY__||{};return r[t]||(r[t]=e())}function r(){return i(s),s}function i(t){return t.__SENTRY__||(t.__SENTRY__={extensions:{}}),t.__SENTRY__}const a=Object.prototype.toString;function c(t,e){return a.call(t)===`[object ${e}]`}function p(t){return c(t,"Object")}function u(t,e){try{return t instanceof e}catch(n){return!1}}function h(){return Date.now()/1e3}const l=function(){const{performance:t}=s;if(!t||!t.now)return h;const e=Date.now()-t.now(),n=void 0==t.timeOrigin?e:t.timeOrigin;return()=>(n+t.now())/1e3}();let d;(()=>{const{performance:t}=s;if(!t||!t.now)return void(d="none");const e=36e5,n=t.now(),o=Date.now(),r=t.timeOrigin?Math.abs(t.timeOrigin+n-o):e,i=r<e,a=t.timing&&t.timing.navigationStart,c="number"===typeof a?Math.abs(a+n-o):e;i||c<e?r<=c?(d="timeOrigin",t.timeOrigin):d="navigationStart":d="dateNow"})();function _(){const t=s,e=t.crypto||t.msCrypto;let n=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>{const t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(o){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&n())>>t/4).toString(16)))}const f="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,g=["debug","info","warn","error","log","assert","trace"],m={};function S(t){if(!("console"in s))return t();const e=s.console,n={},o=Object.keys(m);o.forEach((t=>{const s=m[t];n[t]=e[t],e[t]=s}));try{return t()}finally{o.forEach((t=>{e[t]=n[t]}))}}const y=function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return f?g.forEach((n=>{e[n]=(...e)=>{t&&S((()=>{s.console[n](`Sentry Logger [${n}]:`,...e)}))}})):g.forEach((t=>{e[t]=()=>{}})),e}();function v(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||l(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:_()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"===typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"===typeof e.duration)t.duration=e.duration;else{const e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"===typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}function b(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch(s){f&&y.log(`Failed to add non-enumerable property "${e}" to object`,t)}}const E="_sentrySpan";function x(t,e){e?b(t,E,e):delete t._sentrySpan}function w(t){return t._sentrySpan}class I{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=k()}clone(){const t=new I;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,x(t,w(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&v(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,e){return this._tags={...this._tags,[t]:e},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,e){return this._extra={...this._extra,[t]:e},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,e){return null===e?delete this._contexts[t]:this._contexts[t]=e,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const e="function"===typeof t?t(this):t,[n,s]=e instanceof I?[e.getScopeData(),e.getRequestSession()]:p(e)?[t,t.requestSession]:[],{tags:o,extra:r,user:i,contexts:a,level:c,fingerprint:u=[],propagationContext:h}=n||{};return this._tags={...this._tags,...o},this._extra={...this._extra,...r},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),c&&(this._level=c),u.length&&(this._fingerprint=u),h&&(this._propagationContext=h),s&&(this._requestSession=s),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,x(this,void 0),this._attachments=[],this._propagationContext=k(),this._notifyScopeListeners(),this}addBreadcrumb(t,e){const n="number"===typeof e?e:100;if(n<=0)return this;const s={timestamp:h(),...t},o=this._breadcrumbs;return o.push(s),this._breadcrumbs=o.length>n?o.slice(-n):o,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:w(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,e){const n=e&&e.event_id?e.event_id:_();if(!this._client)return y.warn("No client configured on scope - will not capture exception!"),n;const s=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:s,...e,event_id:n},this),n}captureMessage(t,e,n){const s=n&&n.event_id?n.event_id:_();if(!this._client)return y.warn("No client configured on scope - will not capture message!"),s;const o=new Error(t);return this._client.captureMessage(t,e,{originalException:t,syntheticException:o,...n,event_id:s},this),s}captureEvent(t,e){const n=e&&e.event_id?e.event_id:_();return this._client?(this._client.captureEvent(t,{...e,event_id:n},this),n):(y.warn("No client configured on scope - will not capture event!"),n)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((t=>{t(this)})),this._notifyingListeners=!1)}}function k(){return{traceId:_(),spanId:_().substring(16)}}class ${constructor(t,e){let n,s;n=t||new I,s=e||new I,this._stack=[{scope:n}],this._isolationScope=s}withScope(t){const e=this._pushScope();let n;try{n=t(e)}catch(o){throw this._popScope(),o}return s=n,Boolean(s&&s.then&&"function"===typeof s.then)?n.then((t=>(this._popScope(),t)),(t=>{throw this._popScope(),t})):(this._popScope(),n);var s}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this.getStack().push({client:this.getClient(),scope:t}),t}_popScope(){return!(this.getStack().length<=1)&&!!this.getStack().pop()}}function C(){const t=i(r());return t.hub||(t.hub=new $(o("defaultCurrentScope",(()=>new I)),o("defaultIsolationScope",(()=>new I)))),t.hub}function D(t){return C().withScope(t)}function L(t,e){const n=C();return n.withScope((()=>(n.getStackTop().scope=t,e(t))))}function N(t){return C().withScope((()=>t(C().getIsolationScope())))}function R(t){const e=i(t);return e.acs?e.acs:{withIsolationScope:N,withScope:D,withSetScope:L,withSetIsolationScope:(t,e)=>N(e),getCurrentScope:()=>C().getScope(),getIsolationScope:()=>C().getIsolationScope()}}function P(){return R(r()).getCurrentScope()}new WeakMap;function j(t){if(t)return function(t){return t instanceof I||"function"===typeof t}(t)||function(t){return Object.keys(t).some((t=>O.includes(t)))}(t)?{captureContext:t}:t}const O=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function T(){return R(r()).getIsolationScope().lastEventId()}const M=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function U(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function A(t){const e="string"===typeof t?function(t){const e=M.exec(t);if(!e)return void S((()=>{console.error(`Invalid Sentry Dsn: ${t}`)}));const[n,s,o="",r,i="",a]=e.slice(1);let c="",p=a;const u=p.split("/");if(u.length>1&&(c=u.slice(0,-1).join("/"),p=u.pop()),p){const t=p.match(/^\d+/);t&&(p=t[0])}return U({host:r,pass:o,path:c,projectId:p,port:i,protocol:n,publicKey:s})}(t):U(t);if(e&&function(t){if(!f)return!0;const{port:e,projectId:n,protocol:s}=t;return!["protocol","publicKey","host","projectId"].find((e=>!t[e]&&(y.error(`Invalid Sentry Dsn: ${e} missing`),!0)))&&(n.match(/^\d+$/)?function(t){return"http"===t||"https"===t}(s)?!e||!isNaN(parseInt(e,10))||(y.error(`Invalid Sentry Dsn: Invalid port ${e}`),!1):(y.error(`Invalid Sentry Dsn: Invalid protocol ${s}`),!1):(y.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1))}(e))return e}function B(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}const q="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,Y=s;function F(t={}){if(!Y.document)return void(q&&y.error("Global document not defined in showReportDialog call"));const e=P(),n=e.getClient(),s=n&&n.getDsn();if(!s)return void(q&&y.error("DSN not configured for showReportDialog call"));if(e&&(t.user={...e.getUser(),...t.user}),!t.eventId){const e=T();e&&(t.eventId=e)}const o=Y.document.createElement("script");o.async=!0,o.crossOrigin="anonymous",o.src=function(t,e){const n=A(t);if(!n)return"";const s=`${B(n)}embed/error-page/`;let o=`dsn=${function(t,e=!1){const{host:n,path:s,pass:o,port:r,projectId:i,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&o?`:${o}`:""}@${n}${r?`:${r}`:""}/${s?`${s}/`:s}${i}`}(n)}`;for(const r in e)if("dsn"!==r&&"onClose"!==r)if("user"===r){const t=e.user;if(!t)continue;t.name&&(o+=`&name=${encodeURIComponent(t.name)}`),t.email&&(o+=`&email=${encodeURIComponent(t.email)}`)}else o+=`&${encodeURIComponent(r)}=${encodeURIComponent(e[r])}`;return`${s}?${o}`}(s,t),t.onLoad&&(o.onload=t.onLoad);const{onClose:r}=t;if(r){const t=e=>{if("__sentry_reportdialog_closed__"===e.data)try{r()}finally{Y.removeEventListener("message",t)}};Y.addEventListener("message",t)}const i=Y.document.head||Y.document.body;i?i.appendChild(o):q&&y.error("Not injecting report dialog. No injection point found in HTML")}n(50198);var G=n(89526);const K="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;const V={componentStack:null,error:null,eventId:null};class W extends G.Component{constructor(t){super(t),W.prototype.__init.call(this),this.state=V,this._openFallbackReportDialog=!0;const e=P().getClient();e&&t.showDialog&&(this._openFallbackReportDialog=!1,e.on("afterSendEvent",(e=>{!e.type&&this._lastEventId&&e.event_id===this._lastEventId&&F({...t.dialogOptions,eventId:this._lastEventId})})))}componentDidCatch(t,{componentStack:e}){const{beforeCapture:n,onError:s,showDialog:o,dialogOptions:i}=this.props;!function(...t){const e=R(r());if(2===t.length){const[n,s]=t;return n?e.withSetScope(n,s):e.withScope(s)}e.withScope(t[0])}((r=>{if(function(t){const e=t.match(/^([^.]+)/);return null!==e&&parseInt(e[0])>=17}(G.version)&&function(t){switch(a.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return u(t,Error)}}(t)){const n=new Error(t.message);n.name=`React ErrorBoundary ${t.name}`,n.stack=e,function(t,e){const n=new WeakMap;!function t(e,s){if(!n.has(e))return e.cause?(n.set(e,!0),t(e.cause,s)):void(e.cause=s)}(t,e)}(t,n)}n&&n(r,t,e);const c=(p=t,h={captureContext:{contexts:{react:{componentStack:e}}},mechanism:{handled:!!this.props.fallback}},P().captureException(p,j(h)));var p,h;s&&s(t,e,c),o&&(this._lastEventId=c,this._openFallbackReportDialog&&F({...i,eventId:c})),this.setState({error:t,componentStack:e,eventId:c})}))}componentDidMount(){const{onMount:t}=this.props;t&&t()}componentWillUnmount(){const{error:t,componentStack:e,eventId:n}=this.state,{onUnmount:s}=this.props;s&&s(t,e,n)}__init(){this.resetErrorBoundary=()=>{const{onReset:t}=this.props,{error:e,componentStack:n,eventId:s}=this.state;t&&t(e,n,s),this.setState(V)}}render(){const{fallback:t,children:e}=this.props,n=this.state;if(n.error){let e;return e="function"===typeof t?G.createElement(t,{error:n.error,componentStack:n.componentStack,resetError:this.resetErrorBoundary,eventId:n.eventId}):t,G.isValidElement(e)?e:(t&&K&&y.warn("fallback did not produce a valid ReactElement"),null)}return"function"===typeof e?e():e}}},75884:function(){}}]);
//# sourceMappingURL=@sentry.839c53a0cf65902dabf42451823b50d2.js.map