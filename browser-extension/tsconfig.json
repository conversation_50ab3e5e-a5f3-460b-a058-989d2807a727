{
  "compilerOptions": {
    "target": "es5",
    "module": "commonjs",
    "moduleResolution": "node",
    "rootDir": "src",
    "outDir": "dist/js",
    "sourceMap": true,
    "jsx": "react",
    "lib": [
      "es2015",
      "dom"
    ],
    "experimentalDecorators": true,
    "emitDecoratorMetadata": false,
    "noImplicitAny": true,
    "alwaysStrict": true,
    "forceConsistentCasingInFileNames": true,
    "allowJs": false,
    "removeComments": true,
    "inlineSourceMap": false,
    "noEmitHelpers": true,
    "importHelpers": true,
    "strictNullChecks": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "pretty": true,
  }
}