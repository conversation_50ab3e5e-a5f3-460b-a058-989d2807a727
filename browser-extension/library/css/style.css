.loader {
    border: 6px solid #f3f3f3;
    border-radius: 50%;
    border-top: 8px solid #3498db;
    width: 24px;
    height: 24px;
    -webkit-animation: spin 2s linear infinite; /* Safari */
    animation: spin 2s linear infinite;
    margin: 16px;
  }
  
  /* Safari */
  @-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .templt_list {
      list-style-type: none;
      margin: 0;
      padding: 0;
  }
  
  .templt_list li {
    text-align: left;
    padding: 12px 33px;
    cursor: pointer;
  }

  .templt_list li:hover {
      background-color: #e6e6e6c2; 
  }

  .custom_model {
    padding: 0px 24px;
    font-size: 18px;
  }

  .custom_model.warning {
    color: #ffc107;
  }

  .custom_model.error{
    color: #dc3545;
  }

  .custom_model.success{
    color: #28a745;
  }