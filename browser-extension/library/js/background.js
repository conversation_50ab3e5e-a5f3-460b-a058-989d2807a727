(function () {
    var SmartReach = window.SmartReach = window.SmartReach || {};

    SmartReach.Config = function () {
        var BASE = "https://api.smartreach.io";

        function getCampaigns(params) {
            return authUrl("/api/v1/campaigns", params);
        }

        function getTemplates(params) {
            return authUrl("/api/v1/templates", params);
        }

        function addProspect(params) {
            return authUrl("/api/v1/prospects?campaign_id="+params.campaign_id, params);
        }



        function generateAuthUrl(baseUrl, resource, params) {
                return baseUrl + resource;
        }

        function authUrl(resource, params) {
            return generateAuthUrl(BASE, resource, params);
        }

        var _this = {
            api: {
                templates: {
                    show: getTemplates,
                },
                campaigns: {
                    show: getCampaigns,
                },
                prospects: {
                    add: addProspect,
                }
            }
        }

        return _this;
    };

    SmartReach.Load = function () {
        var BASE = "https://app.smartreach.com";

        function init() {
            self.initLocalStorageListener();
        }

        // localStorage interface
        /////////////////////
        function initLocalStorageListener() {
            chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
                switch (request.method) {
                    case "getLocalStorage":
                        var localStorageData = {};

                        request.keys.forEach(function (key) {
                            localStorageData[key] = localStorage.getItem(key);
                        });

                        sendResponse(localStorageData);
                        break;
                    case "setLocalStorage":
                        Object.keys(request.data).forEach(function (key) {
                            var value = request.data[key];

                            if (typeof value === "object") {
                                value = JSON.stringify(value);
                            }

                            localStorage.setItem(key, value);
                        });
                        sendResponse(request.data);
                        break;
                }
            });
        }

        // intro page
        /////////////////////
        function manageTabs(tabs, url) {
            // user has existing matching tab open
            if (tabs.length > 0) {
                // switch over to the first matching open tab
                chrome.tabs.update(tabs[0]['id'], {
                    highlighted: true,
                    active: true
                });
            } else {
                // open up a new tab with 'url'
                chrome.tabs.create({
                    url: url
                });
            }
        }

        function handleClickToCallInstallationCase() {

        }

        function showIntro() {
            var CHECK_SESSION_URL = BASE + '/api/v1/users/check_for_session';

            // Only show the intro tab on the first install
            if (self.loadIntro() === null) {
                $.ajax({
                    url: CHECK_SESSION_URL
                }).pipe(
                    function (response) {
                        if (response.email) {
                            popGmailTab(response.email);
                        } else {
                            popLandingPage();
                        }
                    },
                    popLandingPage
                );
            }
        }

        function popGmailTab(email) {
            var gmailInboxUrl = "https://mail.google.com/mail/a/?authuser=" + email;

            launchTab(gmailInboxUrl, {
                title: "*" + email + "*",
                url: "*://mail.google.com/*"
            });
        }

        function popLandingPage() {
            var CUSTOM_AUTH_PAGE;
            if ($.browser.mozilla) {
                CUSTOM_AUTH_PAGE = BASE + '/extension_landing/start';
            } else {
                CUSTOM_AUTH_PAGE = BASE + '/auth/google_oauth2?mode=install_gmail';
            }

            launchTab(CUSTOM_AUTH_PAGE, { url: CUSTOM_AUTH_PAGE });
        }

        function launchTab(url, tabQuery) {
            var queryOptions = $.extend({}, tabQuery,
                { windowId: chrome.windows.WINDOW_ID_CURRENT });

            chrome.tabs.query(queryOptions,
                function (matchingTabs) {
                    self.manageTabs(matchingTabs, url);
                    self.saveIntro();
                }
            );
        }

        // intro localStorage
        /////////////////////
        function introKey() {
            return "extension.SmartReach.introduced";
        }

        function salesforceClickToCallKey() {
            return "extension.SmartReach.salesforce-click-to-call";
        }

        function saveSalesforceClickToCall(bool) {
            localStorage.setItem(salesforceClickToCallKey(), bool.toString());
        }

        function salesforceCampaignsKey() {
            return "extension.SmartReach.salesforce-campaigns";
        }

        function saveSalesforceCampaigns(bool) {
            localStorage.setItem(salesforceCampaignsKey(), bool.toString());
        }

        function loadIntro() {
            var tryIntro = localStorage.getItem(introKey());

            if (tryIntro === undefined) {
                return null;
            }

            return JSON.parse(tryIntro);
        }

        function saveIntro() {
            localStorage.setItem(introKey(), JSON.stringify(introJSON()));
        }

        function introJSON() {
            return {
                version: chrome.runtime.getManifest().version,
                shown: (new Date()).getTime()
            };
        }

        var self = {
            init: init,
            initLocalStorageListener: initLocalStorageListener,

            showIntro: showIntro,
            loadIntro: loadIntro,
            saveIntro: saveIntro,

            manageTabs: manageTabs,

            saveSalesforceClickToCall: saveSalesforceClickToCall,
            saveSalesforceCampaigns: saveSalesforceCampaigns,
            handleClickToCallInstallationCase: handleClickToCallInstallationCase
        };

        return self;
    };


    SmartReach.Api = function () {
        var srConfig = SmartReach.Config();
        var apiConfig = srConfig.api;

        // sending Http Request
        function sendRequest(options) {
            var settings = {
                headers: {
                    'X-API-KEY': 'nxv0PIazngXSkWXCOSAGc1sljjrHXQOm'
                },
                success: function () {
                },
                cache: true,
                tryCount: 0,
                retryLimit: 3,
                async: true,
                error: defaultErrorHandler
            };
            // deep copy
            settings = $.extend(true, settings, options);
            
            settings.success = function (response) {
                handleExtensionError(response, options.success, options.sender);
            };
            settings.error = function (response) {
                handleExtensionError(response, options.success, options.sender);
            };

            $.ajax(settings);
        }

        function defaultErrorHandler() {
            // "this" refers to the AJAX function, not to the module
            var self = this;
            self.tryCount++;

            if (self.tryCount > 0) {
                // on first retry, append timestamp to end of url to bust chrome cache
                var timestamp = (new Date()).getTime();

                if (self.url.indexOf("?") === -1) {
                    self.url += ("?" + timestamp);
                } else {
                    self.url += ("&" + timestamp);
                }
            }

            if (self.tryCount <= self.retryLimit) {
                // 5s after first failure, 25s after 2nd, 125s after 3rd
                var tryNextAfter = 1000 * (Math.pow(5, self.tryCount));

                // setTimeout(function () {
                //     $.ajax(self);
                // }, tryNextAfter);
            }
        }


        function handleExtensionError(response, successCallback, sender) {
            try {
                var message = response.messages;
                sendRequestToTab(sender.tab.id, "message.show", message);
                successCallback(response);
            } catch (e) {
                console.log(e);
            }
        }


        function getTemplates(request, callback, sender) {
            sendRequest({
                type: "GET",
                url: apiConfig.templates.show(request.params),
                success: callback,
                sender: sender
            });
        }

        function getCampaigns(request, callback, sender) {
            sendRequest({
                type: "GET",
                url: apiConfig.campaigns.show(request.params),
                success: callback,
                sender: sender
            });
        }

        function addProspect(request, callback, sender) {
            sendRequest({
                type: "POST",
                url: apiConfig.prospects.add(request.params),
                data: JSON.stringify(request.params),
                success: callback,
                sender: sender,
                contentType : "application/json",
                dataType: "json"
            });
        }


        function sendRequestToTab(tabId, action, data) {
            chrome.tabs.sendMessage(tabId, {
                action: action,
                data: data
            });
        }


        return {
            templates: {
                show: getTemplates
            },
            campaigns: {
                show: getCampaigns
            },
            prospects: {
                add: addProspect
            }
        }
    };
})();


window.SmartReach.Load().init();
if (!window.SmartReach.isApiLoaded) {
    window.SmartReach.isApiLoaded = true;
    loadApi();
}

function loadApi() {
    var SmartReach = window.SmartReach = window.SmartReach || {}
    var srApi = SmartReach.Api();

    function onRequest(args) {
        var request = args.request;
        var sender = args.sender;
        var callback = args.callback;

        if (request.action == 'templates.show') {
            srApi.templates.show(request, callback, sender);
        }

        if (request.action == 'campaigns.show') {
            srApi.campaigns.show(request, callback, sender);
        }

        if (request.action == 'prospects.add') {
            srApi.prospects.add(request, callback, sender);
        }
    }

    chrome.runtime.onMessage.addListener(function (request, sender, callback) {
        onRequest({
            request: request,
            sender: sender,
            callback: callback
        });
        return true;
    });
}


function loadFrontend() {
    function initialize() {
        chrome.runtime.onConnect.addListener(function () { });
    }
    initialize();
}

loadFrontend();