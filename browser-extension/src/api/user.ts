import * as server from './server';
import * as store from '../localstorage'


const url = '/v1/lookup';

interface IGetUser {
  account: SRExt.IUserAPI[];
}


export function getUser(userEmail: string): Promise<SRExt.IAPIResponse<IGetUser>> {
  let userInfo: any = store.getUser(userEmail);
  if (userInfo) {
  
    return Promise.resolve(userInfo);

  } else {

    return server.get<IGetUser>(url, { hideSuccess: true })
      .then(
        res => {

        localStorage.setItem(userEmail, JSON.stringify(res.data.account));
        
        return res.data.account;

      }, 
      
      err => {

        return err;

      });
  }
}

