import * as server from './server';
import * as _ from 'lodash';


const url = '/v1/campaigns';

interface IGetCampaigns {
  campaigns: SRExt.ICampaignAPI[];
}

// define local cache
var userCampaignsApiLocalCache: SRExt.IAPIResponse<IGetCampaigns> | null = null;

export function getUserCampaigns(): Promise<SRExt.IAPIResponse<IGetCampaigns>> {

  // return from local cache if present
  if (userCampaignsApiLocalCache) {

    return Promise.resolve(_.cloneDeep(userCampaignsApiLocalCache));

  } else {
    return server.get<IGetCampaigns>(url, { hideSuccess: true })
      .then(res => {

        // set in local cache
        userCampaignsApiLocalCache = _.cloneDeep(res);

        return res;
      });
  }
}

export function addProspectToCampaign(data: SRExt.IProspectNew) {
  var prosUrl = '/v1/prospects?campaign_id='+data.campaign_id;
  // reset local cache
  userCampaignsApiLocalCache = null;

  return server.post(prosUrl, data);
}

