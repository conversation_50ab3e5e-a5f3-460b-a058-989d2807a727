import axios from 'axios';
import * as store from '../localstorage';
// import { alertStore } from '../stores/AlertStore';
// import { logInStore } from '../stores/LogInStore';


// TODO
const logInStore = {
  notAuthorized: () => {
    console.log('TODO: notAuthorized');
  },
  // apiKey: store.getAuthKey(store.getCurrentUserEmail() || "")
};

function findApiKey() {
  return store.getAuthKey(store.getCurrentUserEmail() || "")
}


// TODO
const alertStore = {
  pushAlert: (data: any) => {
    console.log('TODO: pushAlert');
  }
};


const BASE_URL = 'https://api.smartreach.io/api';
// const BASE_URL = 'http://localhost:9000/api';


const axiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },

  // `withCredentials` indicates whether or not cross-site Access-Control requests
  // should be made using credentials
  withCredentials: true
});


const logOut = () => {

  logInStore.notAuthorized(); // FIXME
};

// REF: https://github.com/mzabriskie/axios#interceptors
axiosInstance.interceptors.response.use(

  (response) => {
    return response;
  },

  (err) => {

    if (err.response && err.response.data) {
      if (err.response.status === 401) {
        logOut();
      }

      return Promise.reject(err.response.data);
    } else {

      const errObj: SRExt.IAPIErrorResponse = {
        data: {
          error_type: 'client_error'
        },
        status: 'error',
        message: err.message
      };

      return Promise.reject(errObj);
    }
  }
);



axiosInstance.interceptors.request.use(function (config: any) {

  config.headers = {
    'X-EXT-KEY': findApiKey(),
    "Content-Type": "application/json",
    "Data-Type": "json"
  };

  return config;

}, function (err) {

  return Promise.reject(err);
});


const updateAlertStore = (response: any) => {
  alertStore.pushAlert({ message: response.message, status: response.status });
};

export function post<ResponseDataType>(path: string, data: Object, opts?: SRExt.IAPIOptions): SRExt.IServerResponse<ResponseDataType> {
  const stringifiedData = JSON.stringify(data);

  return axiosInstance
    .post(path, stringifiedData)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

  );
}

export function get<ResponseDataType>(path: string, opts?: SRExt.IAPIOptions): SRExt.IServerResponse<ResponseDataType> {
  return axiosInstance
    .get(path)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

  );
}

/*
export function fetch(path: string, opts?: IAPIOptions) {

  return axiosInstance.get(path)
    .then(
      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        console.log('server fetch ', response);
        const fileName = ((response.headers['content-disposition']) ? (response.headers['content-disposition']).replace("attachment;filename=", "") : 'report.csv');

        console.log('report filename is: ', fileName)
        return FileDownload(response.data, fileName);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

  );
}

export function put<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  return axiosInstance
    .put(path, JSON.stringify(data))
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

  );
}

export function del<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  // REF: https://github.com/mzabriskie/axios/issues/424#issuecomment-241481280
  return axiosInstance
    .request({
      url: path,
      method: 'delete',
      data: JSON.stringify(data)
    })
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

  );
}

export function upload<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  const options = {
    headers: {
      'Accept': 'application/json',
      'Content-Type': undefined
    }
  };

  return axiosInstance
    .post(path, data, options)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

  );

}
*/
