import * as server from './server';
import * as _ from 'lodash';


const url = '/v1/templates';

interface IGetTemplates {
  templates: SRExt.ITemplateAPI[];
}

// define local cache
var userTemplatesApiLocalCache: SRExt.IAPIResponse<IGetTemplates> | null = null;

export function getUserTemplates(): Promise<SRExt.IAPIResponse<IGetTemplates>> {

  // return from local cache if present
  if (userTemplatesApiLocalCache) {

    return Promise.resolve(_.cloneDeep(userTemplatesApiLocalCache));

  } else {
    return server.get<IGetTemplates>(url, { hideSuccess: true })
      .then(res => {

        // set in local cache
        userTemplatesApiLocalCache = _.cloneDeep(res);

        return res;
      });
  }
}

export function createUserTemplate(data: SRExt.ITemplateNew) {
  
  // reset local cache
  userTemplatesApiLocalCache = null;

  return server.post(url, data);
}

/*

export function saveUserTemplate(data: Campaigns.ICampaignStepTemplateFELib, templateId: number | string) {

  // reset local cache
  userTemplatesApiLocalCache = null;

  return server.put(url + '/' + templateId, data)
}

export function deleteUserTemplate(templateId: number | string) {

  // reset local cache
  userTemplatesApiLocalCache = null;

  return server.del(url + '/' + templateId, {});
}

export function updateSharedWithTeam(templateId: number | string, shared_with_team: boolean) {

  // reset local cache
  userTemplatesApiLocalCache = null;

  return server.put(url + '/' + templateId + '/share', { share: shared_with_team });
}

*/
