
const srKey = 'smartreach.';


// Set Gmail Email Address in Browser Heap Memory //

let currentUserEmail: string | null = null;

export function getCurrentUserEmail() {
  return currentUserEmail;
}

export function setCurrentUserEmail(userEmail: string) {
  currentUserEmail = userEmail;
  console.log("setCurrentUserEmail: ", currentUserEmail);
  return currentUserEmail;
}

///////////




export function setAuthKey(userEmail: string, authKey: any) {
  let key  = srKey+userEmail+'.authkey';
  return localStorage.setItem(key, authKey);
}

export function getAuthKey(userEmail: string) {
  let key  = srKey+userEmail+'.authkey';
  return localStorage.getItem(key);
}

export function clearAuthKey(userEmail: string) {
  let key  = srKey+userEmail+'.authkey';
  return localStorage.removeItem(key);
}

export function setUserTeams(userEmail: string, teams: any) {
  let key  = srKey+userEmail+'.teams';
  return localStorage.setItem(key, JSON.stringify(teams));
}

export function getUserTeams(userEmail: string) {
  let key  = srKey+userEmail+'.teams';
  let teams: string | null = localStorage.getItem(key);
  return teams ? JSON.parse(teams) : teams;
}

export function setLogin(userEmail: string, isLogin: boolean) {
  let key  = srKey+userEmail+'.login';
  return localStorage.setItem(key, String(isLogin));
}

export function getLogin(userEmail: string) {
  let key  = srKey+userEmail+'.login';
  return localStorage.getItem(key) === 'true';
}

export function setLookup(userEmail: string, status: string) {
  let key  = srKey+userEmail+'.lookup';
  return localStorage.setItem(key, status);
}

export function getLookup(userEmail: string) {
  let key  = srKey+userEmail+'.lookup';
  return localStorage.getItem(key);
}

export function setUser(user: any, userEmail: string) {
  let key  = srKey+userEmail+'.user';
  return localStorage.setItem(key, JSON.stringify(user));
}

export function getUser(userEmail: string) {
  let key  = srKey+userEmail+'.user';
  let user: any = localStorage.getItem(key);
  return user = JSON.parse(user);
}

export function clearuser(userEmail: string) {
  let key  = srKey+userEmail+'.user';
  return localStorage.removeItems(key);
}

export function setIgnore(userEmail: string, isIgnore: boolean) {
  let key  = srKey+userEmail+'.ignore';
  return localStorage.setItem(key, String(isIgnore));
}

export function getIgnore(userEmail: string) {
  let key  = srKey+userEmail+'.ignore';
  return localStorage.getItem(key) === 'true';
} 

export function clearIgnore(userEmail: string) {
  let key  = srKey+userEmail+'.ignore';
  return localStorage.removeItems(key);
}

  
export function truncate() {
  localStorage.clear();
}