.loader-spinner {
    border: 5px solid #f3f3f3;
    border-radius: 50%;
    border-top: 5px solid #3498db;
    width: 14px;
    height: 14px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    margin: 16px;
}    

/* Safari */
@-webkit-keyframes loader-spinner {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes loader-spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

._list {
    list-style-type: none;
    margin: 0;
    padding: 0;
    width: 160px;
}

._list li:not(.tmp_form) {
  text-align: left;
  padding: 12px 33px;
  cursor: pointer;
}
.teams_list, .templt_list, .cmpgn_list {
  width: auto !important;
}
.teams_list._list li {
  padding: 12px 80px !important;
  font-size: 16px;
  color: black;
  text-align: center !important;
}

._list li.tmp_lbl {
  padding: 12px 6px;
}
._list li.tmp_lbl input {
  height: 22px;
  width: 216px;
}

._list li:hover {
    background-color: #e6e6e6c2; 
}

.custom_model {
  // padding: 8px 12px;
  font-size: 22px;
  // box-shadow: 0 0 4px #cacaca;
  color: black;
  max-width: 500px;
  text-align: center;
}

.custom_model .logo {
  max-width: 200px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.custom_model p  {
  font-size: 14px;
}

.custom_model .ttl {
  font-size: 20px;
  // text-align: left;
  margin-top: 40px;
  margin-bottom: 8px;
  font-weight: 400;
}
.custom_model .sec {
  paddin-bottom: 4px;
}
.custom_model .sec .hed  {
    font-size: 16px;
    line-height: 0;
}

.custom_model .sec .dec  {
  font-size: 14px;
}

.custom_model .sec img  {  
    max-width: 70px;
    float: left;
    padding-right: 12px;
}    
.custom_model.warning {
  color: #ffc107;
}

.custom_model.error{
  color: #dc3545;
}

.custom_model.success{
  color: #28a745;
}
.connect_form {
    // float: right;
    margin-top: 48px;
} 

.connect_form button {
  font-size: 14px !important;
}

.connect_form a{
  display: block;
  margin: 4px;
  color: blue !important;
  font-size: 14px;
  text-decoration: none;
  background: none;
}

.connect_form #ignore:hover {
  text-decoration: underline;
}

.ui.button {
  cursor: pointer;
  // display: inline-block;
  min-height: 1em;
  outline: none;
  border: none;
  vertical-align: baseline;
  background: #E0E1E2 none;
  color: rgba(0, 0, 0, 0.6);
  font-family: 'Lato', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  // margin: 0em 0.25em 0em 0em;
  padding: 0.78571429em 1.5em 0.78571429em;
  text-transform: none;
  text-shadow: none;
  font-weight: bold;
  line-height: 1em;
  font-style: normal;
  text-align: center;
  text-decoration: none;
  // border-radius: 0.28571429rem;
  box-shadow: 0px 0px 0px 1px transparent inset, 0px 0em 0px 0px rgba(34, 36, 38, 0.15) inset;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: opacity 0.1s ease, background-color 0.1s ease, color 0.1s ease, box-shadow 0.1s ease, background 0.1s ease;
  transition: opacity 0.1s ease, background-color 0.1s ease, color 0.1s ease, box-shadow 0.1s ease, background 0.1s ease;
  will-change: '';
  -webkit-tap-highlight-color: transparent;
}

.ui.button.green {
  background: #03e26b;
}
.bAp.b8.UC .vh {
  bottom: auto !important;
  top: -10px !important;
  left: auto !important;
  right: auto !important;
}

.inboxsdk__modal_content {
  margin-top: 0 !important;
}

.login {
  font-size: 10px;
  padding: 8px 16px !important;
}

.login ul {
  list-style-type: none;
  padding: 0 !important;
}
.login ul li {
  padding:  2px 0 2px 0px !important
}
.login ul li:hover {
  background-color: white !important;
}
.login ul li a {
  color: #4183C4;
  text-decoration: none;
}
.login ul li a:hover {
  text-decoration: underline;
}
.login:hover {
  background-color: white !important;
}

#login {
  font-size: 13px;
  text-align: center;
  margin-top: 10px;
  color: #4183C4;
}