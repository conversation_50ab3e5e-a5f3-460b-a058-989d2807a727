import * as templateApi from './api/templates';
import * as campaignApi from './api/campaigns';

import * as views from './views';
import * as $ from 'jquery';
import * as store from './localstorage';

import './styles/styles.scss';


declare const InboxSDK: any;

// const BASE = "http://localhost:3001";
const BASE = "https://app.smartreach.io/";
const API = "https://api.smartreach.io/api/v1";

function initUi(sdk: any) {

  // initSdk.then(function (sdk: any) {

  const showConnectModal = (userEmail: string) => {
    sdk.Widgets.showModalView({
      el: views.getConnectAccontHtml(userEmail)
    });
  }


  /// Staus Messages handles ///
  const showError = (msg: any) => {
    sdk.ButterBar.showError({
      text: msg,
      priority: 1,
      time: 2000,
      hideOnViewChanged: true,
      persistent: false,
      messageKey: 'ready'
    });
  }

  const showMessage = (msg: any) => {
    sdk.ButterBar.showMessage({
      text: msg,
      priority: 1,
      time: 2000,
      hideOnViewChanged: true,
      persistent: false,
      messageKey: 'ready'
    });
  }


  const userEmail: string = sdk.User.getEmailAddress();


  //Loading compose view components only when user loggedin
  if (store.getAuthKey(userEmail) != null) {

    sdk.Compose.registerComposeViewHandler(function (composeView: any) {

      composeView.addButton({
        title: "Templates",
        iconUrl: 'https://smartreach.io/assets/home_page_F5_Icon_On_4x.png',
        hasDropdown: true,
        onClick: function (event: any) {
          event.dropdown.el.innerHTML = views.getLoaderHtml();
          templateApi.getUserTemplates()
            .then(res => {
              const templates = res.data.templates;

              const htmlElement = views.getTemplatesHtml(templates);
              event.dropdown.el.innerHTML = htmlElement;

              $(event.dropdown.el).find('li').click(function (e) {
                var $this = e.target;
                const id = e.target.id;
                if (id != "") {
                  event.composeView.setSubject(templates[id].subject);
                  event.composeView.setBodyHTML(templates[id].body);
                } else {
                  if ($($this).hasClass("button")) {
                    const temp: SRExt.ITemplateNew = { label: "", subject: "", body: "", shared_with_team: false };
                    var label = $(event.dropdown.el).find('.tmp_lbl input').val() as (string | undefined);
                    temp.label = label;
                    temp.subject = event.composeView.getSubject();
                    temp.body = event.composeView.getHTMLContent();
                    temp.shared_with_team = false;

                    if (temp.label == "") {
                      showError("Please add Label for template");
                    } else if (temp.subject == "") {
                      showError("Subject cannot be empty");
                    } else if (temp.body == "") {
                      showError("Email body cannot be empty");
                    } else {
                      templateApi.createUserTemplate(temp)
                        .then(res => {
                          showMessage(res.message);
                        });
                    }
                  }
                }

              });

            },

              err => {

                showError(err.message);

              });

        },
      });


      //Add prospects to campaign handle
      composeView.addButton({
        title: "Add to campaign",
        iconUrl: 'https://smartreach.io/assets/home_page_F6_Icon_On_4x.png',
        hasDropdown: true,
        onClick: function (event: any) {
          const recipient = event.composeView.getToRecipients()[0];
          if (recipient == undefined) {
            showError("Add atleast one recipient");
          } else {
            event.dropdown.el.innerHTML = views.getLoaderHtml();
            campaignApi.getUserCampaigns()
              .then(res => {
                const campaigns = res.data.campaigns;
                const campaignHtml = views.getCampaignsHtml(campaigns);
                event.dropdown.el.innerHTML = campaignHtml;
                $(event.dropdown.el).find('li').click(function (e) {
                  var id = e.target.id;
                  var prospect = {
                    first_name: recipient.name,
                    last_name: recipient.name,
                    email: recipient.emailAddress,
                    campaign_id: id,
                    custom_fields: {},
                    list: null,
                    timezone: null,
                    city: null,
                    company: null,
                    country: null,
                    state: null,
                  }
                  campaignApi.addProspectToCampaign(prospect)
                    .then(res => {
                      showMessage(res.message);
                    });
                });
              },

                err => {
                  showError(err.message);
                });
          }
        },
      });
    });

  }
  else if (isIgnore(userEmail) == false) {
    showConnectModal(userEmail);
  }

  $("#connect").click(function (e) {
    showLogin(userEmail);
  });

  $("#ignore").click(function (e) {
    store.setIgnore(userEmail, true);
    $(".inboxsdk__modal_close").click();
  });
}


function showLogin(userEmail: string) {

  //opening login popup
  var windowInstance = window.open(
    BASE + "/extension/login",
    'Login',
    'directories=0, toolbar=0, location=0, status=0, menubar=0, scrollbars=0, resizable=0, width=500, height=500, top=100, left=500');

  //first Intilizing checksession interval
  initCheckSession(userEmail, windowInstance);
}


function showAnonymousUserModal(userEmail: string, sdk: any) {
  sdk.Widgets.showModalView({
    el: views.getAnonymousUserModalHtml(userEmail)
  });
}

function login(userEmail: string, sdk: any) {
  if(store.getLookup(userEmail) != 'success') {
    showAnonymousUserModal(userEmail, sdk);
  } else {
    showLogin(userEmail);
  }
  
}

function logout(userEmail: string) {
  store.setLogin(userEmail, false);
  store.setIgnore(userEmail, true);
  store.clearAuthKey(userEmail);
  location.reload();
}

function isLogin(userEmail: string) {
  return store.getLogin(userEmail);
}

function isIgnore(userEmail: string) {
  return store.getIgnore(userEmail);
}


function selectTeams(sdk: any, teams: any) {

  const userEmail: string = sdk.User.getEmailAddress();

  sdk.Widgets.showModalView({
    title: "Please select team",
    el: views.getTeamsHtml(teams)
  });

  $(".teams_list").click(function (e) {
    var id = e.target.id;
    setAuth(userEmail, id);
    location.reload();
  });

}


function setAuth(userEmail: string, token: string) {
  store.setAuthKey(userEmail, token);
}

function initCheckSession(userEmail: string, windowInstance: Window | null) {
  var i = 0;

  var intrvl = setInterval(function () {

    chrome.runtime.sendMessage({ type: "lookup", userEmail: userEmail },

      function (res) {
        if (res.status == "success") {
          //when lookup success clearing inrval 
          //and reloading the tab
          clearInterval(intrvl);

          windowInstance && windowInstance.close();

          setTimeout(() => {
            store.setLogin(userEmail, true);
            store.setIgnore(userEmail, false);
            location.reload();
          }, 1000);

        }

      });

    if (i > 60) {
      clearInterval(intrvl);
    }

    i++;

  }, 1000);
}


function initSdk() {

  // localStorage.clear();

  InboxSDK.load(2, 'sdk_SRAPP_a72235f5d6')
    .then(function (sdk: any) {

      const userEmail: string = sdk.User.getEmailAddress();
      store.setCurrentUserEmail(userEmail);
      const userTeams: any = store.getUserTeams(userEmail);

      //Adding toobar Button for Login|logout and Switch Team View
      sdk.Toolbars.addToolbarButtonForApp({
        iconUrl: "https://app.smartreach.io/assets/SmartreachLogo.svg",
        hasDropdown: true,
        onClick(event: any) {
          let toolbarDropDownHtml = views.getToolBarDropdownHtml(userTeams, isLogin(userEmail));
          event.dropdown.el.innerHTML = toolbarDropDownHtml;

          $(event.dropdown.el).find("li").click(function (e) {
            let id = e.target.id;
            event.dropdown.close();
            if (id == "switch_team") {
              selectTeams(sdk, userTeams);
            }
            else if (id == "login") {
              login(userEmail, sdk);
            }
            else if (id == "logout") {
              logout(userEmail);
            }
          });
        }
      });

      var authKey = store.getAuthKey(userEmail);
      chrome.runtime.sendMessage({ type: "lookup", apiUrl: API, userEmail: userEmail, token: authKey },
        function (res) {
          console.log("OUT");
          if (res.status == "success") {
            store.setLookup(userEmail, res.status);
            console.log("success");
            if (authKey != null) {
              console.log("IF");
              initUi(sdk);
            } else if (authKey == null && isLogin(userEmail) && !isIgnore(userEmail)) {
              console.log("ELSE IF");
              var teams = res.data.teams;
              if (teams.length > 1) {
                store.setUserTeams(userEmail, teams);
                selectTeams(sdk, res.data.teams);
              } else {
                setAuth(userEmail, teams[0].token);
                initUi(sdk);
              }
            } else {
              //Show Link My accont Popup
              initUi(sdk);
            }
          } else if(res.message == "EML_SES_MP_ER") {
            store.setIgnore(userEmail, true);
            store.setLookup(userEmail, res.status);
          } else {
            store.setIgnore(userEmail, true);
          }
        });
    });

}

initSdk();