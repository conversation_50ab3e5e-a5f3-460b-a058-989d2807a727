import * as Handlebars from 'handlebars';


/// TEMPLATE DROPDOWN LIST STARETS///

const templateListHtmlSource = `
<ul class="_list templt_list">

  {{#each data}}
    <li id="{{@index}}">{{this.label}}</li>
  {{/each}}

  <li class="tmp_form tmp_lbl"><input type="text" placeholder="Template lable" ></li>
  <li class="tmp_form ui button green">Save Email as Template</li>
</ul>
`;

var templateListHtmlTemplate = Handlebars.compile(templateListHtmlSource);

export function getTemplatesHtml(templates: SRExt.ITemplateAPI[]) {

  const html = templateListHtmlTemplate({
    data: templates
  });
  return html;

}

/// TEMPLATE DROPDOWN LIST ENDS///



/// CAMPAIGNS DROPDOWN LIST STARTS////


const campaignListHtmlSource = `
<ul class="_list cmpgn_list">

  {{#each data}}
    <li id="{{this.id}}">{{this.name}}</li>
  {{/each}}
</ul>
`;

var campaignListHtmlTemplate = Handlebars.compile(campaignListHtmlSource);

export function getCampaignsHtml(campaigns: SRExt.ICampaignAPI[]) {

  const html = campaignListHtmlTemplate({
    data: campaigns
  });
  return html;

}

/// CAMPAIGNS DROPDOWN LIST ENDS////


/// Connect account Popup start ///
const connectAccountHtmlSource = `
<div class="modal">
<div class="custom_model">
<img class="logo" src="{{logo}}"/>
    <h1 class="ttl">Welcome to SmartReach</h2>
    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. 
    Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, 
    when an unknown printer took a galley of type and scrambled it to make a type 
    specimen book.</p>
  <div class="connect_form">
    <button class="ui button green" id="connect">Link my account</button>
    <a href="#" id="ignore">Not now</a>
    </div>
</div>
`;
var connectAccountTemplate = Handlebars.compile(connectAccountHtmlSource);


export function getConnectAccontHtml(email: string) {

  const html = connectAccountTemplate({
    data: email,
    logo: chrome.extension.getURL('logo.png')
  });
  return html;

}

/// Connect account Popup end ///



/// Anonymous account Popup start ///

const AnonymousUserModalHtmlSource = `
<div class="modal">
<div class="custom_model">
<img class="logo" src="{{logo}}"/>
    <h1 class="ttl">Oops! Account not mapped</h2>
    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. 
    Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, 
    when an unknown printer took a galley of type and scrambled it to make a type 
    specimen book.</p>
  <div class="connect_form">
    <a class="ui button green" href="https://app.smartreach.io/">Map</a>
    <a href="#" id="ignore">Not now</a>
    </div>
</div>
`;
var AnonymousUserModalTemplate = Handlebars.compile(AnonymousUserModalHtmlSource);


export function getAnonymousUserModalHtml(email: string) {

  const html = AnonymousUserModalTemplate({
    data: email,
    logo: chrome.extension.getURL('logo.png')
  });
  return html;

}

/// Anonymous account Popup end ///




/// GET TEAMS TEMPLATES STARTS /// 

const teamsHtmlSource = `
<ul class="_list teams_list">

  {{#each teams}}
    <li id="{{this.token}}">{{this.name}}</li>
  {{/each}}

</ul>
`;
var teamsTemplate = Handlebars.compile(teamsHtmlSource);



export function getTeamsHtml(teams: any) {

  const html = teamsTemplate({
    teams: teams
  });

  return html;

}

/// GET TEAMS TEMPLATES ENDS ///




/// TOOLBAR DROP DOWN STARTS ///

const toolBarDropdownTemplateHtmlSource = `
<ul class="_list">

  {{#if teams}}
  {{#if isLogin}}
    <li id="switch_team">Switch team</li>
  {{/if}}
  {{/if}}
  

  {{#if isLogin}}
    <li id="logout">Logout</li>
  {{else}} 
    <li class="login">
      <ul>
      <li class="intro">SmartReach is a tool to make Gmail more efficient for salespeople to reach customers.</li>
      <li><a href="https://smartreach.io/" targt="__blank">Learn more</a></li>
      <li id="login">Login</li>
      </ul>
    </li>
  {{/if}}

</ul>
`;
var toolBarDropdownTemplate = Handlebars.compile(toolBarDropdownTemplateHtmlSource);

export function getToolBarDropdownHtml(teams: any, isLogin: boolean) {

  const html = toolBarDropdownTemplate({
    teams: teams,
    isLogin: isLogin
  });

  return html;

}

/// TOOLBAR DROP DOWN ENDS ///


/// LOADER ///

const loaderHtmlSource = `<div class="loader-spinner"></div>`;
var loaderHtml = Handlebars.compile(loaderHtmlSource);
export function getLoaderHtml() {

  const html = loaderHtml({});

  return html;

}

