declare namespace SRExt {

  /// SERVER API GENERAL ///

  export type IAPIResponse<T> = {
    data: T,
    status: 'success' | 'error',
    message: string
  };
  
  type IAPIErrorResponse = IAPIResponse<{
    error_type: string;
    param?: string;
  }>;
  
  type IServerResponse<T> = Promise<IAPIResponse<T>>;
  
  
  interface IAPIOptions {
    hideSuccess?: boolean;
    hideError?: boolean;
  }


  /// TEMPLATES ///

  interface ITemplateNew {
    label: string | undefined;
    subject: string;
    body: string;
    shared_with_team?: boolean;
  }


  interface ITemplateAPI {
    label: string;
    subject: string;
    body: string;
    id: number | string;
    category: string;
    created_at: string;
    is_owner: boolean;
    shared_with_team: boolean;
  }

  interface IModal {
    msg: string;
  }

/// Campaigns ///

  interface ICampaignAPI {
    account_id: number | string;
    created_at: number;
    error: null
    head_step_id: null
    id: number;
    name: string;
    owner_email: string;
    owner_name: string;
    settings: object;
    spam_test_exists: boolean;
    stats: object;
    status: string;
    team_id: number;
    warmup_is_on: boolean;
  }

  interface IProspectNew {
    first_name: string;
    last_name: string;
    email: string;
    campaign_id: number | string;
    custom_fields: object;
    list: null;
    timezone: null;
    city: null;
    company: null;
    country: null;
    state: null;
  }


  //// User ////

  interface IUserAPI {
    account_id: number;
    company: string;
    created_at: number;
    email: string;
    first_name: string;
    last_name: string;
    team: object;
  }
}