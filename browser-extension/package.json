{"name": "sr_browser_extension", "version": "1.0.0", "description": "SmartReach browser extension", "scripts": {"build": "./node_modules/.bin/webpack", "watch": "./node_modules/.bin/webpack -w"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/axios": "^0.14.0", "@types/chrome": "0.0.60", "@types/handlebars": "^4.0.39", "@types/jquery": "^3.3.10", "@types/lodash": "^4.14.116", "axios": "^0.18.0", "css-loader": "^0.28.11", "handlebars": "^4.0.12", "jquery": "^3.3.1", "lodash": "^4.17.11", "node-sass": "^4.7.2", "sass-loader": "^6.0.7", "style-loader": "^0.20.3", "ts-loader": "^4.0.1", "typescript": "^2.7.2", "webpack": "^4.1.1", "webpack-chrome-extension-reloader": "^0.8.3", "webpack-cli": "^3.1.1"}, "dependencies": {"crx-hotreload": "^1.0.2"}}