(function () {
	var SmartReach = window.SmartReach = window.SmartReach || {};
	SmartReach.Source = function () {

		function showTemplates(data) {
			var el = '<ul class="templt_list">';
			$.each(data, function (i, v) {
				el += '<li id="' + i + '">' + v.label + '</li>'
			});
			el += '</ul>';
			return el;
		}

		function showCampaigns(data) {
			var el = '<ul class="templt_list">';
			$.each(data, function (i, v) {
				el += '<li id="' + v.id + '">' + v.name + '</li>'
			});
			el += '</ul>';
			return el;
		}

		function showSucess(msg){
			return {
				title: "Success",
				el: '<div class="custom_model success"><p>'+ msg +'</p></div>',
				buttons: [{
					text: 'OK',
					type:  'PRIMARY_ACTION',
					onClick: close()
				}]
			}
		}

		function showError(msg){
			return {
				title: "Error",
				el: '<div class="custom_model error"><p>'+ msg +'</p></div>',
				buttons: [{
					text: 'OK',
					type:  'PRIMARY_ACTION',
					onClick: close()
				}]
			}
		}

		function showWarning(msg){
			return {
				title: "Warning",
				el: '<div class="custom_model warning"><p>'+ msg +'</p></div>',
				buttons: [{
					text: 'OK',
					type:  'PRIMARY_ACTION',
					onClick: close()
				}]
			}
		}

		function showSpinner() {
			var el = '<div class="loader"></div>';
			return el;
		}



		return {
			templates: {
				show: showTemplates
			},
			campaigns: {
				show: showCampaigns
			},
			default: {
				loading: showSpinner
			},
			modal: {
				success: showSucess,
				warning: showWarning,
				error: showError 
			}
		
		}
	}
})();


(function () {
	var SmartReach = window.SmartReach = window.SmartReach || {};
	var srSource = SmartReach.Source();
	function sendRequest(request) {
		chrome.runtime.sendMessage(request.action, request.callback);
	}

	function getTemplates(callbackFn) {
		sendRequest({
			action: {
				action: "templates.show",
				params: {}
			},
			callback: callbackFn
		});
	}


	function getCampaigns(callbackFn) {
		sendRequest({
			action: {
				action: "campaigns.show",
				params: {}
			},
			callback: callbackFn
		});
	}


	function addProspect(prospect, campaign_id, callbackFn) {
		sendRequest({
			action: {
				action: "prospects.add",
				params: {
					first_name: prospect.name,
					last_name: prospect.name,
					email: prospect.emailAddress,
					campaign_id: campaign_id,
					custom_fields: {},
					list: null,
					timezone: null,
					city: null,
					company: null,
					country: null,
					state: null
				}
			},
			callback: callbackFn
		});
	}

	InboxSDK.load('1', 'Hello World!').then(function (sdk) {
		// the SDK has been loaded, now do something with it!
		sdk.Compose.registerComposeViewHandler(function (composeView) {

			// Templates handle
			composeView.addButton({
				title: "Templates",
				iconUrl: 'https://smartreach.io/assets/home_page_F5_Icon_On_4x.png',
				hasDropdown: true,
				onClick: function (event) {
					event.dropdown.el.innerHTML = srSource.default.loading();
					getTemplates(function (res) {
						var tempt = res.data.templates;
						var htmlElement = srSource.templates.show(tempt);
						event.dropdown.el.innerHTML = htmlElement;
						$(event.dropdown.el).find('li').click(function (e) {
							var id = e.target.id;
							event.composeView.setSubject(tempt[id].subject);
							event.composeView.setBodyHTML(tempt[id].body);
						});

					});
				},
			});

			//Add prospects to campaign handle
			composeView.addButton({
				title: "Add to campaign",
				iconUrl: 'https://smartreach.io/assets/home_page_F6_Icon_On_4x.png',
				hasDropdown: true,
				onClick: function (event) {
					var recipient = event.composeView.getToRecipients();
					if (recipient.length == "0") {
						sdk.Widgets.showModalView(srSource.modal.warning("Please add atleast one recepient"));
					} else {
						event.dropdown.el.innerHTML = srSource.default.loading();
						getCampaigns(function (res) {
							var camp = res.data.campaigns;
							var htmlElement = srSource.campaigns.show(camp);
							event.dropdown.el.innerHTML = htmlElement;
							$(event.dropdown.el).find('li').click(function (e) {
								addProspect(recipient[0], e.target.id, function(res) {
									if(res.status == 'success')
										sdk.Widgets.showModalView(srSource.modal.success(res.message));
									else
										sdk.Widgets.showModalView(srSource.modal.error(res.responseJSON.message));
								});
							});
						});
					}
				},
			});
		});
	});

	return {
		templates: {
			show: getTemplates
		}
	}
})();


