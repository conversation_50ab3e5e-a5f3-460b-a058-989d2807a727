## SmartReach Browser Extension  

## Building

1.  `npm i`
2.  `npm run build` to compile once or `npm run watch` to run the build task in watch mode

## Installation

1.  Complete the steps to build the project above
2.  Go to [_chrome://extensions_](chrome://extensions) in Google Chrome
3.  With the developer mode checkbox ticked, click **Load unpacked extension...** and select the _dist_ folder from this repo

