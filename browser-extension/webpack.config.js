const path = require("path");
// const ChromeExtensionReloader = require('webpack-chrome-extension-reloader');

module.exports = {
    mode: "development",
    entry: {
        content: path.join(__dirname, "src/content.ts"),
        // background: path.join(__dirname, "src/background.ts"),

    },
    devtool: "inline-source-map",
    output: {
        path: path.join(__dirname, "dist/js"),
        filename: "[name].js"
    },
    module: {
        rules: [
            {
                exclude: /node_modules/,
                test: /\.tsx?$/,
                use: "ts-loader"
            },
            {
                exclude: /node_modules/,
                test: /\.scss$/,
                use: [
                    {
                        loader: "style-loader" // Creates style nodes from JS strings
                    },
                    {
                        loader: "css-loader" // Translates CSS into CommonJS
                    },
                    {
                        loader: "sass-loader" // Compiles Sass to CSS
                    }
                ]
            }
        ]
    },
    resolve: {
        extensions: [".ts", ".tsx", ".js"],

        alias: {
            handlebars: 'handlebars/dist/handlebars.min.js'
        }
    },



    // plugins: [
    //     new ChromeExtensionReloader({
    //       port: 9090, // Which port use to create the server
    //       reloadPage: true, // Force the reload of the page also
    //     //   entries: { //The entries used for the content/background scripts
    //     //     contentScript: 'content-script', //Use the entry names, not the file name or the path
    //     //     background: 'background'
    //     //   }
    //     })
    // ]
};
