
const filesInDirectory = dir => new Promise(resolve =>

    dir.createReader().readEntries(entries =>

        Promise.all(entries.filter(e => e.name[0] !== '.').map(e =>

            e.isDirectory
                ? filesInDirectory(e)
                : new Promise(resolve => e.file(resolve))
        ))
            .then(files => [].concat(...files))
            .then(resolve)
    )
)

const timestampForFilesInDirectory = dir =>
    filesInDirectory(dir).then(files =>
        files.filter(f => f.name === 'content.js').map(f => f.name + f.lastModifiedDate).join())

const reload = () => {

    const MY_GMAIL_TAB_ID = 1790642963;

    console.log("reload called");

    chrome.tabs.query({ active: true, currentWindow: false }, tabs => {

        const activeTabId = (tabs[0]) ? tabs[0].id : null;

        console.log("reload tabs: ", activeTabId);

        if (activeTabId) {

            console.log("RELOADING TAB WITH ID: ", activeTabId);
            chrome.tabs.reload(activeTabId);


        }

        chrome.tabs.reload(MY_GMAIL_TAB_ID);

        chrome.runtime.reload();
    })
}

const watchChanges = (dir, lastTimestamp) => {

    // console.log("watchChanges: ", lastTimestamp);

    timestampForFilesInDirectory(dir).then(timestamp => {

        // console.log("timestampForFilesInDirectory: ", lastTimestamp, timestamp, timestamp === lastTimestamp);

        if (!lastTimestamp || (lastTimestamp === timestamp)) {

            setTimeout(() => watchChanges(dir, timestamp), 1000) // retry after 1s

        } else {

            console.log("timestampForFilesInDirectory RELOAD: ", lastTimestamp, timestamp, timestamp === lastTimestamp);

            reload()
        }
    })

}

chrome.management.getSelf(self => {

    if (self.installType === 'development') {

        chrome.runtime.getPackageDirectoryEntry(dir => watchChanges(dir))
    }
});


chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
    if(request.type == "lookup") {
        let token = request.token ? "&token="+request.token : "";
        var url = request.apiUrl + "/extension/lookup?email="+request.userEmail+token;
    }
    var xhttp = new XMLHttpRequest();
    xhttp.open("GET", url, true);
    xhttp.withCredentials = true;
    xhttp.onload = function(res) {
        sendResponse(JSON.parse(xhttp.responseText));
    }
    xhttp.send();
    return true;
});