name: Deploy Main Frontend to Production

on:
  workflow_dispatch:  # Manual trigger

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Checkout the repository code
      - name: Checkout code
        uses: actions/checkout@v4

      # Set up Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      # Set up gcloud CLI
      - name: Set up Google Cloud SDK
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Install gcloud CLI
        run: |
          curl -sSL https://sdk.cloud.google.com | bash
          echo "$HOME/google-cloud-sdk/bin" >> $GITHUB_PATH

      - name: Debug Directory and Current working directory
        run: |
          echo "PWD: "
          pwd
          echo "ls: "
          ls 

      # Deploy to frontend server using gcloud compute ssh with IAP
      - name: Deploy to Frontend Server
        env:
          FRONTEND_HOST: sr-frontend-4
          ZONE: us-central1-a
          DEPLOY_DIR: /home/<USER>/sr_postlogin_v3/post-login
          BRANCH: ${{ github.ref_name }}
        run: |
          gcloud compute ssh --zone $ZONE ubuntu@$FRONTEND_HOST \
            --tunnel-through-iap \
            --quiet \
            --ssh-flag="-o IdentitiesOnly=yes" \
            --command="
              cd $DEPLOY_DIR
              eval \"\$(ssh-agent -s)\"
              ssh-add ~/.ssh/sr-github-deploy-key-prod-1
              echo \" ssh-add ~/.ssh/sr-github-deploy-key-prod-1 completed\"
              git checkout $BRANCH
              echo \" git checkout $BRANCH completed\"
              git pull origin $BRANCH
              echo \" git pull origin $BRANCH completed\"
              gsutil -m rsync -r /home/<USER>/sr_postlogin_v3/post-login/sr gs://cdn-sr/sr
              echo \" gsutil -m rsync -r /home/<USER>/sr_postlogin_v3/post-login/sr gs://cdn-sr/sr completed\"
            "

      # Health check
      - name: Verify deployment
        run: |
          sleep 10
          curl -s -f "https://app.smartreach.io" || exit 1
