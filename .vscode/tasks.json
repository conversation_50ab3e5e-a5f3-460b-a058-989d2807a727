{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "command": "npm",
  "isShellCommand": true,
  "suppressTaskName": true,
  "tasks": [
    {
      "label": "install",
      "type": "shell",
      "args": [
        "install"
      ],
      "problemMatcher": []
    },
    {
      "label": "update",
      "type": "shell",
      "args": [
        "update"
      ],
      "problemMatcher": []
    },
    {
      "label": "dev",
      "type": "shell",
      "args": [
        "run",
        "dev"
      ],
      "problemMatcher": []
    }
  ]
}