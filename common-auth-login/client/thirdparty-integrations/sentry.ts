import  {
  init,
  browserTracingIntegration,
  replayIntegration,
} from '@sentry/react';

export function initializeSentry(

) {

  try {


    init({
      dsn: "https://<EMAIL>/4506157135298560",

      integrations: [
        browserTracingIntegration(),
        replayIntegration(),
      ],


      // Performance Monitoring
      tracesSampleRate: 0.5, // Capture 50% of the transactions, reduce in production!

      // Session Replay
      replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
      replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.


    });

  } catch (e) {
    console.error('[sentry] initializeSentry: ', e)
  }



}