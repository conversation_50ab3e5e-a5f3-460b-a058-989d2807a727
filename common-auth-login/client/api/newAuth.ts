import { IEmail } from '../containers/login/register-page-v2';
import { alertStore } from '../stores/AlertStore';
import { ILogInResponse } from './auth';
import { SrServer as server } from './server';


const url = '/api/v2/auth';

interface SendOAuthCodeData {
  code: string,
  state: string,
  timezone?: string,
  country_code: string,
  signupType: string
}

interface NewOAuthData {
  accountEmail: string,
  signupType: string,
  invite_code?: string,
  login_challenge ?: string 
}
interface IEmailWithCaptcha{
  accountEmail: String,
  g_response?: String
}
interface ICheckPathResponse {
  signupType?: string ,
  showCaptcha: Boolean,
  passedCaptcha: Boolean
}

export function checkPath(data: IEmailWithCaptcha) {
  return server.post<ICheckPathResponse>(url + '/check_path', data, { hideSuccess: true })
}



export function authorize(data: NewOAuthData) {
  return server.post(url + '/oauth_authorize', data, { hideSuccess: true })
}


export function sendOAuthCode(data: SendOAuthCodeData) {
  return server.post<ILogInResponse>(url + "/oauth_code", data,{hideSuccess:true}).then(response => {
    if(response.data.is_sign_up){
      alertStore.pushAlert({ message: response.message, status: response.status });
    }   
    return response;
  });
}

export function checkLoginPath(data: IEmail) {
  return server.post<ICheckPathResponse>(url + '/check_login_path', data, { hideSuccess: true })
}



export function loginChallengeFromState(data:{state ?: string}) {
  return server.post<{login_challenge ?:string}>(url + '/login_challenge_from_state',data, {hideSuccess: true})
}

