import { Sr<PERSON>erver as server } from './server';
const url='/api/v1/oauth'


interface OAuth2Client {
  authorization_code_grant_access_token_lifespan ?: string,
  authorization_code_grant_id_token_lifespan?: string,
  authorization_code_grant_refresh_token_lifespan?: string,
  client_id ?: string,
  client_name?: string,
  client_secret?: string,
  client_secret_expires_at?: string,
  client_uri?: string,
  created_at?: string,
  grant_types?: Array<string>,
  logo_uri?: string,
  redirect_uris?: Array<string>,
  refresh_token_grant_access_token_lifespan?: string,
  refresh_token_grant_id_token_lifespan?: string,
  refresh_token_grant_refresh_token_lifespan?: string,
  response_types?: Array<string>,
  scope?: string,
  skip_consent?: string,
  subject_type?: string,
  token_endpoint_auth_method?: string,
  updated_at?: string,
}

export interface OAuth2ConsentRequest{
  acr ?: string,
  amr ?: string[]
  challenge: string,
  client: OAuth2Client,
  login_challenge?: string,
  login_session_id?: string,
  request_url?: string,
  requested_scope?:string[]
  skip?: boolean,
  subject?: string,
}





export function handleLogin(login_challenge: string) {
  return server.post<{redirect_to?: string}>(url + `/handle_login`,{login_challenge:login_challenge},{ hideSuccess: true })
}
export function handleConsent(consent_challenge?: string) {
  return server.post<{redirect_to?:string, client_uri ?: string,client_name ?:string, requested_scope ?: string[],logo_uri ?: string}>(url + `/handle_consent`,{consent_challenge: consent_challenge},{hideSuccess: true})
}

export function acceptConsentRequest( remember: boolean,granted_scopes: string[],consent_challenge ?: string) {
  return server.post<{redirect_to:string}>(url +"/accept_consent_request",{
    consent_challenge:consent_challenge,
    remember: remember,
    granted_scopes:granted_scopes
  },{ hideSuccess: true })
}
export function rejectConsentRequest( error: 'request_denied',status_code: number,error_description: string, consent_challenge ?: string) {
  return server.post<{redirect_to:string}>(url +"/reject_consent_request",{
    consent_challenge:consent_challenge,
    error: error,
    error_description:error_description,
    status_code: status_code
  },{ hideSuccess: true })
}

export function handleLogout(logout_challenge: string) {
  return server.post<{redirect_to: string}>(url+"/handle_common_auth_logout",{
    logout_challenge:logout_challenge
  })
}



export function checkIfLoggedIn() {
  return server.get<{is_logged_in?: boolean}>(url+'/user_logged_in',{hideSuccess:true, hideError:true})
}