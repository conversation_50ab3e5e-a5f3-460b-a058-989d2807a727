import {SrServer as server} from './server';

const url = '/api/v2/settings';


export interface IListTimeZone {
  timezones: Array<{ name: string, value: string }>;

}

export function getEmailCustomTrackingDomain(id: number | string) {
  return server.get<{
    custom_tracking_cname_value: string
  }>(url + '/emails/' + id + '/custom_tracking_domain', { hideSuccess: true });
}

export function getTimeZone() {
  return server.get<IListTimeZone>('/api/v2/timezones', { hideSuccess: true })
}


