import { SrServer as server } from './server';
import { makeDashboardNonResponsive, revertToHomepageResponsive } from '../utils/styles';

import isEmpty from 'lodash-es/isEmpty';
import includes from 'lodash-es/includes';


import { crispBoot, crispTrackEvent, crispResetSession } from '../utils/crisp';
import * as queryString from 'query-string';

// import { IProfileInfoFormValues } from '../containers/onboarding-modal-v2';

// import { logInStore } from '../stores/LogInStore';
const url = '/api/v2/auth';

interface IUser {
  email: string;
  password: string;
  rememberMe: boolean;
  g_response?: string;
}

export interface INewUser {
  email: string;
  password: string;
  // first_name: string;
  // last_name: string;
  // company: string;
  timezone: string;
  country_code: string;

  invite_code?: string;
  // rs_code_used?: string;
  // is_agency?: boolean;
  g_response?: string;

  login_challenge?: string;
}

export interface ILogInResponse {
  code: 'enable_2fa' | 'verify_2fa' | 'success' | 'verify_email';
  default_country_code?: string; // for enable_2fa
  aid?: number; // for enable_2fa
  verstate?: string; // for 2fa calls
  two_fa_type?: 'gauth';
  redirect_uri:string;

  account?: LogIn.IAccount;
  redirect_to?: string
  disable_analytics: boolean;
  attemptNumber?: number;
  is_sign_up?: boolean;
}

// interface IGetUsers {
//   invites: Campaigns.IInvitedUser[];
//   teammates: Campaigns.ITeamMate[];
// }

export interface ITeamInfo {
  team_id: number,
  team_name: string,
  org_id: number,
  active: boolean
}

interface IForgotPasswordForm {
  email: string
  g_response: string
}


function disableThirdPartyAnalytics() {
  crispResetSession();
  (window as any)['intercomSettings'] = {};
}


function setupThirdPartyAnalytics(data: {
  account: LogIn.IAccount,
  triggerEvt: string,
  disable_analytics: boolean
}) {

  const account = data.account;
  console.log('setup 3party ', isEmpty(account))

  if (account && account.user_id) {


    if (data.disable_analytics) {

      disableThirdPartyAnalytics()

    } else {

      crispBoot(account);
      crispTrackEvent(data.triggerEvt);

    }
  }

  if (!includes(window.location.pathname, '/extension') && !includes(window.location.pathname, '/verify_email')) {
    makeDashboardNonResponsive();
  }
}

//1.
export function login(user: IUser) {
  return server.post<ILogInResponse>(url + '/login', user, { hideSuccess: true })
    .then(res => {

      setupThirdPartyAnalytics({
        account: res.data.account!,
        disable_analytics: res.data.disable_analytics,
        triggerEvt: 'Log-In'
      })


      return res;

    }, err => {
      throw err;
    });
}

//2.
export function register(newUser: INewUser) {
  return server.post<ILogInResponse>(url + '/signup', newUser, { hideSuccess: false })
    .then(res => {

      try {
        // call the gtag conversion
        (window as any).gtag_report_conversion_Adwords_Signup();
        (window as any).reportCustomSignUpEvent();

      } catch (e) {
        console.error('gtag_report_conversion_Adwords_Signup: ', e)
      }

      if(res.data.account){
        setupThirdPartyAnalytics({
          account: res.data.account,
          disable_analytics: res.data.disable_analytics,
          triggerEvt: 'Register'
        })
      }

      return res;

    }, err => {
      throw err;
    });;
}

//3.
export function logOut() {
  return server.post(url + '/logout', {}, { hideSuccess: true })
    .then(res => {

      crispResetSession();

      revertToHomepageResponsive();

      return res;
    });
}

//4.
export function getOAuthRedirectUrl(data: {
  service_provider: "google" | "outlook" | "hubspot" | "zoho" | "pipedrive",
  campaignId: number | null,
  email_type: string | null,
  email_setting_id: string | number | null,
  email_address?: string;
  confirm_install?: boolean;
  campaign_basic_setup?: boolean;
  is_sandbox?: boolean;
}) {

  const query = queryString.stringify({
    service_provider: data.service_provider,
    campaign_id: data.campaignId,
    email_type: data.email_type,
    email_setting_id: data.email_setting_id,
    email_address: data.email_address,
    confirm_install: data.confirm_install,
    campaign_basic_setup: data.campaign_basic_setup,
    is_sandbox: data.is_sandbox
  }, {skipNull: true});

  return server.get('/api/v2/auth/oauth_authorize?' + query,
    { hideSuccess: true, hideError: true });
}

//5.

export function sendOAuthcode(code: string, hideSuccess: boolean, hideError?: boolean) {
  return server.get('/api/v2/auth/oauth/code' + code, { hideSuccess: hideSuccess, hideError: (hideError ? hideError : false) });
}

//6.
export function authenticate() {
  return server.get<ILogInResponse>(url + '/me', { hideSuccess: true, hideError: true })
    .then(res => {

      if(res.data.account){
        setupThirdPartyAnalytics({
          account: res.data.account,
          disable_analytics: res.data.disable_analytics,
          triggerEvt: 'authenticate'
        });
    }

      return res;

    }, err => {
      throw err;
    });
}

//7.
export function forgotPassword(data: IForgotPasswordForm) {
  return server.post<{ attemptNumber: number }>(url + '/forgot_password', data);
}

//8 update password via forget password flow
export function updatePassword(data: any) {
  return server.post(url + '/forgot_password/update', data);
}

//9
export function changePassword(data: any) {
  return server.post(url + '/change_password', data);
}

//10
export function updateAPIKey(keyType: LogIn.IAPIKeyType) {
  const data = {};
  return server.post<{ new_key: string }>(url + '/update_api_key?key_type=' + keyType, data);
}

//11
export function getAPIKey(keyType: LogIn.IAPIKeyType) {
  return server.get<{ api_key: string | null }>(url + '/api_key?key_type=' + keyType, { hideSuccess: true, hideError: true });
}

//12
export function getEmailfromInviteCode(inviteCode: number | string) {
  return server.get<LogIn.IInvitedMemberDetail>(url + '/invite/' + inviteCode, { hideSuccess: true });
}

//13
export function getEmailNotificationFrequncey(data: any) {
  const query = data.code ? "?code=" + data.code : "?key=" + data.key;
  return server.get<{ settings: 'weekly' | 'never' }>('/api/v2/env1/get_email_notification_settings' + query, data)
}

//14
export function updateEmailNotificationFrequncey(data: any) {
  const query = data.code ? "?code=" + data.code : "?key=" + data.key;
  return server.post<LogIn.IAccount>('/api/v2/env1/update_email_notification_settings' + query, data)
}


//15
export function verifyEmail(data: any) {
  return server.post<ILogInResponse>(url + '/verify_email/verify', data, { hideSuccess: true, hideError: false });
}

//16
export function resendVerificationEmail(data: any) {
  return server.post(url + '/verify_email/resend', data);
}

//17
export function twoFactorAuth(
  request_type: 'send' | 'resend' | 'verify' | 'gengauth',
  data: {
    // email: string,
    aid: number,
    country_code?: string,
    mobile_country_code?: string,
    mobile_number?: number,
    verstate: string,
    is_enable_2fa_flow: boolean,
    two_fa_type: 'gauth',
    code?: number, // for both sms/gauth
    gkey?: string // for gauth
    login_challenge?:string
  }) {
  return server.post<{
    code_sent_to_number?: string,
    // only after verify, which logs in the user
    account?: LogIn.IAccount,
    disable_analytics?: boolean,
    // for gengauth
    gkey?: string
    redirect_to ?: string
  }>(url + '/2fa/' + request_type, data, { hideSuccess: true, hideError: true })
    .then(res => {
      if (request_type === 'verify' && res.data.account)
        setupThirdPartyAnalytics({
          account: res.data.account!,
          disable_analytics: res.data.disable_analytics || false,
          triggerEvt: 'verified2fa'
        });
      return res;
    });;
}











