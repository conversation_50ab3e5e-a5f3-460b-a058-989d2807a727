import axios from 'axios';
import { alertStore } from '../stores/AlertStore';
import { IPINFO_ACCESS_KEY } from '../data/config';
// import * as queryString from 'query-string';
// import { roleIsOrgOwnerOrAgencyAdminForAgency } from '../utils/role';
import { ENV_CONSTANTS } from '../data/env_constants';
// import { redirectTo } from '../utils/redirection';


let BASE_URL = '';

if (window.location.hostname === 'id.smartreach.io') {
  BASE_URL = ENV_CONSTANTS.PRODUCTION_BASE_URL;
} else if (window.location.hostname === 'id.sreml.com') {
  BASE_URL = ENV_CONSTANTS.STAGING_BASE_URL; /*NOTE: Change this url to LOCAL_BASE_URL for local testing*/
} else if(window.location.hostname === 'id2.sreml.com' ){
  BASE_URL = ENV_CONSTANTS.STAGING_BASE_URL_DEV2; /*NOTE: Change this url to LOCAL_BASE_URL for local testing*/
} else if(window.location.hostname === 'id3.sreml.com'){
  BASE_URL = ENV_CONSTANTS.STAGING_BASE_URL_DEV3; /*NOTE: Change this url to LOCAL_BASE_URL for local testing*/
} else if(window.location.hostname === 'id4.sreml.com'){
  BASE_URL = ENV_CONSTANTS.STAGING_BASE_URL_DEV4; /*NOTE: Change this url to LOCAL_BASE_URL for local testing*/
} else if(window.location.hostname === 'id5.sreml.com'){
  BASE_URL = ENV_CONSTANTS.STAGING_BASE_URL_DEV5; /*NOTE: Change this url to LOCAL_BASE_URL for local testing*/
}
else{
  BASE_URL = ENV_CONSTANTS.LOCAL_BASE_URL
}

export type IAPIResponse<T> = {
  data: T,
  status: 'success' | 'error',
  message: string
};

type IAPIErrorResponse = IAPIResponse<{
  error_type: string;
  param?: string;
}>;

export type IServerResponse<T> = Promise<IAPIResponse<T>>;


interface IAPIOptions {
  hideSuccess?: boolean;
  hideError?: boolean;
}

interface I_IPINFO_RESPONSE {
  ip: string;
  hostname: string;
  city: string;
  region: string;
  country: string;
  loc: string;
  org: string;
  postal: string;
  timezone: string;
}

const axiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },

  // `withCredentials` indicates whether or not cross-site Access-Control requests
  // should be made using credentials
  withCredentials: true
});


// REF: https://stackoverflow.com/a/51279029
// used to track slow API calls



// REF: https://github.com/mzabriskie/axios#interceptors
axiosInstance.interceptors.response.use(

  (response) => {


    return response;
  },

  (err) => {

    /**
     * Seems like err.response.config can be undefined if the server takes too long to respond.
     * 
     * ref: https://stackoverflow.com/questions/56836750/error-response-data-is-undefined-when-using-axios-interceptors-request
     */

  

    if (err.response && err.response.data) {
    
      return Promise.reject(err.response.data);
    } else {

      const errObj: IAPIErrorResponse = {
        data: {
          error_type: 'client_error'
        },
        status: 'error',
        message: err.message
      };

      return Promise.reject(errObj);
    }
  }
);






const updateAlertStore = (response: any) => {
  alertStore.pushAlert({ message: response.message, status: response.status });
};

function post<ResponseDataType>(path: string, data: Object, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  const stringifiedData = JSON.stringify(data);

  return axiosInstance
    .post(path, stringifiedData)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {

          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}

function get<ResponseDataType>(path: string, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  return axiosInstance
    .get(path)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}





function put<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  return axiosInstance
    .put(path, JSON.stringify(data))
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}

function del<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  // REF: https://github.com/mzabriskie/axios/issues/424#issuecomment-241481280
  return axiosInstance
    .request({
      url: path,
      method: 'delete',
      data: JSON.stringify(data)
    })
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}

function upload<ResponseDataType>(path: string, data: any, opts?: IAPIOptions): IServerResponse<ResponseDataType> {
  const options = {
    headers: {
      'Accept': 'application/json',
      'Content-Type': undefined
    }
  };

  return axiosInstance
    .post(path, data, options)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );

}

function getLocation(opts?: IAPIOptions): Promise<I_IPINFO_RESPONSE> {
  return axios
    .get(`https://ipinfo.io?token=${IPINFO_ACCESS_KEY}`)
    .then(

      (response) => {
        if (!(opts && opts.hideSuccess)) {
          updateAlertStore(response.data);
        }
        return (response.data);
      },
      (error) => {
        if (!(opts && opts.hideError)) {
          updateAlertStore(error);
        }
        throw (error);
      },

    );
}

export const SrServer = {
  get,
  post,
  getLocation,
  upload,
  del,
  put

}

export const SrServerCallingService = {
  get,
  post
}
