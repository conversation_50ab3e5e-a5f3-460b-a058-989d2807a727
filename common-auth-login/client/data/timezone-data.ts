
export const timezone = [
  {
    name: '(-12:00) Etc/GMT+12',
    id: 'Etc/GMT+12'
  },
  {
    name: '(-12:00) Pacific/Kwajalein',
    id: 'Pacific/Kwajalein'
  },
  {
    name: '(-12:00) Kwajalein',
    id: 'Kwa<PERSON><PERSON>'
  },
  {
    name: '(-12:00) Pacific/Enderbury',
    id: 'Pacific/Enderbury'
  },
  {
    name: '(-11:30) Pacific/Niue',
    id: 'Pacific/Niue'
  },
  {
    name: '(-11:00) Pacific/Samoa',
    id: 'Pacific/Samoa'
  },
  {
    name: '(-11:00) Pacific/Apia',
    id: 'Pacific/Apia'
  },
  {
    name: '(-11:00) America/Adak',
    id: 'America/Adak'
  },
  {
    name: '(-11:00) Pacific/Pago_Pago',
    id: 'Pacific/Pago_Pago'
  },
  {
    name: '(-11:00) US/Aleutian',
    id: 'US/Aleutian'
  },
  {
    name: '(-11:00) America/Atka',
    id: 'America/Atka'
  },
  {
    name: '(-11:00) US/Samoa',
    id: 'US/Samoa'
  },
  {
    name: '(-11:00) Etc/GMT+11',
    id: 'Etc/GMT+11'
  },
  {
    name: '(-11:00) America/Nome',
    id: 'America/Nome'
  },
  {
    name: '(-11:00) Pacific/Mnameway',
    id: 'Pacific/Mnameway'
  },
  {
    name: '(-11:00) Pacific/Fakaofo',
    id: 'Pacific/Fakaofo'
  },
  {
    name: '(-10:40) Pacific/Kiritimati',
    id: 'Pacific/Kiritimati'
  },
  {
    name: '(-10:30) Pacific/Rarotonga',
    id: 'Pacific/Rarotonga'
  },
  {
    name: '(-10:00) Etc/GMT+10',
    id: 'Etc/GMT+10'
  },
  {
    name: '(-10:00) Pacific/Johnston',
    id: 'Pacific/Johnston'
  },
  {
    name: '(-10:00) Pacific/Honolulu',
    id: 'Pacific/Honolulu'
  },
  {
    name: '(-10:00) US/Alaska',
    id: 'US/Alaska'
  },
  {
    name: '(-10:00) America/Anchorage',
    id: 'America/Anchorage'
  },
  {
    name: '(-10:00) US/Hawaii',
    id: 'US/Hawaii'
  },
  {
    name: '(-10:00) HST',
    id: 'HST'
  },
  {
    name: '(-10:00) Pacific/Tahiti',
    id: 'Pacific/Tahiti'
  },
  {
    name: '(-09:30) Pacific/Marquesas',
    id: 'Pacific/Marquesas'
  },
  {
    name: '(-09:00) America/Dawson',
    id: 'America/Dawson'
  },
  {
    name: '(-09:00) Pacific/Gambier',
    id: 'Pacific/Gambier'
  },
  {
    name: '(-09:00) America/Yakutat',
    id: 'America/Yakutat'
  },
  {
    name: '(-09:00) Etc/GMT+9',
    id: 'Etc/GMT+9'
  },
  {
    name: '(-08:30) Pacific/Pitcairn',
    id: 'Pacific/Pitcairn'
  },
  {
    name: '(-08:00) US/Pacific-New',
    id: 'US/Pacific-New'
  },
  {
    name: '(-08:00) America/Bahia_Banderas',
    id: 'America/Bahia_Banderas'
  },
  {
    name: '(-08:00) America/Los_Angeles',
    id: 'America/Los_Angeles'
  },
  {
    name: '(-08:00) America/Tijuana',
    id: 'America/Tijuana'
  },
  {
    name: '(-08:00) America/Hermosillo',
    id: 'America/Hermosillo'
  },
  {
    name: '(-08:00) America/Fort_Nelson',
    id: 'America/Fort_Nelson'
  },
  {
    name: '(-08:00) America/Ensenada',
    id: 'America/Ensenada'
  },
  {
    name: '(-08:00) America/Inuvik',
    id: 'America/Inuvik'
  },
  {
    name: '(-08:00) US/Pacific',
    id: 'US/Pacific'
  },
  {
    name: '(-08:00) Canada/Yukon',
    id: 'Canada/Yukon'
  },
  {
    name: '(-08:00) America/Sitka',
    id: 'America/Sitka'
  },
  {
    name: '(-08:00) America/Metlakatla',
    id: 'America/Metlakatla'
  },
  {
    name: '(-08:00) Mexico/BajaSur',
    id: 'Mexico/BajaSur'
  },
  {
    name: '(-08:00) America/Dawson_Creek',
    id: 'America/Dawson_Creek'
  },
  {
    name: '(-08:00) America/Vancouver',
    id: 'America/Vancouver'
  },
  {
    name: '(-08:00) Etc/GMT+8',
    id: 'Etc/GMT+8'
  },
  {
    name: '(-08:00) Mexico/BajaNorte',
    id: 'Mexico/BajaNorte'
  },
  {
    name: '(-08:00) America/Mazatlan',
    id: 'America/Mazatlan'
  },
  {
    name: '(-08:00) America/Whitehorse',
    id: 'America/Whitehorse'
  },
  {
    name: '(-08:00) Canada/Pacific',
    id: 'Canada/Pacific'
  },
  {
    name: '(-08:00) America/Santa_Isabel',
    id: 'America/Santa_Isabel'
  },
  {
    name: '(-08:00) PST8PDT',
    id: 'PST8PDT'
  },
  {
    name: '(-08:00) America/Juneau',
    id: 'America/Juneau'
  },
  {
    name: '(-07:00) Navajo',
    id: 'Navajo'
  },
  {
    name: '(-07:00) MST',
    id: 'MST'
  },
  {
    name: '(-07:00) America/Swift_Current',
    id: 'America/Swift_Current'
  },
  {
    name: '(-07:00) US/Mountain',
    id: 'US/Mountain'
  },
  {
    name: '(-07:00) Etc/GMT+7',
    id: 'Etc/GMT+7'
  },
  {
    name: '(-07:00) America/North_Dakota/Beulah',
    id: 'America/North_Dakota/Beulah'
  },
  {
    name: '(-07:00) America/Creston',
    id: 'America/Creston'
  },
  {
    name: '(-07:00) America/North_Dakota/Center',
    id: 'America/North_Dakota/Center'
  },
  {
    name: '(-07:00) America/Boise',
    id: 'America/Boise'
  },
  {
    name: '(-07:00) America/Yellowknife',
    id: 'America/Yellowknife'
  },
  {
    name: '(-07:00) America/Cambrnamege_Bay',
    id: 'America/Cambrnamege_Bay'
  },
  {
    name: '(-07:00) MST7MDT',
    id: 'MST7MDT'
  },
  {
    name: '(-07:00) America/Phoenix',
    id: 'America/Phoenix'
  },
  {
    name: '(-07:00) America/Edmonton',
    id: 'America/Edmonton'
  },
  {
    name: '(-07:00) America/Denver',
    id: 'America/Denver'
  },
  {
    name: '(-07:00) America/North_Dakota/New_Salem',
    id: 'America/North_Dakota/New_Salem'
  },
  {
    name: '(-07:00) Canada/Mountain',
    id: 'Canada/Mountain'
  },
  {
    name: '(-07:00) US/Arizona',
    id: 'US/Arizona'
  },
  {
    name: '(-07:00) America/Shiprock',
    id: 'America/Shiprock'
  },
  {
    name: '(-06:00) America/Regina',
    id: 'America/Regina'
  },
  {
    name: '(-06:00) America/Managua',
    id: 'America/Managua'
  },
  {
    name: '(-06:00) America/Winnipeg',
    id: 'America/Winnipeg'
  },
  {
    name: '(-06:00) America/Mexico_City',
    id: 'America/Mexico_City'
  },
  {
    name: '(-06:00) America/Guatemala',
    id: 'America/Guatemala'
  },
  {
    name: '(-06:00) America/Mernamea',
    id: 'America/Mernamea'
  },
  {
    name: '(-06:00) America/Indiana/Petersburg',
    id: 'America/Indiana/Petersburg'
  },
  {
    name: '(-06:00) America/Resolute',
    id: 'America/Resolute'
  },
  {
    name: '(-06:00) Pacific/Easter',
    id: 'Pacific/Easter'
  },
  {
    name: '(-06:00) America/Tegucigalpa',
    id: 'America/Tegucigalpa'
  },
  {
    name: '(-06:00) Etc/GMT+6',
    id: 'Etc/GMT+6'
  },
  {
    name: '(-06:00) Canada/Central',
    id: 'Canada/Central'
  },
  {
    name: '(-06:00) US/Central',
    id: 'US/Central'
  },
  {
    name: '(-06:00) America/El_Salvador',
    id: 'America/El_Salvador'
  },
  {
    name: '(-06:00) America/Knox_IN',
    id: 'America/Knox_IN'
  },
  {
    name: '(-06:00) America/Chicago',
    id: 'America/Chicago'
  },
  {
    name: '(-06:00) Mexico/General',
    id: 'Mexico/General'
  },
  {
    name: '(-06:00) Chile/EasterIsland',
    id: 'Chile/EasterIsland'
  },
  {
    name: '(-06:00) US/Indiana-Starke',
    id: 'US/Indiana-Starke'
  },
  {
    name: '(-06:00) CST6CDT',
    id: 'CST6CDT'
  },
  {
    name: '(-06:00) America/Matamoros',
    id: 'America/Matamoros'
  },
  {
    name: '(-06:00) Canada/Saskatchewan',
    id: 'Canada/Saskatchewan'
  },
  {
    name: '(-06:00) America/Ojinaga',
    id: 'America/Ojinaga'
  },
  {
    name: '(-06:00) Canada/East-Saskatchewan',
    id: 'Canada/East-Saskatchewan'
  },
  {
    name: '(-06:00) America/Belize',
    id: 'America/Belize'
  },
  {
    name: '(-06:00) America/Chihuahua',
    id: 'America/Chihuahua'
  },
  {
    name: '(-06:00) America/Monterrey',
    id: 'America/Monterrey'
  },
  {
    name: '(-06:00) America/Kentucky/Monticello',
    id: 'America/Kentucky/Monticello'
  },
  {
    name: '(-06:00) America/Rainy_River',
    id: 'America/Rainy_River'
  },
  {
    name: '(-06:00) America/Cancun',
    id: 'America/Cancun'
  },
  {
    name: '(-06:00) America/Costa_Rica',
    id: 'America/Costa_Rica'
  },
  {
    name: '(-06:00) America/Rankin_Inlet',
    id: 'America/Rankin_Inlet'
  },
  {
    name: '(-06:00) America/Indiana/Knox',
    id: 'America/Indiana/Knox'
  },
  {
    name: '(-05:00) Brazil/Acre',
    id: 'Brazil/Acre'
  },
  {
    name: '(-05:00) America/Panama',
    id: 'America/Panama'
  },
  {
    name: '(-05:00) Cuba',
    id: 'Cuba'
  },
  {
    name: '(-05:00) America/Indiana/Winamac',
    id: 'America/Indiana/Winamac'
  },
  {
    name: '(-05:00) America/Port-au-Prince',
    id: 'America/Port-au-Prince'
  },
  {
    name: '(-05:00) America/Cayman',
    id: 'America/Cayman'
  },
  {
    name: '(-05:00) America/Indianapolis',
    id: 'America/Indianapolis'
  },
  {
    name: '(-05:00) America/Atikokan',
    id: 'America/Atikokan'
  },
  {
    name: '(-05:00) America/Fort_Wayne',
    id: 'America/Fort_Wayne'
  },
  {
    name: '(-05:00) America/Eirunepe',
    id: 'America/Eirunepe'
  },
  {
    name: '(-05:00) America/Bogota',
    id: 'America/Bogota'
  },
  {
    name: '(-05:00) America/Montreal',
    id: 'America/Montreal'
  },
  {
    name: '(-05:00) America/Thunder_Bay',
    id: 'America/Thunder_Bay'
  },
  {
    name: '(-05:00) America/Nipigon',
    id: 'America/Nipigon'
  },
  {
    name: '(-05:00) America/Detroit',
    id: 'America/Detroit'
  },
  {
    name: '(-05:00) Etc/GMT+5',
    id: 'Etc/GMT+5'
  },
  {
    name: '(-05:00) America/Menominee',
    id: 'America/Menominee'
  },
  {
    name: '(-05:00) America/Indiana/Vevay',
    id: 'America/Indiana/Vevay'
  },
  {
    name: '(-05:00) Pacific/Galapagos',
    id: 'Pacific/Galapagos'
  },
  {
    name: '(-05:00) US/Eastern',
    id: 'US/Eastern'
  },
  {
    name: '(-05:00) US/East-Indiana',
    id: 'US/East-Indiana'
  },
  {
    name: '(-05:00) America/Iqaluit',
    id: 'America/Iqaluit'
  },
  {
    name: '(-05:00) America/Indiana/Tell_City',
    id: 'America/Indiana/Tell_City'
  },
  {
    name: '(-05:00) EST5EDT',
    id: 'EST5EDT'
  },
  {
    name: '(-05:00) America/Guayaquil',
    id: 'America/Guayaquil'
  },
  {
    name: '(-05:00) America/Coral_Harbour',
    id: 'America/Coral_Harbour'
  },
  {
    name: '(-05:00) America/Indiana/Marengo',
    id: 'America/Indiana/Marengo'
  },
  {
    name: '(-05:00) America/Lima',
    id: 'America/Lima'
  },
  {
    name: '(-05:00) EST',
    id: 'EST'
  },
  {
    name: '(-05:00) America/Rio_Branco',
    id: 'America/Rio_Branco'
  },
  {
    name: '(-05:00) America/Toronto',
    id: 'America/Toronto'
  },
  {
    name: '(-05:00) America/Grand_Turk',
    id: 'America/Grand_Turk'
  },
  {
    name: '(-05:00) US/Michigan',
    id: 'US/Michigan'
  },
  {
    name: '(-05:00) Jamaica',
    id: 'Jamaica'
  },
  {
    name: '(-05:00) America/Indiana/Vincennes',
    id: 'America/Indiana/Vincennes'
  },
  {
    name: '(-05:00) America/Havana',
    id: 'America/Havana'
  },
  {
    name: '(-05:00) America/New_York',
    id: 'America/New_York'
  },
  {
    name: '(-05:00) America/Indiana/Indianapolis',
    id: 'America/Indiana/Indianapolis'
  },
  {
    name: '(-05:00) America/Porto_Acre',
    id: 'America/Porto_Acre'
  },
  {
    name: '(-05:00) America/Nassau',
    id: 'America/Nassau'
  },
  {
    name: '(-05:00) Canada/Eastern',
    id: 'Canada/Eastern'
  },
  {
    name: '(-05:00) America/Jamaica',
    id: 'America/Jamaica'
  },
  {
    name: '(-05:00) America/Louisville',
    id: 'America/Louisville'
  },
  {
    name: '(-05:00) America/Kentucky/Louisville',
    id: 'America/Kentucky/Louisville'
  },
  {
    name: '(-04:30) America/Santo_Domingo',
    id: 'America/Santo_Domingo'
  },
  {
    name: '(-04:00) America/Cuiaba',
    id: 'America/Cuiaba'
  },
  {
    name: '(-04:00) America/Campo_Grande',
    id: 'America/Campo_Grande'
  },
  {
    name: '(-04:00) America/St_Kitts',
    id: 'America/St_Kitts'
  },
  {
    name: '(-04:00) America/Virgin',
    id: 'America/Virgin'
  },
  {
    name: '(-04:00) America/Montserrat',
    id: 'America/Montserrat'
  },
  {
    name: '(-04:00) America/Kralendijk',
    id: 'America/Kralendijk'
  },
  {
    name: '(-04:00) America/Dominica',
    id: 'America/Dominica'
  },
  {
    name: '(-04:00) America/Boa_Vista',
    id: 'America/Boa_Vista'
  },
  {
    name: '(-04:00) America/Aruba',
    id: 'America/Aruba'
  },
  {
    name: '(-04:00) America/Grenada',
    id: 'America/Grenada'
  },
  {
    name: '(-04:00) Etc/GMT+4',
    id: 'Etc/GMT+4'
  },
  {
    name: '(-04:00) America/St_Lucia',
    id: 'America/St_Lucia'
  },
  {
    name: '(-04:00) Atlantic/Stanley',
    id: 'Atlantic/Stanley'
  },
  {
    name: '(-04:00) America/Anguilla',
    id: 'America/Anguilla'
  },
  {
    name: '(-04:00) America/Moncton',
    id: 'America/Moncton'
  },
  {
    name: '(-04:00) America/Glace_Bay',
    id: 'America/Glace_Bay'
  },
  {
    name: '(-04:00) America/Caracas',
    id: 'America/Caracas'
  },
  {
    name: '(-04:00) America/Manaus',
    id: 'America/Manaus'
  },
  {
    name: '(-04:00) America/Porto_Velho',
    id: 'America/Porto_Velho'
  },
  {
    name: '(-04:00) America/Pangnirtung',
    id: 'America/Pangnirtung'
  },
  {
    name: '(-04:00) America/Curacao',
    id: 'America/Curacao'
  },
  {
    name: '(-04:00) America/Port_of_Spain',
    id: 'America/Port_of_Spain'
  },
  {
    name: '(-04:00) America/Asuncion',
    id: 'America/Asuncion'
  },
  {
    name: '(-04:00) America/Blanc-Sablon',
    id: 'America/Blanc-Sablon'
  },
  {
    name: '(-04:00) America/Antigua',
    id: 'America/Antigua'
  },
  {
    name: '(-04:00) America/Guadeloupe',
    id: 'America/Guadeloupe'
  },
  {
    name: '(-04:00) America/Lower_Princes',
    id: 'America/Lower_Princes'
  },
  {
    name: '(-04:00) America/St_Vincent',
    id: 'America/St_Vincent'
  },
  {
    name: '(-04:00) America/Marigot',
    id: 'America/Marigot'
  },
  {
    name: '(-04:00) America/Miquelon',
    id: 'America/Miquelon'
  },
  {
    name: '(-04:00) America/Thule',
    id: 'America/Thule'
  },
  {
    name: '(-04:00) America/St_Thomas',
    id: 'America/St_Thomas'
  },
  {
    name: '(-04:00) America/Halifax',
    id: 'America/Halifax'
  },
  {
    name: '(-04:00) America/Barbados',
    id: 'America/Barbados'
  },
  {
    name: '(-04:00) America/Tortola',
    id: 'America/Tortola'
  },
  {
    name: '(-04:00) America/Goose_Bay',
    id: 'America/Goose_Bay'
  },
  {
    name: '(-04:00) America/Puerto_Rico',
    id: 'America/Puerto_Rico'
  },
  {
    name: '(-04:00) Atlantic/Bermuda',
    id: 'Atlantic/Bermuda'
  },
  {
    name: '(-04:00) America/Santarem',
    id: 'America/Santarem'
  },
  {
    name: '(-04:00) America/Martinique',
    id: 'America/Martinique'
  },
  {
    name: '(-04:00) America/St_Barthelemy',
    id: 'America/St_Barthelemy'
  },
  {
    name: '(-04:00) America/La_Paz',
    id: 'America/La_Paz'
  },
  {
    name: '(-04:00) Brazil/West',
    id: 'Brazil/West'
  },
  {
    name: '(-04:00) Canada/Atlantic',
    id: 'Canada/Atlantic'
  },
  {
    name: '(-03:45) America/Guyana',
    id: 'America/Guyana'
  },
  {
    name: '(-03:30) America/Paramaribo',
    id: 'America/Paramaribo'
  },
  {
    name: '(-03:30) Canada/Newfoundland',
    id: 'Canada/Newfoundland'
  },
  {
    name: '(-03:30) America/St_Johns',
    id: 'America/St_Johns'
  },
  {
    name: '(-03:00) America/Argentina/San_Luis',
    id: 'America/Argentina/San_Luis'
  },
  {
    name: '(-03:00) Brazil/East',
    id: 'Brazil/East'
  },
  {
    name: '(-03:00) America/Argentina/Rio_Gallegos',
    id: 'America/Argentina/Rio_Gallegos'
  },
  {
    name: '(-03:00) America/Santiago',
    id: 'America/Santiago'
  },
  {
    name: '(-03:00) America/Argentina/La_Rioja',
    id: 'America/Argentina/La_Rioja'
  },
  {
    name: '(-03:00) America/Danmarkshavn',
    id: 'America/Danmarkshavn'
  },
  {
    name: '(-03:00) America/Sao_Paulo',
    id: 'America/Sao_Paulo'
  },
  {
    name: '(-03:00) America/Argentina/Tucuman',
    id: 'America/Argentina/Tucuman'
  },
  {
    name: '(-03:00) America/Godthab',
    id: 'America/Godthab'
  },
  {
    name: '(-03:00) America/Argentina/Cordoba',
    id: 'America/Argentina/Cordoba'
  },
  {
    name: '(-03:00) America/Cayenne',
    id: 'America/Cayenne'
  },
  {
    name: '(-03:00) America/Argentina/Catamarca',
    id: 'America/Argentina/Catamarca'
  },
  {
    name: '(-03:00) America/Argentina/Buenos_Aires',
    id: 'America/Argentina/Buenos_Aires'
  },
  {
    name: '(-03:00) America/Argentina/San_Juan',
    id: 'America/Argentina/San_Juan'
  },
  {
    name: '(-03:00) America/Catamarca',
    id: 'America/Catamarca'
  },
  {
    name: '(-03:00) America/Montevnameeo',
    id: 'America/Montevnameeo'
  },
  {
    name: '(-03:00) America/Buenos_Aires',
    id: 'America/Buenos_Aires'
  },
  {
    name: '(-03:00) America/Cordoba',
    id: 'America/Cordoba'
  },
  {
    name: '(-03:00) America/Argentina/Mendoza',
    id: 'America/Argentina/Mendoza'
  },
  {
    name: '(-03:00) America/Argentina/Jujuy',
    id: 'America/Argentina/Jujuy'
  },
  {
    name: '(-03:00) America/Jujuy',
    id: 'America/Jujuy'
  },
  {
    name: '(-03:00) America/Fortaleza',
    id: 'America/Fortaleza'
  },
  {
    name: '(-03:00) America/Argentina/Salta',
    id: 'America/Argentina/Salta'
  },
  {
    name: '(-03:00) Chile/Continental',
    id: 'Chile/Continental'
  },
  {
    name: '(-03:00) America/Belem',
    id: 'America/Belem'
  },
  {
    name: '(-03:00) America/Bahia',
    id: 'America/Bahia'
  },
  {
    name: '(-03:00) Antarctica/Palmer',
    id: 'Antarctica/Palmer'
  },
  {
    name: '(-03:00) America/Recife',
    id: 'America/Recife'
  },
  {
    name: '(-03:00) America/Rosario',
    id: 'America/Rosario'
  },
  {
    name: '(-03:00) America/Mendoza',
    id: 'America/Mendoza'
  },
  {
    name: '(-03:00) America/Maceio',
    id: 'America/Maceio'
  },
  {
    name: '(-03:00) Etc/GMT+3',
    id: 'Etc/GMT+3'
  },
  {
    name: '(-03:00) America/Argentina/ComodRivadavia',
    id: 'America/Argentina/ComodRivadavia'
  },
  {
    name: '(-03:00) America/Argentina/Ushuaia',
    id: 'America/Argentina/Ushuaia'
  },
  {
    name: '(-03:00) America/Araguaina',
    id: 'America/Araguaina'
  },
  {
    name: '(-02:00) Atlantic/Cape_Verde',
    id: 'Atlantic/Cape_Verde'
  },
  {
    name: '(-02:00) America/Scoresbysund',
    id: 'America/Scoresbysund'
  },
  {
    name: '(-02:00) Atlantic/South_Georgia',
    id: 'Atlantic/South_Georgia'
  },
  {
    name: '(-02:00) America/Noronha',
    id: 'America/Noronha'
  },
  {
    name: '(-02:00) Etc/GMT+2',
    id: 'Etc/GMT+2'
  },
  {
    name: '(-02:00) Brazil/DeNoronha',
    id: 'Brazil/DeNoronha'
  },
  {
    name: '(-01:00) Etc/GMT+1',
    id: 'Etc/GMT+1'
  },
  {
    name: '(-01:00) Atlantic/Azores',
    id: 'Atlantic/Azores'
  },
  {
    name: '(-01:00) Africa/Bissau',
    id: 'Africa/Bissau'
  },
  {
    name: '(-01:00) Africa/El_Aaiun',
    id: 'Africa/El_Aaiun'
  },
  {
    name: '(-00:44) Africa/Monrovia',
    id: 'Africa/Monrovia'
  },
  {
    name: '(+00:00) Africa/Ouagadougou',
    id: 'Africa/Ouagadougou'
  },
  {
    name: '(+00:00) Africa/Timbuktu',
    id: 'Africa/Timbuktu'
  },
  {
    name: '(+00:00) Antarctica/Rothera',
    id: 'Antarctica/Rothera'
  },
  {
    name: '(+00:00) Africa/Conakry',
    id: 'Africa/Conakry'
  },
  {
    name: '(+00:00) Etc/GMT0',
    id: 'Etc/GMT0'
  },
  {
    name: '(+00:00) GMT-0',
    id: 'GMT-0'
  },
  {
    name: '(+00:00) UCT',
    id: 'UCT'
  },
  {
    name: '(+00:00) UTC',
    id: 'UTC'
  },
  {
    name: '(+00:00) Greenwich',
    id: 'Greenwich'
  },
  {
    name: '(+00:00) Africa/Algiers',
    id: 'Africa/Algiers'
  },
  {
    name: '(+00:00) Antarctica/Troll',
    id: 'Antarctica/Troll'
  },
  {
    name: '(+00:00) Africa/Ceuta',
    id: 'Africa/Ceuta'
  },
  {
    name: '(+00:00) Etc/GMT',
    id: 'Etc/GMT'
  },
  {
    name: '(+00:00) Atlantic/St_Helena',
    id: 'Atlantic/St_Helena'
  },
  {
    name: '(+00:00) Africa/Dakar',
    id: 'Africa/Dakar'
  },
  {
    name: '(+00:00) Etc/UCT',
    id: 'Etc/UCT'
  },
  {
    name: '(+00:00) Atlantic/Madeira',
    id: 'Atlantic/Madeira'
  },
  {
    name: '(+00:00) Africa/Nouakchott',
    id: 'Africa/Nouakchott'
  },
  {
    name: '(+00:00) GMT',
    id: 'GMT'
  },
  {
    name: '(+00:00) Africa/Casablanca',
    id: 'Africa/Casablanca'
  },
  {
    name: '(+00:00) Africa/Accra',
    id: 'Africa/Accra'
  },
  {
    name: '(+00:00) Africa/Sao_Tome',
    id: 'Africa/Sao_Tome'
  },
  {
    name: '(+00:00) Etc/Greenwich',
    id: 'Etc/Greenwich'
  },
  {
    name: '(+00:00) Etc/GMT-0',
    id: 'Etc/GMT-0'
  },
  {
    name: '(+00:00) WET',
    id: 'WET'
  },
  {
    name: '(+00:00) Etc/Universal',
    id: 'Etc/Universal'
  },
  {
    name: '(+00:00) Africa/Abnamejan',
    id: 'Africa/Abnamejan'
  },
  {
    name: '(+00:00) Zulu',
    id: 'Zulu'
  },
  {
    name: '(+00:00) Etc/GMT+0',
    id: 'Etc/GMT+0'
  },
  {
    name: '(+00:00) Africa/Banjul',
    id: 'Africa/Banjul'
  },
  {
    name: '(+00:00) GMT+0',
    id: 'GMT+0'
  },
  {
    name: '(+00:00) Africa/Freetown',
    id: 'Africa/Freetown'
  },
  {
    name: '(+00:00) Etc/UTC',
    id: 'Etc/UTC'
  },
  {
    name: '(+00:00) Universal',
    id: 'Universal'
  },
  {
    name: '(+00:00) GMT0',
    id: 'GMT0'
  },
  {
    name: '(+00:00) Iceland',
    id: 'Iceland'
  },
  {
    name: '(+00:00) Atlantic/Faroe',
    id: 'Atlantic/Faroe'
  },
  {
    name: '(+00:00) Africa/Lome',
    id: 'Africa/Lome'
  },
  {
    name: '(+00:00) Africa/Bamako',
    id: 'Africa/Bamako'
  },
  {
    name: '(+00:00) Atlantic/Reykjavik',
    id: 'Atlantic/Reykjavik'
  },
  {
    name: '(+00:00) Atlantic/Faeroe',
    id: 'Atlantic/Faeroe'
  },
  {
    name: '(+00:00) Etc/Zulu',
    id: 'Etc/Zulu'
  },
  {
    name: '(+00:00) Atlantic/Canary',
    id: 'Atlantic/Canary'
  },
  {
    name: '(+01:00) Europe/Gibraltar',
    id: 'Europe/Gibraltar'
  },
  {
    name: '(+01:00) Eire',
    id: 'Eire'
  },
  {
    name: '(+01:00) Europe/Warsaw',
    id: 'Europe/Warsaw'
  },
  {
    name: '(+01:00) MET',
    id: 'MET'
  },
  {
    name: '(+01:00) Europe/Tirane',
    id: 'Europe/Tirane'
  },
  {
    name: '(+01:00) GB',
    id: 'GB'
  },
  {
    name: '(+01:00) Africa/Douala',
    id: 'Africa/Douala'
  },
  {
    name: '(+01:00) Europe/Malta',
    id: 'Europe/Malta'
  },
  {
    name: '(+01:00) Etc/GMT-1',
    id: 'Etc/GMT-1'
  },
  {
    name: '(+01:00) Europe/Copenhagen',
    id: 'Europe/Copenhagen'
  },
  {
    name: '(+01:00) Europe/Vienna',
    id: 'Europe/Vienna'
  },
  {
    name: '(+01:00) Portugal',
    id: 'Portugal'
  },
  {
    name: '(+01:00) Europe/Brussels',
    id: 'Europe/Brussels'
  },
  {
    name: '(+01:00) Europe/Monaco',
    id: 'Europe/Monaco'
  },
  {
    name: '(+01:00) Europe/Isle_of_Man',
    id: 'Europe/Isle_of_Man'
  },
  {
    name: '(+01:00) Europe/Zurich',
    id: 'Europe/Zurich'
  },
  {
    name: '(+01:00) Europe/Podgorica',
    id: 'Europe/Podgorica'
  },
  {
    name: '(+01:00) Europe/Berlin',
    id: 'Europe/Berlin'
  },
  {
    name: '(+01:00) Europe/Bratislava',
    id: 'Europe/Bratislava'
  },
  {
    name: '(+01:00) Europe/Stockholm',
    id: 'Europe/Stockholm'
  },
  {
    name: '(+01:00) Poland',
    id: 'Poland'
  },
  {
    name: '(+01:00) GB-Eire',
    id: 'GB-Eire'
  },
  {
    name: '(+01:00) Africa/Brazzaville',
    id: 'Africa/Brazzaville'
  },
  {
    name: '(+01:00) Europe/Sarajevo',
    id: 'Europe/Sarajevo'
  },
  {
    name: '(+01:00) Europe/San_Marino',
    id: 'Europe/San_Marino'
  },
  {
    name: '(+01:00) Africa/Porto-Novo',
    id: 'Africa/Porto-Novo'
  },
  {
    name: '(+01:00) Africa/Luanda',
    id: 'Africa/Luanda'
  },
  {
    name: '(+01:00) Africa/Malabo',
    id: 'Africa/Malabo'
  },
  {
    name: '(+01:00) Europe/Busingen',
    id: 'Europe/Busingen'
  },
  {
    name: '(+01:00) Europe/Luxembourg',
    id: 'Europe/Luxembourg'
  },
  {
    name: '(+01:00) Europe/Ljubljana',
    id: 'Europe/Ljubljana'
  },
  {
    name: '(+01:00) Europe/Paris',
    id: 'Europe/Paris'
  },
  {
    name: '(+01:00) Africa/Niamey',
    id: 'Africa/Niamey'
  },
  {
    name: '(+01:00) Africa/Bangui',
    id: 'Africa/Bangui'
  },
  {
    name: '(+01:00) Europe/Prague',
    id: 'Europe/Prague'
  },
  {
    name: '(+01:00) Europe/Amsterdam',
    id: 'Europe/Amsterdam'
  },
  {
    name: '(+01:00) Europe/Jersey',
    id: 'Europe/Jersey'
  },
  {
    name: '(+01:00) Europe/Oslo',
    id: 'Europe/Oslo'
  },
  {
    name: '(+01:00) CET',
    id: 'CET'
  },
  {
    name: '(+01:00) Europe/London',
    id: 'Europe/London'
  },
  {
    name: '(+01:00) Europe/Budapest',
    id: 'Europe/Budapest'
  },
  {
    name: '(+01:00) Europe/Dublin',
    id: 'Europe/Dublin'
  },
  {
    name: '(+01:00) Africa/Ndjamena',
    id: 'Africa/Ndjamena'
  },
  {
    name: '(+01:00) Europe/Madrname',
    id: 'Europe/Madrname'
  },
  {
    name: '(+01:00) Africa/Libreville',
    id: 'Africa/Libreville'
  },
  {
    name: '(+01:00) Africa/Lagos',
    id: 'Africa/Lagos'
  },
  {
    name: '(+01:00) Europe/Lisbon',
    id: 'Europe/Lisbon'
  },
  {
    name: '(+01:00) Africa/Kinshasa',
    id: 'Africa/Kinshasa'
  },
  {
    name: '(+01:00) Atlantic/Jan_Mayen',
    id: 'Atlantic/Jan_Mayen'
  },
  {
    name: '(+01:00) Europe/Belgrade',
    id: 'Europe/Belgrade'
  },
  {
    name: '(+01:00) Africa/Tunis',
    id: 'Africa/Tunis'
  },
  {
    name: '(+01:00) Europe/Rome',
    id: 'Europe/Rome'
  },
  {
    name: '(+01:00) Europe/Belfast',
    id: 'Europe/Belfast'
  },
  {
    name: '(+01:00) Arctic/Longyearbyen',
    id: 'Arctic/Longyearbyen'
  },
  {
    name: '(+01:00) Europe/Vatican',
    id: 'Europe/Vatican'
  },
  {
    name: '(+01:00) Europe/Zagreb',
    id: 'Europe/Zagreb'
  },
  {
    name: '(+01:00) Europe/Guernsey',
    id: 'Europe/Guernsey'
  },
  {
    name: '(+01:00) Europe/Skopje',
    id: 'Europe/Skopje'
  },
  {
    name: '(+01:00) Europe/Andorra',
    id: 'Europe/Andorra'
  },
  {
    name: '(+01:00) Europe/Vaduz',
    id: 'Europe/Vaduz'
  },
  {
    name: '(+02:00) Asia/Beirut',
    id: 'Asia/Beirut'
  },
  {
    name: '(+02:00) Europe/Sofia',
    id: 'Europe/Sofia'
  },
  {
    name: '(+02:00) Africa/Khartoum',
    id: 'Africa/Khartoum'
  },
  {
    name: '(+02:00) Africa/Lusaka',
    id: 'Africa/Lusaka'
  },
  {
    name: '(+02:00) Europe/Helsinki',
    id: 'Europe/Helsinki'
  },
  {
    name: '(+02:00) Asia/Damascus',
    id: 'Asia/Damascus'
  },
  {
    name: '(+02:00) Turkey',
    id: 'Turkey'
  },
  {
    name: '(+02:00) Europe/Mariehamn',
    id: 'Europe/Mariehamn'
  },
  {
    name: '(+02:00) Asia/Hebron',
    id: 'Asia/Hebron'
  },
  {
    name: '(+02:00) Asia/Gaza',
    id: 'Asia/Gaza'
  },
  {
    name: '(+02:00) Egypt',
    id: 'Egypt'
  },
  {
    name: '(+02:00) Africa/Blantyre',
    id: 'Africa/Blantyre'
  },
  {
    name: '(+02:00) Africa/Maseru',
    id: 'Africa/Maseru'
  },
  {
    name: '(+02:00) Europe/Bucharest',
    id: 'Europe/Bucharest'
  },
  {
    name: '(+02:00) Africa/Tripoli',
    id: 'Africa/Tripoli'
  },
  {
    name: '(+02:00) Asia/Amman',
    id: 'Asia/Amman'
  },
  {
    name: '(+02:00) Africa/Windhoek',
    id: 'Africa/Windhoek'
  },
  {
    name: '(+02:00) Africa/Bujumbura',
    id: 'Africa/Bujumbura'
  },
  {
    name: '(+02:00) Israel',
    id: 'Israel'
  },
  {
    name: '(+02:00) Europe/Istanbul',
    id: 'Europe/Istanbul'
  },
  {
    name: '(+02:00) Asia/Jerusalem',
    id: 'Asia/Jerusalem'
  },
  {
    name: '(+02:00) Africa/Gaborone',
    id: 'Africa/Gaborone'
  },
  {
    name: '(+02:00) Asia/Famagusta',
    id: 'Asia/Famagusta'
  },
  {
    name: '(+02:00) Africa/Maputo',
    id: 'Africa/Maputo'
  },
  {
    name: '(+02:00) Europe/Nicosia',
    id: 'Europe/Nicosia'
  },
  {
    name: '(+02:00) EET',
    id: 'EET'
  },
  {
    name: '(+02:00) Africa/Johannesburg',
    id: 'Africa/Johannesburg'
  },
  {
    name: '(+02:00) Europe/Athens',
    id: 'Europe/Athens'
  },
  {
    name: '(+02:00) Africa/Cairo',
    id: 'Africa/Cairo'
  },
  {
    name: '(+02:00) Libya',
    id: 'Libya'
  },
  {
    name: '(+02:00) Asia/Istanbul',
    id: 'Asia/Istanbul'
  },
  {
    name: '(+02:00) Africa/Mbabane',
    id: 'Africa/Mbabane'
  },
  {
    name: '(+02:00) Etc/GMT-2',
    id: 'Etc/GMT-2'
  },
  {
    name: '(+02:00) Africa/Juba',
    id: 'Africa/Juba'
  },
  {
    name: '(+02:00) Asia/Tel_Aviv',
    id: 'Asia/Tel_Aviv'
  },
  {
    name: '(+02:00) Africa/Kigali',
    id: 'Africa/Kigali'
  },
  {
    name: '(+02:00) Africa/Harare',
    id: 'Africa/Harare'
  },
  {
    name: '(+02:00) Africa/Lubumbashi',
    id: 'Africa/Lubumbashi'
  },
  {
    name: '(+02:00) Asia/Nicosia',
    id: 'Asia/Nicosia'
  },
  {
    name: '(+03:00) Europe/Moscow',
    id: 'Europe/Moscow'
  },
  {
    name: '(+03:00) Europe/Vilnius',
    id: 'Europe/Vilnius'
  },
  {
    name: '(+03:00) Asia/Kuwait',
    id: 'Asia/Kuwait'
  },
  {
    name: '(+03:00) Africa/Kampala',
    id: 'Africa/Kampala'
  },
  {
    name: '(+03:00) Europe/Chisinau',
    id: 'Europe/Chisinau'
  },
  {
    name: '(+03:00) Europe/Kaliningrad',
    id: 'Europe/Kaliningrad'
  },
  {
    name: '(+03:00) Europe/Riga',
    id: 'Europe/Riga'
  },
  {
    name: '(+03:00) Europe/Zaporozhye',
    id: 'Europe/Zaporozhye'
  },
  {
    name: '(+03:00) Africa/Nairobi',
    id: 'Africa/Nairobi'
  },
  {
    name: '(+03:00) Indian/Mayotte',
    id: 'Indian/Mayotte'
  },
  {
    name: '(+03:00) Asia/Aden',
    id: 'Asia/Aden'
  },
  {
    name: '(+03:00) Europe/Simferopol',
    id: 'Europe/Simferopol'
  },
  {
    name: '(+03:00) Africa/Addis_Ababa',
    id: 'Africa/Addis_Ababa'
  },
  {
    name: '(+03:00) Africa/Asmara',
    id: 'Africa/Asmara'
  },
  {
    name: '(+03:00) Asia/Baghdad',
    id: 'Asia/Baghdad'
  },
  {
    name: '(+03:00) Asia/Riyadh',
    id: 'Asia/Riyadh'
  },
  {
    name: '(+03:00) W-SU',
    id: 'W-SU'
  },
  {
    name: '(+03:00) Antarctica/Syowa',
    id: 'Antarctica/Syowa'
  },
  {
    name: '(+03:00) Europe/Uzhgorod',
    id: 'Europe/Uzhgorod'
  },
  {
    name: '(+03:00) Etc/GMT-3',
    id: 'Etc/GMT-3'
  },
  {
    name: '(+03:00) Africa/Mogadishu',
    id: 'Africa/Mogadishu'
  },
  {
    name: '(+03:00) Indian/Comoro',
    id: 'Indian/Comoro'
  },
  {
    name: '(+03:00) Indian/Antananarivo',
    id: 'Indian/Antananarivo'
  },
  {
    name: '(+03:00) Europe/Kiev',
    id: 'Europe/Kiev'
  },
  {
    name: '(+03:00) Africa/Djibouti',
    id: 'Africa/Djibouti'
  },
  {
    name: '(+03:00) Africa/Asmera',
    id: 'Africa/Asmera'
  },
  {
    name: '(+03:00) Africa/Dar_es_Salaam',
    id: 'Africa/Dar_es_Salaam'
  },
  {
    name: '(+03:00) Europe/Tallinn',
    id: 'Europe/Tallinn'
  },
  {
    name: '(+03:00) Europe/Minsk',
    id: 'Europe/Minsk'
  },
  {
    name: '(+03:00) Europe/Tiraspol',
    id: 'Europe/Tiraspol'
  },
  {
    name: '(+03:30) Asia/Tehran',
    id: 'Asia/Tehran'
  },
  {
    name: '(+03:30) Iran',
    id: 'Iran'
  },
  {
    name: '(+04:00) Asia/Tbilisi',
    id: 'Asia/Tbilisi'
  },
  {
    name: '(+04:00) Europe/Kirov',
    id: 'Europe/Kirov'
  },
  {
    name: '(+04:00) Europe/Ulyanovsk',
    id: 'Europe/Ulyanovsk'
  },
  {
    name: '(+04:00) Indian/Mahe',
    id: 'Indian/Mahe'
  },
  {
    name: '(+04:00) Asia/Qatar',
    id: 'Asia/Qatar'
  },
  {
    name: '(+04:00) Asia/Yerevan',
    id: 'Asia/Yerevan'
  },
  {
    name: '(+04:00) Asia/Dubai',
    id: 'Asia/Dubai'
  },
  {
    name: '(+04:00) Asia/Baku',
    id: 'Asia/Baku'
  },
  {
    name: '(+04:00) Europe/Astrakhan',
    id: 'Europe/Astrakhan'
  },
  {
    name: '(+04:00) Indian/Mauritius',
    id: 'Indian/Mauritius'
  },
  {
    name: '(+04:00) Indian/Reunion',
    id: 'Indian/Reunion'
  },
  {
    name: '(+04:00) Etc/GMT-4',
    id: 'Etc/GMT-4'
  },
  {
    name: '(+04:00) Asia/Bahrain',
    id: 'Asia/Bahrain'
  },
  {
    name: '(+04:00) Asia/Muscat',
    id: 'Asia/Muscat'
  },
  {
    name: '(+04:00) Europe/Volgograd',
    id: 'Europe/Volgograd'
  },
  {
    name: '(+04:00) Europe/Samara',
    id: 'Europe/Samara'
  },
  {
    name: '(+04:30) Asia/Kabul',
    id: 'Asia/Kabul'
  },
  {
    name: '(+05:00) Etc/GMT-5',
    id: 'Etc/GMT-5'
  },
  {
    name: '(+05:00) Indian/Kerguelen',
    id: 'Indian/Kerguelen'
  },
  {
    name: '(+05:00) Asia/Oral',
    id: 'Asia/Oral'
  },
  {
    name: '(+05:00) Asia/Aqtau',
    id: 'Asia/Aqtau'
  },
  {
    name: '(+05:00) Asia/Qyzylorda',
    id: 'Asia/Qyzylorda'
  },
  {
    name: '(+05:00) Asia/Samarkand',
    id: 'Asia/Samarkand'
  },
  {
    name: '(+05:00) Asia/Ashgabat',
    id: 'Asia/Ashgabat'
  },
  {
    name: '(+05:00) Indian/Chagos',
    id: 'Indian/Chagos'
  },
  {
    name: '(+05:00) Indian/Maldives',
    id: 'Indian/Maldives'
  },
  {
    name: '(+05:00) Asia/Aqtobe',
    id: 'Asia/Aqtobe'
  },
  {
    name: '(+05:00) Asia/Karachi',
    id: 'Asia/Karachi'
  },
  {
    name: '(+05:00) Asia/Ashkhabad',
    id: 'Asia/Ashkhabad'
  },
  {
    name: '(+05:00) Asia/Yekaterinburg',
    id: 'Asia/Yekaterinburg'
  },
  {
    name: '(+05:30) Asia/Thimbu',
    id: 'Asia/Thimbu'
  },
  {
    name: '(+05:30) Asia/Kathmandu',
    id: 'Asia/Kathmandu'
  },
  {
    name: '(+05:30) Asia/Kolkata',
    id: 'Asia/Kolkata'
  },
  {
    name: '(+05:30) Asia/Colombo',
    id: 'Asia/Colombo'
  },
  {
    name: '(+05:30) Asia/Calcutta',
    id: 'Asia/Calcutta'
  },
  {
    name: '(+05:30) Asia/Thimphu',
    id: 'Asia/Thimphu'
  },
  {
    name: '(+05:30) Asia/Katmandu',
    id: 'Asia/Katmandu'
  },
  {
    name: '(+06:00) Asia/Dushanbe',
    id: 'Asia/Dushanbe'
  },
  {
    name: '(+06:00) Antarctica/Mawson',
    id: 'Antarctica/Mawson'
  },
  {
    name: '(+06:00) Asia/Dacca',
    id: 'Asia/Dacca'
  },
  {
    name: '(+06:00) Asia/Kashgar',
    id: 'Asia/Kashgar'
  },
  {
    name: '(+06:00) Asia/Bishkek',
    id: 'Asia/Bishkek'
  },
  {
    name: '(+06:00) Etc/GMT-6',
    id: 'Etc/GMT-6'
  },
  {
    name: '(+06:00) Asia/Almaty',
    id: 'Asia/Almaty'
  },
  {
    name: '(+06:00) Asia/Urumqi',
    id: 'Asia/Urumqi'
  },
  {
    name: '(+06:00) Asia/Dhaka',
    id: 'Asia/Dhaka'
  },
  {
    name: '(+06:00) Asia/Hovd',
    id: 'Asia/Hovd'
  },
  {
    name: '(+06:00) Asia/Tashkent',
    id: 'Asia/Tashkent'
  },
  {
    name: '(+06:00) Asia/Omsk',
    id: 'Asia/Omsk'
  },
  {
    name: '(+06:00) Antarctica/Vostok',
    id: 'Antarctica/Vostok'
  },
  {
    name: '(+06:30) Asia/Yangon',
    id: 'Asia/Yangon'
  },
  {
    name: '(+06:30) Indian/Cocos',
    id: 'Indian/Cocos'
  },
  {
    name: '(+06:30) Asia/Rangoon',
    id: 'Asia/Rangoon'
  },
  {
    name: '(+07:00) Antarctica/Davis',
    id: 'Antarctica/Davis'
  },
  {
    name: '(+07:00) Etc/GMT-7',
    id: 'Etc/GMT-7'
  },
  {
    name: '(+07:00) Asia/Barnaul',
    id: 'Asia/Barnaul'
  },
  {
    name: '(+07:00) Asia/Bangkok',
    id: 'Asia/Bangkok'
  },
  {
    name: '(+07:00) Asia/Phnom_Penh',
    id: 'Asia/Phnom_Penh'
  },
  {
    name: '(+07:00) Asia/Jakarta',
    id: 'Asia/Jakarta'
  },
  {
    name: '(+07:00) Indian/Christmas',
    id: 'Indian/Christmas'
  },
  {
    name: '(+07:00) Asia/Tomsk',
    id: 'Asia/Tomsk'
  },
  {
    name: '(+07:00) Asia/Vientiane',
    id: 'Asia/Vientiane'
  },
  {
    name: '(+07:00) Asia/Krasnoyarsk',
    id: 'Asia/Krasnoyarsk'
  },
  {
    name: '(+07:00) Asia/Ulaanbaatar',
    id: 'Asia/Ulaanbaatar'
  },
  {
    name: '(+07:00) Asia/Novokuznetsk',
    id: 'Asia/Novokuznetsk'
  },
  {
    name: '(+07:00) Asia/Ulan_Bator',
    id: 'Asia/Ulan_Bator'
  },
  {
    name: '(+07:00) Asia/Choibalsan',
    id: 'Asia/Choibalsan'
  },
  {
    name: '(+07:00) Asia/Novosibirsk',
    id: 'Asia/Novosibirsk'
  },
  {
    name: '(+07:30) Asia/Singapore',
    id: 'Asia/Singapore'
  },
  {
    name: '(+07:30) Singapore',
    id: 'Singapore'
  },
  {
    name: '(+07:30) Asia/Kuala_Lumpur',
    id: 'Asia/Kuala_Lumpur'
  },
  {
    name: '(+08:00) Asia/Macau',
    id: 'Asia/Macau'
  },
  {
    name: '(+08:00) Australia/West',
    id: 'Australia/West'
  },
  {
    name: '(+08:00) Hongkong',
    id: 'Hongkong'
  },
  {
    name: '(+08:00) Asia/Taipei',
    id: 'Asia/Taipei'
  },
  {
    name: '(+08:00) ROC',
    id: 'ROC'
  },
  {
    name: '(+08:00) Asia/Chongqing',
    id: 'Asia/Chongqing'
  },
  {
    name: '(+08:00) Asia/Hong_Kong',
    id: 'Asia/Hong_Kong'
  },
  {
    name: '(+08:00) Australia/Perth',
    id: 'Australia/Perth'
  },
  {
    name: '(+08:00) Asia/Harbin',
    id: 'Asia/Harbin'
  },
  {
    name: '(+08:00) Asia/Irkutsk',
    id: 'Asia/Irkutsk'
  },
  {
    name: '(+08:00) Asia/Brunei',
    id: 'Asia/Brunei'
  },
  {
    name: '(+08:00) Asia/Pontianak',
    id: 'Asia/Pontianak'
  },
  {
    name: '(+08:00) Asia/Ujung_Pandang',
    id: 'Asia/Ujung_Pandang'
  },
  {
    name: '(+08:00) Antarctica/Casey',
    id: 'Antarctica/Casey'
  },
  {
    name: '(+08:00) Asia/Macao',
    id: 'Asia/Macao'
  },
  {
    name: '(+08:00) Asia/Shanghai',
    id: 'Asia/Shanghai'
  },
  {
    name: '(+08:00) Asia/Makassar',
    id: 'Asia/Makassar'
  },
  {
    name: '(+08:00) Asia/Saigon',
    id: 'Asia/Saigon'
  },
  {
    name: '(+08:00) Asia/Ho_Chi_Minh',
    id: 'Asia/Ho_Chi_Minh'
  },
  {
    name: '(+08:00) Asia/Manila',
    id: 'Asia/Manila'
  },
  {
    name: '(+08:00) Asia/Kuching',
    id: 'Asia/Kuching'
  },
  {
    name: '(+08:00) PRC',
    id: 'PRC'
  },
  {
    name: '(+08:00) Etc/GMT-8',
    id: 'Etc/GMT-8'
  },
  {
    name: '(+08:00) Asia/Chungking',
    id: 'Asia/Chungking'
  },
  {
    name: '(+08:45) Australia/Eucla',
    id: 'Australia/Eucla'
  },
  {
    name: '(+09:00) Etc/GMT-9',
    id: 'Etc/GMT-9'
  },
  {
    name: '(+09:00) Asia/Khandyga',
    id: 'Asia/Khandyga'
  },
  {
    name: '(+09:00) ROK',
    id: 'ROK'
  },
  {
    name: '(+09:00) Asia/Chita',
    id: 'Asia/Chita'
  },
  {
    name: '(+09:00) Asia/Ust-Nera',
    id: 'Asia/Ust-Nera'
  },
  {
    name: '(+09:00) Pacific/Palau',
    id: 'Pacific/Palau'
  },
  {
    name: '(+09:00) Japan',
    id: 'Japan'
  },
  {
    name: '(+09:00) Asia/Pyongyang',
    id: 'Asia/Pyongyang'
  },
  {
    name: '(+09:00) Asia/Tokyo',
    id: 'Asia/Tokyo'
  },
  {
    name: '(+09:00) Asia/Seoul',
    id: 'Asia/Seoul'
  },
  {
    name: '(+09:00) Asia/Yakutsk',
    id: 'Asia/Yakutsk'
  },
  {
    name: '(+09:00) Asia/Dili',
    id: 'Asia/Dili'
  },
  {
    name: '(+09:00) Asia/Jayapura',
    id: 'Asia/Jayapura'
  },
  {
    name: '(+09:30) Australia/South',
    id: 'Australia/South'
  },
  {
    name: '(+09:30) Australia/Broken_Hill',
    id: 'Australia/Broken_Hill'
  },
  {
    name: '(+09:30) Australia/Adelanamee',
    id: 'Australia/Adelanamee'
  },
  {
    name: '(+09:30) Australia/Darwin',
    id: 'Australia/Darwin'
  },
  {
    name: '(+09:30) Australia/North',
    id: 'Australia/North'
  },
  {
    name: '(+09:30) Australia/Yancowinna',
    id: 'Australia/Yancowinna'
  },
  {
    name: '(+10:00) Pacific/Bougainville',
    id: 'Pacific/Bougainville'
  },
  {
    name: '(+10:00) Australia/NSW',
    id: 'Australia/NSW'
  },
  {
    name: '(+10:00) Australia/Melbourne',
    id: 'Australia/Melbourne'
  },
  {
    name: '(+10:00) Asia/Vladivostok',
    id: 'Asia/Vladivostok'
  },
  {
    name: '(+10:00) Australia/Lord_Howe',
    id: 'Australia/Lord_Howe'
  },
  {
    name: '(+10:00) Australia/Lindeman',
    id: 'Australia/Lindeman'
  },
  {
    name: '(+10:00) Australia/Brisbane',
    id: 'Australia/Brisbane'
  },
  {
    name: '(+10:00) Pacific/Port_Moresby',
    id: 'Pacific/Port_Moresby'
  },
  {
    name: '(+10:00) Pacific/Yap',
    id: 'Pacific/Yap'
  },
  {
    name: '(+10:00) Pacific/Saipan',
    id: 'Pacific/Saipan'
  },
  {
    name: '(+10:00) Australia/Currie',
    id: 'Australia/Currie'
  },
  {
    name: '(+10:00) Pacific/Chuuk',
    id: 'Pacific/Chuuk'
  },
  {
    name: '(+10:00) Pacific/Guam',
    id: 'Pacific/Guam'
  },
  {
    name: '(+10:00) Australia/LHI',
    id: 'Australia/LHI'
  },
  {
    name: '(+10:00) Pacific/Truk',
    id: 'Pacific/Truk'
  },
  {
    name: '(+10:00) Australia/ACT',
    id: 'Australia/ACT'
  },
  {
    name: '(+10:00) Australia/Victoria',
    id: 'Australia/Victoria'
  },
  {
    name: '(+10:00) Australia/Canberra',
    id: 'Australia/Canberra'
  },
  {
    name: '(+10:00) Etc/GMT-10',
    id: 'Etc/GMT-10'
  },
  {
    name: '(+10:00) Australia/Sydney',
    id: 'Australia/Sydney'
  },
  {
    name: '(+10:00) Australia/Queensland',
    id: 'Australia/Queensland'
  },
  {
    name: '(+10:00) Antarctica/DumontDUrville',
    id: 'Antarctica/DumontDUrville'
  },
  {
    name: '(+11:00) Asia/Sakhalin',
    id: 'Asia/Sakhalin'
  },
  {
    name: '(+11:00) Pacific/Noumea',
    id: 'Pacific/Noumea'
  },
  {
    name: '(+11:00) Australia/Hobart',
    id: 'Australia/Hobart'
  },
  {
    name: '(+11:00) Asia/Magadan',
    id: 'Asia/Magadan'
  },
  {
    name: '(+11:00) Etc/GMT-11',
    id: 'Etc/GMT-11'
  },
  {
    name: '(+11:00) Pacific/Efate',
    id: 'Pacific/Efate'
  },
  {
    name: '(+11:00) Pacific/Ponape',
    id: 'Pacific/Ponape'
  },
  {
    name: '(+11:00) Australia/Tasmania',
    id: 'Australia/Tasmania'
  },
  {
    name: '(+11:00) Antarctica/Macquarie',
    id: 'Antarctica/Macquarie'
  },
  {
    name: '(+11:00) Asia/Srednekolymsk',
    id: 'Asia/Srednekolymsk'
  },
  {
    name: '(+11:00) Pacific/Guadalcanal',
    id: 'Pacific/Guadalcanal'
  },
  {
    name: '(+11:00) Pacific/Pohnpei',
    id: 'Pacific/Pohnpei'
  },
  {
    name: '(+11:30) Pacific/Nauru',
    id: 'Pacific/Nauru'
  },
  {
    name: '(+11:30) Pacific/Norfolk',
    id: 'Pacific/Norfolk'
  },
  {
    name: '(+12:00) Pacific/Fiji',
    id: 'Pacific/Fiji'
  },
  {
    name: '(+12:00) Antarctica/South_Pole',
    id: 'Antarctica/South_Pole'
  },
  {
    name: '(+12:00) Pacific/Tarawa',
    id: 'Pacific/Tarawa'
  },
  {
    name: '(+12:00) Pacific/Auckland',
    id: 'Pacific/Auckland'
  },
  {
    name: '(+12:00) Pacific/Wallis',
    id: 'Pacific/Wallis'
  },
  {
    name: '(+12:00) Pacific/Majuro',
    id: 'Pacific/Majuro'
  },
  {
    name: '(+12:00) Pacific/Wake',
    id: 'Pacific/Wake'
  },
  {
    name: '(+12:00) Etc/GMT-12',
    id: 'Etc/GMT-12'
  },
  {
    name: '(+12:00) Pacific/Kosrae',
    id: 'Pacific/Kosrae'
  },
  {
    name: '(+12:00) NZ',
    id: 'NZ'
  },
  {
    name: '(+12:00) Asia/Kamchatka',
    id: 'Asia/Kamchatka'
  },
  {
    name: '(+12:00) Antarctica/McMurdo',
    id: 'Antarctica/McMurdo'
  },
  {
    name: '(+12:00) Pacific/Funafuti',
    id: 'Pacific/Funafuti'
  },
  {
    name: '(+12:45) NZ-CHAT',
    id: 'NZ-CHAT'
  },
  {
    name: '(+12:45) Pacific/Chatham',
    id: 'Pacific/Chatham'
  },
  {
    name: '(+13:00) Etc/GMT-13',
    id: 'Etc/GMT-13'
  },
  {
    name: '(+13:00) Pacific/Tongatapu',
    id: 'Pacific/Tongatapu'
  },
  {
    name: '(+13:00) Asia/Anadyr',
    id: 'Asia/Anadyr'
  },
  {
    name: '(+14:00) Etc/GMT-14',
    id: 'Etc/GMT-14'
  }
];
