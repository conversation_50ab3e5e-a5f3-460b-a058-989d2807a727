import * as React from 'react';
import {  observer } from 'mobx-react';
import { RouteComponentProps} from 'react-router-dom';
import * as oAuthApi from '../../api/oauth';
import * as queryString from 'query-string';

import { SrSpinner } from '@sr/design-component-lite';
import { redirectTo } from '../../utils/redirection';

interface ILogoutProps extends RouteComponentProps<any> {
}
interface ILogoutStates {
  isLoading: boolean;
}
class LogoutPage extends React.Component<ILogoutProps, ILogoutStates> {
  constructor(props:ILogoutProps){
    super(props);
    this.state = {
      isLoading: true
    }
  }
  componentDidMount(): void {
    
    console.log("Called Component did mount")
    const queryParams = queryString.parse(this.props.location.search)
    const logout_challenge  = queryParams.logout_challenge as string;
    
    oAuthApi
    .handleLogout(logout_challenge)
    .then(res => {
      // const requested_scopes = (res.data.requested_scope as string[]).join(" ")
      if(res.data.redirect_to){
        console.log("redirect_to url")
        console.log(res.data.redirect_to)
        redirectTo(res.data.redirect_to)
      }else{
        //if it fails redirect to login Page
        console.log('failed')
      }
    })
    .catch(e =>{
      console.log("Error Occurred")
      this.setState({isLoading: false})
      console.log(e);
    })
  }
  render() {
   
    
    return (
      <div className="min-h-full flex flex-col py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex items-center mt-2 justify-center">
          {this.state.isLoading && <SrSpinner spinnerTitle="Loading ..." />}
        </div>
      </div>
    );
  }
};
export const Logout = (observer(LogoutPage));    