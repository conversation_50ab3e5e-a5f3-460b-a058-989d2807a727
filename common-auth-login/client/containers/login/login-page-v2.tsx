import * as React from 'react';
import {  observer } from 'mobx-react';

import { RouteComponentProps, Link } from 'react-router-dom';
import * as queryString from 'query-string';
import { CONSTANTS } from '../../data/constants';
// import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import { IEmail, ISignupType } from './register-page-v2';
import { SrSpinner } from '@sr/design-component-lite';
import * as newAuthApi from '../../api/newAuth';
import { LogInWithPassword } from './login-page';
import { OAuthPage } from './oAuthPage';
import { GetEmail } from './get-email-new-auth-flow';
import * as oAuthApi  from '../../api/oauth'
import { redirectTo } from '../../utils/redirection';
import { Helmet } from 'react-helmet';


interface ILogInProps extends RouteComponentProps<any> {

}

interface ILogInStates {
  isLoading?: boolean;
  accountEmail: string;
  signupType?: ISignupType;
  showCaptcha: Boolean;
  passedCaptcha: Boolean;
  g_response?: string;
  showCaptchaError: boolean;
  login_challenge: string;
  isLoadingLoginPage: boolean
}


class LogInPageV2 extends React.Component<ILogInProps, ILogInStates> {

  constructor(props: ILogInProps) {
    super(props);
    this.state = {
      isLoading: false,
      accountEmail: '',
      signupType: undefined,
      passedCaptcha: true,
      showCaptcha: false,
      showCaptchaError: false,
      login_challenge: '',
      isLoadingLoginPage: true
    };
    this.submitLogInForm = this.submitLogInForm.bind(this);
    this.setSignupType = this.setSignupType.bind(this);
    this.setEmail = this.setEmail.bind(this);
    this.setGResponse = this.setGResponse.bind(this);
  }

  componentDidMount() {
    //to reload if you get to login page through back button


    const queryParams = queryString.parse(this.props.location.search)
    const login_challenge = queryParams.login_challenge
    if (login_challenge) {
      this.setState({ login_challenge: login_challenge as string });
      oAuthApi
        .handleLogin(login_challenge as string)
        .then((res) => {
          if (res.data.redirect_to) {
            redirectTo(res.data.redirect_to)
          }else{
            this.setState({isLoadingLoginPage: false })
          }
        })
        .catch((e) => {
          console.log("Error Occurred");
          this.setState({isLoadingLoginPage: false })
          console.log(e);
        });

    }
  }

  setEmail(accountEmail: string) {
    this.setState({ accountEmail: accountEmail })
  }

  setSignupType(signupType: string) {
    if (signupType == 'google') {
      this.setState({ signupType: ISignupType.Google })
    } else if (signupType == 'microsoft') {
      this.setState({ signupType: ISignupType.Microsoft })
    } else {
      this.setState({ signupType: ISignupType.Password })
    }
  }

  setGResponse(g_response: string) {
    this.setState({ g_response: g_response })
  }




  submitLogInForm(data: IEmail, setSubmitting: (isSubmitting: boolean) => void) {
    this.setEmail(data.accountEmail);

    const req = {
      accountEmail: data.accountEmail,
      g_response: this.state.g_response
    }
    if (this.state.showCaptcha && this.state.g_response == undefined) {
      setSubmitting(false);
      this.setState({ showCaptchaError: true })
    } else {
      newAuthApi.checkLoginPath(req)
        .then((res: any) => {
          setSubmitting(false);
          const signupType = res.data.signupType;
          const showCaptcha = res.data.showCaptcha;
          const passedCaptcha = res.data.passedCaptcha;
          this.setState({ showCaptcha: showCaptcha, passedCaptcha: passedCaptcha })
          this.setSignupType(signupType)


        })
        .catch((errResponse: any) => {
          this.setState({ accountEmail: "" })
          setSubmitting(false);
        });
    }
  }

  render() {
    const isLoading = this.state.isLoading;
    const isLoadingLoginPage = this.state.isLoadingLoginPage
    const signupType = this.state.signupType;

    return (
      <>
      <Helmet>
        <title>SmartReach.io Login | Your Cold Outreach Dashboard</title>
        <meta name="description" content="Log in to your SmartReach.io account to manage cold outreach campaigns, track leads, and boost sales." />
      </Helmet>

        {isLoadingLoginPage &&
          (
          <div className="min-h-screen flex justify-center items-center py-12 px-4 sm:px-6 lg:px-8">
            < SrSpinner />
          </div>
          )
        }
        { !isLoadingLoginPage &&
        <>
        {isLoading &&
           <SrSpinner spinnerTitle='loading ..' />

        }

        <div className={"min-h-full flex flex-col py-12 px-4 sm:px-6 lg:px-8"}>
          <div className="sm:mx-auto sm:w-full sm:max-w-md">
            <a className="flex items-center justify-center" href='https://smartreach.io' target='_blank'>
              <img
                className="h-12"
                src={CONSTANTS.CDN_URL + "/assets/SmartreachLogo.svg"}
                alt="SmartReach.io Logo"
              />
              <span className='pl-4 font-muli text-lg text-black'>SmartReach</span>
            </a>
          </div>

          {!isLoading && (!signupType || this.state.showCaptcha) &&
            <GetEmail submitForm={this.submitLogInForm} setEmail={this.setEmail} authType="Sign in" showCaptcha={this.state.showCaptcha} setGResponse={this.setGResponse} showCaptchaError={this.state.showCaptchaError} />
          }

          {!isLoading && (signupType == ISignupType.Google || signupType == ISignupType.Microsoft) && this.state.passedCaptcha &&
            <OAuthPage login_challenge={this.state.login_challenge} accountEmail={this.state.accountEmail} signupType={signupType} setSignupType={this.setSignupType} authType='Sign in' />
          }

          {!isLoading && (signupType == ISignupType.Password) && this.state.passedCaptcha &&
            <LogInWithPassword login_challenge= {this.state.login_challenge} accountEmail={this.state.accountEmail} showCaptcha={this.state.showCaptcha} history={this.props.history} location={this.props.location} match={this.props.match} />
          }

          <div className="sm:mx-auto sm:w-full sm:max-w-md ">
            <div className='py-2 flex items-center justify-center'>
              <div className='sr-p-basic'>Don't have an account? <Link className='text-sr-primary-80 sr-h6' to={`/login?type=register&login_challenge=${this.state.login_challenge}`}>Sign Up Now</Link></div>
            </div>
          </div>
        </div>
        </>
      }


      </>
    );
  }
};


export const LogInV2 = (observer(LogInPageV2));
