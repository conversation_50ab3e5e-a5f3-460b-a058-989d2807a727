import * as React from 'react';
import {  observer } from 'mobx-react';
import { RouteComponentProps} from 'react-router-dom';
import * as oAuthApi from '../../api/oauth';
import * as queryString from 'query-string';
import  isEqual  from 'lodash-es/isEqual';
import { SRButtonFilled, SRButtonOutline, SRFormCheckboxGroup, SrPageCenter } from '@sr/design-component-lite';
import { Formik, Form, FormikProps } from 'formik';
import { SrSpinner } from '@sr/design-component-lite';
import { CONSTANTS } from '../../data/constants';
import { setClientRedirectUri } from '../../utils/localStorage';
import { redirectTo } from '../../utils/redirection';
interface IConsentProps extends RouteComponentProps<any> {
 
}
interface IConsentStates {
  consent_challenge ?: string;
  isLoading: boolean;
  logo_uri ?: string;
  client_name ?: string;
  requested_scope ?: string[];
  client_uri ?: string;
  
}
interface IFormikValues {
  scopes: string[],
  denyClicked: boolean
}
class ConsentPage extends React.Component<IConsentProps, IConsentStates> {
  constructor(props:IConsentProps){
    super(props);
    this.state = {
      isLoading: true
    }
  }
  componentDidMount(): void {
    
    console.log("Called Component did mount")
    const queryParams = queryString.parse(this.props.location.search)
    const consent_challenge  = queryParams.consent_challenge as string;
    
    oAuthApi
    .handleConsent(consent_challenge)
    .then(res => {
       
      console.log("Client redirect uri")
      console.log(res.data)
      console.log(res.data.client_uri)
      setClientRedirectUri(res.data.client_uri!)
      console.log("Redirect to",res.data.redirect_to)
      this.setState({
        client_uri: res.data.client_uri,
        client_name: res.data.client_name,
        requested_scope:res.data.requested_scope,
        logo_uri: res.data.logo_uri,
        consent_challenge: consent_challenge

      },()=>{
        if(res.data.redirect_to){
          redirectTo(res.data.redirect_to)
        }else{
          this.setState({isLoading:false })
        }
      })
      // const requested_scopes = (res.data.requested_scope as string[]).join(" ")
    })
    .catch(e =>{
      console.log("Error Occurred")
      this.setState({isLoading: false});
      console.log(e);
    })
  }
  handleConsent(allow: boolean){

    if(allow){
      oAuthApi
      .acceptConsentRequest(
        true,
        ["profile","email","offline_access"],
        this.state.consent_challenge
      ).then(res => {
        redirectTo(res.data.redirect_to)
      })
      .catch(e =>{
        console.log("Error occurred")
        console.log(e)
      })
    } else {
      oAuthApi
      .rejectConsentRequest(
        'request_denied',
        400,
        'Application was denied to the requested resources',
        this.state.consent_challenge
      ).then(res => {
        redirectTo(res.data.redirect_to)
      })
    }
  }
  render() {
    const smartreachLogo = `${CONSTANTS.CDN_URL}/assets/SmartreachLogo.svg`
    
    return (
    <>
    {this.state.isLoading &&
      <SrPageCenter>
        < SrSpinner />
      </SrPageCenter>
     
    }
    <div className='min-h-full flex flex-col py-12 px-4 sm:px-6 lg:px-8'>
      
      { !this.state.isLoading && <div className='flex text-md items-center justify-center'>
        <div className='rounded-lg  p-3'>
          <div className='flex items-center justify-center mb-5'>
            {<img src={smartreachLogo}  height={40} width={40} />}
            <span className="pl-4 font-muli text-lg text-black">SmartReach</span>
          </div>
          <div className='flex items-center justify-center'>
           <h1 className=' my-h2 sr-h2 mr-3'> Give consent to  {this.state.client_name ? this.state.client_name : 'application'}</h1>
           <span>{this.state.logo_uri ?<img src={this.state.logo_uri} height={40} width={40} /> : <></>}</span>
          </div>
        
          
          <div>
            <Formik
              initialValues={{
                scopes: this.state.requested_scope,
                denyClicked: false
              }}
              onSubmit={(values:IFormikValues)=>{
                console.log(values)
                console.log(typeof values)
                console.log("allowed scopes");
                console.log(values.scopes);
                if(isEqual(values.scopes, this.state.requested_scope) && !values.denyClicked){
                  this.handleConsent(true);
                } else{
                  this.handleConsent(false);
                }
              }}
            >
               {(props: FormikProps<any>) => (
               <Form>
                <div className='text-sr-subtext-grey text-md py-4 font-extralight'>{this.state.client_name ? this.state.client_name : 'application'} wants to access the following items from your  SmartReach <br/> Account </div>
                <SRFormCheckboxGroup
                  groupName='scopes'
                  options={ this.state.requested_scope!.map(item => {
                    return {
                      name: item,
                      displayText: item == 'profile' ? 'User Profile' : item
                    }
                  })}
                />
              <div className='flex w-full'>
                <div className='mx-1 flex-1'>
                  <SRButtonOutline
                    text='Deny'
                    type='submit'
                    disable ={props.isSubmitting}
                    onClick={() =>{props.setFieldValue('denyClicked',true)}}
                    width='fluid'
                  />
                </div>
                <div className='mx-1 flex-1'>
                  <SRButtonFilled
                    text='Allow'
                    type='submit'
                    disable ={props.isSubmitting}
                    onClick={() => {props.setFieldValue('denyClicked',false)}}
                    isPrimary
                    width='fluid'
                  />
              </div>
          
            </div>
                
               </Form>
               
               )
               }
              
            </Formik>
          </div>
        </div>
      </div>}
    </div>
    </>
    );
  }
};
export const Consent = (observer(ConsentPage));