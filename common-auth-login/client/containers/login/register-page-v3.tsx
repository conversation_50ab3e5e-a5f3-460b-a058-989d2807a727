import * as React from 'react';
import { observer } from 'mobx-react';
import { RouteComponentProps } from 'react-router-dom';
import { SRIconCircleTick<PERSON>ightGreen, SRIconTickOctagon, SrSpinner } from '@sr/design-component-lite';
import { OAuthPage } from './oAuthPage';
// import { password } from './password-signup';
import { RegisterWithPassword } from './register-page';
import { GetEmail } from './get-email-new-auth-flow';
import * as queryString from 'query-string';
import * as newAuthApi from '../../api/newAuth';
import * as authApi from '../../api/auth';
import { CONSTANTS } from '../../data/constants';
import { getClientRedirectUri } from '../../utils/localStorage';
import * as oAuthApi from '../../api/oauth'
import { ENV_CONSTANTS } from '../../data/env_constants';
import { redirectTo } from '../../utils/redirection';
import { CarouselComponent1 } from '../../components/review-stories';
import { Helmet } from 'react-helmet';



export enum ISignupType {
  Google = 'google',
  Microsoft = 'microsoft',
  Password = 'password'
}

interface IRegisterV3Props extends RouteComponentProps<any> {
}

interface IRegisterV3States {
  isLoading?: boolean;
  accountEmail: string;
  signupType?: ISignupType;
  inviteData?: LogIn.IInvitedMemberDetail
  showCaptcha: Boolean;
  passedCaptcha: Boolean;
  g_response?: string;
  showCaptchaError: boolean;
  login_challenge: string;

}

export interface IEmail {
  accountEmail: string
}

class RegisterPageV3 extends React.Component<IRegisterV3Props, IRegisterV3States> {

  constructor(props: IRegisterV3Props) {
    super(props);
    this.state = {
      isLoading: false,
      accountEmail: '',
      signupType: undefined,
      passedCaptcha: true,
      showCaptcha: false,
      showCaptchaError: false,
      login_challenge: ''

    };
    this.setSignupType = this.setSignupType.bind(this);
    this.setEmail = this.setEmail.bind(this);
    this.submitForm = this.submitForm.bind(this);
    this.fetchAndSetSignupType = this.fetchAndSetSignupType.bind(this);
    this.fetchInvitedUserData = this.fetchInvitedUserData.bind(this);
    this.setGResponse = this.setGResponse.bind(this);
  }

  setEmail(accountEmail: string) {
    this.setState({ accountEmail: accountEmail })
  }

  setGResponse(g_response: string) {
    this.setState({ g_response: g_response, showCaptchaError: false })
  }

  setSignupType(signupType: string) {
    if (signupType == 'google') {
      this.setState({ signupType: ISignupType.Google })
    } else if (signupType == 'microsoft') {
      this.setState({ signupType: ISignupType.Microsoft })
    } else {
      this.setState({ signupType: ISignupType.Password })
    }
  }

  fetchAndSetSignupType(data: IEmail, isInvited?: boolean) {//akhilesh edit - to check by animesh
    const req = {
      accountEmail: data.accountEmail,
      g_response: this.state.g_response
    }
    return newAuthApi.checkPath(req)
      .then((res: any) => {
        const showCaptcha = isInvited ? false : res.data.showCaptcha;//akhilesh edit - to check by animesh
        const passedCaptcha = isInvited ? true : res.data.passedCaptcha;//akhilesh edit - to check by animesh
        this.setState({ showCaptcha: showCaptcha, passedCaptcha: passedCaptcha })
        this.setSignupType(res.data.signupType as string)
      }).catch(() => {
        this.setState({ accountEmail: '' })
      })

  }

  submitForm(data: IEmail, setSubmitting: (isSubmitting: boolean) => void) {
    this.setEmail(data.accountEmail)
    if (this.state.showCaptcha && this.state.g_response == undefined) {
      setSubmitting(false);
      this.setState({ showCaptchaError: true })
    } else {
      this.fetchAndSetSignupType(data)
        .then((res) => {
          setSubmitting(false);
        })
        .catch((errResponse) => {
          this.setState({ accountEmail: "" })
          setSubmitting(false);
        });
    }
  }

  fetchInvitedUserData(inviteCode: string): Promise<void> {

    return authApi.getEmailfromInviteCode(inviteCode)
      .then((response) => {
        this.setState({
          accountEmail: response.data.email,
          inviteData: response.data,
        });
      })
  }

  componentDidMount() {

    const query = queryString.parse(this.props.location.search);
    const inviteCode = query.invite_code as (string | null);
    const login_challenge = query.login_challenge

    this.setState({ isLoading: true }, () => {
      oAuthApi
        .checkIfLoggedIn()
        .then(resp => {
          if (resp.data.is_logged_in) {
            redirectTo(ENV_CONSTANTS.APP_URL)
          }
          this.setState({ isLoading: false });
        }).catch(e => {
          console.log(`Error Occurred :: ${e}`)
          this.setState({ isLoading: false });

        })
    })

    if (login_challenge) {
      this.setState({ login_challenge: login_challenge as string })
    }

    if (inviteCode) {

      this.setState({ isLoading: true });

      this.fetchInvitedUserData(inviteCode)
        .then(res => {

          const data: IEmail = {
            accountEmail: this.state.inviteData!.email
          };

          return this.fetchAndSetSignupType(data, true);//akhilesh edit - to check by animesh

        })
        .then(_ => {
          this.setState({ isLoading: false })
        })
        .catch(_ => {
          this.setState({ isLoading: false })
        });

    }
  }


  render() {
    const isLoading = this.state.isLoading;
    const signupType = this.state.signupType;
    const query = queryString.parse(this.props.location.search);
    const inviteCode = query.invite_code as string;

    const clientUri = getClientRedirectUri()
    const loginRedirect = clientUri ?
      <a href={clientUri} className='text-blue-1'>Log in </a> :
      <a href={'https://app.smartreach.io'} className='text-blue-1'>Log in </a>;


    return (
      <>
      <Helmet>
        <title> SmartReach.io Register | Free Cold Outreach Tool</title>
        <meta name="description" content="Sign up for a free SmartReach.io account and scale your cold outreach efforts. Find, engage, and convert leads effortlessly." />
      </Helmet>
      <div className='register-page h-screen'>


        {isLoading &&
          <div className="min-h-screen flex justify-center items-center py-12 px-4 sm:px-6 lg:px-8">
            < SrSpinner />
          </div>
        }

        {!isLoading &&
          <div className="font-sourcesanspro h-[inherit] flex flex-col">
            <div className={"flex h-full w-full" + (!inviteCode ? ' md:grid md:grid-cols-2' : ' justify-center')}>

              {/*fdiv containing inputs*/}
              <div className=" mb-auto h-fit flex flex-col  items-center justify-center">

              <div className='mt-[52px] ml-[48px]' style={{ marginTop: '52px', marginLeft: '48px' }}>
                    <a className="flex" href='https://smartreach.io' target='_blank'>
                      <img
                        className="w-[30px] h-[30px]"
                        src={CONSTANTS.CDN_URL + "/assets/SmartreachLogo.svg"}
                        alt="SmartReach.io Logo"
                      />
                      <span className='font-muli  px-2 text-xl text-sr-grey-primary italic' style={{ paddingTop: '4px' }}>SmartReach</span>
                    </a>
                  </div>

              {(!signupType || this.state.showCaptcha) &&
                <GetEmail submitForm={this.submitForm} setEmail={this.setEmail} authType="Sign up" showCaptcha={this.state.showCaptcha} setGResponse={this.setGResponse} showCaptchaError={this.state.showCaptchaError} accountEmail={this.state.accountEmail} />
              }
              {(signupType == ISignupType.Google || signupType == ISignupType.Microsoft) && this.state.passedCaptcha &&
                <OAuthPage login_challenge={this.state.login_challenge} accountEmail={this.state.accountEmail} signupType={signupType} setSignupType={this.setSignupType} authType="Sign up" invite_code={inviteCode}
                  inviteDetail={this.state.inviteData}
                />
              }
              {signupType == ISignupType.Password && this.state.passedCaptcha &&
                <RegisterWithPassword prefilledEmail={this.state.accountEmail} isNewAuthFlow={true} invite_code={inviteCode} />
                // password(this.state.accountEmail)
              }

              <div className='text-sm text-center'>
                <p className='pb-6'>Already have an account? {loginRedirect}</p>
                <div className='flex justify-center pb-6'><SRIconTickOctagon/> <span className='font-noto text-[14px]'>On-call onboarding assistance for cold email setup</span></div>
                  <p className='pb-2'>By signing up, you agree to our
                    <br />
                  <a className='text-sr-subtext-grey hover:underline' target='_blank' href={CONSTANTS.HOME_URL + '/terms-and-conditions'}> Terms</a>,
                  <a className='text-sr-subtext-grey hover:underline' target='_blank' href={CONSTANTS.HOME_URL + '/privacy-policy'}> Privacy Policy</a>,
                  <a className='text-sr-subtext-grey hover:underline' target='_blank' href={CONSTANTS.HOME_URL + '/usage-and-anti-spam-policy'}> Usage and Anti-Spam Policy</a>.
                </p>
              </div>
              </div>

              {/*left part for advertisement*/}
              {!inviteCode &&
              <div className='h-full hidden md:flex md:flex-col'>
                <div className='bg-sr-blue-20 h-full flex flex-col items-center'>
                  {/* <div className='mt-[52px] ml-[48px]' style={{ marginTop: '52px', marginLeft: '48px' }}>
                    <a className="flex" href='https://smartreach.io' target='_blank'>
                      <img
                        className="w-[40px] h-[40px]"
                        src={CONSTANTS.CDN_URL + "/assets/SmartreachLogo.svg"}
                        alt="SmartReach.io Logo"
                      />
                      <span className='font-muli  px-2 text-3xl text-white italic' style={{ paddingTop: '4px' }}>SmartReach</span>
                    </a>
                  </div> */}

                  <div className='mt-[100px]'>
                    <div className='text-white text-2xl font-readexpro font-semibold'>With <span className='text-sr-warning-50'>14-day</span> free trial you’ll get</div>
                    <div className='w-[450px] flex justify-between'>
                      <div>
                        <div className='flex gap-2 sr-noto text-white mt-[18px]'>
                          <SRIconCircleTickLightGreen classes='!w-[18px] !h-[18px] text-sr-grey-primary ' />
                          <div>Unlimited inboxes</div>
                        </div>
                        <div className='flex gap-2 sr-noto text-white mt-4'>
                          <SRIconCircleTickLightGreen classes='!w-[18px] !h-[18px] text-sr-grey-primary ' />
                          <div>Access to all features</div>
                        </div>
                        <div className='flex gap-2 sr-noto text-white mt-4'>
                          <SRIconCircleTickLightGreen classes='!w-[18px] !h-[18px] text-sr-grey-primary ' />
                          <div>{"Contact "} <strong>200 </strong> {" prospects"}</div>
                        </div>
                      </div>

                      <div>
                        <div className='flex gap-2 sr-noto text-white mt-4'>
                          <SRIconCircleTickLightGreen classes='!w-[18px] !h-[18px] text-sr-grey-primary ' />
                          <div><strong>$5 </strong> {" Calling credits"}</div>
                        </div>
                        <div className='flex gap-2 sr-noto text-white mt-4'>
                          <SRIconCircleTickLightGreen classes='!w-[18px] !h-[18px] text-sr-grey-primary ' />
                          <div><strong>200 </strong> {" AI & Leadfinder credits"}</div>
                        </div>
                        <div className='flex gap-2 sr-noto text-white mt-4'>
                          <SRIconCircleTickLightGreen classes='!w-[18px] !h-[18px] text-sr-grey-primary ' />
                          <div><strong>24X6 </strong> {" Support"}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* <div className='mt-[48px]'><SalesLeaderStories3 /></div> */}
                  <div className='mt-[48px]'>
                    <CarouselComponent1 />
                  </div>

                  <div className='flex mt-[75px] gap-[18px]'>
                    <img className='m-auto w-24 ' src={CONSTANTS.CDN_URL + '/assets/nov22/amazon_logo_white.png'} alt='amazon' />
                    <img className='m-auto w-24 ' src={CONSTANTS.CDN_URL + '/assets/nov22/Adobe_white.png'} alt='adobe' />
                    <img className='m-auto w-20 h-[32px]' src={CONSTANTS.CDN_URL + '/assets/nov22/ibm_white.png'} alt='ibm' />
                    <img className='m-auto w-24 ' src={CONSTANTS.CDN_URL + '/assets/nov22/verizon_white.png'} alt='verizon' />
                    <img className='m-auto w-24 ' src={CONSTANTS.CDN_URL + '/assets/nov22/deloitte_white.png'} alt='deloitte' />
                  </div>
                </div>
                </div>
              }
              {/* fdiv containing inputs
              <div className="w-[600px] mb-auto h-fit flex flex-col  items-center justify-center border rounded-[20px] p-12 my-[100px]" style={{ boxShadow: '6px 6px 30px 0px rgba(0, 0, 0, 0.25)' }}>



                {(!signupType || this.state.showCaptcha) &&
                  <GetEmail submitForm={this.submitForm} setEmail={this.setEmail} authType="Sign up" showCaptcha={this.state.showCaptcha} setGResponse={this.setGResponse} showCaptchaError={this.state.showCaptchaError} accountEmail={this.state.accountEmail} />
                }
                {(signupType == ISignupType.Google || signupType == ISignupType.Microsoft) && this.state.passedCaptcha &&
                  <OAuthPage login_challenge={this.state.login_challenge} accountEmail={this.state.accountEmail} signupType={signupType} setSignupType={this.setSignupType} authType="Sign up" invite_code={inviteCode}
                    inviteDetail={this.state.inviteData}
                  />
                }
                {signupType == ISignupType.Password && this.state.passedCaptcha &&
                  <RegisterWithPassword prefilledEmail={this.state.accountEmail} isNewAuthFlow={true} invite_code={inviteCode} />
                  // password(this.state.accountEmail)
                }

                <div className='text-sm text-center'>
                  <p className='pb-6'>Already have an account? {loginRedirect}</p>
                  <p className='pb-2'>By signing up, you agree to our
                    <br />
                    <a className='text-sr-subtext-grey hover:underline' target='_blank' href={CONSTANTS.HOME_URL + '/terms-and-conditions'}> Terms</a>,
                    <a className='text-sr-subtext-grey hover:underline' target='_blank' href={CONSTANTS.HOME_URL + '/privacy-policy'}> Privacy Policy</a>,
                    <a className='text-sr-subtext-grey hover:underline' target='_blank' href={CONSTANTS.HOME_URL + '/usage-and-anti-spam-policy'}> Usage and Anti-Spam Policy</a>.
                  </p>
                </div>
              </div> */}
            </div>
          </div>
        }
      </div >
      </>
    )
  }
};





export const RegisterV3 = (observer(RegisterPageV3));
