import * as React from 'react';
import {  observer } from 'mobx-react';
import { RouteComponentProps} from 'react-router-dom';

import { SrSpinner } from '@sr/design-component-lite';
import { getClientRedirectUri } from '../../utils/localStorage';
import { redirectTo } from '../../utils/redirection';
import { ENV_CONSTANTS } from '../../data/env_constants';

interface ILogoutCallbackPageProps extends RouteComponentProps<any> {
}
interface ILogoutCallbackPageStates {
  isLoading: boolean;
}
class LogoutCallbackPage extends React.Component<ILogoutCallbackPageProps, ILogoutCallbackPageStates> {
  constructor(props:ILogoutCallbackPageProps){
    super(props);
    this.state = {
      isLoading: true
    }
  }
  componentDidMount(): void {
    
    const client_redirect_url = getClientRedirectUri()
    redirectTo(client_redirect_url || ENV_CONSTANTS.APP_URL)
  }
  render() {
   
    
    return (
      <div className="min-h-full flex flex-col py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex items-center mt-2 justify-center">
          {this.state.isLoading && <SrSpinner spinnerTitle="Loading ..." />}
        </div>
      </div>
    );
  }
};
export const LogoutCallback = (observer(LogoutCallbackPage));    