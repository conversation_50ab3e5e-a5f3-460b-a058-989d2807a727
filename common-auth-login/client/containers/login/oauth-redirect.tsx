import * as React from 'react';
import { Sr<PERSON><PERSON><PERSON>enter, SrSpinner } from '@sr/design-component-lite';
import * as queryString from 'query-string';
import { RouteComponentProps, withRouter } from 'react-router-dom';
import * as newAuthApi from '../../api/newAuth';
import * as settingsApi from '../../api/settings';
import { ITimezone } from './register-page';
import { getCurrentTimeZone } from '../../utils/timezone';
import { SrServer as server } from '../../api/server';
import {  observer } from 'mobx-react';
import { TwoFactorAuthModal } from '../../components/2fa-prompt-modal';
import { redirectTo } from '../../utils/redirection';

interface IoAuthProps extends RouteComponentProps<any> {

}



interface IoAuthStates {
  timezone: string,
  country_code: string,
  // 2FA related
  show2FAModal: boolean;
  show2FAModalType: 'enable_2fa' | 'verify_2fa';
  two_fa_type: 'sms' | 'gauth';
  account_id?: number;
  verstate?: string;
  state_param ?: string;

}

interface IOAuthCodeData {
  state: string;
  code: string;
  timezone: string;
  country_code: string;
  signupType: string;
}

export class OAuthRedirectMidware extends React.Component<IoAuthProps, IoAuthStates> {

  constructor(props: IoAuthProps) {
    super(props);
    this.state = {
      timezone: "",
      country_code: "US",
      show2FAModal: false,
      show2FAModalType: 'enable_2fa',
      two_fa_type: 'gauth'
    }

    this.sendOAuthCode = this.sendOAuthCode.bind(this);
    this.close2FAModal = this.close2FAModal.bind(this);
  }
  close2FAModal() {
    this.setState({ show2FAModal: false });
  }

  sendOAuthCode(data: IOAuthCodeData) {
    newAuthApi.sendOAuthCode(data)
      .then((response) => {
        const resCode = response.data.code;
        console.log('resCOde')
        console.log(resCode)
        if (resCode === 'enable_2fa') {
          console.log("in if")
          this.setState({
            account_id: response.data.aid,
            show2FAModal: true,
            show2FAModalType: 'enable_2fa',
            verstate: response.data.verstate,
            two_fa_type: response.data.two_fa_type ? response.data.two_fa_type : 'gauth',
          });

        } else if (resCode === 'verify_2fa') {
          console.log("in else if ")

          this.setState({
            account_id: response.data.aid,
            show2FAModal: true,
            show2FAModalType: 'verify_2fa',
            verstate: response.data.verstate,
            two_fa_type: response.data.two_fa_type ? response.data.two_fa_type : 'gauth',
          });

        } else {
          console.log("IN THE ELSE CONDITION")
          setTimeout(() => {
            redirectTo(response.data.redirect_uri)
          }, 1000); // Added set timeout as the toaster was not appearing  as the redirection was too quick 
      
        }

      }).catch((error) => {
        console.log("Error occurred in oauth-redirect")
        this.props.history.push({
          pathname: '/register'
        });
      })
  }

  componentDidMount() {
    console.log("Inside component did mount")
    const query = queryString.parse(this.props.location.search);
    server.getLocation().then((resp) => {
      this.setState({ country_code: resp.country || 'US' }, () => {

        console.log("Query state is ")
        console.log(query.state)
        settingsApi.getTimeZone()
          .then((res) => {
            let timezonesFind: ITimezone[] = [];
            res.data.timezones.forEach((zone) => {
              timezonesFind.push({ name: zone.name, id: zone.value });
            });
            this.setState({ timezone: getCurrentTimeZone(timezonesFind || [] as ITimezone[]) , state_param: query.state as string || undefined })

            if (query.state && query.code) {
              const params = this.props.match.params;
              const signupType = params.signupType as string
              const data = {
                state: query.state as string,
                code: query.code as string,
                timezone: this.state.timezone,
                country_code: this.state.country_code,
                signupType: signupType
              }
              this.sendOAuthCode(data);
            } else if (query.error && query.state) {

              this.props.history.push({
                pathname: '/register'
              });
            } else {
              this.props.history.push({
                pathname: '/register'
              });
            }

          })
          .catch(() => {

            this.setState({ timezone: "" });

            if (query.state && query.code) {
              const params = this.props.match.params;
              const signupType = params.signupType as string
              const data = {
                state: query.state as string,
                code: query.code as string,
                timezone: this.state.timezone,
                country_code: this.state.country_code,
                signupType: signupType
              }
              this.sendOAuthCode(data);
            } else if (query.error && query.state) {

              this.props.history.push({
                pathname: '/register'
              });
            } else {
              this.props.history.push({
                pathname: '/register'
              });
            }
          });
      })

    }).catch(() => {
      this.setState({ country_code: 'US' })
      settingsApi.getTimeZone()
        .then((res) => {
          let timezonesFind: ITimezone[] = [];
          res.data.timezones.forEach((zone) => {
            timezonesFind.push({ name: zone.name, id: zone.value });
          });
          this.setState({ timezone: getCurrentTimeZone(timezonesFind || [] as ITimezone[]) })

          if (query.state && query.code) {
            const params = this.props.match.params;
            const signupType = params.signupType as string
            const data = {
              state: query.state as string,
              code: query.code as string,
              timezone: this.state.timezone,
              country_code: this.state.country_code,
              signupType: signupType
            }
            this.sendOAuthCode(data);
          } else if (query.error && query.state) {

            this.props.history.push({
              pathname: '/register'
            });
          } else {
            this.props.history.push({
              pathname: '/register'
            });
          }

        })
        .catch(() => {

          this.setState({ timezone: "" });

          if (query.state && query.code) {
            const params = this.props.match.params;
            const signupType = params.signupType as string
            const data = {
              state: query.state as string,
              code: query.code as string,
              timezone: this.state.timezone,
              country_code: this.state.country_code,
              signupType: signupType
            }
            this.sendOAuthCode(data);
          } else if (query.error && query.state) {

            this.props.history.push({
              pathname: '/register'
            });
          } else {
            this.props.history.push({
              pathname: '/register'
            });
          }
        });
    })


  }
  render() {
    return (
      <>
        <SrPageCenter>
          < SrSpinner />
        </SrPageCenter>
        {this.state.show2FAModal && this.state.account_id && this.state.verstate &&
          < TwoFactorAuthModal
            accountId={this.state.account_id!}
            accountEmail={'smartreachemail'}
            verstate={this.state.verstate!}
            initialForm2FAType={this.state.show2FAModalType}
            onClose={this.close2FAModal}
            state = {this.state.state_param}
          />
        }
      </>
    );
  }
}

export const OAuthRedirect = withRouter((observer(OAuthRedirectMidware)))