import * as React from 'react';
import {  observer } from 'mobx-react';
import {  RouteComponentProps } from 'react-router-dom';
import { SrSpinner } from '@sr/design-component-lite';
import { OAuthPage } from './oAuthPage';
// import { password } from './password-signup';
import { RegisterWithPassword } from './register-page';
import { GetEmail } from './get-email-new-auth-flow';
import * as queryString from 'query-string';
import * as newAuthApi from '../../api/newAuth';
import * as authApi from '../../api/auth';
import { CONSTANTS } from '../../data/constants';
import { SrIconTickCircle} from '@sr/design-component-lite';
import { getClientRedirectUri } from '../../utils/localStorage';
import * as oAuthApi from '../../api/oauth'
import { ENV_CONSTANTS } from '../../data/env_constants';
import { redirectTo } from '../../utils/redirection';



export enum ISignupType {
  Google = 'google',
  Microsoft = 'microsoft',
  Password = 'password'
}

interface IRegisterV2Props extends RouteComponentProps<any> {
}

interface IRegisterV2States {
  isLoading?: boolean;
  accountEmail: string;
  signupType?: ISignupType;
  inviteData?: LogIn.IInvitedMemberDetail
  showCaptcha: Boolean;
  passedCaptcha: Boolean;
  g_response?: string;
  showCaptchaError: boolean;
  login_challenge: string;

}

export interface IEmail {
  accountEmail: string
}

class RegisterPageV2 extends React.Component<IRegisterV2Props, IRegisterV2States> {

  constructor(props: IRegisterV2Props) {
    super(props);
    this.state = {
      isLoading: false,
      accountEmail: '',
      signupType: undefined,
      passedCaptcha: true,
      showCaptcha: false,
      showCaptchaError: false,
      login_challenge: ''

    };
    this.setSignupType = this.setSignupType.bind(this);
    this.setEmail = this.setEmail.bind(this);
    this.submitForm = this.submitForm.bind(this);
    this.fetchAndSetSignupType = this.fetchAndSetSignupType.bind(this);
    this.fetchInvitedUserData = this.fetchInvitedUserData.bind(this);
    this.setGResponse = this.setGResponse.bind(this);
  }

  setEmail(accountEmail: string) {
    this.setState({ accountEmail: accountEmail })
  }

  setGResponse(g_response: string) {
    this.setState({ g_response: g_response, showCaptchaError: false })
  }

  setSignupType(signupType: string) {
    if (signupType == 'google') {
      this.setState({ signupType: ISignupType.Google })
    } else if (signupType == 'microsoft') {
      this.setState({ signupType: ISignupType.Microsoft })
    } else {
      this.setState({ signupType: ISignupType.Password })
    }
  }

  fetchAndSetSignupType(data: IEmail, isInvited?: boolean) {//akhilesh edit - to check by animesh
    const req = {
      accountEmail: data.accountEmail,
      g_response: this.state.g_response
    }
  return newAuthApi.checkPath(req)
      .then((res: any) => {
        const showCaptcha = isInvited ? false : res.data.showCaptcha;//akhilesh edit - to check by animesh
        const passedCaptcha = isInvited ? true : res.data.passedCaptcha;//akhilesh edit - to check by animesh
        this.setState({ showCaptcha: showCaptcha, passedCaptcha: passedCaptcha })
        this.setSignupType(res.data.signupType as string)
      }).catch(() => {
        this.setState({ accountEmail: '' })
      })

  }

  submitForm(data: IEmail, setSubmitting: (isSubmitting: boolean) => void) {
    this.setEmail(data.accountEmail)
    if (this.state.showCaptcha && this.state.g_response == undefined) {
      setSubmitting(false);
      this.setState({ showCaptchaError: true })
    } else {
      this.fetchAndSetSignupType(data)
        .then((res) => {
          setSubmitting(false);
        })
        .catch((errResponse) => {
          this.setState({ accountEmail: "" })
          setSubmitting(false);
        });
    }
  }

  fetchInvitedUserData(inviteCode: string): Promise<void> {

    return authApi.getEmailfromInviteCode(inviteCode)
      .then((response) => {
        this.setState({
          accountEmail: response.data.email,
          inviteData: response.data,
        });
      })
  }

  componentDidMount() {

    const query = queryString.parse(this.props.location.search);
    const inviteCode = query.invite_code as (string | null);
    const login_challenge = query.login_challenge 

    this.setState({isLoading:true},()=> {
    oAuthApi
      .checkIfLoggedIn()
      .then(resp => {
      if(resp.data.is_logged_in){
        redirectTo(ENV_CONSTANTS.APP_URL)
      }
      this.setState({ isLoading: false });
    }).catch(e =>{
      console.log(`Error Occurred :: ${e}`)
      this.setState({ isLoading: false });

    })
  })
  
    if(login_challenge){
      this.setState({login_challenge: login_challenge as string})
    }

   if (inviteCode) {

      this.setState({ isLoading: true });

      this.fetchInvitedUserData(inviteCode)
        .then(res => {

          const data: IEmail = {
            accountEmail: this.state.inviteData!.email
          };

          return this.fetchAndSetSignupType(data, true);//akhilesh edit - to check by animesh

        })
        .then(_ => {
          this.setState({ isLoading: false })
        })
        .catch(_ => {
          this.setState({ isLoading: false })
        });

    }
  }


  render() {
    const isLoading = this.state.isLoading;
    const signupType = this.state.signupType;
    const query = queryString.parse(this.props.location.search);
    const inviteCode = query.invite_code as string;

    const clientUri = getClientRedirectUri()
    const loginRedirect =  clientUri ? 
    <a href={clientUri}>Log in </a> : 
    <a href={'https://app.smartreach.io'}>Log in </a> ;
    

    return (
      <div className='register-page h-screen'>

      
        {isLoading &&
          <div className="min-h-screen flex justify-center items-center py-12 px-4 sm:px-6 lg:px-8">
            < SrSpinner />
          </div>        
        }

        {!isLoading &&
          <div className="font-sourcesanspro h-[inherit] flex flex-col">
            <div className={"flex-grow items-center justify-center w-full" + (!inviteCode ? ' md:grid md:grid-cols-2' : '')}>

              {!inviteCode &&
                <div className='bg-gradient-to-r from-blue-2 to-sr-default-blue h-full hidden md:flex md:flex-col justify-center'>
                  <div className='mx-auto mb-10'>
                    <img src={CONSTANTS.CDN_URL + '/assets/reg_image.svg'} alt='reg_image' />
                  </div>
                  <div className='lg:pl-24 sm:pl-8'>
                    <h1 className= 'sr-h1 text-white mb-10 ' >
                      Book more meetings &<br />Close deals with confidence.
                    </h1>
                    <div className='sr-h4 flex !font-normal flex-row text-white mb-4'>
                      <SrIconTickCircle className="h-8 w-8 pr-2 text-green-400" /> Deliver email directly  to prospects’ inboxes
                    </div>
                    <div className='sr-h4 flex !font-normal flex-row text-white mb-4'>
                      <SrIconTickCircle className="h-8 w-8 pr-2 text-green-400" /> Get more replies with Multi-channel outreach
                    </div>
                    <div className='sr-h4 flex !font-normal flex-row text-white mb-4'>
                      <SrIconTickCircle className="h-8 w-8 pr-2 text-green-400" /> Boost team collaboration using Shared Inbox
                    </div>
                    <div className='sr-h4 flex !font-normal flex-row text-white mb-4'>
                      <SrIconTickCircle className="h-8 w-8 pr-2 text-green-400" /> Build prospect lists with our LinkedIn email finder
                    </div>
                  </div>
                </div>
              }
              <div className="sm:mx-auto sm:w-full sm:max-w-md mb-auto h-full flex flex-col justify-center">
                <a className="flex items-center justify-center my-4" href='https://smartreach.io' target='_blank'>
                  <img
                    className="h-12"
                    src={CONSTANTS.CDN_URL + "/assets/SmartreachLogo.svg"}
                    alt="SmartReach.io Logo"
                  />
                  <span className='font-muli  px-2 text-2xl'>SmartReach</span>
                </a>

                {(!signupType || this.state.showCaptcha) &&
                  <GetEmail submitForm={this.submitForm} setEmail={this.setEmail} authType="Sign up" showCaptcha={this.state.showCaptcha} setGResponse={this.setGResponse} showCaptchaError={this.state.showCaptchaError} accountEmail={this.state.accountEmail} />
                }
                {(signupType == ISignupType.Google || signupType == ISignupType.Microsoft) && this.state.passedCaptcha &&
                  <OAuthPage  login_challenge={this.state.login_challenge} accountEmail={this.state.accountEmail} signupType={signupType} setSignupType={this.setSignupType} authType="Sign up" invite_code={inviteCode}
                    inviteDetail={this.state.inviteData}
                  />
                }
                {signupType == ISignupType.Password && this.state.passedCaptcha &&
                  <RegisterWithPassword   prefilledEmail={this.state.accountEmail} isNewAuthFlow={true} invite_code={inviteCode} />
                  // password(this.state.accountEmail)
                }

                <div className='text-sm text-center'>
                  <p className='pb-6'>Already have an account? {loginRedirect}</p>
                  <p className='pb-2'>By signing up, you agree to our
                    <br />
                    <a className='text-sr-subtext-grey' target='_blank' href={CONSTANTS.HOME_URL + '/terms-and-conditions'}> <u>Terms</u></a>,
                    <a className='text-sr-subtext-grey' target='_blank' href={CONSTANTS.HOME_URL + '/privacy-policy'}> <u>Privacy Policy</u></a>,
                    <a className='text-sr-subtext-grey' target='_blank' href={CONSTANTS.HOME_URL + '/usage-and-anti-spam-policy'}> <u>Usage</u> and <u>Anti-Spam Policy</u></a>.
                  </p>
                </div>
              </div>
            </div>

            <div className='hidden md:flex h-32 w-full flex-col justify-center'>
              <h3 className="text-sr-subtext-grey font-normal lg:text-lg text-center mt-2">
                You’re in good company. Trusted by <b>3,000+ businesses</b> worldwide
              </h3>
              <div className='flex mt-1'>
                <img className='m-auto filter w-24 grayscale opacity-50' src={CONSTANTS.CDN_URL + '/assets/apr23/oracle_2.png'} alt='oracle' />
                <img className='m-auto filter w-24 grayscale opacity-50' src={CONSTANTS.CDN_URL + '/assets/apr23/lenovo.svg'} alt='lenovo' />
                <img className='m-auto filter w-24 grayscale opacity-50' src={CONSTANTS.CDN_URL + '/assets/apr23/dubairports.svg'} alt='dubairports' />
                <img className='m-auto filter w-24 grayscale opacity-50' src={CONSTANTS.CDN_URL + '/assets/apr23/razorpay.png'} alt='razorpay' />
                <img className='m-auto filter w-24 grayscale opacity-50' src={CONSTANTS.CDN_URL + '/assets/apr23/siemens.svg'} alt='siemens' />
              </div>
            </div>
          </div>
        }
      </div >
    )
  }
};





export const RegisterV2 = (observer(RegisterPageV2));
