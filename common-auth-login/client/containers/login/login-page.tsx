import * as React from 'react';
import * as authApi from '../../api/auth';
import { observer } from 'mobx-react';
import { RouteComponentProps } from 'react-router-dom';
import { validateEmail } from '../../utils/validations';
import { ResetPasswordModal } from '../../components/reset-password-modal';
import * as queryString from 'query-string';
import { TwoFactorAuthModal } from '../../components/2fa-prompt-modal';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import { CONSTANTS } from '../../data/constants';
import { ResetPasswordPreLogin } from '../account/reset-password-page-pre-login';
import { EmailVerification } from './email-verification-page';
import ReCAPTCHA from "react-google-recaptcha";
import { EyeIcon, EyeOffIcon } from '@heroicons/react/outline';
import { SRButtonFilled } from '@sr/design-component-lite';
import { redirectTo } from '../../utils/redirection'
import { classNames } from '../../utils/sr-utils';

interface ILogInProps extends RouteComponentProps<any> {
  accountEmail: string;
  login_challenge?: string;
  showCaptcha: Boolean;
}

interface ILogInStates {
  showResetModal?: boolean;
  accountEmailForActions?: string;
  isEmailAlreadyExists?: boolean;
  isEmaiVerificationRequired?: boolean;

  // 2FA related
  show2FAModal: boolean;
  show2FAModalType: 'enable_2fa' | 'verify_2fa';
  two_fa_type: 'sms' | 'gauth';
  defaultCountryCode?: string;
  account_id?: number;
  verstate?: string;
  g_response?: string;
  showCaptchaError: boolean;
  showResetPassword: boolean;
  attemptNumberForForgotPassword: number;
  attemptNumberForVerify: number
  // login_email: string;
  // login_password: string;
  // errorMsg: string;
  showPassword: boolean;
  redirect_to: string
}

interface ILogInFormFields {
  email: string;
  password: string;
}

class LogInWithPWD extends React.Component<ILogInProps, ILogInStates> {
  recaptchaInstance: any; //This is used to reload the recaptcha using ref
  constructor(props: ILogInProps) {
    super(props);
    this.state = {
      showResetModal: false,
      accountEmailForActions: this.props.accountEmail,
      isEmaiVerificationRequired: false,
      show2FAModal: false,
      show2FAModalType: 'enable_2fa',
      two_fa_type: 'gauth',
      showCaptchaError: false,
      showResetPassword: false,
      attemptNumberForForgotPassword: 0,
      attemptNumberForVerify: 0,
      // login_email: '',
      // login_password: '',
      // errorMsg: '',
      showPassword: false,
      redirect_to: ''
    };

    this.submitLogInForm = this.submitLogInForm.bind(this);
    this.validateLoginForm = this.validateLoginForm.bind(this);
    this.showResetModal = this.showResetModal.bind(this);
    this.closeResetModal = this.closeResetModal.bind(this);
    this.resendVerificationEmail = this.resendVerificationEmail.bind(this);
    this.close2FAModal = this.close2FAModal.bind(this);
    this.setGResponse = this.setGResponse.bind(this);
    this.onClickShowHidePwd = this.onClickShowHidePwd.bind(this);
    this.setAttemptNumber = this.setAttemptNumber.bind(this);
    this.closeResetPassword = this.closeResetPassword.bind(this);
  }

  onClickShowHidePwd() {
    this.setState({ showPassword: !this.state.showPassword });
  }


  setGResponse(g_response: string) {
    this.setState({ g_response: g_response, showCaptchaError: false })
  }

  setAttemptNumber(count: number) {
    this.setState({ attemptNumberForForgotPassword: count, showResetPassword: true })
  }
  close2FAModal() {
    this.setState({ show2FAModal: false });
  }
  closeResetPassword() {
    this.setState({ showResetPassword: false });
  }

  closeResetModal() {
    this.setState({ showResetModal: false });
  }

  showResetModal() {
    this.setState({ showResetModal: true });
  }

  submitLogInForm(values: ILogInFormFields, { setSubmitting }: FormikHelpers<ILogInFormFields>) {
    const email = values.email;
    const password = values.password;


    this.setState({ accountEmailForActions: email });
    const user = { email: email, password: password, rememberMe: false, g_response: this.state.g_response, login_challenge: this.props.login_challenge! };

    if (!this.state.g_response && this.props.showCaptcha) {
      setSubmitting(false)
      this.setState({ showCaptchaError: true })
    } else {
      authApi.login(user)
        .then((res) => {
          console.log("redirect_to url")
          console.log(res.data.redirect_to)
          setSubmitting(false);
          const resCode = res.data.code;
          this.setState({ g_response: undefined })
          if (resCode === 'verify_email') {
            console.log("verify_email condition")
            this.setState({ isEmaiVerificationRequired: true, attemptNumberForVerify: res.data.attemptNumber! })

          } else if (resCode === 'enable_2fa') {
            console.log("enable_2fa condition")

            this.setState({
              defaultCountryCode: res.data.default_country_code,
              account_id: res.data.aid,
              show2FAModal: true,
              show2FAModalType: 'enable_2fa',
              verstate: res.data.verstate,
              two_fa_type: res.data.two_fa_type ? res.data.two_fa_type : 'gauth',
              redirect_to: res.data.redirect_to!
            });

          } else if (resCode === 'verify_2fa') {

            console.log("verify_2fa condition")

            this.setState({
              defaultCountryCode: res.data.default_country_code,
              account_id: res.data.aid,
              show2FAModal: true,
              show2FAModalType: 'verify_2fa',
              verstate: res.data.verstate,
              two_fa_type: res.data.two_fa_type ? res.data.two_fa_type : 'gauth',
              redirect_to: res.data.redirect_to!
            });

          } else {

            console.log("else condition")
            redirectTo(res.data.redirect_to!)

          }

        })
        .catch((errResponse: any) => {
          console.error('login handleSubmit CATCH ERROR', errResponse);
          this.resetRecaptcha();
          setSubmitting(false);
          this.setState({ g_response: undefined })

        });
      this.setState({ g_response: undefined })
    }

  }

  resetRecaptcha() {
    this.recaptchaInstance.reset();
  };

  validateLoginForm(values: ILogInFormFields) {
    const email = values.email;
    const password = values.password;
    let errors = {} as ILogInFormFields;

    if (!email || !validateEmail(email)) {
      errors.email = 'Please enter a valid email';
    }

    if (!password) {
      errors.password = 'Please enter your password';
    } else if ((password.length < 8) || (password.length > 50)) {
      errors.password = 'Password length must be between 8 to 50 characters';
    }

    return errors;

  }

  getInitialLogInFormValues() {
    const initialValues: ILogInFormFields = {
      email: this.props.accountEmail,
      password: ''
    }
    return initialValues;
  }

  resendVerificationEmail() {
    if (!this.state.g_response) {
      alert("Please verify captcha")
    } else {
      const data = { email: this.state.accountEmailForActions, g_response: this.state.g_response };
      authApi.resendVerificationEmail(data)
      this.resetRecaptcha();
    }
  }

  componentDidMount() {
    const query = queryString.parse(this.props.location.search);
    if (query.emailExists) {
      this.setState({ isEmailAlreadyExists: true });
    } else {
      this.setState({ isEmailAlreadyExists: false });
    }
  }

  render() {
    const showResetModal = this.state.showResetModal;
    const isEmailAlreadyExists = this.state.isEmailAlreadyExists;
    const isEmaiVerificationRequired = this.state.isEmaiVerificationRequired;

    return (
      <>
        <div>

          {!isEmaiVerificationRequired && !this.state.showResetPassword && !showResetModal &&
            <div className="sm:mx-auto sm:w-full sm:max-w-md">

              <h1 className="my-2 sr-h2 text-sr-gray-100 text-center">Log in to your account</h1>
              {isEmailAlreadyExists && <p>(An account already exists with this email id. Please login or reset password)</p>}

              <div className="bg-white sm:rounded-lg0 mt-12">
                <div className="py-8 px-4 sm:px-10">
                  <Formik
                    initialValues={this.getInitialLogInFormValues()}
                    validate={this.validateLoginForm}
                    onSubmit={this.submitLogInForm}
                  >
                    {({ isSubmitting, errors }) => (
                      <Form>
                        <div className='mb-6'>
                          <div className='font-noto text-[16px] font-semibold text-sr-gray-100'>Email</div>
                          <Field autoComplete='nope' type="email" name="email" placeholder='<EMAIL>' className='input-formik w-full h-10' disabled />
                          <ErrorMessage name="email" component="div" className='error-formik' />
                        </div>

                        <div className='mb-6'>


                          <div className='mb-2 text-left'>
                            <label className='font-noto text-[16px] font-semibold text-sr-gray-100' htmlFor='register_password'>Password</label>
                          </div>
                          <div className='relative'>
                            <Field
                              type={this.state.showPassword ? 'text' : "password"}
                              name="password"
                              autoFocus
                              placeholder='Enter password'
                              className={classNames('rounded-md w-full pr-10', (errors.password ? 'mb-1 !border-sr-danger-60' : ''))}
                            />
                            {this.state.showPassword ? (
                              <EyeOffIcon
                                className="h-5 w-5 absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer"
                                aria-hidden="true"
                                onClick={this.onClickShowHidePwd.bind(this)}
                              />
                            ) : (
                              <EyeIcon
                                className="h-5 w-5 absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer"
                                aria-hidden="true"
                                onClick={this.onClickShowHidePwd.bind(this)}
                              />
                            )}
                          </div>
                          <ErrorMessage name="register_password" component="div" className='error-formik' />
                        </div>
                        <div className='mb-6 text-left'>
                           <a href='#' className='text-sr-primary-80 sr-h6' onClick={this.showResetModal}>Forgot your password?</a>
                        </div>
                        <div className='mb-6 flex items-center justify-center'>
                          <ReCAPTCHA
                            id='LoginRecaptcha'
                            sitekey={CONSTANTS.G_RECAPTCHA_SITE_KEY}
                            onChange={this.setGResponse}
                            ref={(e: any) => this.recaptchaInstance = e}
                          />
                          {this.state.showCaptchaError &&
                            <div className='error-formik'>Please validate Captcha</div>}
                        </div>
                        <div className='flex justify-center'><SRButtonFilled type="submit" text="Log In" disable={isSubmitting} loading={isSubmitting} isPrimary={true} className='!text-[16px] !font-noto !font-normal h-10 !bg-sr-grey-primary !text-white !px-16 !py-3' /></div>

                      </Form>
                    )}

                  </Formik>

                </div>

              </div>

            </div>
          }

          {showResetModal &&
            <ResetPasswordModal onClose={this.closeResetModal} email={this.props.accountEmail} setAttemptNumber={this.setAttemptNumber} />
          }

          {this.state.showResetPassword &&
            <ResetPasswordPreLogin email={this.props.accountEmail} closeResetPassword={this.closeResetPassword} attemptNumber={this.state.attemptNumberForForgotPassword} />
          }
          {isEmaiVerificationRequired &&

            <div className='mt-8 sm:mx-auto sm:w-full sm:max-w-md'>
              <h1 className="my-6 text-center text-3xl font-extrabold text-gray-900">Please verify your email</h1>

              <EmailVerification email={this.state.accountEmailForActions!} attemptNumber={this.state.attemptNumberForVerify} history={this.props.history} match={this.props.match} location={this.props.location} />


              <p>The email contains an OTP code. Please use this OTP code to verify your email account and start using SmartReach.</p>

            </div>

          }

          {this.state.show2FAModal && this.state.account_id && this.state.verstate &&
            < TwoFactorAuthModal
              accountId={this.state.account_id!}
              accountEmail={this.state.accountEmailForActions || 'smartreachemail'}
              verstate={this.state.verstate!}
              initialForm2FAType={this.state.show2FAModalType}
              onClose={this.close2FAModal}
            />
          }
        </div>
      </>
    );
  }
};


export const LogInWithPassword = (observer(LogInWithPWD));
