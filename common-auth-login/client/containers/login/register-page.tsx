import * as React from 'react';
import * as authApi from '../../api/auth';
import { observer } from 'mobx-react';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import { newPasswordValidation, validateEmail } from '../../utils/validations';
import { SrServer as server } from '../../api/server';
import { getCurrentTimeZone } from '../../utils/timezone';
import * as settingsApi from '../../api/settings';
// const Cookies = require('universal-cookie');
import * as queryString from 'query-string';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import { SrIconTickCircle, SrSpinner } from '@sr/design-component-lite';
import { CONSTANTS } from '../../data/constants';
import { EmailVerification } from './email-verification-page';
import ReCAPTCHA from "react-google-recaptcha";
import { EyeIcon, EyeOffIcon } from '@heroicons/react/outline';
import { SRButtonFilled } from '@sr/design-component-lite';
import { redirectTo } from '../../utils/redirection';
import { classNames } from '../../utils/sr-utils';
export interface ITimezone {
  id: string;
  name: string;
}
interface IRegisterFormFields {
  register_email: string;
  register_password: string;
}
interface IRegisterProps extends RouteComponentProps<any> {
  prefilledEmail?: string;
  isNewAuthFlow?: boolean;
  invite_code?: string;
}
interface IRegisterStates {
  isLoading?: boolean;
  inviteCode?: string;
  invitedEmail?: string;
  invitedFirstName?: string;
  invitedLastName?: string;
  invitedOrgName?: string;
  inviterName?: string;
  inviterTeam?: string;
  timezones: ITimezone[];
  isEmaiVerificationRequired?: boolean;
  registerdEmail: string;
  g_response?: string;
  showCaptchaError: boolean;
  // 2FA related
  show2FAModal: boolean;
  defaultCountryCode?: string;
  account_id?: number;
  verstate?: string;
  two_fa_type?: 'sms' | 'gauth';
  showCaptcha: boolean;
  isEmailInInviteListMsg: string;
  showPassword: boolean;
  attemptNumber: number;
  isPasswordLengthValid: boolean;
  isPasswordUppercaseValid: boolean;
  isPasswordNumberValid: boolean;
}

class RegisterWithPWD extends React.Component<IRegisterProps, IRegisterStates> {
  recaptchaInstance: any; //This is used to reload the recaptcha using ref
  constructor(props: IRegisterProps) {
    super(props);
    this.state = {
      isLoading: false,
      invitedEmail: '',
      inviterName: '',
      inviterTeam: '',
      timezones: [],
      isEmaiVerificationRequired: false,
      registerdEmail: '',
      inviteCode: '',
      show2FAModal: false,
      isEmailInInviteListMsg: '',
      showCaptchaError: false,
      showPassword: false,
      showCaptcha: false,
      attemptNumber: 0,
      isPasswordLengthValid: false,
      isPasswordUppercaseValid: false,
      isPasswordNumberValid: false,
    };
    this.submitRegisterForm = this.submitRegisterForm.bind(this);
    this.validateRegisterForm = this.validateRegisterForm.bind(this);
    this.getInitialRegisterFormValues = this.getInitialRegisterFormValues.bind(this);
    this.close2FAModal = this.close2FAModal.bind(this);
    this.setGResponse = this.setGResponse.bind(this);
    this.onClickShowHidePwd = this.onClickShowHidePwd.bind(this);
  }
  onClickShowHidePwd() {
    this.setState({ showPassword: !this.state.showPassword });
  }
  setGResponse(g_response: string) {
    this.setState({ g_response: g_response, showCaptchaError: false })
  }
  close2FAModal() {
    this.setState({ show2FAModal: false });
  }
  getInitialRegisterFormValues() {
    const query = queryString.parse(this.props.location.search);
    const inviteCode = query.invite_code || this.props.invite_code;
    const toRegisterEmail = this.props.prefilledEmail;
    const initialEmailValue = (inviteCode) ? this.state.invitedEmail : (toRegisterEmail ? toRegisterEmail : '');
    const initialRegisterFormValues: IRegisterFormFields = {
      register_email: initialEmailValue || '',
      register_password: '',
    };
    return initialRegisterFormValues
  }
  resetRecaptcha() {
    this.recaptchaInstance.reset();
  };
  submitRegisterForm(values: IRegisterFormFields, { setSubmitting }: FormikHelpers<IRegisterFormFields>) {
    this.setState({ isEmailInInviteListMsg: '' });
    const query = queryString.parse(this.props.location.search);
    const inviteCode = query.invite_code as (string | undefined) || this.props.invite_code;
    /*
    query.login_challenge is of type string | string[] | null | undefined

    but ideally the login_challenge should be present in query param as a single string so converting the
    type to string | undefined
    */
    const login_challenge = (query.login_challenge && typeof query.login_challenge === "string") ? query.login_challenge : undefined
    let user: authApi.INewUser = {
      email: values.register_email,
      password: values.register_password,
      // first_name: values.register_first_name,
      // last_name: values.register_last_name,
      // company: values.register_company,
      invite_code: inviteCode,
      // rs_code_used: inviteCode ? null : rscode,
      timezone: 'America/Los_Angeles',
      country_code: 'US',
      // is_agency: values.register_is_agency,
      g_response: this.state.g_response,
      login_challenge: login_challenge
    };
    console.log('register on submit', user);
    if (!this.state.g_response) {
      setSubmitting(false)
      this.setState({ showCaptchaError: true })
    } else {
      server.getLocation().then((resp) => {
        user.country_code = resp.country || 'US';
        // in case ipinfo doesnt work
        const backupCurrentTimeZoneId = getCurrentTimeZone(this.state.timezones || [] as ITimezone[]);
        user.timezone = resp.timezone || backupCurrentTimeZoneId || 'America/Los_Angeles';
        authApi.register(user)
          .then((response) => {
            const resCode = response.data.code;
            if (resCode === 'enable_2fa') {
              this.setState({
                defaultCountryCode: response.data.default_country_code,
                account_id: response.data.aid,
                // show2FAModal: true,
                verstate: response.data.verstate,
                two_fa_type: response.data.two_fa_type ? response.data.two_fa_type : 'gauth',
              });
            } else {
              //checking this condition because invited users accounts verified automatically
              if (response.data.account && response.data.account.email_verified) {
                this.setState({ isEmaiVerificationRequired: false });
                setSubmitting(false);

                redirectTo(response.data.redirect_to!)

              } else {
                this.setState({
                  isEmaiVerificationRequired: true,
                  registerdEmail: user.email,
                  attemptNumber: response.data.attemptNumber!
                });
              }
            }
          })
          .catch((errResponse) => {
            this.resetRecaptcha();
            const isEmailAlreadyRegistered = (errResponse.data ? (errResponse.data.error_code === 'account_with_email_exists') : false);
            const isEmailInInviteList = (errResponse.data ? (errResponse.data.error_code === 'email_in_invite_list') : false);
            console.log('register err', isEmailAlreadyRegistered, errResponse);
            if (isEmailAlreadyRegistered) {
              setTimeout(() => {
                this.props.history.push('/login?emailExists=true');
              }, 1000);
            } else if (isEmailInInviteList) {
              this.setState({ isEmailInInviteListMsg: errResponse.message });
            }
            setSubmitting(false);
          });
      })
        .catch((err: any) => {
          authApi.register(user)
            .then((response) => {
              const resCode = response.data.code;
              if (resCode === 'enable_2fa') {
                this.setState({
                  defaultCountryCode: response.data.default_country_code,
                  account_id: response.data.aid,
                  // show2FAModal: true,
                  verstate: response.data.verstate,
                  two_fa_type: response.data.two_fa_type ? response.data.two_fa_type : 'gauth',
                });
              } else {
                //checking this condition because invited users accounts verified automatically
                if (response.data.account && response.data.account.email_verified) {
                  this.setState({ isEmaiVerificationRequired: false });
                  setSubmitting(false);

                  redirectTo(response.data.redirect_to!)

                } else {
                  this.setState({ isEmaiVerificationRequired: true, registerdEmail: user.email });
                }
              }
            })
            .catch((errResponse) => {
              this.resetRecaptcha();
              const isEmailAlreadyRegistered = (errResponse.data ? (errResponse.data.error_code === 'account_with_email_exists') : false);
              const isEmailInInviteList = (errResponse.data ? (errResponse.data.error_code === 'email_in_invite_list') : false);
              console.log('register err', isEmailAlreadyRegistered, errResponse);
              if (isEmailAlreadyRegistered) {
                setTimeout(() => {
                  this.props.history.push('/login?emailExists=true');
                }, 1000);
              } else if (isEmailInInviteList) {
                this.setState({ isEmailInInviteListMsg: errResponse.message });
              }
              setSubmitting(false);
              this.resetRecaptcha();
            });
        })
      this.setState({ g_response: undefined })
    }
  }
  validateRegisterForm(values: IRegisterFormFields) {
    const errors = {} as IRegisterFormFields;
    const email = values.register_email;
    const password = values.register_password;
    if (email === '' || !validateEmail(email)) {
      errors.register_email = 'Please enter a valid email';
    }
    if (password === '') {
      errors.register_password = 'Please enter your password';
    } else {
      let passwordError = newPasswordValidation(password)
      if (passwordError) errors.register_password = passwordError
    }
    // Update password validation states
    const isPasswordLengthValid = password.length >= 8;
    const isPasswordUppercaseValid = /[A-Z]/.test(password);
    const isPasswordNumberValid = /[0-9]/.test(password);

    this.setState({
      isPasswordLengthValid,
      isPasswordUppercaseValid,
      isPasswordNumberValid,
    });
    return errors;
  }
  prefillEmailInvited(inviteCode: string) {
    this.setState({ isLoading: true, inviteCode: inviteCode });
    authApi.getEmailfromInviteCode(inviteCode)
      .then((response) => {
        this.setState({
          invitedEmail: response.data.email,
          invitedFirstName: response.data.first_name,
          invitedLastName: response.data.last_name,
          invitedOrgName: response.data.org_name,
          inviterName: response.data.inviter_name,
          inviterTeam: response.data.team_name,
        }, () => {
          this.setState({ isLoading: false })
        });
      })
      .catch(() => {
        this.setState({ isLoading: false });
      })
  }
  componentDidMount() {
    // const rscode = this.props.location.query ? this.props.location.query.rscode : '';
    // if (rscode) {
    //   if (cookies.get('rscode') !== rscode) {
    //     cookies.set('rscode', rscode, { path: '/', domain: '.smartreach.io', maxAge: 2592000 });
    //   }
    // }
    window.scrollTo(0, 0);
    const query = queryString.parse(this.props.location.search);
    setTimeout(() => { this.setState({ showCaptcha: true }) }, 1000)
    const inviteCode = (query.invite_code || '') as string || this.props.invite_code;
    if (inviteCode) {
      this.prefillEmailInvited(inviteCode);
    }
    settingsApi.getTimeZone()
      .then((res) => {
        let timezones: ITimezone[] = [];
        res.data.timezones.forEach((zone) => {
          timezones.push({ name: zone.name, id: zone.value });
        });
        this.setState({
          timezones: timezones,
        });
      })
      .catch(() => {
        // this.setState({
        // });
      });
    //to reload if you get to register page through back button
    // const isLoggedIn = this.props.logInStore.getLogInStatus;
    // if (isLoggedIn) {
    //   window.location.reload();
    // }

  }
  render() {
    const isLoading = this.state.isLoading;
    const inviterName = this.state.inviterName;
    const inviterTeam = this.state.inviterTeam;
    const isEmaiVerificationRequired = this.state.isEmaiVerificationRequired;
    const isEmailInInviteList = this.state.isEmailInInviteListMsg;
    const isInvited = !!this.state.inviteCode;
    return (
      <>

        {isLoading &&
          <SrSpinner spinnerTitle='loading ..' />
        }
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          {!isEmaiVerificationRequired && !isLoading &&
            <div className='sm:mx-auto sm:w-full sm:max-w-md'>
              <div>
                <h1 className="my-2 font-readexpro text-[28px] font-semibold text-sr-gray-100 text-center">Launch your <span className='text-sr-primary-90'>campaign today</span></h1>
                {isInvited ?
                  <p className='text-center'><b>{inviterName}</b> has invited you to join their team - <b>{inviterTeam}</b></p>
                  : <h4 className="text-sr-subtext-grey font-normal text-center">No credit card. No surprises. Just Results</h4>}
              </div>
              {isEmailInInviteList !== '' &&
                <div className='p-auto mb-4 border border-red-300 bg-red-100'>{isEmailInInviteList}</div>
              }
              <div className="sm:rounded-lg0">
                <div className="py-4">
                  <Formik
                    initialValues={this.getInitialRegisterFormValues()}
                    validate={this.validateRegisterForm}
                    onSubmit={this.submitRegisterForm}
                  >
                    {({ isSubmitting, errors }) => (
                      <Form>
                        <div className='mb-4'>
                          <div className='mb-2 text-left'><label className='font-noto text-[16px] font-semibold text-sr-gray-100' htmlFor='register_email'>Work Email</label></div>
                          <Field autoComplete='nope' autoFocus={!this.props.isNewAuthFlow} type="email" name="register_email" placeholder='<EMAIL>' className='input-formik w-full' disabled={this.props.isNewAuthFlow} />
                          <ErrorMessage name="register_email" component="div" className='error-formik' />
                        </div>
                        <div className='mb-8'>
                          <div className='mb-2 text-left'>
                            <label className='font-noto text-[16px] font-semibold text-sr-gray-100' htmlFor='register_password'>Create Password</label>
                          </div>
                          <div className='relative'>
                            <Field
                              type={this.state.showPassword ? 'text' : "password"}
                              name="register_password"
                              autoFocus
                              placeholder='Enter password'
                              className={classNames('rounded-md w-full pr-10', (errors.register_password ? 'mb-1 !border-sr-danger-60' : ''))}
                            />
                            {this.state.showPassword ? (
                              <EyeOffIcon
                                className="h-5 w-5 absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer"
                                aria-hidden="true"
                                onClick={this.onClickShowHidePwd}
                              />
                            ) : (
                              <EyeIcon
                                className="h-5 w-5 absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer"
                                aria-hidden="true"
                                onClick={this.onClickShowHidePwd}
                              />
                            )}
                          </div>
                          {/* <ErrorMessage name="register_password" component="div" className='error-formik' /> */}
                          <div className='flex gap-2 mt-1'>
                            <div className='flex gap-1'>
                              {this.state.isPasswordLengthValid ? <SrIconTickCircle className="h-[14px] w-[14px] text-sr-default-green" /> : <div className='h-[14px] w-[14px] rounded-full border border-sr-danger-50 mt-0.5'></div>}
                              <p className='text-sr-subtext-grey text-[12px] font-noto'>At least 8 characters</p>
                            </div>
                            <div className='flex gap-1'>
                              {this.state.isPasswordUppercaseValid ? <SrIconTickCircle className="h-[14px] w-[14px] text-sr-default-green" /> : <div className='h-[14px] w-[14px] rounded-full border border-sr-danger-50 mt-0.5'></div>}
                              <p className='text-sr-subtext-grey text-[12px] font-noto'>One uppercase letter</p>
                            </div>
                            <div className='flex gap-1'>
                              {this.state.isPasswordNumberValid ? <SrIconTickCircle className="h-[14px] w-[14px] text-sr-default-green" /> : <div className='h-[14px] w-[14px] rounded-full border border-sr-danger-50 mt-0.5'></div>}
                              <p className='text-sr-subtext-grey text-[12px] font-noto'>One number</p>
                            </div>

                          </div>
                        </div>
                        {
                          !isEmaiVerificationRequired &&
                          <div className='mb-6 flex items-center justify-center'>
                            <ReCAPTCHA
                              sitekey={CONSTANTS.G_RECAPTCHA_SITE_KEY}
                              onChange={this.setGResponse}
                              ref={(e: any) => this.recaptchaInstance = e}
                            />
                            {this.state.showCaptchaError &&
                              <div className='error-formik'>Please validate Captcha</div>}
                          </div>
                        }
                        <SRButtonFilled type="submit" text="Create Account" disable={isSubmitting} loading={isSubmitting} isPrimary={true} className='!text-[16px] !font-noto !font-normal h-10 !bg-sr-grey-primary !text-white !px-16 !py-3' width='fluid'/>
                      </Form >
                    )
                    }
                  </Formik>
                </div >
              </div >
            </div >
          }
          {
            isEmaiVerificationRequired && !isLoading &&
            <div className='mx-4 mt-2 sm:mx-auto sm:w-full sm:max-w-md flex flex-col justify-center'>
              {/* <div className='mx-auto mb-4'>
                <img className='w-20' src={CONSTANTS.CDN_URL + "/assets/Envelope.png"} alt='reg_image' />
              </div> */}
              <div>
                {/* <div className='flex flex-col items-start text-left'> */}
                <div>
                  <h2 className='font-readexpro text-[28px] font-semibold mb-2 text-center'>Verify your email</h2>
                  <div className='text-sr-subtext-grey text-sm text-center'>
                    Enter the one-time verification code we sent to <u>{this.state.registerdEmail}</u>
                  </div>
                </div>
                <div className='mt-2'>
                  <EmailVerification email={this.props.prefilledEmail!} attemptNumber={this.state.attemptNumber} history={this.props.history} match={this.props.match} location={this.props.location} />
                </div >
              </div >
            </div>
          }
        </div >
        {/* {
          this.state.show2FAModal && this.state.account_id && this.state.verstate &&
          <TwoFactorAuthModal
            accountId={this.state.account_id!}
            accountEmail={this.state.registerdEmail}
            verstate={this.state.verstate!}
            initialForm2FAType='enable_2fa'
            onClose={this.close2FAModal}
          />
        } */}
      </>
    );
  }
}
export const RegisterWithPassword = withRouter((observer(RegisterWithPWD)));
