import { Form, Field, ErrorMessage, Formik, FormikHelpers } from 'formik';
import * as React from 'react';
import { validateEmail } from '../../utils/validations';
import { IEmail } from './register-page-v2';
import { CONSTANTS } from '../../data/constants';
import { SRButtonFilled } from '@sr/design-component-lite';
import { classNames } from '../../utils/sr-utils';
import ReCAPTCHA from "react-google-recaptcha";

export function GetEmail(
  props: {
    submitForm: (data: IEmail, setSubmitting: (isSubmitting: boolean) => void) => void,
    setEmail: (accountEmail: string) => void,
    authType: "Sign up" | 'Sign in',
    showCaptcha: Boolean,
    showCaptchaError: boolean,
    setGResponse: (g_response: string) => void,
    accountEmail?: string
  }) {

  function validateGetEmailForm(value: { accountEmail: string }) {
    const errors = {} as { accountEmail: string };
    const email = value.accountEmail;

    if (email === "" || !(validateEmail(email))) {
      errors.accountEmail = 'Please enter a valid email address';
    }

    return errors;
  }
  let captcha: any;

  const setCaptchaRef = (ref: any) => {
    if (ref) {
      return captcha = ref;
    }
  };

  const resetCaptcha = () => {
    captcha.reset();
  }

  function submitEmailForm(data: IEmail, setSubmitting: (isSubmitting: boolean) => void) {
    props.submitForm(data, setSubmitting);
    if (props.showCaptcha) { //workaround to reload the recaptcha here as we are making the api call in the parent
      setTimeout(resetCaptcha, 2000)
    }


  }
  return (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-md flex flex-col">
        <h1 className={`my-2 font-readexpro text-[28px] font-semibold text-sr-gray-100 ${props.authType === 'Sign in' ? 'text-center' : 'text-center'
          }`}>
          {props.authType == 'Sign in' && <>Log in to your account</>}
          {props.authType == 'Sign up' && <>Launch your <span className='text-sr-primary-90'>campaign today</span></>}
        </h1>
        <h4 className="text-sr-subtext-grey font-normal text-center">
          {props.authType == 'Sign up' && <>No credit card. No surprises. Just Results</>}
        </h4>
        <div className="sm:rounded-lg0 p-4 w-full mt-12">
          <div className="py-4">
            <Formik
              initialValues={{ accountEmail: props.accountEmail ? props.accountEmail : "" }}
              validate={validateGetEmailForm}
              onSubmit={(values, { setSubmitting }: FormikHelpers<IEmail>) => {
                props.setEmail(values.accountEmail)
                const data = { accountEmail: values.accountEmail };
                submitEmailForm(data, setSubmitting)
              }}
              validateOnBlur={false} // Disable validation on blur
            >
              {({ isSubmitting, errors }) => (
                <Form>
                  <div className='mb-8'>
                    <div className='mb-1.5 text-left text-sr-gray-90'><label className='font-noto text-[16px] font-semibold text-sr-gray-100' htmlFor='accountEmail'>Work Email</label></div>
                    <Field autoComplete='nope' autoFocus type="email" name="accountEmail" placeholder='<EMAIL>'
                      className={classNames('rounded-md h-10 pl-4 w-full', (errors.accountEmail ? 'mb-1 !border-sr-danger-60' : ''))} />
                    <ErrorMessage name="accountEmail" component="div" className='error-formik' />
                  </div>
                  <div className='mb-6 flex items-center justify-center'>
                    {props.showCaptcha &&
                      <ReCAPTCHA
                        id='getEmailRecaptcha'
                        sitekey={CONSTANTS.G_RECAPTCHA_SITE_KEY}
                        onChange={props.setGResponse}
                        ref={(r: any) => setCaptchaRef(r)}
                      />
                    }
                    {props.showCaptcha && props.showCaptchaError &&
                      <div className='error-formik'>Please validate Captcha</div>}
                  </div>

                  {/* <div className='flex justify-center'><SRButtonFilled type="submit" text={props.authType == "Sign in" ? "Continue" : "Create Account"} disable={isSubmitting} loading={isSubmitting} isPrimary={true} className='!text-[16px] !font-noto !font-normal h-10 !bg-sr-grey-primary !text-white !px-16 !py-3' /></div> */}
                  <SRButtonFilled type="submit" text={props.authType == "Sign in" ? "Continue" : "Create Account"} disable={isSubmitting} loading={isSubmitting} isPrimary={true} className='!text-[16px] !font-noto !font-normal h-10 !bg-sr-grey-primary !text-white !px-16 !py-3' width='fluid' />
                </Form>
              )
              }
            </Formik>
          </div>
        </div>
      </div>
    </>
  );
}