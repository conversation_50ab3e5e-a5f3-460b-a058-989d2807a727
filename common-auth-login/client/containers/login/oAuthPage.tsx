import { Form, Formik, FormikHelpers } from 'formik';
import * as React from 'react';
import * as newAuthApi from '../../api/newAuth';
import capitalize from 'lodash-es/capitalize';

import { CONSTANTS } from '../../data/constants';
import { IEmail, ISignupType } from './register-page-v2';
import { SRButtonFilled } from '@sr/design-component-lite';
import { redirectTo } from '../../utils/redirection';

export function OAuthPage(
  props: {
    setSignupType: (signupType: string) => void,
    accountEmail: string, signupType: ISignupType,
    authType: "Sign up" | 'Sign in',
    inviteDetail?: LogIn.IInvitedMemberDetail,
    invite_code?: string,
    login_challenge ?: string
  }
) {
  function signupTypeSet() {
    props.setSignupType('password')
  }

  const isInvited = !!props.invite_code && !!props.inviteDetail;
  const buttonTitle = props.authType + " with " + capitalize(props.signupType)

  return (
    <>
      <div className="pb-4 mx-2 sm:mx-auto sm:w-full sm:max-w-md">

        <h1 className="my-6 text-center text-xl text-gray-900"> {props.authType === 'Sign in'
          ? 'Sign in to your account'
          : 'Sign up now!'
        } </h1>

        {isInvited &&
          <>
            <p className='text-center'><b>{props.inviteDetail!.inviter_name}</b> has invited you to join their team - <b>{props.inviteDetail!.team_name}</b></p>
            <br />
          </>
        }


        <div className="bg-white border sm:rounded-lg0">
          <div className="py-8 px-4 sm:px-10">

            <Formik
              initialValues={{ accountEmail: props.accountEmail, signupType: props.signupType }}

              onSubmit={(values, { setSubmitting }: FormikHelpers<IEmail>) => {
                const data = {
                  accountEmail: values.accountEmail,
                  signupType: props.signupType.toString(),
                  invite_code: props.invite_code,
                }
                  
                const modifiedData = props.login_challenge ? {...data,login_challenge: props.login_challenge}:data
                 
              
                newAuthApi.authorize(modifiedData)
                  .then((res: any) => {
                    setSubmitting(false);
                    redirectTo(res.data.redirect_to)
                  })
                  .catch((errResponse: any) => {
                    setSubmitting(false);
                  });
                  
             }
            }>
              {({ isSubmitting }) => (
                <Form>
                  {props.signupType === 'google' ?
                    <button
                      type='submit'
                      className='flex h-[40px] bg-[#4285F4] items-center mx-auto'
                    // disabled={!this.state.gsuite_email_valid}
                    >
                      <div className="h-[inherit]"><img className="h-[inherit]" src={CONSTANTS.CDN_URL + '/assets/2023_jan_google_button_dark_gauth.svg'} /></div>
                      <div className='flex-1 text-white px-[8px] font-roboto'>Sign in with Google</div>
                    </button>
                    :

                    <SRButtonFilled type="submit" text={buttonTitle} disable={isSubmitting} loading={isSubmitting} isPrimary={true} className='!text-[14px] oauth-submit-heap' width='fluid' />}
                </Form>
              )}
            </Formik>

          </div>
        </div>
        {props.authType == "Sign up" &&
          <div className='py-8 px-4 sm:px-10 bg-gray-200 flex items-center'>
            <div>Want to signup with password? <a className='default-anchor' onClick={signupTypeSet}>Click here</a></div>
            <div></div>
          </div>
        }
      </div>

    </>
  )
}