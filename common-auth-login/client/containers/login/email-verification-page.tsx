import * as React from 'react';
import * as authApi from '../../api/auth';
import { RouteComponentProps } from 'react-router-dom';
import { observer } from 'mobx-react';
// import * as queryString from 'query-string';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import { CONSTANTS } from '../../data/constants';
import ReCAPTCHA from "react-google-recaptcha";
import { SRButtonFilled } from '@sr/design-component-lite';
import { redirectTo } from '../../utils/redirection';
interface EmailVerificationComponentProps extends RouteComponentProps<any> {
  email: string;
  attemptNumber: number,
}


interface EmailVerificationComponentStates {
  g_response?: string;
  showCaptchaError: boolean;
  attemptNumberForOTP: number;
  isLoading: boolean;
  attemptNumber: number;
  resendCounter: number;
  disableResendBtn: boolean;
  showCaptcha: boolean; //flag used to overcome the page breaking because of using reCaptcha in multiple places
  isResendLoading: boolean;
}


class EmailVerificationComponent extends React.Component<EmailVerificationComponentProps, EmailVerificationComponentStates> {

  recaptchaInstance: any; //This is used to reload the recaptcha using ref
  constructor(props: EmailVerificationComponentProps) {
    super(props);
    this.state = {
      showCaptchaError: false,
      attemptNumberForOTP: 0,
      isLoading: false,
      attemptNumber: this.props.attemptNumber,
      resendCounter: 30,
      disableResendBtn: true,
      showCaptcha: false,
      isResendLoading: false
    };
    this.setGResponse = this.setGResponse.bind(this);
    this.resetRecaptcha = this.resetRecaptcha.bind(this);
    this.startResendCounter = this.startResendCounter.bind(this);
    this.resendVerificationEmail = this.resendVerificationEmail.bind(this);
    this.handleSubmitVerifyEmail = this.handleSubmitVerifyEmail.bind(this);
  }

  componentDidMount() {
    setTimeout(() => { this.setState({ showCaptcha: true }) }, 1000)
    this.startResendCounter();
  }

  getInitialVerifyEmailFormValues() {
    return {
      otp: ''
    }
  }
  setGResponse(g_response: string) {
    this.setState({ g_response: g_response, showCaptchaError: false })
  }

  resetRecaptcha() {
    this.recaptchaInstance.reset();
  }

  startResendCounter() {
    const interval = setInterval(() => {

      const counter = this.state.resendCounter;

      if (counter > 0) {
        this.setState({ resendCounter: counter - 1 });
      } else {
        this.setState({ disableResendBtn: false });
        clearInterval(interval);
      }

    }, 1000)
  }
  resendVerificationEmail() {
    if (!this.state.g_response) {
      this.setState({ showCaptchaError: true });
    } else {
      this.setState({ isResendLoading: true });
      const data = { email: this.props.email, g_response: this.state.g_response };
      authApi.resendVerificationEmail(data).then((res: any) => {
        this.setState({ attemptNumber: res.data.attemptNumber })
        this.resetRecaptcha();
        this.setState({ resendCounter: 30, disableResendBtn: true, g_response: undefined, isResendLoading: false }, () => {
          this.startResendCounter();
        })

      }).catch(() => {
        this.setState({ resendCounter: 30, disableResendBtn: true, g_response: undefined, isResendLoading: false }, () => {
          this.startResendCounter();
        })
      })
    }
  }
  validateVerifyEmailForm(values: { otp: string }) {
    let errors = {} as { otp: string }

    if (values.otp === '') {
      errors.otp = 'Enter OTP';
    } else if (values.otp.length != 6) {
      errors.otp = 'Please enter the 6 digit otp';
    } else if (!values.otp.match("^[0-9]*$")) {
      errors.otp = 'OTP can only have digits';
    }

    return errors;

  }

  handleSubmitVerifyEmail(values: { otp: string }, { setSubmitting }: FormikHelpers<{ otp: string }>) {
    if (!this.state.g_response) {
      this.setState({ showCaptchaError: true })
      setSubmitting(false)
    } else {
      const queryParams = new URLSearchParams(this.props.location.search);
      const specificParamValue = queryParams.get('login_challenge');

      let data = {
        otp: values.otp,
        email: this.props.email,
        g_response: this.state.g_response,
        login_challenge: specificParamValue
      }
      authApi.verifyEmail(data).then(res => {

        if (res.data.account && res.data.redirect_to) {
          setSubmitting(false);
          console.log("Redirect to ")
          redirectTo(res.data.redirect_to!)

          // this.props.logInStore!.logIn(res.data.account, res.data.disable_analytics);
          // setSubmitting(false);
          // this.props.history.push({
          //   pathname: '/dashboard/campaigns',
          //   search: queryString.stringify({
          //     tid: this.props.logInStore!.getCurrentTeamId
          //   })
          // });
        } else {
          this.setState({ isLoading: false });
          setSubmitting(false)
        }


      }).catch(err => {
        let errMessage: string = err.message
        this.resetRecaptcha();
        setSubmitting(false)
        console.log("ERROR", err)
        if (errMessage.indexOf("Sorry please try again!") > -1) { //Checking if we are allowing to try again, if not we relocate to login page
          this.setState({ isLoading: false });
          setSubmitting(false)
        } else {
          this.setState({ isLoading: false });
          setTimeout(() => {
            this.props.history.push('/login');
          }, 5000);
        }

      });

    }

  }



  render() {
    return (
      <Formik
        initialValues={this.getInitialVerifyEmailFormValues()}
        validate={this.validateVerifyEmailForm}
        onSubmit={this.handleSubmitVerifyEmail}
      >
        {({ isSubmitting, errors }) => (
          <Form className='mr-4 py-2'>
            <div className={errors.otp ? 'mb-6' : 'mb-2'}>
              <div className='flex flex-row'>
                <label className='font-noto text-[16px] font-semibold text-sr-gray-100' htmlFor='otp'>OTP</label>
                <div className='text-sm text-sr-dark-yellow ml-auto'>{(3 - this.state.attemptNumber) < 2 ? `${3 - this.state.attemptNumber} Attempts remaining` : ""}</div>
              </div>
              <Field type="text" name="otp" autoFocus placeholder='Enter the OTP' className='rounded-md h-10 pl-4 w-full' />
              <ErrorMessage name="otp" component="div" className='error-formik' />
            </div>





            {/* <div className='mt-1 mb-3 text-left text-sm flex flex-col md:flex-row text-sr-default-grey'>
              <div className='mx-1'> Can’t find it? check your spam folder or </div>
              {this.state.disableResendBtn ?
                <div className='flex'><a className='default-anchor px-1'>Resend email</a> {this.state.resendCounter > 0 && (3 - this.state.attemptNumber) > 0 ? `in ${this.state.resendCounter} seconds` : ''}</div> :
                (this.state.isResendLoading ? <p className='text-sr-default-grey px-1'>Resending ...</p>
                  : <a className='default-anchor px-1' onClick={this.resendVerificationEmail}>Resend email</a>)
              }
            </div> */}

            <div className='mt-1 mb-3 text-left font-noto text-[12px] flex flex-col md:flex-row text-sr-default-grey'>
              <div className='mx-1'>Can’t find it? check your spam folder or</div>
              <div className='flex'>
                {this.state.disableResendBtn ? (
                  <>
                    <span className='default-anchor px-1 '>Resend email</span>
                    {this.state.resendCounter > 0 && (3 - this.state.attemptNumber) > 0 ? ` in ${this.state.resendCounter} seconds` : ''}
                  </>
                ) : (
                  this.state.isResendLoading ? (
                    <p className='text-sr-default-grey px-1'>Resending ...</p>
                  ) : (
                    <a className='default-anchor px-1' onClick={this.resendVerificationEmail}>Resend email</a>
                  )
                )}
              </div>
            </div>

            {/* <div className='mt-1 mb-3 text-left font-noto text-[12px] flex flex-row items-center justify-between'>
              <div className='mx-1'>Can’t find it? check your spam folder or</div>
              <div className='flex items-center'>
                {this.state.disableResendBtn ? (
                  <>
                    <span className='default-anchor px-1'>Resend email</span>
                    {this.state.resendCounter > 0 && (3 - this.state.attemptNumber) > 0 ? ` in ${this.state.resendCounter} seconds` : ''}
                  </>
                ) : (
                  this.state.isResendLoading ? (
                    <p className='text-sr-default-grey px-1'>Resending ...</p>
                  ) : (
                    <a className='default-anchor px-1' onClick={this.resendVerificationEmail}>Resend email</a>
                  )
                )}
              </div>
            </div> */}

            {this.state.showCaptcha &&
              <div className={(this.state.showCaptchaError ? 'mb-5' : 'mb-4') + ' flex items-center justify-center'}>
                <ReCAPTCHA
                  sitekey={CONSTANTS.G_RECAPTCHA_SITE_KEY}
                  onChange={this.setGResponse}
                  ref={(e: any) => this.recaptchaInstance = e}
                />
                {this.state.showCaptchaError &&
                  <div className='error-formik'>Please validate Captcha</div>}
              </div>
            }

            {/* <div className='mb-2 flex flex-row'> */}
            <SRButtonFilled type="submit" text="Verify Email" disable={isSubmitting} loading={isSubmitting} isPrimary={true} className='!text-[16px] !font-noto !font-normal h-10 !bg-sr-grey-primary !text-white !px-16 !py-3' width='fluid'/>
            {/* </div> */}
          </Form>
        )}

      </Formik>
    )
  }
}

export const EmailVerification = (observer(EmailVerificationComponent));