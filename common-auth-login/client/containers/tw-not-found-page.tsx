import * as React from 'react';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import {  observer } from 'mobx-react';

interface ITWNotFoundPageProps extends RouteComponentProps<any> {
}

class TWNotFoundPageComponent extends React.Component<ITWNotFoundPageProps, {}> {

  constructor(props: any) {
    super(props);

  }


  componentWillReceiveProps(nextProps: any, prevProps: any) {
    console.log('not found page', arguments);
  }

  render() {
    return (
      <div className='flex justify-center items-center h-2/4'>
        <div>
          <h1 className='text-5xl'>Oops!</h1>
          <h2 className='text-3xl mb-2'>We can't seem to find the page you're looking for. </h2>
          <h2 className='mt-0'>
            Go to the <a  className="cursor-pointer">main site</a>
          </h2>
        </div>
      </div>
    );
  }
}

export const TWNotFoundPage = withRouter((
  observer(TWNotFoundPageComponent)
));
