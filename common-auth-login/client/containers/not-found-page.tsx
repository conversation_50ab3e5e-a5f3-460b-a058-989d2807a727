import * as React from 'react';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import {  observer } from 'mobx-react';

interface INotFoundPageProps extends RouteComponentProps<any> {
}

class NotFoundPageComponent extends React.Component<INotFoundPageProps, {}> {

  constructor(props: any) {
    super(props);

  }

  

  componentWillReceiveProps(nextProps: any, prevProps: any) {
    console.log('not found page', arguments);
  }

  render() {
    return (
      <div className='ui centered grid page-not-found'>
        <div className='eight wide column' style={{ marginTop: '70px' }}>
          <h1>Oops!</h1>
          <h2 className='ui header'>We can't seem to find the page you're looking for. </h2>
          <h2>
            Go to the <a  style={{ cursor: 'pointer' }}>main site</a>
          </h2>
        </div>
      </div>
    );
  }
}

export const NotFoundPage = withRouter((
  observer(NotFoundPageComponent)
));
