declare namespace LogIn {

  type IPlanName = 'standard' | 'ultimate' | 'custom' | 'pro' | 'inactive' | 'trial';

  interface ILogInStore {
    isLoggedIn: boolean;
    accountInfo: IAccount;
    gotoHomePageSection: string;
    toRegisterEmail: string;
    currentTeamId: number;
    redirectToLoginPage: boolean;
    isTeamAdmin: boolean;
    disableAnalytics: boolean;
    planType: IPlanType;
    showPricingModal: boolean;
    checkForUpgradePrompt: boolean;
    isLoggingOut: boolean;
    // urlToRetain: string;
    showFeed: boolean;
    showFeedBubble: boolean;
    isUpdateProspectModalOpen?: boolean;//to prevent closing of feed on action in update prospect modal

    getLogInStatus: boolean;
    getAccountInfo: IAccount;
    getCurrentTeamId: number;
    getRedirectToLoginPage: boolean;
    getCurrentTeamObj: LogIn.ITeam;
    getCurrentTeamMemberObj: LogIn.IMember;
    getIsTeamAdmin: boolean;
    isOrgOwner: boolean;
    roleIsOrgOwnerOrAgencyAdminForAgency: boolean;
    getPlanType: IPlanType;
    getShowPricingModal: boolean;
    getCheckForUpgradePrompt: boolean;
    getIsLoggingOut: boolean;
    // getUrlToRetain: string;
    getShowFeedStatus: boolean;
    getShowFeedBubbleStatus: boolean;
    getisUpdateProspectModalOpen: boolean;
    getTeamRolePermissions: IRole;

    logIn: (accountInfo: IAccount, disableAnalytics?: boolean, tid?: number) => void;
    logOut: () => void;
    notAuthenticated: () => void;
    changeRedirectToLoginPage: (flag: boolean) => void;
    updateAccountInfo: (accountInfo: IAccount) => void;
    updateGotoHomePageSection: (gotoHomePageSection: string) => void;
    updateToRegisterEmail: (toRegisterEmail: string) => void;
    updateCurrentTeamId: (newTeamId: number) => void;
    updateIsTeamAdmin: (flag: boolean) => void;
    updatePlanType: (planType: IPlanType) => void;
    updateShowPricingModal: (flag: boolean) => void;
    updateCheckForUpgradePrompt: (flag: boolean) => void;
    updateIsLoggingOut: (flag: boolean) => void;
    // updateUrlToRetain: (url: string) => void;
    updateShowFeedStatus: (flag: boolean) => void;
    updateShowFeedBubbleStatus: (flag: boolean) => void;
    updateIsUpdateProspectModalOpen: (flag: boolean) => void;

    featureFlagsObj: IFeatureFlags;
    updateFeatureFlagsObj: (flags: IFeatureFlags) => void;
    getFeatureFlagsObj: IFeatureFlags;
  }

  interface IFeatureFlags {
    ff_sending_holiday_calendar: boolean;
    ff_ab_testing: boolean;
    ff_reports_hot_prospect: boolean;
    ff_campaign_send_start_report: boolean
  }


  type IAccountType = 'individual' | 'team' | 'agency';
  type Notification = 'weekly' | 'never';

  interface IAccountProfile {
    first_name: string;
    last_name: string;
    company?: string;
    timezone: string;
    country_code?: string;
    mobile_country_code?: string;
    mobile_number?: number;
    twofa_enabled: boolean;
    weekly_report_emails?: string;
    onboarding_phone_number?: string;
  }
  interface AccountAccess {
    inbox_access: boolean
  }

  interface ITeamMetaData {
    inbox_v3_enabled: boolean
  }

  interface ICalendarAccountData {
    calendar_user_id?: number;
    default_cal_event_type_id?: number;
    default_cal_event_type_slug?: string;
    is_individual_calendar?: boolean;
    calendar_username_slug?: string;
    selected_calendar_team_id?: number;
    selected_calednar_team_slug?: string;
  }

  interface IAccount {
    // plan?: string;
    // trial_ends_in_days?: number;
    user_id: string;
    internal_id: number;
    account_type: IAccountType;
    org_role?: 'owner' | 'agency_admin';
    profile: IAccountProfile;
    company: string;
    timezone: string;
    created_at: string;
    email: string;
    first_name?: string;
    last_name?: string;
    teams: ITeam[];
    org: IOrg;
    email_notification_summary: Notification;
    intercom_hash: String;
    email_verified: boolean;
    account_metadata: IAccountMetaData;
    signupType?: 'google' | 'microsoft' | 'password';
    account_access: AccountAccess;
    calendar_account_data: ICalendarAccountData;
  }

  interface IAccountMetaData {
    is_profile_onboarding_done?: boolean;
  }

  type IPlanType = 'trial' | 'free' | 'paid' | 'inactive';
  type IOnboardingStep = "about_company" | "about_me" | "demo_schedule" | "done";

  interface IOrgMetaData {
    is_custom_plan?: boolean;
    show_promo_option?: boolean;
    ff_multichannel?: boolean;
    allow_user_level_api_key?: boolean;
    can_download_report_with_replies?: boolean;
    is_onboarding_done?: boolean;
    show_agency_pricing?: boolean;
    show_business_plans?: boolean;
    show_business_standard_plan?: boolean;
    show_business_pro_plan?: boolean;
    enable_native_calling?: boolean;
    show_new_reports?: boolean;
    enable_campaign_ai_sequence?: boolean

    show_agency_admin_role?: boolean;
    enable_calendar?: boolean;

    show_individual_plans?: boolean;

    enable_content_ai?: boolean;
    enable_linkedin_automation?: boolean;

    ff_autoreply_rescheduling?: boolean;
    show_campaign_tags?: boolean;
    limit_on_prospect_accounts?: boolean;
    // ff_holiday_calendar?: boolean;
    ff_emails_sent_report?: boolean;
    show_campaign_send_start_report?: boolean
    allowed_for_new_google_api_key?: boolean
    enable_warmup_box?: boolean;
    show_referral_program?: boolean;
  }

  interface IOrg {
    id: number;
    name: string;

    counts: {
      current_sending_email_accounts: number;
      current_prospect_sent_count_org: number;
      total_sending_email_accounts: number;
      max_prospect_limit_org: number;

      additional_licence_count: number;
      additional_spam_tests: number;
      base_licence_count: number;
    };

    plan: {
      is_business_plan: boolean;
      plan_id: IPlanName;
      plan_name: string;
      plan_type: IPlanType;

      fs_account_id?: string;
      //fs_lookup_id?: string;
      stripe_customer_id?: boolean;

      payment_gateway: string;
      payment_due_invoice_link?: string;
      payment_due_campaign_pause_at?: number;

    };


    trial_ends_at?: number;

    error: string;
    paused_till: Date;
    error_code: string;
    is_agency: boolean;
    owner_account_id: number;


    warnings: Alerts.IWarningBanner[];
    errors: Alerts.IErrorBanner[];

    settings: {
      // enable_ab_testing: boolean;
      allow_2fa: boolean;
      show_2fa_setting: boolean;
      enforce_2fa: boolean;
      allow_native_crm_integration: boolean;
    }
    // errors: Alerts.IWarningBanner[]; - not used currently
    org_metadata: IOrgMetaData;
  }

  interface ITeam {
    access_members: IMember[];
    all_members: ITeamMember[];
    team_id: number;
    team_name: string;
    total_members: number;
    org_id: number | string;
    active: boolean;
    role: IRole;

    // team_metadata: ITeamMetaData // 12 Aug 2023: It was not being used, so removed it.

    prospect_categories_custom: IProspectCategory[];
    max_emails_per_prospect_per_day: number;
    max_emails_per_prospect_per_week: number;
    max_emails_per_prospect_account_per_day: number;
    max_emails_per_prospect_account_per_week: number;
    // allow_assigning_prospects_to_multiple_campaigns: boolean;
    reply_handling: 'pause_all_campaigns' | 'pause_specific_campaign';
    //tp_integrations: ITPIntegrations;
  }

  interface IRole {
    name: 'owner' | 'admin' | 'member';
    permissions: IPermissions;
  }

  type IPermissionOwnerShip = 'owned' | 'all' | 'no_access'

  interface IPermissions {
    just_loggedin: IPermissionV2;
    zapier_access: IPermissionV2;

    manage_billing: IPermissionV2;

    view_user_management: IPermissionV2;//user management & roles
    edit_user_management: IPermissionV2;

    view_prospects: IPermissionV2;
    edit_prospects: IPermissionV2;
    delete_prospects: IPermissionV2;

    view_campaigns: IPermissionV2;
    edit_campaigns: IPermissionV2;
    delete_campaigns: IPermissionV2;
    change_campaign_status: IPermissionV2;

    view_reports: IPermissionV2;
    edit_reports: IPermissionV2;
    download_reports: IPermissionV2;

    view_inbox: IPermissionV2;
    edit_inbox: IPermissionV2;
    send_manual_email: IPermissionV2;

    view_templates: IPermissionV2;
    edit_templates: IPermissionV2;
    delete_templates: IPermissionV2;

    view_blacklist: IPermissionV2;
    edit_blacklist: IPermissionV2;

    view_workflows: IPermissionV2;
    edit_workflows: IPermissionV2;

    view_prospect_accounts: IPermissionV2;
    edit_prospect_accounts: IPermissionV2;

    view_email_accounts: IPermissionV2;
    edit_email_accounts: IPermissionV2;
    delete_email_accounts: IPermissionV2;

    view_linkedin_accounts: IPermissionV2;
    edit_linkedin_accounts: IPermissionV2;
    delete_linkedin_accounts: IPermissionV2;

    view_whatsapp_accounts: IPermissionV2;
    edit_whatsapp_accounts: IPermissionV2;
    delete_whatsapp_accounts: IPermissionV2;

    view_sms_accounts: IPermissionV2;
    edit_sms_accounts: IPermissionV2;
    delete_sms_accounts: IPermissionV2;

    view_team_config: IPermissionV2;//pros cat & sending limit
    edit_team_config: IPermissionV2;

    view_tasks: IPermissionV2;
    edit_tasks: IPermissionV2;
    delete_tasks: IPermissionV2;
  }

  type SrResourceTypes =
    "prospect" |
    "campaign" |
    "inbox" |
    "report" |
    "template" |
    "workflow" |
    "webhook" |
    "prospect_account" |
    "email_account" |
    "blacklist" |
    "user" |
    "team" |
    "task" |
    "other";

  type PermissionLevel =
    "view" |
    "edit" |
    "delete" |
    "other";

  type PermissionType =
    "just_loggedin" |
    "zapier_access" |
    "view_user_management" |
    "edit_user_management" |
    "manage_billing" |
    "view_prospects" |
    "edit_prospects" |
    "delete_prospects" |
    "view_campaigns" |
    "edit_campaigns" |
    "delete_campaigns" |
    "change_campaign_status" |
    "view_reports" |
    "edit_reports" |
    "download_reports" |
    "view_inbox" |
    "edit_inbox" |
    "send_manual_email" |
    "view_templates" |
    "edit_templates" |
    "delete_templates" |
    "view_blacklist" |
    "edit_blacklist" |
    "view_workflows" |
    "edit_workflows" |
    "view_prospect_accounts" |
    "edit_prospect_accounts" |
    "view_email_accounts" |
    "edit_email_accounts" |
    "delete_email_accounts" |
    "view_team_config" |
    "edit_team_config" |
    "view_tasks" |
    "edit_tasks" |
    "delete_tasks"
    ;

  interface IPermissionV2 {
    ownership: IPermissionOwnerShip;
    entity: SrResourceTypes;
    permissionLevel: PermissionLevel;
    permissionType: PermissionType
    version: 'v2'
  }

  interface IMember {
    user_id: number;
    email: string;
    first_name: string;
    last_name: string;
    team_role: 'admin' | 'member' | '' // for invite section;
  }

  interface ITeamMember {
    user_id: number;
    email: string;
    first_name: string;
    last_name: string;
  }

  interface IProspectCategory {
    id: number;
    name: string;
    text_id: string;
    label_color: string;
    is_custom: boolean;
    team_id: number;
  }

  //agency related





  interface ITPIntegrations {
    [hubspot: string]: boolean;
    pipedrive: boolean;
    zoho: boolean;
    zoho_recruit: boolean;
    salesforce: boolean;
  }
  //
  type IFeatureName = 'Email Automation' | 'Email Deliverability' | 'Inbox 360' | 'Smart Pause' | 'Prospect Management' | 'Team Collaboration' | 'API & Integrations' | 'Detailed Analytics';

  type IAPIKeyType = 'user' | 'team_user' | 'prospectdaddy' | 'warmupbox';



  // getInviteEmailByInviteCode api response
  interface IInvitedMemberDetail {
    email: string;
    org_name: string;
    team_name: string;
    inviter_name: string;
    first_name: string;
    last_name: string;
  }


}



