import * as React from 'react';
import { with<PERSON><PERSON><PERSON>, RouteComponentProps, Route, Switch } from 'react-router-dom';
import { inject, observer } from 'mobx-react';
import * as authApi from '../api/auth';
import { SrPageCenter, SrSpinner } from '@sr/design-component-lite';
import { Toastr } from '@sr/design-component-lite';

import { SRRedirect } from '../components/helpers';
// import { RegisterV2 } from './login/register-page-v2';
import { OAuthRedirect } from './login/oauth-redirect';

import { LogInV2 } from './login/login-page-v2';


import { Consent } from './login/consent-page';
import { Logout } from './login/logout-page';
import { LogoutCallback } from './login/logout-callback';
import { RegisterV3 } from './login/register-page-v3';







interface IAppEntryProps extends RouteComponentProps<any> {
  alertStore: Alerts.IAlertStore;
}

interface IAppEntryStates {
  isLoading: boolean;
}

class AppEntry extends React.Component<IAppEntryProps, IAppEntryStates> {

  constructor(props: IAppEntryProps) {
    super(props);
    this.state = {
      isLoading: true
    };
  }

  componentDidMount() {
    console.log("hello app CDMOUNT entry: ", this.props.location, this.props.match)

     authApi.authenticate()
      .then((response) => {



        this.setState({ isLoading: false });

      })
      .catch((err) => {
        console.log('authenticate fail: ', err);
        this.setState({ isLoading: false });
      });
  }


  render() {
    const {  alertStore } = this.props;

    const isLoading = this.state.isLoading;
    const alert = alertStore.getAlerts; //FIXME

    const queryParams = new URLSearchParams(this.props.location.search);
    const isRegister = queryParams.get('type') === 'register';



    // const user_name_email = getUserNameAndEmail(logInStore.accountInfo);

    console.log('APP-ENTRY RENDER', this.props.location.pathname, this.props.match);

    console.log("DOUBLECODECALL APP-ENTRY RENDER")
    return (
      <div  className="app-container">

        {/* routeKey is important because all components must be re-mounted on aid, tid change */}
        <Toastr alert={alert} />
        {/* outside feedsidebar so that toastr is visible on top of modals */}
        {isLoading ? (
          <SrPageCenter>
            < SrSpinner />
          </SrPageCenter>
          ) : (
          <div className="app-contents">
            {(
              <div className="logged-out-app">
                {/* Routes for logged out app */}
                <Switch>
                  {/* <Route exact path='/login' component={LogIn} /> */}
                  {/* <Route exact path='/register' component={Register} /> */}

                  {isRegister && <Route exact path="/login" component={RegisterV3} />}

                  <Route exact path="/login" component={LogInV2} />

                  <Route exact path="/logout" component={Logout} />
                  <Route
                    exact
                    path="/logout-callback"
                    component={LogoutCallback}
                  />
                  <Route exact path="/consent" component={Consent} />
                  <Route
                    exact
                    path="/auth/oauth-redirect/:signupType"
                    component={OAuthRedirect}
                  />
                  <SRRedirect exact from="/" to={"/login"} />
                  <SRRedirect from="*" to={"/login"} />
                </Switch>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

}

export default withRouter(inject( 'alertStore')(observer(AppEntry)));
