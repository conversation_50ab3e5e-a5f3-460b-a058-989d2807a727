import * as React from 'react';
import * as authApi from '../../api/auth';
import { observer } from 'mobx-react';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>onFilled, SrSpinner } from '@sr/design-component-lite';
import { CONSTANTS } from '../../data/constants';
import ReCAPTCHA from "react-google-recaptcha";
import { Formik, Form as FormFormik, Field, ErrorMessage, FormikHelpers } from 'formik';
import { newPasswordValidation } from '../../utils/validations';

interface ResetPasswordPreLoginProps extends RouteComponentProps<any> {
  email: string;
  closeResetPassword: () => void;
  attemptNumber: number
}

interface ResetPasswordPreLoginStates {
  isLoading?: boolean;
  g_response?: string;
  showCaptchaError: boolean;
  showCaptcha: boolean; //flag used to overcome the page breaking because of using reCaptcha in multiple places
  attemptNumber: number;
  disableResendBtn: boolean;
  resendCounter: number;
}

interface IResetPwdFormFields {
  password: string;
  confirm_password: string;
  otp: string;
}

class ResetPasswordPreLoginComponent extends React.Component<ResetPasswordPreLoginProps, ResetPasswordPreLoginStates> {
  recaptchaInstance: any; //This is used to reload the recaptcha using ref
  constructor(props: ResetPasswordPreLoginProps) {
    super(props);
    this.state = {
      isLoading: false,
      showCaptchaError: false,
      showCaptcha: false,
      attemptNumber: this.props.attemptNumber,
      disableResendBtn: true,
      resendCounter: 30,
    };
    this.setGResponse = this.setGResponse.bind(this);
    this.handleSubmitFormik = this.handleSubmitFormik.bind(this);
    this.resendOTPEmail = this.resendOTPEmail.bind(this);
    this.startResendCounter = this.startResendCounter.bind(this);
  }

  startResendCounter() {
    const interval = setInterval(() => {

      const counter = this.state.resendCounter;

      if (counter > 0) {
        this.setState({ resendCounter: counter - 1 });
      } else {
        this.setState({ disableResendBtn: false });
        clearInterval(interval);
      }

    }, 1000)
  }
  setGResponse(g_response: string) {
    this.setState({ g_response: g_response, showCaptchaError: false })
  }
  resetRecaptcha() {
    this.recaptchaInstance.reset();
  };

  handleSubmitFormik(values: IResetPwdFormFields, { setSubmitting }: FormikHelpers<IResetPwdFormFields>) {
    setSubmitting(true);
    if (!this.state.g_response) {
      this.setState({ isLoading: false, showCaptchaError: true });
      console.log('rp 1');
    } else {
      const data = { password: values.password, code: values.otp, g_response: this.state.g_response, email: this.props.email };
      authApi.updatePassword(data).then(res => {
        setSubmitting(false);
        this.props.closeResetPassword();
      }).catch(err => {
        setSubmitting(false);
        this.resetRecaptcha();

      });
    }
  }
  componentDidMount() {
    setTimeout(() => { this.setState({ showCaptcha: true }) }, 1000)
    this.startResendCounter();

  }

  resendOTPEmail() {
    if (!this.state.g_response) {
      this.setState({ showCaptchaError: true })
    }
    else {
      const data = {
        email: this.props.email,
        g_response: this.state.g_response
      }
      authApi.forgotPassword(data)
        .then((res) => {
          this.setState({ attemptNumber: res.data.attemptNumber })
          this.resetRecaptcha()
          this.setState({ resendCounter: 30, disableResendBtn: true, g_response: undefined }, () => {
            this.startResendCounter();
          })
        })
        .catch((err: any) => {
          this.resetRecaptcha()
          this.setState({ resendCounter: 30, disableResendBtn: true, g_response: undefined }, () => {
            this.startResendCounter();
          })
        });
    }
  }

  getInitialResetPwdFormValues() {
    const initialValues: IResetPwdFormFields = {
      password: '',
      confirm_password: '',
      otp: ''
    }
    return initialValues;
  }

  validateResetPwdFormFormik(values: IResetPwdFormFields) {
    let errors = {} as IResetPwdFormFields;

    if (!values.password) {
      errors.password = 'Please enter your password';
    } else {
      let passwordError = newPasswordValidation(values.password)

      if (passwordError) errors.password = passwordError

    }

    if (values.confirm_password === '') {
      errors.confirm_password = 'Enter password';
    } else if (values.password !== values.confirm_password) {
      errors.confirm_password = 'Please enter same password';
    }
    if (values.otp === '') {
      errors.otp = 'Enter OTP';
    } else if (values.otp.length != 6) {
      errors.otp = 'Please enter the 6 digit otp';
    } else if (!values.otp.match("^[0-9]*$")) {
      errors.otp = 'OTP can only have digits';
    }

    return errors;

  }

  render() {
    const isLoading = this.state.isLoading;

    return (
      <>
        <div className='login-page'>
          {
            isLoading && <SrSpinner spinnerTitle='logging in ..' />
          }
          {
            !isLoading &&
            <div className='sm:mx-auto sm:w-full sm:max-w-md'>
              <div className='mt-10'>
                <h2 className='font-bold pb-4 border-b-0'>Reset Password</h2>
                <Formik
                  initialValues={this.getInitialResetPwdFormValues()}
                  validate={this.validateResetPwdFormFormik}
                  onSubmit={this.handleSubmitFormik}
                >
                  {({ isSubmitting }) => (
                    <FormFormik>

                      <div className='mb-6'>
                        <label className='label-formik' htmlFor='password'>Password</label>
                        <Field type="password" autoFocus name="password" placeholder='Enter your new password' className='input-formik w-full' />
                        <ErrorMessage name="password" component="div" className='error-formik' />
                      </div>

                      <div className='mb-6'>
                        <label className='label-formik' htmlFor='password'>Confirm Password</label>
                        <Field type="password" autoFocus name="confirm_password" placeholder='Re-enter your new password' className='input-formik w-full' />
                        <ErrorMessage name="confirm_password" component="div" className='error-formik' />
                      </div>
                      <div className='mb-6'>
                        <div className='flex flex-row'>
                          <label className='label-formik ' htmlFor='otp'>OTP</label>
                          <div className='text-sm ml-auto'>{(3 - this.state.attemptNumber) < 2 ? `${3 - this.state.attemptNumber} Attempts remaining` : ""}</div>
                        </div>
                        <Field type="text" name="otp" placeholder='Enter the OTP' className='input-formik w-full' />
                        <ErrorMessage name="otp" component="div" className='error-formik' />
                      </div>

                      {this.state.showCaptcha &&
                        <div className='mb-6 flex items-center justify-center'>
                          <ReCAPTCHA
                            id='resetPasswordPagePreLoginRecaptcha'
                            sitekey={CONSTANTS.G_RECAPTCHA_SITE_KEY}
                            onChange={this.setGResponse}
                            ref={(e: any) => this.recaptchaInstance = e}
                          />
                          {this.state.showCaptchaError &&
                            <div className='error-formik'>Please validate Captcha</div>}
                        </div>}
                      <div className='text-sm ml-auto'>(Verify the ReCaptcha before resending OTP)</div>
                      <div className='mb-6 flex flex-row'>
                        <button
                          className="button-formik-primary-outline w-full mr-10"
                          type='button'
                          onClick={this.resendOTPEmail}
                          disabled={this.state.disableResendBtn || (3 - this.state.attemptNumber) < 1}
                        >
                          Resend OTP
                          {this.state.resendCounter > 0 && (3 - this.state.attemptNumber) > 0 ? `(${this.state.resendCounter})` : ''}
                        </button>

                        <SRButtonFilled type="submit" text="Reset" disable={isSubmitting} loading={isSubmitting} isPrimary={true} className='!w-full !text-[16px] !font-noto !font-normal h-10 !bg-sr-grey-primary !text-white' />
                      </div>
                    </FormFormik>
                  )}

                </Formik>

              </div>
            </div>
          }
        </div>
      </>
    );
  }
}

export const ResetPasswordPreLogin = withRouter((observer(ResetPasswordPreLoginComponent)));