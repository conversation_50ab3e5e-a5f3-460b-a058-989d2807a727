import * as React from 'react';
import * as authApi from '../../api/auth';
import {  observer } from 'mobx-react';
import { RouteComponentProps } from 'react-router-dom';
import { SrSpinner } from '@sr/design-component-lite';
import { getOAuthRedirect, removeOAuthRedirect } from '../../utils/localStorage';

interface IVerifyEmailProps extends RouteComponentProps<any> {
  
}

interface IVerifyEmailStates {
  isLoading?: boolean;
  statusMessage?: string;
}

class VerifyEmailComponent extends React.Component<IVerifyEmailProps, IVerifyEmailStates> {

  constructor(props: IVerifyEmailProps) {
    super(props);
    this.state = {
      isLoading: false,
      statusMessage: ''
    };

    this.verifyEmail = this.verifyEmail.bind(this)
  }

  verifyEmail() {
    
    const redirectUrl = getOAuthRedirect();
    
      this.setState({ isLoading: true });
      const data = { code: this.props.match.params.code , login_challenge: this.props.match.params.login_challenge};

      authApi.verifyEmail(data).then(res => {

      

        console.log("redirectUrl", redirectUrl)
        if (!!redirectUrl) {
          this.props.history.push({
            pathname: redirectUrl.pathName,
            search: redirectUrl.search,
          });
          removeOAuthRedirect();
        } else {
          this.props.history.push({
            pathname: '/dashboard/campaigns'
          });
        }

      }).catch(err => {

        this.setState({ isLoading: false, statusMessage: err.message });
        setTimeout(() => {
          this.props.history.push('/login');
        }, 5000);
      });
    

  }


  componentDidMount() {
    this.verifyEmail()
  }


  render() {
    const isLoading = this.state.isLoading || true;

    return (

      <div className='app-container'>
        <div className='app-contents'>
          <div className='sm:mx-auto sm:w-full sm:max-w-md'>
            <div className='mt-20'>
              {
                isLoading &&
                <div>
                  <h2 className='font-bold'>Verifying email</h2>
                  <SrSpinner spinnerTitle='Verifying email ..' />
                </div>
              }

              {!isLoading &&
                <h2 className='font-bold'>{this.state.statusMessage}</h2>
              }
            </div>
          </div>
        </div>
      </div>

      // <div style={{ textAlign: 'center', marginTop: '8em' }} className='ui centered grid'>
      //   {
      //     isLoading &&
      //     <div>
      //       <h2 className='center'>Verifying email</h2>
      //       <Spinner spinnerTitle='Verifying email ..' />
      //     </div>
      //   }

      //   {!isLoading &&
      //     <h2 className='center'>{this.state.statusMessage}</h2>
      //   }
      // </div>
    );
  }
}
export const VerifyEmail = (observer(VerifyEmailComponent));
