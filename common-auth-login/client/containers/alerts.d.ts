declare namespace Alerts {
  type IAlertType = 'success' | 'error' | 'warning' | 'info';
  type IWarningCode = 'prospect_limit_80_percent' | 'prospect_limit_exceeded' | 'low_calling_credit' | 'calling_feature_suspended';

  interface IAlert {
    status: IAlertType;
    message: string | number;
  }

  interface IBannerAlert extends IAlert {
    id: number | string;
    canClose: boolean;
  }

  interface IWarningBanner {
    warning_at: number;
    warning_code: IWarningCode;
    warning_msg: string;
    upgrade_now_prompt: boolean;
    add_call_credit_button: boolean;
    new_prospects_paused_till?: number;
  }

  interface IErrorBanner {
    error_at: number;
    error_code: string;
    error_msg: string;
    upgrade_now_prompt: boolean;
  }

  interface INotificationAlert {
    notificationType: 'info' | 'success' | 'error' | 'in_progress';
    title: string;
    description: any;
    notificationEventType: string; // make as enum when more notifs are added
    redirectUrl?: string;
  }

  interface IAlertStore {
    alert: IAlert;
    pushAlert: (newAlert: Alerts.IAlert) => void;
    getAlerts: IAlert;
    resetAlerts: any;
  }
}