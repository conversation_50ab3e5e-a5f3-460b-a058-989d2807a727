import { ComponentType, lazy } from "react";
import { reload } from "../utils/redirection";

// ref: https://raphael-leger.medium.com/react-webpack-chunkloaderror-loading-chunk-x-failed-ac385bd110e0

export function lazyWithRetry<T extends ComponentType<any>>(
  componentImport: () => Promise<{ default: T }>
) {
  return lazy(async (): Promise<{ default: T }> => {
    try {
      const component = await componentImport();

      window.sessionStorage.setItem("page-has-been-force-refreshed", "false");

      return component;
    } catch (error) {
      const pageHasAlreadyBeenForceRefreshed = JSON.parse(
        window.sessionStorage.getItem("page-has-been-force-refreshed") || "false"
      );

      if (!pageHasAlreadyBeenForceRefreshed) {
        // Assuming that the user is not on the latest version of the application.
        // Let's refresh the page immediately.
        window.sessionStorage.setItem("page-has-been-force-refreshed", "true");
        reload()
      }

      // The page has already been reloaded
      // Assuming that user is already using the latest version of the application.
      // Let's let the application crash and raise the error.
      throw error;
    }
  });
}
