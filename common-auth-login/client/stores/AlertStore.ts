import { observable, action, computed, toJS, makeObservable } from 'mobx';


class AlertStore implements Alerts.IAlertStore {
  initialAlerts = {} as Alerts.IAlert;

  alert = this.initialAlerts;

  pushAlert = (newAlert: Alerts.IAlert) => {
    this.alert = newAlert;
    setTimeout(() => {
      this.resetAlerts();
    }, 50);
  };

  resetAlerts = () => {
    this.alert = this.initialAlerts;
  };

  constructor() {
    makeObservable(this, {
      alert: observable,
      pushAlert: action,
      resetAlerts: action,
      // removeNotificationAlert: action,
      getAlerts: computed
    });
  }

  get getAlerts() {
    const alert = toJS(this.alert);
    return alert;
  }
};

export const alertStore = new AlertStore();
