import * as React from 'react';
import * as ReactDOM from 'react-dom';
import { Provider } from 'mobx-react';

import { BrowserRouter } from 'react-router-dom';
import routes from './routes';
import { CONSTANTS } from './data/constants';

import 'es6-promise/auto';

import './new-styles/tailwind.css';


// configure mobx stores and pass them to the Provider wrapper component
import { alertStore } from './stores/AlertStore';
import { initializeSentry } from './thirdparty-integrations/sentry';
import  {ErrorBoundary} from '@sentry/react';
const stores = {  alertStore };

(window as any).__webpack_public_path__ = process.env.REACT_APP_ASSET_PATH || '/assets/'
// enable sentry only in production
if (CONSTANTS.IS_PRODUCTION) {

  initializeSentry();

}


let rootElement = document.getElementById('root')
rootElement?.classList.remove("loader")

ReactDOM.render( 
    <Provider {...stores}>
      <ErrorBoundary showDialog={true}>
          {/* <div style={{minHeight:'100%', height:'100%'}} > */}
          <div className='index-container'>
            <BrowserRouter>
              {routes}
            </BrowserRouter>
          </div>
      </ErrorBoundary>
      </Provider> as React.ReactElement<any>,
      rootElement
);
