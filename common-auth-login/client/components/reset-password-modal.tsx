import * as React from 'react';
// import { <PERSON><PERSON>, Mo<PERSON> } from 'semantic-ui-react';
// import { Form } from './form';
// import { InputField } from './form/field-types';
import { validateEmail } from '../utils/validations';
import { SrModalDefault } from '@sr/design-component-lite';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as authApi from '../api/auth';
import { CONSTANTS } from '../data/constants';
import ReCAPTCHA from "react-google-recaptcha";


interface IResetPasswordModalProps {
  onClose: () => void,
  email: string,
  setAttemptNumber: (count: number) => void
}

interface IResetPasswordFormFields {
  email: string;
}
interface IResetPasswordModalState {
  g_response?: string;
  showCaptchaError: boolean;
  showCaptcha: boolean; //flag used to overcome the page breaking because of using reCaptcha in multiple places
}

export class ResetPasswordModal extends React.Component<IResetPasswordModalProps, IResetPasswordModalState>{
  recaptchaInstance: any; //This is used to reload the recaptcha using ref
  constructor(props: IResetPasswordModalProps) {
    super(props);
    this.state = {
      showCaptchaError: false,
      showCaptcha: false

    };
    this.submitForm = this.submitForm.bind(this);
    this.setGResponse = this.setGResponse.bind(this);
  }


  validateForm(values: IResetPasswordFormFields) {
    let errors = {} as IResetPasswordFormFields;
    if (!values['email']) {
      errors['email'] = 'Please enter your email';
    } else if (!validateEmail(values['email'])) {
      errors['email'] = 'Enter a valid email';
    }
    return errors;
  }
  componentDidMount() {
    setTimeout(() => { this.setState({ showCaptcha: true }) }, 1000)
  }

  getInitialFormValues() {
    const initialValues: IResetPasswordFormFields = {
      email: this.props.email
    };
    return initialValues;
  }
  setGResponse(response: string) {
    this.setState({ g_response: response })
  }
  resetRecaptcha() {
    this.recaptchaInstance.reset();
  };

  submitForm(values: IResetPasswordFormFields, { setSubmitting }: FormikHelpers<IResetPasswordFormFields>) {
    if (!this.state.g_response) {
      this.setState({ showCaptchaError: true })
      setSubmitting(false);

    } else {
      const data = {
        email: values.email,
        g_response: this.state.g_response
      }
      authApi.forgotPassword(data)
        .then((res) => {
          setSubmitting(false);
          this.props.setAttemptNumber(res.data.attemptNumber)
          this.props.onClose();
        })
        .catch((err: any) => {
          this.resetRecaptcha();
          setSubmitting(false);
        });
    }
  }

  render() {
    return (

      <SrModalDefault onClose={this.props.onClose} heading={'Reset password'}>
        <Formik
          initialValues={this.getInitialFormValues()}
          validate={this.validateForm}
          onSubmit={this.submitForm}
        >
          {({ isSubmitting }) => (
            <Form>
              <div className='mb-6'>
                <label className='label-formik' htmlFor='email'>Email</label>
                <Field autoFocus type="email" name="email" placeholder='<EMAIL>' className='input-formik w-full' disabled={true} />
                <ErrorMessage name="email" component="div" className='error-formik' />
              </div>

              <ul className='list-disc list-inside'>
                <li className='mb-4'>If this email is registered with SmartReach then mail containing password reset link will be sent.</li>
              </ul>
              {this.state.showCaptcha &&
                <div className='mb-6'>
                  <ReCAPTCHA
                    id='resetPasswordModalRecaptcha'
                    sitekey={CONSTANTS.G_RECAPTCHA_SITE_KEY}
                    onChange={this.setGResponse}
                    ref={(e: any) => this.recaptchaInstance = e}
                  />
                  {this.state.showCaptchaError &&
                    <div className='error-formik'>Please validate Captcha</div>}
                </div>}
              <button type="submit" disabled={isSubmitting} className="button-formik-primary ml-auto mt-8">
                Send
              </button>

            </Form>
          )}

        </Formik>
      </SrModalDefault>
      // <Modal size='small' open={true} closeIcon='close' onClose={this.props.onClose}>
      //   <Modal.Header className='modal-header'>Reset password</Modal.Header>
      //   <Modal.Content>
      //     <Form id='resetpassword' onSubmit={this.props.onSubmit}
      //       validateDefs={this.validateDefs.bind(this)}>
      //       <InputField
      //         type='text'
      //         name='email'
      //         label='Registered email' />
      //       <ul>
      //         <li>Mail containing password reset link will be sent to this email address.</li>
      //       </ul>
      //       <br />
      //       <div className='ui grid'>x
      //         <div className='sixteen wide column'>
      //           <Button type='submit' size='small' primary
      //             form='resetpassword' icon='mail' labelPosition='right' content='Send'
      //             loading={this.props.isSendigResetPwdLink || false}
      //             className='right floated left aligned' /></div></div>
      //     </Form>
      //   </Modal.Content>
      // </Modal>

    );
  }
}