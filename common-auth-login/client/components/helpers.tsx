{/*NOTE: <SRRedirect>< reatains query params unless <Redirect> */ }

import * as React from 'react';
import {  Redirect, RedirectProps } from 'react-router-dom';
import { observer,  } from 'mobx-react';
import * as queryString from 'query-string';
// import { RouteComponentProps, with<PERSON>outer } from 'react-router-dom';





interface ISRRedirectProps extends RedirectProps {
  from: string;
  to: string;
  className?: string;
}

export const SRRedirect = (observer(class SRRedirect extends React.Component<ISRRedirectProps, any> {
  render() {

    const { children, from, to, ...props } = this.props;

    const urlSplit = to.split('?');

    const baseUrl = urlSplit[0];
    const queryParamsString = (urlSplit.length > 1) ? urlSplit[1] : '';

    //console.log("team inbox: from, to, baseUrl, queryParam", from, to, baseUrl, queryParamsString);

    const queryParams = queryString.parse(queryParamsString);


    return (

      <Redirect {...props} exact={this.props.exact} from={from} to={{
        pathname: baseUrl,
        search: queryString.stringify({
          ...queryParams,
        })
      }} />
    )
  }
}));
