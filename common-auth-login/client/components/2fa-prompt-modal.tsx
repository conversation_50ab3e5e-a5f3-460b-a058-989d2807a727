import * as React from 'react';
import { twoFactorAuth } from '../api/auth';
import { SrSpinner } from '@sr/design-component-lite';
import {QRCodeSVG} from 'qrcode.react';
import { SrModalDefault } from '@sr/design-component-lite';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as queryString from 'query-string';
import * as newAuthApi from '../api/newAuth'
import { redirectTo, reload } from '../utils/redirection';



interface I2FAFormFields {
  verification_code: string;
}

interface ITwoFactoAuthModalProps {
  accountId: number;
  accountEmail: string;
  verstate: string;

  initialForm2FAType: 'enable_2fa' | 'verify_2fa';

  onClose: () => void;
  state ?:string

}

interface ITwoFactoAuthModalState {
  form2FAType: 'enable_2fa' | 'verify_2fa';
  isModalLoading: boolean;
  verification_code?: string;
  error?: string;

  gkey?: string; // google auth key
  login_challenge?: string;
}

export class TwoFactorAuthModal extends React.Component<ITwoFactoAuthModalProps, ITwoFactoAuthModalState>{

  constructor(props: ITwoFactoAuthModalProps) {
    super(props);

    this.state = {
      form2FAType: props.initialForm2FAType,
      isModalLoading: false,
      verification_code: undefined,
      login_challenge: undefined
    };

    this.showEnableGAuth = this.showEnableGAuth.bind(this)
    this.validate2FAForm = this.validate2FAForm.bind(this);
    this.twoFASubmit = this.twoFASubmit.bind(this);
  }

  validate2FAForm(values: I2FAFormFields) {
    const verification_code = values.verification_code;
    let errors = {} as I2FAFormFields;

    if (!verification_code) {
      errors.verification_code = 'Please enter your verification code';
    } else if (verification_code.length !== 6) {
      errors.verification_code = 'Verfication code length must be 6 characters';
    }

    return errors;

  }

  getInitial2FAFormValues() {
    const initialValues: I2FAFormFields = {
      verification_code: ''
    }
    return initialValues;
  }

  componentDidMount() {
    const p = this.props;

    const queryParams = queryString.parse(window.location.search)
    console.log(queryParams)
    const loginChallengeQueryParam  = queryParams.login_challenge

    console.log("Logging state from props")
    console.log(this.props.state)
    if(loginChallengeQueryParam){
      console.log("Login_challenge parameter in 2fa model")
      console.log(loginChallengeQueryParam)
      this.setState({login_challenge: loginChallengeQueryParam as string || undefined})

    }else{
      newAuthApi
      .loginChallengeFromState({state : this.props.state})
      .then(res => {
        if(res.data.login_challenge){
          this.setState({login_challenge: res.data.login_challenge})
        }else{
          // reloading the page as login challenge is not found
          console.log("login challenge is missing")
          reload()
        }
      })
    }

   

    if (p.initialForm2FAType === 'enable_2fa') {

      this.showEnableGAuth();

    }
  }

  showEnableGAuth() {

    this.setState({
      isModalLoading: true,
      error: undefined,
    });
    const p = this.props;

    twoFactorAuth('gengauth', {
      aid: p.accountId,
      verstate: p.verstate,
      is_enable_2fa_flow: p.initialForm2FAType === 'enable_2fa',
      two_fa_type: 'gauth'

    })
      .then(res => {

        this.setState({ gkey: res.data.gkey, isModalLoading: false })

      })
      .catch(err => {
        this.setState({ error: err.message, isModalLoading: false })
      })
  }

  twoFASubmit(values: I2FAFormFields, { setSubmitting }: FormikHelpers<I2FAFormFields>) {
    const s = this.state;
    const p = this.props;

    if ((s.form2FAType === 'verify_2fa') || (s.form2FAType === 'enable_2fa')) {

      this.setState({ error: undefined });

      twoFactorAuth('verify', {
        aid: p.accountId,
        code: parseInt(values.verification_code),
        verstate: p.verstate,
        is_enable_2fa_flow: p.initialForm2FAType === 'enable_2fa',
        two_fa_type: 'gauth',
        gkey: s.gkey ,// for gauth flow,
        login_challenge: this.state.login_challenge
      })
        .then(res => {

          // verify api will also login the user
          // reloading the page will redirect the user to the right dashboard
          setSubmitting(false);
          if(res.data.redirect_to){
            console.log("going to the consent page")
            redirectTo( res.data.redirect_to)
          } else{
            console.log("reloading the page")
            reload()
          }

        })
        .catch(err => {
          console.error("verify2fa: ", err);
          setSubmitting(false);
          this.setState({ error: err.message });
        })
    }

  }

  render() {

    const {
      form2FAType,
      isModalLoading,
    } = this.state;

    return (

      <SrModalDefault
        onClose={this.props.onClose}
        heading={

          (form2FAType === 'enable_2fa')

            ? `Setup 2FA via Google Authenticator`

            : `Enter Google Authenticator code`
        }
        subHeading={(form2FAType === 'enable_2fa') ? 'Two-factor authentication (2FA) has been enforced for all users by your SmartReach account admin.' : ''}
      >

        {isModalLoading && <SrSpinner spinnerTitle='loading ..' />}

        {!isModalLoading &&
          <div>

            {this.state.error &&
              <div className='mt-8 py-8 px-4 sm:px-10 bg-red-300'>
                <p>{this.state.error}</p>
              </div>
            }

            {(form2FAType === 'enable_2fa') && this.state.gkey &&

              <div className='mb-6 mx-auto'>
                <p className='mb-4'>Scan the QR code in your Google Authenticator app</p>
                <div className='flex justify-center'>
                  <QRCodeSVG value={`otpauth://totp/SmartReach.io:${this.props.accountEmail}?secret=${this.state.gkey}&issuer=SmartReach.io`} /></div>
              </div>

            }

            <Formik
              initialValues={this.getInitial2FAFormValues()}
              validate={this.validate2FAForm}
              onSubmit={this.twoFASubmit}
            >


              {({ isSubmitting }) => (
                <Form>
                  <div className='mb-6'>
                    {/* <label className='label-formik' htmlFor='email'>Email</label> */}
                    <Field autoFocus autoComplete='nope' required type="text" name="verification_code" placeholder='Enter Google Authenticator code ...' className='input-formik w-full' />
                    <ErrorMessage name="verification_code" component="div" className='error-formik' />
                  </div>

                  <button type="submit" disabled={isSubmitting} className="button-formik-primary ml-auto mt-8">
                    {form2FAType === 'enable_2fa' ? 'Verify' : 'Log In'}
                  </button>

                </Form>
              )}

            </Formik>
          </div>
        }


      </SrModalDefault>


    );
  }
}

