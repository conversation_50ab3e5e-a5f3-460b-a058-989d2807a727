import React from "react";
import { classNames } from "../utils/sr-utils";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/outline";
import { CONSTANTS } from "../data/constants";









export const salesLeaderStoryData = [
  {
    img: '/assets/mc07/case_studies/dean_shapiro.svg',
    title: 'Flytech got more meetings by using SmartReach.io',
    desc: 'SmartReach gives us a straightforward view, along with key metrics like which prospects are clicking on different links.',
    author: '<PERSON>',
    authorInfo: 'CEO, Flytech',
    doclink: '/case-studies/learn-how-flytech-books-more-meetings'
  },
  {
    img: '/assets/mc07/case_studies/sales_cor.jpeg',
    title: 'increased our MQL to Discovery Call conversion ratio from 55% to 70%-75%',
    desc: ' It streamlined our workflow, enhancing convenience and reliability, allowing the team to focus on managing mailboxes.',
    author: '<PERSON>',
    authorInfo: '',
    doclink: '/case-studies/learn-how-SalesCOR-boosts-demo-booking/'
  },
  {
    img: '/assets/client_turner_terraboost_headshot.png',
    title: 'Thousands of deals are closing thanks to SmartReach.io emails',
    desc: 'The native integration with Salesforce improves pipeline visibility and helps in higher win rates.',
    author: 'Turner Rollins',
    authorInfo: 'Marketing Innovation Manager, Terraboost Media',
    doclink: '/case-studies/learn-how-TerraBoost-Media-has-made-tens-of-thousands-of-deals'
  },
  {
    img: '/assets/client_onramp_emmett.jpg',
    title: 'Our client  landed a $10,000 design project all within a month',
    desc: "SmartReach.io supercharges our client sales, connecting them to more opportunities.",
    author: 'Emmett Armstrong',
    authorInfo: 'Founder, OnRamp Data',
    doclink: '/case-studies/learn-how-OnRamp-Data-landed-a-10,000-usd-project'
  },
  {
    img: '/assets/client_remoteforce_ben.png',
    title: 'Smartreach.io helped to close a 6 million dollar deal',
    desc: "We're continuing to use smartreach.io, and if we get any new client, we would put them straight onto your platform.",
    author: 'Ben Sullivan',
    authorInfo: 'Founder, RemoteForce',
    doclink: '/case-studies/learn-how-remoteforce-closed-6million-dollar-deal/'
  }

]

export const SalesLeaderStories3 = () => {
  const [index, setIndex] = React.useState(0);

  return (
    <div className="mx-2 md:mx-0 flex flex-col items-center justify-center">
      <div className='!h-[175px] !w-[500px] flex justify-center items-center'>
        <div onClick={() => { (index !== 0) && setIndex(ind => ind - 1) }} >
          <ChevronLeftIcon className={classNames('hidden md:flex justify-center rounded-[10px] items-center h-10 w-10 px-1 md:mr-4', (index === 0) ? 'text-white' : 'cursor-pointer text-sr-grey-primary bg-white')} />
        </div>
        <div className='h-[175px] flex flex-row items-center justify-between bg-white rounded-3xl p-4 md:p-0'>
          <div className="hidden md:flex w-[96px] mx-[21px]">
            <img key={salesLeaderStoryData[index].img}
              src={CONSTANTS.CDN_URL + salesLeaderStoryData[index].img}
              alt='db'
              // skeletonClassName="w-[200px] h-[200px]"
              className='w-[96px] h-[96px] flex items-end rounded-2xl border-2 border-white overflow-hidden grayscale-[50%] bg-grey-1' />
          </div>
          <div className='relative w-full h-[112px] md:w-3/4 pl-2 flex flex-col justify-center'>
            <div className='hidden md:block z-0 absolute top-[-24px] left-[-1px] font-ptsans font-extrabold text-transparent text-[150px] leading-[1] bg-clip-text bg-gradient-to-b from-[#0F69FA4D]  to-[#FFFFFF80] to-50% ' >“</div>
            <div className="flex flex-col items-center">
              {/* <h3 className='text-white text-[20px] md:text-[26px] my-auto'>{salesLeaderStoryData[index].title}</h3> */}
              <div className="flex gap-2">
                {/* <div className="hidden md:block text-white -ml-8 text-4xl font-lexend font-bold">“</div> */}
                <p className='text-sr-grey-primary mt-4 text-[14px] font-noto pr-2'>{salesLeaderStoryData[index].desc}</p>
              </div>
            </div>
            <div className='mt-6 mb-4'>
              <p className=' text-sr-grey-primary text-[10px] font-readexpro font-semibold'>{salesLeaderStoryData[index].author}</p>
              <p className='text-sr-grey-primary text-[10px] font-readexpro'>{salesLeaderStoryData[index].authorInfo}</p>
            </div>
          </div>
        </div>
        <div onClick={() => { (index !== (salesLeaderStoryData.length - 1)) && setIndex(ind => ind + 1) }} >
          <ChevronRightIcon className={classNames('hidden md:flex justify-center rounded-[10px] items-center h-10 w-10 px-1 md:ml-4', (index === (salesLeaderStoryData.length - 1)) ? 'text-white' : 'cursor-pointer text-sr-grey-primary bg-white')} />
        </div>
      </div>
      <div className="h-full mt-4 w-full flex md:hidden gap-8 justify-center items-center">
        <div onClick={() => { (index !== 0) && setIndex(ind => ind - 1) }} >
          <ChevronLeftIcon className={classNames('flex justify-center rounded-full items-center h-10 w-10 px-1 md:mr-4', (index === 0) ? 'text-grey-2 bg-gray-100' : 'cursor-pointer text-white bg-blue-1')} />
        </div>
        <div onClick={() => { (index !== (salesLeaderStoryData.length - 1)) && setIndex(ind => ind + 1) }} >
          <ChevronRightIcon className={classNames('flex justify-center rounded-full items-center h-10 w-10 px-1 md:ml-4', (index === (salesLeaderStoryData.length - 1)) ? 'text-grey-2 bg-gray-50' : 'cursor-pointer text-white bg-blue-1')} />
        </div>
      </div>
    </div>

  )
}


export const CarouselComponent1 = () => {
  const [currentIndex, setCurrentIndex] = React.useState(0);

  // Automatically move to the next slide every 5 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === salesLeaderStoryData.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <div className="relative w-full max-w-lg mx-auto overflow-hidden">
      {/* Carousel Slides */}
      <div
        className="flex transition-transform duration-500"
        style={{
          transform: `translateX(-${currentIndex * 100}%)`,
          // width: `${salesLeaderStoryData.length * 100}%`
        }}
      >
        {salesLeaderStoryData.map((item, index) => (
          <div
            key={index}
            className="w-[464px] h-[246px] flex-shrink-0 bg-white shadow-md rounded-[16px] px-12 py-6"
            style={{ flexBasis: "100%" }}
          >
            <div className="flex items-center mb-4">
              <div className="relative">
              <img
                src={CONSTANTS.CDN_URL + item.img}
                alt={item.author}
                className="w-[72px] h-[72px] rounded-full mr-4"
              />
              <div className="absolute w-6 h-6 bg-sr-grey-primary rounded-full text-[22px] font-readexpro font-semibold text-white text-center top-[-9px] right-[20px]">“</div>
              </div>
              <div>
                <h3 className="text-[16px] font-semibold text-gray-800">
                  {item.author}
                </h3>
                <p className="text-[16px] text-gray-500">{item.authorInfo}</p>
              </div>
            </div>
            <p className="text-gray-700 text-[16px]">{item.desc}</p>
          </div>
        ))}
      </div>

      {/* Navigation Dots */}
      <div className="absolute left-[48px] bottom-[15%] flex justify-center mt-4 space-x-2 z-50">
        {salesLeaderStoryData.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={` h-3 rounded-full ${
              currentIndex === index ? 'w-12 bg-sr-gray-80' : 'w-3 bg-gray-300'
            }`}
          ></button>
        ))}
      </div>
    </div>
  );
};