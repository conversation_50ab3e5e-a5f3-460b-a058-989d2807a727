
export function setOAuthRedirect(value: any) {
  return localStorage.setItem("oauth_redirect", JSON.stringify(value))
}


export function setClientRedirectUri(clientRedirectUri:string ){
  return  localStorage.setItem("client_redirect_uri",clientRedirectUri)
}
export function getClientRedirectUri(){
  return localStorage.getItem("client_redirect_uri")
}


export function getOAuthRedirect() {
  const value = localStorage.getItem("oauth_redirect")
  return (value ? JSON.parse(value) : null)
}

export function removeOAuthRedirect() {
  return localStorage.removeItem("oauth_redirect")
}


export function setOAuthIntegrationIsSuccess(value: boolean) {
  return localStorage.setItem("isAuthrized", String(value))
}

export function getOAuthIntegrationIsSuccess() {
  const value = localStorage.getItem("isAuthrized")
  return value
}

export function removeOAuthIntegrationIsSuccess() {
  return localStorage.removeItem("isAuthrized")
}

export function setOAuthIntegrationResponse(value: string) {
  return localStorage.setItem("OAuthIntegrationResponse", value)
}

export function getOAuthIntegrationResponse() {
  const value = localStorage.getItem("OAuthIntegrationResponse")
  return value
}

export function removeOAuthIntegrationResponse() {
  return localStorage.removeItem("OAuthIntegrationResponse")
}


// export function setMultichannelAnnouncementFlag(): void {
//   return localStorage.setItem("mc_announcement_flag", "true")
// }

// export function getMultichannelAnnouncementFlag(): boolean {
//   const value = localStorage.getItem("mc_announcement_flag")
//   return (value == "true")
// }