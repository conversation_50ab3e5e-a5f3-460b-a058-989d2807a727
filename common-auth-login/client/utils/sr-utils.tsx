import * as React from 'react';
import {
  SrIconAdd, SrIconRevert, SrIconDone, SrIconEdit, SrIconForward, SrIconMail, SrIconMore, SrIconReply, SrIconSnooze, SrIconCompany, SrIconChevronRight, SrIconUser, SrIconHelp, SrIconChevronLeft, SrIconChevronUp, SrIconChevronDown, SrIconSearch, SrIconClose, SrIconAccounts, SrIconAccountsSolid, SrIconAlert, SrIconCampaign, SrIconCampaignSolid, SrIconTasks, SrIconTasksSolid, SrIconFeed, SrIconFeedSolid, SrIconInbox, SrIconInboxSolid, SrIconIssues, SrIconIssuesSolid, SrIconProspects, SrIconProspectsSolid, SrIconReports, SrIconReportsSolid, SrIconSettings, SrIconSettingsSolid, SrIconSpamTest, SrIconSpamTestSolid, SrIconTemplate, SrIconTemplateSolid, SrIconLogIn, SrIconLogOut, SrIconPause, SrIconPlay, SrRefresh, SrInfo, SrIconStars, SrIconTick, SrIconTickCircle, SrIconUpload, SrIconContent, SrIconContentSolid, SRIconTag, SrIconArrowLeft, SrIconChannelSetup, SrIconChannelSetupSolid, SrIconPreview, SrIconPreviewSolid, SrIconAddCircle, SrIconFilter, SrIconSave, SrIconShowContent, SRIconWhatsapp, SRIconLinkedin, SRIconSmiley, SrIconOutlineCircle, SrIconDownload, SrIconDelete, SrIconSoftStart, SrIconQuestionTelegram, SrIconQuestionMark, SRIconPhone, SRIconGeneral, SrIconCalendar, SRIconUpgradePlan, SrIconAssign, SrIconUnAssign, SrIconCategoryChange, SrIconSend, SrIconEmailOpen, SrIconSms, SrAIIcon, SrIconCheckFilled, SrIconVideo, SrIconHideContent
} from '@sr/design-component-lite'

export function classNames(...classes: any) {
  return classes.filter(Boolean).join(' ')
}

export function fetchIcon(icon: string) {
  // const IconKey = lo_upperFirst(lo_camelCase(icon));
  // console.log('icon key', IconKey);
  // const Func = new Function(
  //   "return function "+ IconKey +
  // )
  // // const iconComponent = Components[iconKey];
  // return (
  // <>
  // {/* <IconKey /> */}
  // {React.createElement(SrIconAccounts, {})}

  // </>
  // )
  // return React.createElement(IconKey, {});
  switch (icon) {
    case 'sr_icon_add':
      return <SrIconAdd />
    case 'sr_ai_icon':
      return <SrAIIcon />
    case 'sr_icon_revert':
      return <SrIconRevert />
    case 'sr_icon_done':
      return <SrIconDone />
    case 'sr_icon_edit':
      return <SrIconEdit />
    case 'sr_icon_forward':
      return <SrIconForward />
    case 'sr_icon_mail':
      return <SrIconMail />
    case 'sr_icon_more':
      return <SrIconMore />
    case 'sr_icon_reply':
      return <SrIconReply />
    case 'sr_icon_snooze':
      return <SrIconSnooze />
    case 'sr_icon_company':
      return <SrIconCompany />
    case 'sr_icon_chevron_right':
      return <SrIconChevronRight />
    case 'sr_icon_user':
      return <SrIconUser />
    case 'sr_icon_help':
      return <SrIconHelp />
    case 'sr_icon_chevron_left':
      return <SrIconChevronLeft />
    case 'sr_icon_chevron_up':
      return <SrIconChevronUp />
    case 'sr_icon_chevron_down':
      return <SrIconChevronDown />
    case 'sr_icon_user':
      return <SrIconUser />
    case 'sr_icon_search':
      return <SrIconSearch />
    case 'sr_icon_close':
      return <SrIconClose />
    case 'sr_icon_accounts':
      return <SrIconAccounts />
    case 'sr_icon_accounts_solid':
      return <SrIconAccountsSolid />
    case 'sr_icon_alert':
      return <SrIconAlert />
    case 'sr_icon_campaign':
      return <SrIconCampaign />
    case 'sr_icon_campaign_solid':
      return <SrIconCampaignSolid />
    case 'sr_icon_tasks':
      return <SrIconTasks />
    case 'sr_icon_tasks_solid':
      return <SrIconTasksSolid />
    case 'sr_icon_feed':
      return <SrIconFeed />
    case 'sr_icon_feed_solid':
      return <SrIconFeedSolid />
    case 'sr_icon_inbox':
      return <SrIconInbox />
    case 'sr_icon_inbox_solid':
      return <SrIconInboxSolid />
    case 'sr_icon_issues':
      return <SrIconIssues />
    case 'sr_icon_issues_solid':
      return <SrIconIssuesSolid />
    case 'sr_icon_prospects':
      return <SrIconProspects />
    case 'sr_icon_prospects_solid':
      return <SrIconProspectsSolid />
    case 'sr_icon_reports':
      return <SrIconReports />
    case 'sr_icon_reports_solid':
      return <SrIconReportsSolid />
    case 'sr_icon_settings':
      return <SrIconSettings />
    case 'sr_icon_settings_solid':
      return <SrIconSettingsSolid />
    case 'sr_icon_spam_test':
      return <SrIconSpamTest />
    case 'sr_icon_spam_test_solid':
      return <SrIconSpamTestSolid />
    case 'sr_icon_template':
      return <SrIconTemplate />
    case 'sr_icon_template_solid':
      return <SrIconTemplateSolid />
    case 'sr_icon_log_in':
      return <SrIconLogIn />
    case 'sr_icon_log_out':
      return <SrIconLogOut />
    case 'sr_icon_refresh':
      return <SrRefresh />
    case 'sr_icon_info':
      return <SrInfo />
    case 'sr_icon_pause':
      return <SrIconPause />
    case 'sr_icon_play':
      return <SrIconPlay />
    case 'sr_icon_stars':
      return <SrIconStars />
    case 'sr_icon_tick':
      return <SrIconTick />
    case 'sr_icon_upload':
      return <SrIconUpload />
    case 'sr_icon_tick_circle':
      return <SrIconTickCircle />
    case 'sr_icon_show_content':
      return <SrIconShowContent />
    case 'sr_icon_hide_content':
      return <SrIconHideContent />
    case 'sr_icon_filter':
      return <SrIconFilter />
    case 'sr_icon_content':
      return <SrIconContent />
    case 'sr_icon_content_solid':
      return <SrIconContentSolid />
    case 'sr_icon_tag':
      return <SRIconTag />
    case 'sr_icon_arrow_left':
      return <SrIconArrowLeft />
    case 'sr_icon_channel_setup':
      return <SrIconChannelSetup />
    case 'sr_icon_channel_setup_solid':
      return <SrIconChannelSetupSolid />
    case 'sr_icon_preview':
      return <SrIconPreview />
    case 'sr_icon_preview_solid':
      return <SrIconPreviewSolid />
    case 'sr_icon_add_circle':
      return <SrIconAddCircle />
    case 'sr_icon_save':
      return <SrIconSave />
    case 'sr_icon_whatsapp':
      return <SRIconWhatsapp />
    case 'sr_icon_linkedin':
      return <SRIconLinkedin />
    case 'sr_icon_smiley':
      return <SRIconSmiley />
    case 'sr_icon_outline_circle':
      return <SrIconOutlineCircle />
    case 'sr_icon_download':
      return <SrIconDownload />
    case 'sr_icon_delete':
      return <SrIconDelete />
    case 'sr_icon_soft_start':
      return <SrIconSoftStart />
    case 'sr_icon_question_mark':
      return <SrIconQuestionMark />
    case 'sr_icon_question_telegram':
      return <SrIconQuestionTelegram />
    case 'sr_icon_calendar':
      return <SrIconCalendar />
    case 'sr_icon_phone':
      return <SRIconPhone />
    case 'sr_icon_general':
      return <SRIconGeneral />
    case 'sr_icon_upgrade_plan':
      return <SRIconUpgradePlan />
    case 'sr_icon_sms':
      return <SrIconSms />
    case 'sr_icon_assign':
      return <SrIconAssign />
    case 'sr_icon_unassign':
      return <SrIconUnAssign />
    case 'sr_icon_changed_category':
      return <SrIconCategoryChange />
    case 'sr_icon_send':
      return <SrIconSend />
    case 'sr_icon_email_open':
      return <SrIconEmailOpen />
    case 'sr_icon_check_filled':
      return <SrIconCheckFilled />
    case 'sr_icon_video':
      return <SrIconVideo />
    default:
      return <SrIconAdd />
  }
}
