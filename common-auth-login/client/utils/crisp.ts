export function crispBoot(accInfo: LogIn.IAccount) {
  try {
    /**
     * 18-Jul-2024: many times intercom loading failed
     * as our react app was loading before the intercom script had loaded
     * and this fn call failed because of that
     *
     * Therefore, now we are checking if the script has loaded every second
     * for the first 20 seconds, and once the script has loaded we call the
     * intercomBoot fn and stop the timer
     *
     *
     * 11-Jan-2025
     * Similar to the Intercom implementation, we'll check if <PERSON>risp
     * has loaded before initializing it. We'll retry for 20 seconds
     * before giving up.
     */

    let secondsPassed = 0;

    const timer = setInterval(() => {
      if (!!(window as any).$crisp) {
        // stop the timer
        clearInterval(timer);

        console.log(
          `[crisp] crispBoot: loading crisp after ${secondsPassed} seconds`
        );

        // Set user information
        (window as any).$crisp.push(["set", "user:email", accInfo.email]);
        (window as any).$crisp.push([
          "set",
          "user:nickname",
          `${accInfo.first_name} ${accInfo.last_name}`,
        ]);
        // (window as any).$crisp.push(['set', 'user:avatar', null]); // If you have user avatar URL

        // Set custom user data
        (window as any).$crisp.push([
          "set",
          "session:data",
          [
            ["userId", accInfo.internal_id],
            ["userHash", accInfo.intercom_hash],
            ["firstName", accInfo.first_name],
            ["lastName", accInfo.last_name],
            ["orgRole", accInfo.org_role],
            ["emailVerified", accInfo.email_verified],
            ["createdAt", accInfo.created_at],
          ],
        ]);

        // Set company information
        (window as any).$crisp.push([
          "set",
          "session:data",
          [
            ["companyId", accInfo.org.id],
            ["companyName", accInfo.org.name],
            ["planName", accInfo.org.plan.plan_name],
            ["trialEndsAt", accInfo.org.trial_ends_at],
          ],
        ]);
      } else if (secondsPassed >= 20) {
        // stop the timer after 20 seconds
        clearInterval(timer);
        console.error(
          "[crisp] crispBoot: 20 seconds passed, crisp still not found, ignoring loading crisp"
        );
      } else {
        secondsPassed += 1;
      }
    }, 1000);
  } catch (e) {
    console.error("[crisp] crispBoot: ", e);
  }
}

export function crispResetSession() {
  try {
    // Reset the session and clear user data
    (window as any).$crisp.push(["do", "session:reset"]);
  } catch (e) {
    console.error("[crisp] crispResetSession: ", e);
  }
}

export function crispShowChatBox() {
  try {
    (window as any).$crisp.push(["do", "chat:open"]);
  } catch (e) {
    console.error("[crisp] crispShowChatBox: ", e);
  }
}

export function crispHideChatBox() {
  try {
    (window as any).$crisp.push(["do", "chat:hide"]);
  } catch (e) {
    console.error("[crisp] crispHideChatBox: ", e);
  }
}

export function crispTrackEvent(event: string) {
  try {
    (window as any).$crisp.push(["set", "session:event", [[[event, {}]]]]);
  } catch (e) {
    console.error("[crisp] crispTrackEvent: ", event, e);
  }
}
