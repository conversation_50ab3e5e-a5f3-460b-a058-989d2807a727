import filter from 'lodash-es/filter';
import includes from 'lodash-es/includes';
interface ITimezone {
  id: string;
  name: string;
}
export function getCurrentTimeZone(list: ITimezone[]) {
  const d = new Date();
  const hour = (d.getTimezoneOffset() / 60 > 0) ? Math.floor(d.getTimezoneOffset() / 60)
    : Math.ceil(d.getTimezoneOffset() / 60) * -1;
  let hourStr = '';
  if (d.getTimezoneOffset() / 60 >= 0) {
    if (d.getTimezoneOffset() / 60 < 10) {
      hourStr = '-0' + hour;
    } else {
      hourStr = '-' + hour;
    }
  } else if (d.getTimezoneOffset() / 60 < 0) {
    if (d.getTimezoneOffset() / 60 > -10) {
      hourStr = '+0' + hour;
    } else {
      hourStr = '+' + hour;
    }
  }
  const currTimeZoneOffset =
    hourStr
    + (d.getTimezoneOffset() % 60 !== 0 ? ':30' : '');
  const currZone = filter(list, (zone:ITimezone) => {
    return includes(zone.name, currTimeZoneOffset);
  });
  if (currZone.length > 0) {
    return currZone[0].id;
  } else {
    return '';
  }
}