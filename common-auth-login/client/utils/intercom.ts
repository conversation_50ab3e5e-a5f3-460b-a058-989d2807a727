

// export function intercomBoot(accInfo: LogIn.IAccount) {
//   try {


//     const intercomUser = {
//       user_id: accInfo.internal_id,
//       email: accInfo.email,
//       user_hash: accInfo.intercom_hash,
//       name: accInfo.first_name + ' ' + accInfo.last_name,
//       "firstname": accInfo.first_name,
//       "lastname": accInfo.last_name,
//       // "teamAdmin": isTeamAdmin,
//       "createdAt": accInfo.created_at,
//       "orgRole": accInfo.org_role,
//       company: {
//         company_id: accInfo.org.id,
//         name: accInfo.org.name,
//         // planType: this.props.logInStore.getPlanType,
//         planName: accInfo.org.plan.plan_name,
//         trialEndsAt: accInfo.org.trial_ends_at,
//         // nextBillingDate: accInfo.org.next_billing_date,
//         // accountType: accInfo.account_type,
//         // plan: this.props.logInStore.getPlanType || ''
//       }
//     };


//     (window as any).Intercom('boot', {
//       app_id: 'xmya8oga',
//       ...intercomUser
//     });
//   } catch (e) {
//     console.error('[intercom] intercomBoot: ', e)
//   }
// }


// export function intercomResetSession() {
//   try {
//     (window as any).Intercom('shutdown')
//   } catch (e) {
//     console.error('[intercom] intercomResetSession: ', e)
//   }
// }

// export function intercomShowChatBox() {
//   try {
//     // Opens chatbox in an async-safe way
//     (window as any).Intercom('show');
//   } catch (e) {
//     console.error('[intercom] intercomToggleChatBox: ', e)
//   }
// }

// export function intercomHideChatBox() {
//   try {
//     // Opens chatbox in an async-safe way
//     (window as any).Intercom('hide');
//   } catch (e) {
//     console.error('[intercom] intercomToggleChatBox: ', e)
//   }
// }

// export function intercomTrackEvent(event: string) {
//   try {
//     (window as any).Intercom('trackEvent', event);
//   } catch (e) {
//     console.error('[intercom] intercomTrackEvent trackEvent: ', event, e)
//   }
// }