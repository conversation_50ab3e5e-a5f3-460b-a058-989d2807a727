body {
  font-size: 1rem;
  color: #282828; // as per figma

  font-family: 'Roboto', sans-serif; // as per figma


  #root {
    overflow: hidden;
    height: 100%; //check
    width: 100%;
    .index-container {
      height: inherit;
      width: inherit;
    }
  }
}

.spinner {
  width: 100%;
  // height: 600px;
  position: relative;
  .ui.active.big.loader:after {
    border-color: #ffe100 transparent transparent transparent;
  }
  .ui.loader {
    position: relative;
    margin-top: 5em;
    margin-bottom: -1em;
    font-size: 1em;
  }
}

// .main-content-strip{
//   .spinner {
//     .ui.big.loader{
//       font-size: 1em;
//     }
//   }
// }

.spinner-loader {
  border: 6px solid #f3f3f3;
  border-radius: 50%;
  border-top: 6px solid #ffe100;
  width: 50px;
  height: 50px;
  -webkit-animation: spin 0.5s linear infinite;
  animation: spin 0.5s linear infinite;
  margin: 5% auto 0 auto;
}

.app-container {
  height: inherit;
  width: inherit;
  .app-contents {
    height: inherit;
    width: inherit;
    .logged-in-app {
      display: flex;
      flex-direction: row;
      height: inherit;
      // margin-right: 1em;
      .side-navbar.version-teams {
        width: 80px;
        overflow-y: overlay;
        .nav-item.nav-item-page{
          .icon-holder {
            width: 100%;
            flex-direction: column;
            border-radius: 0;
            height: 50px;
            padding: 5px 0;
            .nav-title {
              text-align: center;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.8);
              display: block;
            }
          }
        }
        .nav-item.nav-item-page.active-item{
          .icon-holder {
            .nav-title {
              // color: black !important;
            }
          }
        }
      }
      .side-navbar {
        height: inherit;
        background: rgba(232,232,232,0.8);
        width: 60px;
        display: inherit;
        align-content: center;
        flex-direction: column;
        // overflow-y: auto;
        .logo {
          .icon-holder {
            background: none !important;
            box-shadow: none;
            .logo {
              width: 100%;
              .logo-image {
                margin: auto;
                width: 100%;
              }
            }
          }
        }
        .bottom-aligned {
          margin-top: auto !important;
        }

        
        .nav-item {
          width: inherit;
          height: 60px;
          display: inherit;
          justify-content: center;
          align-content: center;
          // margin-top: 0.5em;
          cursor: pointer;
          .icon-holder {
            border-radius: 20px;
            width: 40px;
            height: 40px;
            display: inherit;
            justify-content: center;
            align-content: center;
            margin: auto;
            .nav-title {
              display: none;
            }
            .icon {
              color: rgba(0,0,0,0.8);
              margin: auto;
              height: 1.18em;//same as width to centre the icon
            }
          }
        }
        .nav-item:hover {
          .icon-holder {
            background-color: #ffffff;
            // .icon {
            //   color: #f5f5f5 ;
            // }
            .icon {
              // color: black !important;
              color: #0f69fa !important;
              opacity: 0.8 !important;
            }
            .nav-title {
              color: #0f69fa !important;
              opacity: 0.8 !important;
            }
          }
        }
        .active-item {
          padding-left: 3px;
          .icon-holder {
            // background-color: rgba(232,232,232,0.4) !important;
            // background-color: rgba(255,255,0,0.3)!important;
            box-shadow: inset 2px 0px 0px 0 #0f69fa;
            // border-right: 5px solid  #ffe100;
            // box-shadow: 0px 0px 0px 15px rgba(255,255,0,0.2);
            .icon {
              // color: black !important;
              color: #0f69fa !important;
              opacity: 1 !important;
            }
            .nav-title {
              color: #0f69fa !important;
              opacity: 1 !important;
            }
          }
        }
      }
      .feed-sidebar-wrapper {
        height: inherit;
        flex: 1;
        overflow-x: auto;
        .sidebar.feed-sidebar {
          background: #ffffff;
          padding-bottom: 60px !important;

          .feeds {
            padding-left: 2em;
            padding-right: 1em;
            .ui.feed > .event {
              padding-top: 0px;
              padding-bottom: 0px;
              border-left: 1px dashed lightgray;
            }
            .ui.feed > .event .label {
              margin-top: 2.25em;
              margin-left: -1em;
              position: relative;
              .count-bubble {
                min-width: 1em;
                min-height: 1em;
                padding: 0.5em !important;
                line-height: 0.6em;
                border-radius: 500rem;
                background-color: #4183c4;
                color: white;
                position: absolute;
                right: 7px;
                width: auto;
                height: auto;
                text-align: center;
                font-weight: bold;
                cursor: pointer;
                font-size: xx-small;
              }
            }

            .ui.feed > .event > .content .extra.text {
              max-width: 100%;
            }
            .ui.feed > .event > .label > img,
            .ui.feed > .event > .label > .icon {
              background-color: #FFFFFF;
              border-radius: 500rem;
              color: rgba(0,0,0,0.4);
              width: 2rem;
              height: 2rem;
              line-height: 1.3;
              opacity: 1;
              position: relative;

              box-shadow: none;
              border: 1px dashed rgba(0, 0, 0, 0.4);
              color: rgba(0, 0, 0, 0.4);

              font-size: 1.1em !important;
            }
            .extra.text {
              min-height: 20px;
            }
            .feed-item.content {
              box-shadow: none;
              padding: 1em;
              // background: rgba(0,0,0,0.02);
              border-bottom: 1px soild lightgray;
              margin-bottom: 0em !important;
              margin-left: 0em !important;
              line-height: 1.6em;
              .stats {
                .prospect-name {
                  color: #4183c4;
                  cursor: pointer;
                  font-weight: bold;
                }
                .event-details {
                  color: rgba(0,0,0,0.4);
                  font-weight: bold;
                }
                .campaign-details {
                  color: #4183c4;
                  cursor: pointer;
                  font-weight: bold;
                }
                .icon {
                  float: right;
                }
              }

              .event-time {
                padding-top:0.3em;
                color: rgba(0,0,0,0.4);
                font-size: 0.8em;
                font-weight: 600;
                display: inline-block;
              }

              .sub-events-section {
                padding-top: 1em;
              }
            }
          }
        }
        .feed-sidebar-wrapper-pushable {
          height: inherit;
          min-width: 964px;
          .feed-sidebar-wrapper-pusher {
            height: inherit;
            .main-app-segment {
              height: inherit;
              // flex: 1; //check
              // overflow-x: auto;
              display: flex;
              flex-direction: column;
              .top-banner {
                background: rgba(255, 255, 0, 0.2);
                flex: 0 0 30px;
                // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                padding-right: 1em;
                padding-left: 1em;
                display: flex;
                flex-direction: row;
                align-items: center;
                .teams-switch {
                  margin-right: 2em;
                  .ui.dropdown {
                    .menu {
                      max-height: 300px;
                      overflow-y: auto;
                    }
                  }
                }
                .banner-message {
                  margin: auto;
                }
                a {
                  cursor: pointer;
                }
              }
              .top-banner.error{
                color: #912d2b;
                background-color: #fff6f6;
                box-shadow: 0px 1px 0px #e0b4b4;
                margin-top: 1px;
                .message {
                  margin: auto;
                }
              }
              .top-banner.warning{
                color: #573a08;
                background-color: #fff8db;
                box-shadow: 0px 1px 0px #c9ba9b;
                margin-top: 1px;
                .message {
                  margin: auto;
                }
              }
              .top-banner.upgrade-invoice-link {
                padding: 10px 2em;
                justify-content: center;
                box-shadow: none;
                font-size: 18px;
                a {
                  text-decoration: underline;
                  font-weight: bold;
                }
              }
              .rest-after-top-banner {
                flex: 1;
                height: inherit;
                overflow: hidden;

                .global-page {
                  height: inherit;
                  min-width: 964px;
                  .global-page-flex {
                    height: inherit;
                    display: flex;
                    flex-direction: column;
                  }
                }

                .local-page {
                  height: inherit;
                  .local-page-flex {
                    height: inherit;
                    display: flex;
                    flex-direction: column;
                  }
                }

                .specific-account-prospect-tab {
                  .filter-banner {
                    margin: 0 1em;
                  }
                }

                .global-page .global-page-flex, .local-page .local-page-flex, .specific-campaign-page .specific-campaign-page-flex {
                  .heading-strip {
                    flex: 0 0 60px;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    .heading {
                      font-size: 18px;
                      padding-left: 1em;
                      padding-right: 1em;
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      label {
                        color: rgba(40, 40, 40, 0.6);
                      }
                      .text {
                        display: inline-flex;
                      }
                      .help-resources {
                        display: inline-flex;
                        margin-left: 1em;
                        border-left: 1px solid #282828;
                      }
                      .button.back {
                        border-radius: 6px;
                        background: transparent;
                        padding-left: 0.25em;
                        padding-right: 0.25em;
                      }
                    }
                    .search-input {
                      margin-left: auto;
                      height: 40px;
                      // box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);
                      border: 1px solid #f5f5f5;
                      background-color: transparent;
                      .ui.input {
                        height: inherit;
                        width: 350px;
                        input {
                          border: none;
                        }
                      }
                    }
                    .action {
                      margin-left: auto;
                      margin-right: 1em;
                      height: 40px;
                      .ui.button {
                        height: inherit;
                        border-radius: 6px;
                      }
                    }
                  }
                  .prospect-tabs-strip {
                    flex: 0 0 30px;
                    // box-shadow: 0px 1px 0px rgb(40 40 40 / 10%);
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    margin: 0 1em 1em 1em;
                    .icon {
                      margin: 0;
                    }
                    .ui.menu {
                      // border: none;
                      box-shadow: none;
                      height: 100%;
                      // margin: auto;
                      flex: 1;
                      border-bottom: 1px solid rgba(34, 36, 38, 0.15);
                      .active.item {
                        border-color: #0f69fa;
                        color: #0f69fa;
                      }
                    }
                  }
                  .filter-strip {
                    flex: 0 0 30px;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    height: 30px;
                    margin: 0 1em 1em 1em;
                    .ui.button {
                      height: inherit;
                      border-radius: 6px;
                      font-size: 12px;
                      .icon {
                        // color: #ffffff;
                        opacity: 1;
                        margin: 0;
                      }
                    }
                    .filter-section {
                      height: inherit;
                      //sidebar transition
                      // -webkit-transition: width 500ms ease;
                      // transition: width 500ms ease;
                      -webkit-transition: none;
                      transition: none;
                      margin-right: 1em;
                      .filter-sidebar-heading {
                        height: inherit;
                        // background-color: #2185D0;
                        color: #ffffff;
                        display: flex;
                        flex-direction: row;
                        .clear-filter {
                          height: inherit;
                        }
                        .collapse-filter {
                          height: inherit;
                          // margin-left: auto;
                        }
                        .ui.button {
                          margin: 0;
                        }
                      }
                    }
                    .pagination-section {
                      margin-left: auto;
                      margin-right: 1em;
                      height: inherit;
                      display: inline-flex;
                      // box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);
                      .page-items-count {
                        display: inline-flex;
                        height: inherit;
                        align-items: center;
                        padding-left: 2em;
                        white-space: nowrap;
                      }
                      .page-controls {
                        display: inline-flex;
                        padding-left: 1em;
                        padding-right: 1em;
                        height: inherit;
                        align-items: center;
                        .ui.basic.button {
                          border: none;
                          box-shadow: none;
                        }
                      }
                    }
                    .table-columns, .download-button {
                      height: inherit;
                    }
                    .actions-dropdown {
                      height: inherit;
                      .ui.button.dropdown {
                        // margin: 0;
                        // border: none;
                        // height: inherit;s
                        // padding-top: 15px;
                        // border-radius: 10px;
                        // border-left: 1px solid;
                        // border-right: 1px solid;
                        .menu {
                          // max-height: 275px;
                          max-height: fit-content;
                          overflow-y: auto;
                          // margin-top: 5px;
                          .item {
                            padding-top: 0.5em !important;
                            padding-bottom: 0.5em !important;
                          }
                        }
                        .icon {
                          padding-left: 5px;
                        }
                      }
                    }
                    .action-button {
                      margin-left: auto;
                      height: inherit;
                    }
                    .refresh-action-button {
                      height: inherit;
                    }
                    .search-input {
                      height: inherit;
                      background-color: transparent;
                      // margin-left: 1em;
                      .ui.input {
                        height: inherit;
                        width: 350px;
                        input {
                          // border: none;
                        }
                      }
                    }
                  }
                  .pushable {
                    .ui.sidebar.prospect-sidebar {
                      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                      // box-shadow: none;
                      // background: #f5f5f5;
                      // border-right: 1px solid rgba(0, 0, 0, 0.2);
                      // border-top: 1px solid rgba(0, 0, 0, 0.2);
                      padding: 0 1em 1em 1em;
                      height: inherit;
                      .prospect-card, .account-card {
                        background: #f5f5f5;
                        border: 1px solid rgba(0, 0, 0, 0.2);
                        height: inherit;
                        overflow-y: auto;
                      }
                    }
                    .ui.sidebar.filter-sidebar {
                      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                      // background: #ffffff;
                      // background: #f5f5f5;
                      // border-right: 1px solid rgba(0, 0, 0, 0.2);
                      // border-top: 1px solid rgba(0, 0, 0, 0.2);
                      padding: 0 1em 1em 1em;
                      height: inherit;
                      // .ui.segment {
                      //   background-color: #f5f5f5;
                      //   label {
                      //     display: block;
                      //     font-weight: bold;
                      //     margin-bottom: 0.25em;
                      //   }
                      //   .multiple.selection.dropdown {
                      //     .ui.label {
                      //       background-color: rgba(35, 135, 205, 0.2);
                      //     }
                      //   }
                      // }
                      .conditional-filter-section {
                        z-index: 13;
                        // margin-top: 0.5em;
                        // margin-bottom: 0.5em;
                        padding: 1em;
                        background: #f5f5f5;
                        border: 1px solid rgba(0, 0, 0, 0.2);
                        // height: inherit;
                        .ui.segment {
                          // background-color: rgba(40, 40, 40, 0.04);
                          border: none;
                          border-radius: 6px;
                          label {
                            display: inline-block;
                            margin-bottom: 0.5em;
                            color: rgba(0, 0, 0, 0.6);
                          }
                        }
                        .saved-filter-section {
                          display: flex;
                          flex-direction: row;
                          .ui.button.dropdown {
                            margin-left: auto;
                            background-color: rgba(40,40,40,0.8);
                            color: #ffffff;
                          }
                          .ui.button.selection.dropdown {
                            margin-left: auto;
                            background-color: rgba(40,40,40,0.8);
                            color: #ffffff;
                            min-width: 10em;
                            .text {
                              font-weight: inherit !important;
                              color: inherit !important;
                            }
                            .menu {
                              width: 250px;
                            }
                          }
                        }
                        .owners-dropdown {

                        }
                        .ui.divider {
                          margin: 2em 0;
                        }
                        .main-clause {
                          margin: 1em 0;
                          .ui.button.dropdown {
                            background-color: rgba(35, 135, 205, 0.2);
                            border: 1px solid rgba(35, 135, 205, 0.5);
                            border-radius: 2px;
                          }
                        }
                        .sub-clause {
                          margin-top: 0.2em;
                          margin-bottom: 0.2em;
                          .ui.button.dropdown {
                            background-color: rgba(35, 135, 205, 0.2);
                          }
                        }
                        .add-new-condition {
                          margin-top: 1em;
                          margin-bottom: 5em;
                          .ui.fluid.button {
                            // background-color: rgba(40,40,40,0.16) !important;
                          }
                        }
                        .add-sub-filter {
                          margin-top: 0.2em;
                          margin-bottom: 0.2em;
                          .ui.button {
                            background-color: rgba(40,40,40,0.16);
                          }
                        }
                        .sub-filters {
                          .sub-filter-row1 {
                            padding-top: 0.2em;
                            padding-bottom: 0.2em;
                            display: flex;
                            flex-direction: row;
                            .sub-filter-row1-column {
                              width: 50%;
                            }
                            .column1 {
                              margin-right: 0.5em;
                            }
                          }
                          .sub-filter-row2 {
                            padding-top: 0.2em;
                            padding-bottom: 0.2em;
                            display: flex;
                            flex-direction: row;
                            .sub-filter-row2-column-main {
                              flex: 1; //check
                            }
                            .sub-filter-row2-column-action {
                              margin-left: auto;
                              .ui.button {
                                margin: 0 0 0 1em;
                              }
                              .remove-sub-filter {
                                background-color: rgba(40,40,40,0.1) ;
                                color: rgba(40,40,40,0.4);
                              }
                            }
                          }
                        }
                        .ui.error.input {
                          background: #FFF6F6;
                          border-color: #E0B4B4;
                          color: #9F3A38;
                          input {
                            background: #FFF6F6;
                            border-color: #E0B4B4;
                            color: #9F3A38;
                          }
                        }
                        .custom-date-range-picker.error {
                          background: #FFF6F6;
                          border-color: #E0B4B4;
                          color: #9F3A38;
                        }
                        .filterActions {
                          margin-top: 1em;
                          text-align: right;
                        }
                        .react-datepicker-popper {
                          z-index: 4;
                        }
                        .react-datepicker__input-container {
                          // font-size: 0.78571429em;
                        }
                      }
                    }
                    .pusher {
                      height: inherit;
                      .main-content-strip {
                        height: inherit;
                        flex: 1 1 auto;
                        overflow: auto;
                        // padding: 2em;
                        // padding-top: 1px;
                        // padding: 1em;
                        // .proceed-button {
                        //   margin-right: 1em;
                        // }
                      }
                      .main-content-strip.content-with-side-menu {
                        display: flex;
                        flex-direction: row;
                        padding: 0;
                        overflow: hidden;
                        // transition: padding 500ms;
                        .content-beside-menu {
                          flex: 1; //check
                          display: flex;
                          flex-direction: column;
                          overflow-x: auto;
                          padding-top: 1em;
                          .main-content-header {
                            height:41px;
                            box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                            display: flex;
                            flex-direction: row;
                            justify-content: center;
                            align-items: center;
                            .content-heading {
                              font-weight: bold;
                              height: inherit;
                              margin: 0 auto;
                              padding-top: 12px;
                              margin: 0 auto 0 2em;
                            }
                            .action-button {
                              height: inherit;
                              .ui.button {
                                height: inherit;
                                border-radius: 0;
                                margin: 0;
                              }
                            }
                          }
                          .main-content-section {
                            flex: 1; //check
                            overflow: auto;
                            width: auto;
                          }
                        }
                        .main-content-section {
                          flex: 1; //check
                          // width: 200px;
                          height: inherit;
                          overflow: auto;
                          // padding: 2em;
                        }
                        
                      }
                      .main-content-strip.has-filter-banner {
                        display: flex;
                        flex-direction: column;
                        // transition: padding 500ms;
                        .filter-banner {
                          flex: 0 0 35px;
                          // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                          background-color: rgba(40, 40, 40, 0.04);
                          // height: inherit;
                          .filter-banner-wrapper {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            padding-left: 1em;
                            padding-right: 1em;
                            height: 100%;
                            cursor: pointer;
                            .filter-banner-content {
                              white-space: nowrap;
                              padding: 0.35em 0.5em;
                            }
                            .extra-text {
                              font-size: 0.85714286rem;//fonnt size of label
                              a {
                                cursor: pointer;
                              }
                            }
                          }
                        }
                        .after-filter-banner {
                          flex: 1;
                          height: inherit;
                          // padding-bottom: 4em;
                          overflow-y: auto;
                          padding: 1em 1em 0em 1em;
                        }
                        .after-filter-banner.has-side-menu {
                          display: flex;
                          flex-direction: row;
                          padding: 0;
                        }
                        
                      }
                      .main-content-strip.content-with-side-menu, .main-content-strip.has-filter-banner {
                        .side-menu {
                          flex: 0 0 300px;
                          height: inherit;
                          // overflow-y: auto;
                          // background-color: rgba(40,40,40,0.04);
                          // box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);s
                          // padding-bottom: 2em;
                          padding: 1em;
                          .inline.loader {
                            margin-top: 2em;
                          }
                          .ui.vertical.menu {
                            border-radius: 0;
                            box-shadow: none;
                            border-right: none;
                            height: inherit;
                            overflow-y: auto;
                            background-color: rgba(40,40,40,0.04);
                            height: inherit;
                            .item.side-menu-heading {
                              // background-color: rgba(40,40,40,0.04);
                              padding: 0;
                              margin-top: 2em;
                              .header {
                                margin: 0 0 0 1em;
                                color: rgba(40, 40, 40, 0.6);

                                // as per figma
                                line-height: 122%;
                                font-style: normal;
                                font-weight: normal;
                              }
                            }
                            .item {
                              display: flex;
                              flex-direction: row;
                              align-items: center;

                              // as per figma
                              font-weight: 500;
                              line-height: 114%;

                              margin-left: 5px;
                              .item-holder {
                                margin-left: 2em;
                              }
                              .icon {
                                margin-left: auto;
                                margin-right: 1em;
                              }
                            }
                            .active.item {
                              // box-shadow: inset -4px 0px 0px #2387CD;
                              background: none;
                              border-radius: 0;
                              box-shadow: inset 2px 0px 0px 0 #0f69fa;
                              .item-holder {
                                color: #0f69fa !important;
                              }
                            }
                          }
                        }
                        .after-side-menu {
                          flex: 1;
                          padding: 2em;
                          height: inherit;
                          overflow: auto;
                        }
                      }
                    }
                  }
                }
              }

            }
          }
        }
      }

    }
    .logged-out-app {
      height: inherit;
      .branding {
        display: flex;
        flex-direction: row;
        height: 50px;
        align-items: center;
        margin-bottom: 3em;
        margin-top: 2em;
        a {
          color: inherit;
        }
        a:hover {
          color: inherit;
        }
        .logo {
          margin-left: auto;
          .logo-image {
            width: 50px;
          }
        }
        .logo-name {
          font-family: 'muli';
          font-size: 18px;
          font-weight: 400;
          padding-left: 5px;
          margin-right: auto;
        }
      }
      .login-page {
        font-size: 16px;
        padding-bottom: 2em;
        padding-right: 1em;
        overflow: auto;
        height: inherit;
        a {
          cursor: pointer;
        }
        .ui.segment {
          box-shadow: 0 -2px 0 0 royalblue;
          margin-top: 2em;;
          .login-actions {
            display: flex;
            flex-direction: row;
            margin-top: 2em;
            margin-bottom: 2em;
            align-items: center;
            .login-button {
              margin-left: auto;
            }
          }
          .login-other-actions {
            display: flex;
            flex-direction: row;
            margin: -1em;
            margin-top: 1em;
            padding: 2em 1em;
            background: #f5f5f5;
            align-items: center;
            .create-account {
              margin-left: auto;
              .ui.basic.button {
                background: #ffffff !important;
              }
            }
          }
        }
      }
      .register-page {
        font-size: 16px;
        padding-bottom: 2em;
        padding-right: 1em;
        overflow: auto;
        height: inherit;
        .four.wide.column {
          margin-top: auto;
          margin-bottom: auto;
        }
        .free-trial-details {
          .ui.segment {
            height: 100%;
          }
          .title {
            color: #03e26b;
          }
          .over-view {
            text-align: left;
            margin-left: 1em;
            p {
              margin: 0;
              font-size: 20px
            }
          }
          .trial-period {
            margin-top: 30%;
            margin-bottom: 30%;
            .ui.statistic {
              padding: 5px;
              border: 1px solid rgba(0,0,0,0.8);
              border-radius: 1em;
            }
          }
        }
      }
      // .register-page, .login-page {
      //   font-size: 16px;
      //   overflow: auto;
      //   height: inherit;
      //   font-family: PT Sans;
      //   font-style: normal;
      //   font-weight: normal;
      //   font-size: 14px;
      //   line-height: 22px;
      //   letter-spacing: 0.02em;
      //   color: rgba(0, 0, 0, 0.8);
      //   // .ui.mobile.only.centered.stackable.grid.after-nav  {
      //   //   margin: 0;
      //   // }
      //   .ui.stackable.grid.after-nav  {
      //     margin: 0;
      //     height: 100%;
      //     .column.illustration {
      //       background: url(https://d3r6z7ju6qyotm.cloudfront.net/postlogin-assets/signInPhoto.svg) no-repeat;
      //       background-size: cover;
      //       padding: 0;
      //     }
      //     .column.account-form {
      //       padding: 4em 0;
      //       background: #f5f5f5;
      //       >.ui.centered.grid {
      //         margin: 0;
      //       }
      //       .heading {
      //         padding-top: 2em;
      //         h2 {
      //           font-family: Playfair Display;
      //           font-style: normal;
      //           font-weight: bold;
      //           font-size: 45px;
      //           line-height: 60px;
      //         }
      //         p {
      //           color: rgba(0, 0, 0, 0.8);
      //         }
      //       }
      //       .ui.divider  {
      //         margin: 3em 0;
      //       }
      //       .ui.form {
      //         input {
      //           padding: 1em;
      //           border: none;
      //           background: #FFFFFF;
      //           box-shadow: 0px 2px 6px rgba(12, 113, 195, 0.1);
      //           border-radius: 6px;
      //         }
      //       }
      //       .form-action {
      //         display: flex;
      //         flex-direction: row;
      //         align-items: center;
      //         .trial-info {
      //           .plan {
      //             font-weight: bold;
      //             text-decoration: underline;
      //           }
      //         }
      //         .form-submit-button {
      //           margin-left: auto;
      //           min-width: 90px;
      //           .ui.button {
      //             background: #0F69FA;
      //             box-shadow: 0px 4px 14px rgba(15, 105, 250, 0.3);
      //             border-radius: 30px;
      //             font-family: PT Sans;
      //             font-style: normal;
      //             font-weight: bold;
      //             font-size: 12px;
      //             line-height: 30px;
      //             letter-spacing: 0.13em;
      //             text-transform: uppercase;

      //             color: #FFFFFF;
      //           }
      //         }
      //       }
      //       .ui.message {
      //         border: none;
      //         background: rgba(15,105,250,0.06);
      //         border-radius: 30px;
      //         margin-top: 3em;
      //         padding-left: 3em;
      //         padding-right: 3em;
      //         .links {
      //           font-weight: bold;
      //           a {
      //             color: rgba(0, 0, 0, 1);
      //           }
      //         }
      //       }
      //       .hint {
      //         margin-top: 2em;
      //         font-size: 16px;
      //         a {
      //           text-transform: uppercase;
      //         }
      //       }
      //     }
      //   }
        
      // }
    }
  }

}

.align.center {
  text-align: center;
}

.ui.button.primary-two {
  background-color: #1b689e;
}

.side-nav-popup {
  margin-left: -0.4em !important;
}

.side-nav-popup.options {
  padding: 0;
  .ui.vertical.menu {
    .item {

    }
  }
}

.show-hide-popup.ui.bottom.right.popup{
  max-height: 250px;
  overflow-y: auto;
  margin-top: 0;
}

.margin.top.five.percent {
  margin-top: 5%;
}

.ui.secondary.pointing.menu {
  .item {
    border-radius: 0 !important;
  }
}

//sidebar trannsition
.ui.push.sidebar {
  transition: none;
}

.pushable > .pusher {
  transition: none;
}

.onboarding-modal {
  .header {
    // background: aliceblue !important;
    .logo {
      width: 100px;
      margin-top: -75px;
    }
    .logo-text {
      font-family: 'muli';
      font-weight: bold;
      font-size: 18px;
      text-align: center;
    }
    p {
      font-weight: normal;
      font-size: 16px;
    }
  }
  .onboarding-details {
    padding: 1em !important;
    .onboarding-option.button {
      height: 100px;
      background-color: lavender;
      font-size: 16px;
    }
    .book-demo {
      margin: 3em;
      text-align: center;
    }
    .info {
      margin-top: 3em;
      text-align: center;
    }
  }
  .skip-action {
    margin-right: 1em;
    text-decoration: underline;
    cursor: pointer;
  }
}

.ui.dropdown:not(.button) > .default.text, .ui.default.dropdown:not(.button) > .text {
  // color: rgba(191, 191, 191, 0.87);
  color: rgba(0, 0, 0, 0.87);
}

table {
  td {
    word-break: break-all;
  }
}

.inbox-version-toggle {
  margin-left: 0.5em;
  // margin-top: 0.5em;
  // display: inline-block;
  .ui.loader {
    
  }
  .ui.toggle.checkbox {
    input {
      width: 2rem;
      height: 1rem;
    }
    label {
      padding-left: 2.5rem;
      padding-top: 0;
      font-weight: normal;
    }
    label:before {
      width: 2rem;
      height: 1rem;
    }
    label:after {
      width: 1rem;
      height: 1rem;
    }
    input:checked ~ label:after {
      left: 1.15rem;
    }
  }
}

// ::-webkit-input-placeholder {
//   color: #FF0000;
//   opacity: 1 !important; /* for older chrome versions. may no longer apply. */
// }

// :-moz-placeholder { /* Firefox 18- */
//   color: #FF0000;
//   opacity: 1 !important;
// }

// ::-moz-placeholder {  /* Firefox 19+ */
//   color: #FF0000;
//   opacity: 1 !important;
// }

// :-ms-input-placeholder {  
//  color: #FF0000;
// }

body ::-webkit-scrollbar {
  // -webkit-appearance: none;
  width: 6px;
  height: 6px;
}

body ::-webkit-scrollbar-track {
  // background: rgba(0, 0, 0, 0.1);
  background: transparent;
  // border-radius: 0px;
}

body ::-webkit-scrollbar-thumb {
  // cursor: pointer;
  // border-radius: 5px;
  background: rgba(0, 0, 0, 0.45);
  // -webkit-transition: color 0.2s ease;
  // transition: color 0.2s ease;
}

.assign-more-modal-flex {
  .filter-strip {
    margin-top: 1em !important;
  }
}

//Repeated fromm above
.assign-more-modal-flex {
  .heading-strip {
    flex: 0 0 60px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .heading {
      font-size: 18px;
      padding-left: 1em;
      padding-right: 1em;
      display: flex;
      flex-direction: row;
      align-items: center;
      label {
        color: rgba(40, 40, 40, 0.6);
      }
      .text {
        display: inline-flex;
      }
      .help-resources {
        display: inline-flex;
        margin-left: 1em;
        border-left: 1px solid #282828;
      }
      .button.back {
        border-radius: 6px;
        background: transparent;
        padding-left: 0.25em;
        padding-right: 0.25em;
      }
    }
    .search-input {
      margin-left: auto;
      height: 40px;
      // box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);
      border: 1px solid #f5f5f5;
      background-color: transparent;
      .ui.input {
        height: inherit;
        width: 350px;
        input {
          border: none;
        }
      }
    }
    .action {
      margin-left: auto;
      margin-right: 1em;
      height: 40px;
      .ui.button {
        height: inherit;
        border-radius: 6px;
      }
    }
  }
  .filter-strip {
    flex: 0 0 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 30px;
    margin: 0 1em 1em 1em;
    .ui.button {
      height: inherit;
      border-radius: 6px;
      font-size: 12px;
      .icon {
        // color: #ffffff;
        opacity: 1;
        margin: 0;
      }
    }
    .filter-section {
      height: inherit;
      //sidebar transition
      // -webkit-transition: width 500ms ease;
      // transition: width 500ms ease;
      -webkit-transition: none;
      transition: none;
      margin-right: 1em;
      .filter-sidebar-heading {
        height: inherit;
        // background-color: #2185D0;
        color: #ffffff;
        display: flex;
        flex-direction: row;
        .clear-filter {
          height: inherit;
        }
        .collapse-filter {
          height: inherit;
          // margin-left: auto;
        }
        .ui.button {
          margin: 0;
        }
      }
    }
    .pagination-section {
      margin-left: auto;
      margin-right: 1em;
      height: inherit;
      display: inline-flex;
      // box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);
      .page-items-count {
        display: inline-flex;
        height: inherit;
        align-items: center;
        white-space: nowrap;
      }
      .page-controls {
        display: inline-flex;
        padding-left: 1em;
        padding-right: 1em;
        height: inherit;
        align-items: center;
        .ui.basic.button {
          border: none;
          box-shadow: none;
        }
      }
    }
    .table-columns, .download-button {
      height: inherit;
    }
    .actions-dropdown {
      height: inherit;
      .ui.button.dropdown {
        // margin: 0;
        // border: none;
        // height: inherit;s
        // padding-top: 15px;
        // border-radius: 10px;
        // border-left: 1px solid;
        // border-right: 1px solid;
        .menu {
        // max-height: 275px;
          max-height: fit-content;
          overflow-y: auto;
          // margin-top: 5px;
          .item {
            padding-top: 0.5em !important;
            padding-bottom: 0.5em !important;
          }
        }
        .icon {
          padding-left: 5px;
        }
      }
    }
    .action-button {
      margin-left: auto;
      height: inherit;
    }
    .refresh-action-button {
      height: inherit;
    }
    .search-input {
      height: inherit;
      background-color: transparent;
      // margin-left: 1em;
      .ui.input {
        height: inherit;
        width: 350px;
        input {
          // border: none;
        }
      }
    }
  }
  .pushable {
    .ui.sidebar.prospect-sidebar {
      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      background: #ffffff;
      border-right: 1px solid rgba(0, 0, 0, 0.2);
      border-top: 1px solid rgba(0, 0, 0, 0.2);
      padding: 0;
      height: inherit;
    }
    .ui.sidebar.filter-sidebar {
      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      // background: #ffffff;
      // background: #f5f5f5;
      // border-right: 1px solid rgba(0, 0, 0, 0.2);
      // border-top: 1px solid rgba(0, 0, 0, 0.2);
      padding: 0 1em;
      height: inherit;
      // .ui.segment {
      //   background-color: #f5f5f5;
      //   label {
      //     display: block;
      //     font-weight: bold;
      //     margin-bottom: 0.25em;
      //   }
      //   .multiple.selection.dropdown {
      //     .ui.label {
      //       background-color: rgba(35, 135, 205, 0.2);
      //     }
      //   }
      // }
      .conditional-filter-section {
        z-index: 13;
        // margin-top: 0.5em;
        // margin-bottom: 0.5em;
        padding: 1em;
        background: #f5f5f5;
        border: 1px solid rgba(0, 0, 0, 0.2);
        height: inherit;
        .ui.segment {
          // background-color: rgba(40, 40, 40, 0.04);
          border: none;
          border-radius: 6px;
          label {
            display: inline-block;
            margin-bottom: 0.5em;
            color: rgba(0, 0, 0, 0.6);
          }
        }
        .saved-filter-section {
          display: flex;
          flex-direction: row;
          .ui.button.dropdown {
            margin-left: auto;
            background-color: rgba(40,40,40,0.8);
            color: #ffffff;
          }
          .ui.button.selection.dropdown {
            margin-left: auto;
            background-color: rgba(40,40,40,0.8);
            color: #ffffff;
            min-width: 10em;
            .text {
              font-weight: inherit !important;
              color: inherit !important;
            }
            .menu {
              width: 250px;
            }
          }
        }
        .owners-dropdown {

        }
        .ui.divider {
          margin: 2em 0;
        }
        .main-clause {
          margin: 1em 0;
          .ui.button.dropdown {
            background-color: rgba(35, 135, 205, 0.2);
            border: 1px solid rgba(35, 135, 205, 0.5);
            border-radius: 2px;
          }
        }
        .sub-clause {
          margin-top: 0.2em;
          margin-bottom: 0.2em;
          .ui.button.dropdown {
            background-color: rgba(35, 135, 205, 0.2);
          }
        }
        .add-new-condition {
          margin-top: 1em;
          margin-bottom: 5em;
          .ui.fluid.button {
            // background-color: rgba(40,40,40,0.16) !important;
          }
        }
        .add-sub-filter {
          margin-top: 0.2em;
          margin-bottom: 0.2em;
          .ui.button {
            background-color: rgba(40,40,40,0.16);
          }
        }
        .sub-filters {
          .sub-filter-row1 {
            padding-top: 0.2em;
            padding-bottom: 0.2em;
            display: flex;
            flex-direction: row;
            .sub-filter-row1-column {
              width: 50%;
            }
            .column1 {
              margin-right: 0.5em;
            }
          }
          .sub-filter-row2 {
            padding-top: 0.2em;
            padding-bottom: 0.2em;
            display: flex;
            flex-direction: row;
            .sub-filter-row2-column-main {
              flex: 1; //check
            }
            .sub-filter-row2-column-action {
              margin-left: auto;
              .ui.button {
                margin: 0 0 0 1em;
              }
              .remove-sub-filter {
                background-color: rgba(40,40,40,0.1) ;
                color: rgba(40,40,40,0.4);
              }
            }
          }
        }
        .ui.error.input {
          background: #FFF6F6;
          border-color: #E0B4B4;
          color: #9F3A38;
          input {
            background: #FFF6F6;
            border-color: #E0B4B4;
            color: #9F3A38;
          }
        }
        .custom-date-range-picker.error {
          background: #FFF6F6;
          border-color: #E0B4B4;
          color: #9F3A38;
        }
        .filterActions {
          margin-top: 1em;
          text-align: right;
        }
        .react-datepicker-popper {
          z-index: 4;
        }
        .react-datepicker__input-container {
          // font-size: 0.78571429em;
        }
      }
    }
    .pusher {
      height: inherit;
      .main-content-strip {
        height: inherit;
        flex: 1 1 auto;
        overflow: auto;
        // padding: 2em;
        padding-top: 1px;
      }
      .main-content-strip.content-with-side-menu {
        display: flex;
        flex-direction: row;
        padding: 0;
        overflow: hidden;
        // transition: padding 500ms;
        .content-beside-menu {
          flex: 1; //check
          display: flex;
          flex-direction: column;
          overflow-x: auto;
          .main-content-header {
            height:41px;
            box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            .content-heading {
              font-weight: bold;
              height: inherit;
              margin: 0 auto;
              padding-top: 12px;
              margin: 0 auto 0 2em;
            }
            .action-button {
              height: inherit;
              .ui.button {
                height: inherit;
                border-radius: 0;
                margin: 0;
              }
            }
          }
          .main-content-section {
            flex: 1; //check
            overflow: auto;
            width: auto;
          }
        }
        .main-content-section {
          flex: 1; //check
          // width: 200px;
          height: inherit;
          overflow: auto;
          // padding: 2em;
        }
        
      }
      .main-content-strip.has-filter-banner {
        display: flex;
        flex-direction: column;
        // transition: padding 500ms;
        .filter-banner {
          flex: 0 0 35px;
          // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
          background-color: rgba(40, 40, 40, 0.04);
          // height: inherit;
          .filter-banner-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-left: 1em;
            padding-right: 1em;
            height: 100%;
            cursor: pointer;
            .filter-banner-content {
              white-space: nowrap;
              padding: 0.35em 0.5em;
            }
            .extra-text {
              font-size: 0.85714286rem;//fonnt size of label
              a {
                cursor: pointer;
              }
            }
          }
        }
        .after-filter-banner {
          flex: 1;
          height: inherit;
          // padding-bottom: 4em;
          overflow-y: auto;
          padding: 1em;
        }
        .after-filter-banner.has-side-menu {
          display: flex;
          flex-direction: row;
          padding: 0;
        }
        
      }
      .main-content-strip.content-with-side-menu, .main-content-strip.has-filter-banner {
        .side-menu {
          flex: 0 0 300px;
          height: inherit;
          overflow-y: auto;
          background-color: rgba(40,40,40,0.04);
          // box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);s
          padding-bottom: 2em;
          .inline.loader {
            margin-top: 2em;
          }
          .ui.vertical.menu {
            border-radius: 0;
            box-shadow: none;
            border-right: none;
            .item.side-menu-heading {
              // background-color: rgba(40,40,40,0.04);
              padding: 0;
              margin-top: 2em;
              .header {
                margin: 0 0 0 1em;
                color: rgba(40, 40, 40, 0.6);

                // as per figma
                line-height: 122%;
                font-style: normal;
                font-weight: normal;
              }
            }
            .item {
              display: flex;
              flex-direction: row;
              align-items: center;

              // as per figma
              font-weight: 500;
              line-height: 114%;

              margin-left: 5px;
              .item-holder {
                margin-left: 2em;
              }
              .icon {
                margin-left: auto;
                margin-right: 1em;
              }
            }
            .active.item {
              // box-shadow: inset -4px 0px 0px #2387CD;
              background: none;
              border-radius: 0;
              box-shadow: inset 2px 0px 0px 0 #0f69fa;
              .item-holder {
                color: #0f69fa !important;
              }
            }
          }
        }
        .after-side-menu {
          flex: 1;
          padding: 2em;
          height: inherit;
          overflow: auto;
        }
      }
    }
  }
}

.crm-wizard-modal{
  .outline {
    outline: none;
  }
  .main-content-strip.content-with-side-menu {
    height: 518px;
    .side-menu {
      flex: 0 0 300px;
      height: inherit;
      overflow-y: auto;
      // background-color: rgba(40,40,40,0.04);
      // box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);s
      padding-bottom: 2em;
      .inline.loader {
        margin-top: 2em;
      }
      .ui.vertical.menu {
        border-radius: 0;
        box-shadow: none;
        border-right: none;
        .item.side-menu-heading {
          // background-color: rgba(40,40,40,0.04);
          padding: 0;
          margin-top: 2em;
          .header {
            margin: 0 0 0 1em;
            color: rgba(40, 40, 40, 0.6);

            // as per figma
            line-height: 122%;
            font-style: normal;
            font-weight: normal;
          }
        }
        .item {
          display: flex;
          flex-direction: row;
          align-items: center;

          // as per figma
          font-weight: 500;
          line-height: 114%;

          margin-left: 5px;
          .item-holder {
            margin-left: 2em;
          }
          .icon {
            margin-left: auto;
            margin-right: 1em;
          }
        }
        .active.item {
          // box-shadow: inset -4px 0px 0px #2387CD;
          background: none;
          border-radius: 0;
          box-shadow: inset 2px 0px 0px 0 #0f69fa;
          .item-holder {
            color: #0f69fa !important;
          }
        }
      }
    }
    .after-side-menu {
      flex: 1;
      padding: 2em;
      height: inherit;
      overflow: auto;
    }
  }
}


.crm-fields-component, 
.crm-status-component, 
.crm-other-settings-component, 
.crm-users-component,
.crm-dnc-component,
.crm-review-settings-component {
  margin: 2rem 0rem 0rem 0rem;
  .ui.toggle.checkbox input:checked ~ .box:before, .ui.toggle.checkbox input:checked ~ label:before {
    background-color: #21BA45 !important;
  }
}
.fields-content, 
.status-content, 
.other-settings-content, 
.users-content,
.dnc-content,
.syncs-content,
.review-settings-content {
  margin: 0rem 0rem 1.5rem;
}
.review-settings-content {
  .icon {
    font-size: 4em;
    margin-bottom: 0.5em;
  }
  p {
    font-size: 1.3em;
  }
}
.fields-content.page.scroll, 
.status-content.page.scroll, 
.other-settings-content.page.scroll, 
.users-content.page.scroll,
.dnc-content.page.scroll,
.review-settings-content.page.scroll {
  height: 370px;
}
.fields-content.scroll, 
.status-content.scroll, 
.other-settings-content.scroll, 
.users-content.scroll,
.dnc-content.scroll,
.review-settings-content.scroll {
  height: 386px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.crm-settings-page, .crm-wizard-modal {

  .side-menu-heading {
    .header{ 
    display: flex;
    }
  }
.side-menu-heading {
  .icon {
    font-size: 1.3em;
    margin-right: 2px !important;
  }
}
.side-menu {
  .item {
  .item-holder {
    margin-left: 2.6em !important;
  }
}
}
}

.ui.definition.table tr td:first-child:not(.ignored), .ui.definition.table tr td.definition {
  background: #ffffff;
}

.delete-modal-header {
  background: #DB2828 !important;
  color: white !important;
}

.onboarding-modal {
  .ui.dropdown:not(.button) > .default.text, .ui.default.dropdown:not(.button) > .text {
    color: grey;
  }
  .ui.checkbox .box, .ui.checkbox label {
    font-size: 0.875em;
  }
}

.ui.fluid.action.input.white-bg {
  background: white;
}

.tox-menu.tox-collection.tox-collection--list {
  max-height: 280px !important;
}
