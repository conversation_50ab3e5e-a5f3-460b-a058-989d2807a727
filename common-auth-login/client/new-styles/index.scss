//$icon-font-path: "~bootstrap-sass/assets/fonts/bootstrap/";
//@import "~bootstrap-sass/assets/stylesheets/_bootstrap.scss";

// $icon-font-path: "~semantic-ui-css/themes/default/assets/fonts/";
@import "./stylelab";
@import "./animate.min.css"; //only for toastr
@import "./toastr.min.css"; //only for taoastr
@import "./toaster.scss";
// @import "~semantic-ui-css/semantic.css";
@import "./campaigns.scss";
@import "./prospects.scss";
@import "./stats.scss";
@import "./inbox.scss";
@import "./sr-avatar.scss";
@import "../../node_modules/react-datepicker/dist/react-datepicker.css";
@import "./agency.scss";
@import "./account-settings.scss";
@import "./templates.scss";
@import "./accounts.scss";
@import "./navbar.scss";
// @import "./features.scss";
html, body {
  min-height: 100%;
  height: 100%;
  // min-width: 1024px;
  // overflow: auto;
}

.logged-in-app {
  .outline {
    outline-style: none;
  }
}

