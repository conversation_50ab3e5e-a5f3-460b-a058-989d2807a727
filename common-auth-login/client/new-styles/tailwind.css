@tailwind base;

@tailwind components;

@tailwind utilities;



body {
  color: rgba(0, 0, 0, 0.8);
}

.date-picker1-custom-range .react-datepicker__triangle {
  display: none;
}
.date-picker1-custom-range .react-datepicker__header {
  background-color: transparent;
}
.date-picker1-custom-range .react-datepicker__day {
  color: #8992a1;

  padding: 2px 6px !important;
}
.date-picker1-custom-range .react-datepicker__day--selected {
  background-color: #0f69fa !important;
  color: white !important;
}
.date-picker1-custom-range .react-datepicker__day--in-range {
  background-color: #e4eeff;
  color: #8992a1;
}
.date-picker1-custom-range .react-datepicker__day--in-selecting-range {
  background-color: #73a7fd;
  color: white;
}
.date-picker1-custom-range .react-datepicker__day--selecting-range-end {
  background-color: #0f69fa !important;
  color: white;
}
.date-picker1-custom-range .react-datepicker__day--selecting-range-start {
  background-color: #0f69fa !important;
  color: white;
}
.date-picker1-custom-range .react-datepicker__day--keyboard-selected {
  background-color: red;
  color: white;
}

.multi-select-style .rmsc .dropdown-container {
  height: inherit;
  border: #dbdfe5 1px solid;
}
.multi-select-style .rmsc .dropdown-heading {
  height: inherit;
}
.multi-select-style .rmsc .dropdown-content {
  z-index: 10 !important;
}
.ag-theme-material .ag-header {
  text-transform: uppercase;
  --ag-header-background-color: #f9fafb;
}

.ag-theme-material .ag-icon-asc {
  --ag-icon-font-code-asc: var(--ag-icon-font-code-small-up);
}

.custom-gradient {
  background: linear-gradient(to right, #175CD3, #0CA5ED);
}

.input-formik {
  @apply appearance-none block px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-300 focus:border-gray-300 sm:text-sm;
}

.error-formik {
  @apply text-sm text-red-400 absolute;
}

.label-formik {
  @apply text-sm font-bold;
}

.simple-icon-button {
  @apply !w-fit !mr-1;
}

.button-submit-lg {
  @apply mx-[10px] py-1 px-[30px] !w-[190px] !text-base;
}

.button-formik-primary {
  @apply flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-1 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-1;
}

.button-formik-primary-outline {
  @apply flex justify-center py-2 px-4 border text-opacity-100 border-blue-1 rounded-md shadow-sm text-sm font-medium text-blue-1 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-1;
}

.button-formik-basic {
  @apply flex justify-center p-2 border border-transparent rounded-md shadow-sm text-sm font-medium bg-gray-300 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

button:disabled,
button[disabled] {
  @apply opacity-60 hover:bg-opacity-60 focus:ring-0;
}

.button-default-1 {
  @apply flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm font-medium bg-white focus:outline-none hover:ring-2 hover:ring-offset-2 hover:ring-gray-300;
}

/* h1,h2,h3,h4 {
  @apply font-playfairdisplay text-black text-opacity-80 font-bold tracking-normal;
}

p {
  @apply font-ptsans text-base text-black text-opacity-80 tracking-normal;
}

h1 {
  @apply text-3xl md:text-4xl lg:text-5xl;
}

h2 {
  @apply text-2xl md:text-3xl lg:text-4xl;
}

h3 {
  @apply text-xl md:text-2xl lg:text-3xl;
}

h4 {
  @apply text-xl;
} */

a {
  @apply cursor-pointer;
}

/* .segment-default {
  @apply font-ptsans rounded-2xl border-none shadow-xl;
} */

.default-anchor {
  @apply text-blue-default-anchor;
}

.default-dark-anchor {
  @apply text-sr-default-blue cursor-pointer hover:underline;
}

.sr-outline-button {
  @apply !bg-white !border-sr-default-grey hover:!border-sr-default-blue hover:!bg-sr-light-blue hover:!text-sr-default-blue
}

.spinner-border {
  vertical-align: -0.125em;
  border: 0.25em solid #CFD3DA;
  border-right-color: transparent;
}

.spinner-grow {
  vertical-align: -0.125em;
  animation: 0.75s linear infinite _spinner-grow;
}

.spinner-visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.toast-success {
  background-color: rgba(163, 243, 200, 1);
}

.toast-success-message {
  color: rgba(0, 0, 0, 0.87);
  text-align: center;
}

.toast-error {
  background-color: rgba(255, 204, 204, 1);
}

.sr-align-right {
  @apply float-right ml-auto;
}

.toast-error-message {
  color: rgba(0, 0, 0, 0.87);
  text-align: center;
}

.toast-warning {
  background-color: #fff8db;
}

.toast-warning-message {
  color: #b58105;
}

.toast-title {
  color: rgba(0, 0, 0, 0.87);
  font-weight: bold;
  text-align: center;
}

.toast-notification {
  background: white !important;
  /* box-shadow: none !important; */
  opacity: 1 !important;
}

.toast-notification > .toast-title {
  color: rgba(0, 0, 0, 0.87);
  font-weight: bold;
  text-align: left;
  margin-bottom: 1em;
}

.toast-notification.toast-success > .toast-title {
  color: darkgreen;
}

.toast-notification.toast-error > .toast-title {
  color: red;
}

.toast-notification > .toast-success-message,
.toast-error-message,
.toast-warning-message,
.toast-info-message,
.toast-in-progress-message {
  color: rgba(0, 0, 0, 0.87) !important;
  text-align: left !important;
}

.tw-segment {
  position: relative;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0 rgb(34 36 38 / 15%);
  margin: 1rem 0em;
  padding: 1em 1em;
  border-radius: 0.28571429rem;
  border: 1px solid rgba(34, 36, 38, 0.15);
}

#toast-container > div {
  padding: 15px;
}

#toast-container > .toast-success {
  background-image: none !important;
}

#toast-container > .toast-error {
  background-image: none !important;
}

#toast-container > .toast-warning {
  background-image: none !important;
}

.toast-close-button {
  color: #000;
}

.toast-top-center {
  top: 25px;
  right: 0;
  width: 100%;
}

/* .pricing-table-row {
  @apply border-b border-l border-r bg-white;
}

td {
  @apply p-4;
} */

[type="text"],
[type="email"],
[type="url"],
[type="password"],
[type="number"],
[type="date"],
[type="datetime-local"],
[type="month"],
[type="search"],
[type="tel"],
[type="time"],
[type="week"],
[multiple],
textarea,
select {
  border-color: inherit;
}

[type="text"]:focus,
[type="email"]:focus,
[type="url"]:focus,
[type="password"]:focus,
[type="number"]:focus,
[type="date"]:focus,
[type="datetime-local"]:focus,
[type="month"]:focus,
[type="search"]:focus,
[type="tel"]:focus,
[type="time"]:focus,
[type="week"]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
  @apply ring-transparent;
}

/* Design system related */

.sr-inbox {
  @apply font-sourcesanspro tracking-normal font-normal leading-[24px] text-[16px];
}

.sr-inbox h1,
.sr-h1 {
  @apply font-sourcesanspro tracking-normal font-semibold leading-[48px] text-[32px];
}

.sr-inbox h2,
.sr-h2 {
  @apply font-sourcesanspro tracking-normal font-semibold leading-[36px] text-[24px];
}

.sr-inbox h3,
.sr-h3 {
  @apply font-sourcesanspro tracking-normal font-semibold leading-[32px] text-[20px];
}

.sr-inbox h4,
.sr-h4 {
  @apply font-sourcesanspro tracking-normal font-semibold leading-[28px] text-[18px];
}

.sr-inbox h5,
.sr-h5 {
  @apply font-sourcesanspro tracking-normal font-semibold leading-[24px] text-[16px];
}

.sr-inbox h6,
.sr-h6 {
  @apply font-sourcesanspro tracking-normal font-semibold leading-[20px] text-[14px];
}

.sr-inbox h7,
.sr-h7 {
  @apply font-sourcesanspro tracking-normal font-semibold leading-[18px] text-[12px];
}

.sr-p {
  @apply font-sourcesanspro tracking-normal font-normal leading-[20px] text-[15px];
}

.sr-p-basic {
  @apply font-sourcesanspro tracking-normal font-normal leading-[20px] text-[14px];
}

.sr-noto {
  @apply font-noto tracking-normal font-normal leading-[20px] text-[16px];
}

.sr-inbox button:disabled:hover,
button[disabled]:hover,
button:disabled,
button[disabled] {
  opacity: 1;
  --tw-bg-opacity: 1;
}

.sr-inbox .sr-pill {
  @apply hover:bg-sr-light-blue hover:text-sr-default-blue font-normal font-sourcesanspro tracking-normal leading-[18px] text-[12px] inline-flex items-center align-bottom pr-[8px] pl-[16px] py-[6px] border border-transparent rounded text-black;
}

.sr-inbox .sr-pill.active-pill {
  @apply bg-sr-light-blue text-sr-default-blue font-semibold;
}

.multi-dropdown:hover > .multi-dropdown-content {
  opacity: 1;
  display: block;
}

.sr-label {
  padding: 0px 10px 0px 10px;
  border-radius: 5px;
}

.sr-filled-button-lg {
  @apply !text-base !font-semibold !px-5 !py-2;
}

.multi-dropdown-content {
  background: white;
  opacity: 1;
  border: 1px solid rgba(25, 59, 103, 0.05);
  box-shadow: 0px 8px 16px -4px rgba(28, 50, 79, 0.16);
  border-radius: 4px;
}
.multi-dropdown {
  background: white;
  opacity: 1;
  border-color: #000;
}

.multi-dropdown-lable:hover {
  background: rgba(209, 227, 250, 0.58);
}

.circular-btn {
  @apply inline-flex text-white justify-center relative  items-center text-[12px] outline-0 p-[9px] rounded-[20px];
}

.schedule-setting-subheader {
  @apply inline-flex items-center font-sourcesanspro text-[14px]  text-sr-default-grey;
}

.main-header-campaign-setting {
  @apply text-[16px] mb-[16px] pb-[5px] font-sourcesanspro border-b border-solid border-sr-light-grey;
}

.page-heading-strip-tw {
  @apply flex px-[16px] py-[10px] items-center;
}

.page-heading-strip-tw > .heading-tw {
  @apply flex items-center sr-h4;
}

.page-heading-strip-tw > .heading-actions-tw {
  @apply ml-auto;
}

ul {
  @apply list-disc;
}

.main-content-header-tw {
  @apply flex items-center place-content-between pb-2 px-8 border-b sr-h3;
}

.table-header-cell {
  @apply bg-sr-header-grey !text-sr-subtext-grey sr-h6 !font-semibold;
}

.sr-lines-tab-inactive {
  @apply hover:bg-sr-lighter-grey hover:border-b-sr-border-grey hover:border-b-2 hover:text-sr-default-grey;
}

.sr-button-primary {
  @apply bg-sr-default-blue hover:bg-sr-dark-blue rounded-l-none space-x-[4px] inline-flex items-center justify-center align-bottom px-2 py-[6px] sr-h7 border border-transparent rounded-[4px] text-white hover:text-white
}