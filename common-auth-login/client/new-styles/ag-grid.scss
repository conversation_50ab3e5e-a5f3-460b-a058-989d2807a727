/*

To customize, datagrid, following references would be helpful:

https://www.ag-grid.com/javascript-grid-themes-provided/#customizing-sass-variables
https://stackoverflow.com/questions/49183522/issues-customizing-the-ag-grid-themes/49325516

*/

/*
// Set the colors to blue and amber
$ag-primary-color: #2196f3; // blue-500
$ag-accent-color: #2185d0; // amber-A200

$ag-grid-size: 7px; // 8 by default
$ag-icon-size: 16px; // 18 by default

$ag-font-family: inherit;
$ag-secondary-font-family: inherit;

$ag-font-size: inherit;
$ag-secondary-font-size: inherit;

$ag-border-color: #e9e9e9; // as per figma
$ag-foreground-color: inherit; // inherit font color from the body
*/

@import "~ag-grid-community/src/styles/ag-grid.scss";

// Import the ag-Grid material theme
// @import "~ag-grid-community/src/styles/ag-theme-material/sass/ag-theme-material.scss";
@import "~ag-grid-community/src/styles/ag-theme-material/sass/ag-theme-material-mixin.scss";


// REF: https://www.ag-grid.com/javascript-grid-themes-v23-migration/
.ag-theme-material {
  border: 1px solid rgba(34, 36, 38, 0.15);
  padding: 3px;
  border-radius: 6px;

  @include ag-theme-material(
    (
      material-primary-color: #2196f3, // blue-500
      material-accent-color: #0F69FA, // amber-A200
      grid-size: 7px, // 8 by default
      icon-size: 16px, // 18 by default
      font-family: inherit,
      // secondary-font-family: inherit,
      font-size: inherit,
      // secondary-font-size: inherit,
      border-color: #e9e9e9, // as per figma
      // foreground-color: inherit, // inherit font color from the body
    )
  );

  // .ag-header {
  //     // or write CSS selectors to make customisations beyond what the parameters support
  //     text-shadow: deeppink;
  // }
}

.ag-theme-material {
  .ag-row-selected  {
      background-color: #E4EEFF;
      &::before {
          background-color: transparent;
      }
     
  }
}


.ag-theme-material .ag-checkbox-input {
  position: absolute;
}  


/*
// Following is an attempt to create a border shadow for the pinned "email" first column
.ag-theme-material .ag-ltr .ag-root:not(.ag-has-focus) .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right) {
  border-right: 1px solid #e2e2e2;
  box-shadow: 3px 0 5px -2px rgba(136, 136, 136, .3) !important;
}


.ag-theme-material .ag-ltr .ag-root:not(.ag-has-focus) .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right) .ag-row-hover {
  border-right: 1px solid #e2e2e2;
  box-shadow: 3px 0 5px -2px rgba(136, 136, 136, .3) !important;
}
*/
