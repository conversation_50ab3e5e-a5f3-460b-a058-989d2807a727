// .inbox-page {
// 	margin-bottom: 2em;
// 	.inbox-settings {
// 			// float: right;
// 			.campaign-filter {
// 				display: inline-block;
// 				margin-right: 2em;
// 			}
// 			.ui.basic.button {
// 				border-radius: 0.28571429rem;
// 			}
// 			.count {
// 				float: right;
// 				// display: inline-block;
// 				p {
// 					display: inline-block;
// 					margin-right: 1em
// 				}
// 			}
// 	}
// 	table {
// 			thead {
// 					// display: none;
// 			}
// 			tr {
// 				cursor: pointer;
// 				td {
// 					.meta {
// 						color: rgba(0,0,0,0.6);
// 					}
// 				}
// 			}
//   }

//   .ui.segments {
//     .segment {
//       .meta {
//         font-style: italic;
//         color: rgba(0,0,0,0.6);
//       }
//     }
//   }
// }

// .inbox-page-v2 {
//   margin-bottom: 2em;
//   .ui.grid {
//     margin: 0;
//     .row {
//       padding-top: 0;
//     }
//   }
//   .refresh-button {
//     border-radius: 0.28571429rem;
//   }
//   .count {
//     float: right;
//     // display: inline-block;
//     p {
//       display: inline-block;
//       margin-right: 1em
//     }
// 	}
// 	table {
// 			thead {
// 					// display: none;
// 			}
// 			tr {
// 				cursor: pointer;
// 				td {
// 					.meta {
// 						color: rgba(0,0,0,0.6);
// 					}
// 				}
// 			}
//   }

//   .column {
//     padding-left: 0 !important;
//     padding-right: 0 !important;
//   }

//   .categories-panel {
//     .ui.vertical.menu {
//       width: 100%;
//       background: #F9FAFB;
//       // overflow-y: auto;
//       height: 100%;
//       display: flex;
//       flex-direction: column;
//       .categories-list {
//         // height: calc(100% - 52px);
//         flex-grow: 1;
//         overflow-y: auto;
//       }
//       .item {
//         padding: 0.5em;
//       }
//       .campaign-filter {
//         width: 100%;
//         .ui.dropdown {
//           min-width: 100%;
//         }
//       }
//       .active.item {
//         // .label {
//         //   color: white;
//         //   background: teal;
//         // }
//         // color: #ffe100;
//         word-wrap: break-word;
//         font-weight: bold;
//       }
//     }
//   }

//   .replies-list-panel {
//     .replies-flex {
//       display: flex;
//       flex-direction: column;
//       height: 100%;
//       .search-refresh {
//         .item {
//           padding: 0.57em !important;
//           border-bottom: none !important;
//           .ui.header {
//             display: inline-block;
//             margin: 0.5em;
//           }
//           input {
//             max-width: 150px;
//           }
//           .refresh-button {
//             border-radius: 0.28571429rem;
//             float: right;
//           }
//         }
//       }
//       .reply-list {
//         // height: calc(100% - 54px);
//         flex-grow: 1;
//         overflow-y: auto;
//         border-top: none !important;
//       }
//       .ui.list {
//         font-size: 14px;
//         border: 1px solid rgba(34, 36, 38, 0.15);
//         border-left: none;
//         margin-bottom: 0;
//         margin-top: 0;
//         // overflow-y: auto;
//         // height: 100%;
//         .item {
//           padding: 1em;
//           border-bottom: 1px solid rgba(34, 36, 38, 0.15);
//         }
//         .item.reply {
//           cursor: pointer;
//           .meta {
//             font-style: italic;
//             color: rgba(0,0,0,0.6);
//           }
//           .body {
//             word-wrap: break-word;
//           }
//           .header {
//             .sender {
//               color: #1e70bf;
//               margin-bottom: 5px;
//             }
//             p {
//               color: #1e70bf;
//               margin-bottom: 5px;
//             }
//             .received-date {
//               // display: inline-block;
//               float: right;
//             }
//           }
//         }
//         .active.item.reply {
//           background: #F9FAFB;
//         }
//         .item.load-more-replies {
//           cursor: pointer;
//         }
//         .item.load-more-replies:hover {
//           background: #F9FAFB;
//         }
//         .no-replies {
//           margin-top: 2em;
//         }
//       }
//     }
//   }

//   .events-panel {
//     .prospect-events {
//       // overflow-y: auto;
//       height: 100%;
//       // padding: 1em;
//       border: 1px solid rgba(34, 36, 38, 0.15);
//       border-left: none;
//       .fixed-part {
//         padding: 1em;
//         .actions {
//           .dropdown-button {
//             margin-right: 1em;
//           }
//         }
//         .ui.header {
//           margin: 0.5em 2em 0.5em 0;
//           display: inline-block
//           // padding: 0 1em 1em 1em;
//         }
//         .prospect-basic-info {
//           display: inline-block;
//           .label {
//             margin-right: 1em;
//           }
//         }
//       }
//       .events-list {
//         // height: calc(100% - 93px);
//         flex-grow: 1;
//         overflow-y: auto;
//         padding: 0.5em;
//         margin: 0;
//       }

//       .ui.feed > .event {
//         padding-top: 0px;
//         padding-bottom: 0px;
//         min-height: 50px;
//       }
//       .ui.feed > .event .label {
//         border-left: 3px solid #DDDDDD;
//       }
//       .ui.feed > .event:last-child .label {
//         border-left-color: transparent;
//       }
//       .ui.feed > .event > .label {
//         margin-left: 0.6em;
//         width: 0.5em;
//       }
//       .ui.feed > .event > .content {
//         margin-bottom: 2em;
//       }
//       .ui.feed > .event > .content .stats {
//         .blue {
//           color: #009FDA;
//         }
//       }
//       .ui.feed > .event > .content .extra.text {
//         max-width: 100%;
//         padding: 1em;
//       }
//       .ui.feed > .event > .label > img,
//       .ui.feed > .event > .label > .icon {
//         background-color: #FFFFFF;
//         border-radius: 500rem;
//         color: #009FDA;
//         width: 3rem;
//         height: 2rem;
//         // line-height: 1.3;
//         left: -1.6rem;
//         opacity: 1;
//         position: relative;
//         font-size: 1em;
//       }

//       .subject {
//         padding-bottom: 1em;
//         .label {
//           margin-top: auto;
//           margin-bottom: auto;
//         }
//         input {
//           border: none;
//           border-bottom: 1px solid lightgray;
//         }
//       }

//       .ui.segments {
//         border: none;
//         box-shadow: none;
//         .segment {
//           border-top: none;
//           padding: 0;
//         }
//       }

//       .email-editor {
//         border: 1px solid #ccc;
//         border-radius: 22px;
//         margin-top: 1em;
//         .ql-toolbar {
//           // margin-bottom: 1em;
//           border: 1px solid aliceblue;
//           border-top-left-radius: 22px;
//           border-top-right-radius: 22px;
//           background: aliceblue;
//           .insert-variable {
//             width: 200px;
//           }
//         }
//         .subject-body-padding {
//           padding: 1em;
//           .ql-container.ql-snow {
//             border: none;
//               .ql-editor {
//               min-height: 200px;
//               padding: 12px 0 0 0;
//               p {
//                 // margin-top: 1em;
//               }
//             }
//           }
//         }
//       }

//       .open-event {
//         margin-bottom: 2em !important;
//         // text-align: right;
//         display: block !important;
//       }
//     }
//   }

//   .prospect-details-panel {
//     // height:100%;
//     // padding: 0.5em;
//     font-size: 14px;
//     border: 1px solid rgba(34, 36, 38, 0.15);
//     border-left: none;
//     .prospect-details-flex {
//       display: flex;
//       flex-direction: column;
//       height: 100%;
//       .ui.header {
//         display: inline-block;
//         margin: 0.5em;
//       }
//       h4 {
//         padding: 0.5em;
//       }
//       .prospect-details {
//         // height: calc(100% - 52px);
//         flex-grow: 1;
//         overflow-y: auto;
//         padding: 1em;
//         border-top: 1px solid rgba(34, 36, 38, 0.15);
//         .detail {
//           margin-top: 5px;
//           margin-bottom: 5px;
//           .prospect-tag {
//             font-weight: bold;
//           }
//           .prospect-value {
//             font-style: italic;
//             color: rgba(0,0,0,0.6);
//             // input {
//             //   font-style: italic;
//             //   color: rgba(0,0,0,0.6);
//             //   border: none;
//             //   border-bottom: 1px solid rgba(34, 36, 38, 0.15) !important;
//             // }
//           }
//         }
//       }
//     }
//   }

//   .border-when-no-events {
//     border: 1px solid rgba(34, 36, 38, 0.15);
//     border-left: none;
//     .ui.header.no-prospect-selected {
//       margin-top: 25%;
//     }
//   }


// }

// .inbox-page-v3 {
//   .choose-campaign {
//     .ui.dropdown {
//       background: rgba(0,0,0,0.05);
//       border: none;
//     }
//   }
//   .choose-category {
//     .ui.vertical.menu {
//       border: none;
//       box-shadow: none;
//       width: 100%;
//       .item::before {
//         height: 0;
//       }
//       .active.item {
//         font-weight: bold;
//       }
//     }
//   }
//   .search-refresh-count {
//     margin-bottom: 1em;
//     .right-actions {
//       float: right;
//       .ui.pagination.menu {
//         box-shadow: none;
//       }
//       .count {
//         display: inline-block;
//         margin-right: 10px;
//       }
//     }
//     .left-actions {
//       display: inline-block;
//       .refresh-button {
//         border-radius: 3px;
//       }
//     }
//   }
//   .choose-reply {
//     // border: 1px solid lightgray;
//     // border-radius: 5px;
//     height: inherit;
//     table {
//       table-layout: fixed;
//       white-space: nowrap;
//       // border: none;
//       td {
//         overflow: hidden;
//       }
//       .reply.unread{
//         .sender {
//           font-weight: bold;
//         }
//         .subject {
//           font-weight: bold;
//         }
//         .date {
//           font-weight: bold;
//           text-align: right;
//         }
//       }
//       .reply.read {
//         background: rgba(0,0,0,0.05);
//         .sender {
//           font-weight: bold;
//         }
//         .subject {
//           font-weight: bold;
//         }
//         .date {
//           font-weight: bold;
//           text-align: right;
//         }
//       }
//       .reply {
//         cursor: pointer;
//         .label {
//           margin-right: 5px;
//         }
//       }
//       tr:hover {
//         background: rgba(0,0,0,0.10) !important;
//       }
//     }
//     .no-replies {
//       height: 100%;
//       .icon.header {
//         margin: 10% auto;
//       }
//     }
//   }
//   .prospect-events {
//     .top-actions{
//       margin-bottom: 1em;
//       .button {
//         border-radius: 3px;
//         background: transparent;
//         border-right: 1px solid rgba(0,0,0,0.05);
//       }
//       h3 {
//         display: inline-block;
//         margin-left: 1em;
//       }
//       .dropdown-button {
//         text-align: left;
//         border-radius: 2px;
//         background: rgba(1, 0, 0, 0.06);
//         color: rgba(0,0,0,0.87);
//       }
//     }
//     .timeline-details {
//       margin-left: 0;
//       margin-right: 0;
//       .prospect-timeline {
//         border-top: 1px solid rgba(0,0,0,0.05);
//         .event {
//           .icon-section{

//           }
//           .timeline-section {
//             border-bottom: 1px solid rgba(0,0,0,0.05);
//             .title-actions {
//               .title{
//                 font-weight: bold;
//               }
//               .actions {
//                 text-align: right;
//                 .icon {
//                   cursor: pointer;
//                 }
//                 p {
//                   display: inline-block;
//                   width: 150px;
//                 }
//               }
//             }
//             .content {

//             }
//           }
//           // 'added_prospect' | 'assigned_campaign' | 'sent' | 'opened' | 'bounced' | 'replied' | 'opted_out'
//           // | 'completed' | 'unassigned_campaign' | 'assigned_member' | 'changed_category' | 'paused_campaign';
//           .added_prospect, .assigned_campaign, .assigned_member, .changed_category {
//             background: rgba(255,225,0,0.1);
//           }
//           .completed, .opened {
//             background: rgba(0,117,255,0.06);
//           }
//           .opted_out, .paused_campaign, .bounced {
//             background: rgba(220,80,80,0.08);
//           }
//         }

//       }
//       .prospect-details {
//         border-top: 1px solid rgba(0,0,0,0.05);
//         background: rgba(0,0,0,0.05);
//         h4 {
//           display: inline-block;
//         }
//         a{
//           float: right;
//         }
//         .detail {
//           margin-bottom: 1em;
//           .prospect-tag {
//             font-weight: 100;
//           }
//           .prospect-value {
//             font-weight: bold;
//           }
//         }
//       }
//     }

//   }
// }

.inbox-page-v4, .inbox-threads-page {
  .ui.grid {
    .row1 {
      padding-bottom: 5px;
    }
    .row2 {
      padding: 0;
    }
  }
  .choose-campaign {
    h4 {
      font-size: 1em;
      margin-bottom: 0.5em;
    }
    .ui.dropdown {
      background: rgba(0,0,0,0.05);
      border: none;
    }
  }
  .choose-category {
    .loader.inline.centered {
      display: block !important;
      margin: 1em auto;
    }
    .ui.vertical.menu {
      // border: none;
      box-shadow: none;
      width: 100%;
      .item.categories-list {
        padding: 0;
        .category-non-empty {
          font-weight: bold;
        }
      }
      .item::before {
        height: 0;
      }
      .active.item {
        font-weight: bold;
      }
      .item {
        // border-top: 1px solid rgba(0,0,0,0.05);
        box-shadow: inherit;
      }
      .ui.vertical.menu {
        .item:first-child {
          border-top: none;
        }
      }
    }
  }
  .search-refresh-count {
    margin-bottom: 1em;
    .right-actions {
      float: right;
      .ui.pagination.menu {
        box-shadow: none;
      }
      .count {
        display: inline-block;
        margin-right: 10px;
      }
    }
    .left-actions {
      display: inline-block;
      .refresh-button {
        border-radius: 3px;
      }
    }
  }
  .choose-reply {
    // border: 1px solid lightgray;
    // border-radius: 5px;
    height: inherit;
    display: flex;
    flex-direction: column;
    .inbox-threads-list {
      flex: 1;
      overflow-y: auto;
    }
    table {
      table-layout: fixed;
      white-space: nowrap;
      margin: 0;
      border-radius: 0;
      // border: none;
      td {
        overflow: hidden;
      }
      .reply.unread{
        .sender {
          font-weight: bold;
        }
        .subject {
          font-weight: bold;
        }
        .date {
          font-weight: bold;
          text-align: right;
        }
      }
      .reply.read {
        background: rgba(0,0,0,0.05);
        .sender {
          // font-weight: bold;
        }
        .subject {
          // font-weight: bold;
        }
        .date {
          // font-weight: bold;
          text-align: right;
        }
      }
      .reply {
        cursor: pointer;
        height: 50px;
        // .label {
        //   margin-right: 5px;
        // }
        .label-subject-section {
          height: inherit;
          .label {
            margin-right: 0.5em;
            .eye.icon {
              margin: 0;
            }
            .initials {
              font-weight: 12px;
              font-weight: normal;
            }
          }
        }

        .sender {
          .tags-label {
            margin-right: 0.5em;
            .icon {
              margin: 0;
            }
          }
        }

        .checkbox-thread {
          text-align: center;
          cursor: default;
          border-right: 1px solid rgba(0,0,0,0.05);
        }
        // .on-hover {
        //   display: none;
        //   cursor: default;
        // }
        .on-hover {
          // position:fixed;
          // right: 20px;
          // width: 200px;
          // background-color:  #ffffff !important;
          // visibility: visible;
          // display: inherit;
          // padding-top: 0.65em;
          // padding-bottom: 0.65em;
          text-align: right;
        }
      }
      // .reply:hover {
      //   background: rgba(0,0,0,0.10) !important;
      //   .on-hover {
      //     position:fixed;
      //     right: 20px;
      //     // width: 200px;
      //     // background-color:  #ffffff !important;
      //     // visibility: visible;
      //     display: inherit;
      //     padding-top: 0.65em;
      //     padding-bottom: 0.65em;
      //   }
      //   // .date {
      //   //   // visibility: hidden;
      //   //   display: none;
      //   // }
      // }
    }
    .no-replies {
      // height: 100%;
      .icon.header {
        margin: 10% auto;
      }
    }
  }
}

.prospect-events,.updateProspectModal,.thread-events, .prospect-events-modal, .activity-tab{
  .top-actions{
    margin-bottom: 1em;
    .first-line {
      margin-bottom: 1em;
      .actions-right {
        text-align: right;
        .ui.pagination.menu {
          margin-left: 1em;
          margin-top: -4px;
        }
      }
    }
    .back-button {
      border-radius: 3px;
      background: transparent;
      border-right: 1px solid rgba(0,0,0,0.05);
    }
    .pause-unpause {
      background: rgba(1, 0, 0, 0.06);
    }
    h3 {
      display: inline-block;
      margin-left: 1em;
      margin-top: 0;
    }
    .ui.grid {
      .column {
        // padding-top: 0;
        // padding-bottom: 0;
      }
      .dropdown-button {
        text-align: left;
        border-radius: 2px;
        background: rgba(1, 0, 0, 0.06);
        color: rgba(0,0,0,0.87);
        .meta {
          color: rgba(0,0,0,0.4);
        }
      }
    }
    .action-row {
      margin-right: 0;
    }
    .prev-next {
      text-align: right;
      .prev {
        margin-right: 10px;
      }
    }
  }
  .timeline-details {
    margin-left: 0;
    margin-right: 0;
    // margin-top: 1em;
    .bordered.icon {
      box-shadow: none;
      border: 1px dashed rgba(0,0,0,0.4);
      color: rgba(0,0,0,0.4);
    }
    .dropdown-button {
      text-align: left;
      border-radius: 2px;
      background: rgba(1, 0, 0, 0.06);
      color: rgba(0,0,0,0.87);
      .meta {
        color: rgba(0,0,0,0.4);
      }
    }
    .prospect-timeline, .thread-timeline {
      border-top: 1px solid rgba(0,0,0,0.05);
      .event {
        .icon-section{

        }
        .timestamp {
          p {
            color: rgba(0,0,0,0.5);
            font-size: 12px;
            display: inline;
            // margin-right: 1em;
          }
          padding: 1em;
          width: 165px;
        }
        .timeline-section {
          border-bottom: 1px solid rgba(0,0,0,0.05);
          // margin-left: -1em;
          .title-actions {
            .title{
              .highlight {
              font-weight: bold;
              color: skyblue;
              }
              .highlight1 {
                // font-weight: bold;
                font-style: italic;
              }
              // font-weight: bold;
            }
            .actions {
              text-align: right;
              color: rgba(0,0,0,0.4);
              .icon {
                cursor: pointer;
              }
              p {
                display: inline-block;
                width: 150px;
              }
            }
          }
          .subject {
            font-weight: bold;
            margin-bottom: 10px;
          }
          .content {

          }
          .show-more {
            margin: 0;
            padding: 0;
            box-shadow: 0 -10px 20px rgba(0,0,0,0.05);
            width: 100%;
            text-align: center;
            background: rgba(0,0,0,0.05);
          }
          .cursor-pointer {
            cursor: pointer;
          }
        }
        // 'added_prospect' | 'assigned_campaign' | 'sent' | 'opened' | 'bounced' | 'replied' | 'opted_out'
        // | 'completed' | 'unassigned_campaign' | 'assigned_member' | 'changed_category' | 'paused_campaign';
        .added_prospect, .assigned_campaign, .assigned_member, .changed_category {
          background: rgba(255,225,0,0.1);
        }
        .completed, .opened {
          background: rgba(0,117,255,0.06);
        }
        .opted_out, .paused_campaign, .bounced {
          background: rgba(220,80,80,0.08);
        }
        .replied {
          background: rgba(34,210,1122,0.1);
        }
      }

      .event.initial-reply {
        .initial-reply-box {
          border: 1px solid rgba(0,0,0,0.2);
          margin-top: 1em;
          height: 80px;
          margin-right: 1em;
        }
      }

    }

    .thread-timeline {
      padding-left: 0px !important;
      .subject-actions {
        .subject {
          font-weight: bold;
          margin-bottom: 10px;
        }
      }
      .title-actions {
        .title{
          .highlight {
          font-weight: bold;
          color: skyblue;
          }
          .highlight1 {
            // font-weight: bold;
            font-style: italic;
          }
          // font-weight: bold;
        }
        .actions {
          text-align: right;
          color: rgba(0,0,0,0.4);
          .icon {
            cursor: pointer;
          }
          p {
            display: inline-block;
            width: 150px;
          }
        }
      }
      .event-title {
        display: inline-block;
        margin-left: 20px;
        margin-bottom: 15px;
      }
      .show-details {
        display: inline-block;
        margin-left: 5px;
        .ui.button {
          background: transparent;
          padding: 2px;
        }
        .ui.button:hover {
          background-color: #BABBBC;
        }
      }
      .email-time-stamp {
        display: inline-block;
        padding-right: 10px;
        margin-right: 10px;
        border-right: 1px solid lightgray;
      }
      .initial-reply {
        margin-left: 0px;
      }
    }

    .prospect-details {
      border-top: 1px solid rgba(0,0,0,0.05);
      background: rgba(0,0,0,0.05);
      .prospect-info {
        h4 {
          display: inline-block;
        }
        .edit{
          float: right;
        }
        .detail {
          margin-bottom: 1.3em;
          .prospect-tag {
            color: rgba(0,0,0,0.4);
            margin-bottom: 3px;
            font-weight: bold;
            word-wrap: break-word;
          }
          .prospect-value {
            font-weight: bold;
            word-wrap: break-word;
            .pause-unpause {
              margin-left: 5px;
            }
          }
        }
      }
      .complete-activity {

      }
      .other-threads {

      }
      .divider {
        margin-top: 10px;
        margin-bottom: 10px;
        border: 1px solid rgba(0,0,0,0.1);
      }
    }
  }
  

}

.thread-events-subtab{
  height: inherit;
  .thread-timeline-flex {
    display: flex;
    flex-direction: column;
    height: inherit;
    .thread-header {
      flex: 0 0 33px;
      display: flex;
      flex-direction: row;
      background-color: rgba(40, 40, 40, 0.04);
      color: rgba(40, 40, 40, 0.6);
      .thread-subject {
        font-weight: bold;
        padding: 0.5em 1em;
        flex: 1;
      }
      .thread-actions {
        margin-left: auto;
        height: inherit;
        display: flex;
        .button {
          margin: 0;
          border-radius: 0;
          height: 100%;
          border-left: 1px solid;
          padding-left: 1em;
          padding-right: 1em;
        }
        .ui.dropdown.button {
          .icon {
            margin: 0;
          }
          .item {
            .icon {
              margin-right: 5px;
            }
          }
        }
      }
    }
    .thread-content {
      flex:1;
      padding: 1em;
      height: inherit;
      overflow: auto;
      .compose-reply  {
        min-height: 300px;
      }
    }
  }
}

.show-details-inbox-popup {
  .ui.grid {
    .row {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      .column.detail-label {
        text-align: right;
      }
      .column.detail-label {

      }
    }
  }
}

.inbox-wrapper {
  height: inherit;
  .global-page-flex {
    .heading-strip {
      .heading {
        width: 100%;
        .page-controls {
          margin-left: auto;
        }
      }
    }
  }
}

.prospect-emails-tab {
  display: flex;
  flex-direction: row;
  height: inherit;
  margin-left: 1em;
  .threads-list {
    height: inherit;
    width: 300px;
    // overflow-y: auto;
    box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1), -1px 0px 0px rgba(40, 40, 40, 0.1);
    display: flex;
    flex-direction: column;
    .threads-items-list {
      overflow-y: auto;
      flex: 1;
      .thread-preview.active {
        // box-shadow: inset -4px 0px 0px #2387CD, 0px 1px 0px rgba(40, 40, 40, 0.1);
        box-shadow: inset 2px 0px 0px 0 #0f69fa, 1px 1px 0px rgba(40, 40, 40, 0.1);
      }
      .thread-preview {
        padding: 1em;
        box-shadow: 1px 1px 0px rgba(40, 40, 40, 0.1);
        overflow-y: auto ;
        margin-left: 5px;
        cursor: pointer;
        .unread {
          font-weight: bold;
        }
        .subject-preview {
          white-space: nowrap;
          overflow: hidden;
          color: rgba(40, 40, 40, 0.6);
        }
        .body-preview {
          white-space: nowrap;
          overflow: hidden;
        }
        .thread-meta-details {
          margin-top: 0.5em;
          display: flex;
          flex-direction: row;
          .sender.ui.label.read {
            font-weight: normal;
            width: 200px;
            word-break: break-all;
          }
          .date {
            margin-left: auto;
            color: rgba(40, 40, 40, 0.6);
          }
        }
      }
    }
    .threads-header {
      padding: 0.5em 1em;
      background-color: rgba(40, 40, 40, 0.04);
      color: rgba(40, 40, 40, 0.6);
      border-right: 1px solid rgba(40, 40, 40, 0.6);
    }
    
  }
  .specific-thread {
    height: inherit;
    flex: 1;
    overflow: hidden;
  }
}

.email-details-popup {
  .email-details-row {
    .email-detail-label {
      width: 50px;
      color: grey;
      display: inline-block;
      margin-right: 1em;
      text-align: right;
    }
  }
}

.inbox-wrapper {
  .new-inbox-v3 {
    height: inherit;
    .inbox-pane-flex {
      height: inherit;
      display: flex;
      flex-direction: row;
      .inbox-pane {
        width: 200px;
        // overflow: hidden;
        // overflow-y: overlay;
        .ui.menu.fluid.vertical {
          height: 100%;
          background: white;
          box-shadow: none;
          border: none;
         //border-right: 1px solid rgba(34, 36, 38, 0.15);
          overflow-y: overlay;
          border-radius: 0;
          .item {
            .menu {
              .item {
                padding-left: 2em;
                padding-top: 10px;
                padding-bottom: 10px;
                word-break: break-all;
                // padding-right: 20px;
              }
              .active.item {
                background-color: rgba(0,0,0,0.05);
              }
            }
          }
        }
      }
      .ui.menu.vertical::-webkit-scrollbar-track {
        background: transparent;
      }
      .ui.menu.vertical:hover::-webkit-scrollbar-track {
        // background:rgba(0, 0, 0, 0.1);
      }
      .ui.menu.vertical::-webkit-scrollbar-thumb {
        background: transparent;
      }
      .ui.menu.vertical:hover::-webkit-scrollbar-thumb {
        background:rgba(0, 0, 0, 0.45);
      }
      .ui.menu.vertical::-webkit-scrollbar {
        width: 6px;
      }
      // .inbox-pane:hover {
      //   overflow-y: auto;
      //   .ui.menu.fluid.vertical {
      //     .item {
      //       .menu {
      //         .item {
      //           padding-right: 10px;
      //         }
      //       }
      //     }
      //   }
      // }
      .rest-after-inbox-pane {
        flex: 1;
        max-width: calc(100% - 200px);
        background-color: rgba(0, 0, 0, 0.05);
        .inbox-threads-section {
          width: 100%;
          height: 100%;
          .threads-pane-flex {
            height: inherit;
            display: flex;
            flex-direction: row;
            .threads-pane {
              width: 300px;
              background-color: rgba(0, 0, 0, 0.05);
              //border-right: 1px solid rgba(34, 36, 38, 0.15);
              // overflow: hidden;
              // overflow-y: auto;
              // overflow-y: overlay;
              .ui.menu.vertical {
                height: 100%;
                border: none;
                box-shadow: none;
                overflow-y: auto;
                //https://github.com/antfu/icones/issues/29 overflow-y: overlay not supported in firefox
                overflow-y: overlay;
                .item {
                  .inbox-folders  {
                    margin: 1em 0;
                    .ui.button {
                      border-radius: 2em;
                      margin-right: 1em;
                    }
                  }
                  .item {
                    padding-top: 10px;
                    padding-bottom: 10px;
                    word-break: break-all;
                    // padding-right: 20px;
                  }
                  .active.item {
                    background-color: rgba(0,0,0,0.05);
                  }
                }
              }
              .ui.menu.vertical::-webkit-scrollbar-track {
                background: transparent;
              }
              .ui.menu.vertical:hover::-webkit-scrollbar-track {
                // background:rgba(0, 0, 0, 0.1);
              }
              .ui.menu.vertical::-webkit-scrollbar-thumb {
                background: transparent;
              }
              .ui.menu.vertical:hover::-webkit-scrollbar-thumb {
                background:rgba(0, 0, 0, 0.45);
              }
              .ui.menu.vertical::-webkit-scrollbar {
                width: 6px;
              }
            }
            // .threads-pane:hover {
            //   overflow-y: overlay;
            //   .ui.menu.fluid.vertical {
            //     .item {
            //       .menu {
            //         .item {
            //           padding-right: 10px;
            //         }
            //       }
            //     }
            //   }
            // }
          }
            .rest-after-threads-pane  {
              flex: 1;
              height: inherit;
              max-width: calc(100% - 300px);
              .pushable {
                .ui.sidebar.prospect-sidebar {
                  box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                  background: #ffffff;
                  border-top: 1px solid rgba(0, 0, 0, 0.2);
                  padding: 0;
                  height: inherit;
                  border-left: 1px solid rgba(34, 36, 38, 0.15);
                }
                .pusher {
                  height: inherit;
                  // overflow: auto;
                }
              }
              .thread-events-subtab {
                .select-thread-prompt  {
                  padding: 15%;
                }
                .thread-timeline-flex {
                  .thread-header {
                    .thread-subject {
                      white-space: nowrap;
                      overflow: hidden;
                    }
                    .thread-actions {
                      margin-left: 1em;
                    }
                  }
                  .thread-content {
                    .event {
                      .step.content {
                        .extra.text {
                          overflow-x: auto;
                        }
                      }
                    }
                    // overflow: hidden;
                    // padding-right: 20px;
                    overflow-y: overlay;
                  }
                  // .thread-content:hover  {
                  //   overflow-y: auto;
                  //   padding-right: 10px;
                  // }
                  .thread-content::-webkit-scrollbar-track {
                    background: transparent;
                  }
                  .thread-content:hover::-webkit-scrollbar-track {
                    // background:rgba(0, 0, 0, 0.1);
                  }
                  .thread-content::-webkit-scrollbar-thumb {
                    background: transparent;
                  }
                  .thread-content:hover::-webkit-scrollbar-thumb {
                    background:rgba(0, 0, 0, 0.45);
                  }
                  .thread-content::-webkit-scrollbar {
                    width: 6px;
                  }
                }
              }
              .thread-events-subtab.sidebar-padding-left {
                padding-left: 260px;
              }
            }
        }
      }
    }
      
  }
}

.search .results {
  overflow-y: auto;
  max-height: 250px;
  }

.team_inbox_menu {
  display: flex;

  .team_inbox_name{
    flex: 1;
  }
  .edit_icon{
    background-color: rgba(0, 0, 0, 0.05);
  }
}  


.empty-inbox {
  .ui.segment {
    min-height: 190px;
  }
  .ui.segment:hover {
    cursor: pointer;
    margin-top: -5px;
  }
}
.email_search{
  position: relative;
  height: inherit;
  width: inherit;
  display: flex;
  z-index: 100;
  .email_search_input{
    flex-grow: 1;
    transform: scale(1.09, 1) translateX(4%);
  }
  .search_filter{
    transform: translate(-10%, 35%); 
    color: #999999; 

    .search_filter_icon{
      cursor: pointer;
    
      .menu{
        right: 0;
        left: auto;
      }
    }
  }
}