.templates-page{
  margin-bottom: 2em;

  .page-header {
    margin-top: 5px;
    .title {
      display: inline-block;
      margin-left: 0.5em;
    }
  }

  .ui.vertical.menu {
    width: 80%;
  }

  .empty.template-list {
    .ui.segment {
      min-height: 190px;
    }
    .ui.segment:hover {
      cursor: pointer;
      margin-top: -5px;
    }
  }
  .text-truncate {
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .search-box {
    .ui.icon.input{
      width: 300px;
    }
  }
  a:hover {
    cursor: pointer;
  }

  .templates-table-section {
    // padding: 2em;
    .template-unit{
      margin-bottom: 2em;
      // h4 {
      //   margin-top: 2em;
      // }
      .ui.segments.template {
        .ui.segment.label-height {
          height: 49px;
          .label {
            height: inherit;
            // padding-top: 22px;
            .label-padding {
              padding-top: 10px;
              display: inline-block;
            }
            .actions {
              display: inline-block;
              float: right;
              .share-with-team {
                background: white;
              }
              .button {
                margin-left: 2em;
              }
              .checkbox {
                margin-right: 5px;
              }
            }
          }
        }
        .ui.segment.subject {
          .label {
            margin-right: 1em;
          }
        }
        .ui.segment.body {
          p {
            margin: 0 !important;
          }
        }
      }
      .users.icon {
        margin-left: 1em;
      }
      .owner-info {
        color: burlywood;
        font-style: italic;
      }
    }
  }

  

}

.template-step {
  .subject {
    padding-bottom: 1em;
    .label {
      margin-top: auto;
      margin-bottom: auto;
    }
    input {
      border: none;
      border-bottom: 1px solid lightgray;
    }
    #toolbar {
      .insert-variable {
        width: 200px;
      }
    }
  }

  .ui.segments {
    border: none;
    box-shadow: none;
    .segment {
      border-top: none;
    }
  }

  .ql-editor {
    min-height: 200px;
    p {
      margin-top: 1em;
    }
  }


  .share-with-team {
    background: white;
  }
}
