.account-settings-page {
  margin-bottom: 2em;
  .ui.vertical.menu {
    width: 90%;
  }
  .page-header {
    margin-top: 5px;
    .title {
      display: inline-block;
      margin-left: 0.5em;
    }
  }
  .account-settings-tab {
    padding: 2em;
    margin-bottom: 2em;
    .agency-teammates-list {
      .grey.icon {
        color: #d1d1d1 !important;
      }
    }
    .api-key {
      label {
        margin: auto;
      }
      .key {
        margin: auto;
        p {
          border-bottom: 1px solid lightgray;
        }
      }
    }
    .user-management {
      .invitees-list {
        margin-bottom: 3em;
      }
      .teammates-list {

      }
      .ui.table {
        font-size: inherit;
      }
      .team-level-details {
        .invite {
          text-align: right;
        }
      }
      .grey.icon {
        color: #d1d1d1 !important;
      }
    }
    .user-management.agency{
      .team-list {
        h4 {
          margin-top: 1em;
        }
        .six.wide.column {
          text-align: right;
        }
      }
      .team-level-details {
        margin-top: 3em;
        .teammates-list {
          .header-line {
            h4 {
              padding-top: 1em;
            }
            .actions {
              text-align: right;
            }
          }
        }
      }
      .grey.icon {
        color: #d1d1d1 !important;
      }
    }

  }

  .email-accounts-settings-page {
    .add-email-account {
      margin-top: -1em;
    }
    .ui.cards {
      .card {
        .edit-signature {
          float: right;
        }
      }
    }
    .email-accounts-list-table{
      overflow-x: auto;

      .red-outline:hover{
        border: 1px solid red;
      }
    }
  }
  .empty-donotcontact-list {
    margin-top: 50px;
    margin-bottom: 125px !important;
  }

  .permissions-section {
    .showLeftBorder {
      border-left: 1px solid rgba(34, 36, 38, 0.1) !important;
    }
  }

  .ui.button.back {
    border-radius: 6px;
    background: transparent;
    padding-left: 0.25em;
    padding-right: 0.25em;
  }

}

.signature {
  .ql-editor {
    min-height: 100px;
  }
}

.email-settings-modal {
  .asp_row {
    background: beige;
  }
  .ui.segment {
    border-radius: 5px;
    border-width: 1px;
    .ui.grid.padding {
      padding: 1em;
      .seven.wide.column.smtp, .seven.wide.column.imap {
        border: 1px solid lightgray;
        border-radius: 22px;
        background: aliceblue;
        .field {
          margin-bottom: 1em;
        }
      }
    }
    .cursor.not-allowed {
      .disabled.radio.checkbox label {
        cursor: not-allowed !important;
      }
    }
  }

  .delay-settings {
    label {
      font-weight: normal !important;
    }
  }

}

.pricing-plans-modal {
  .menu-holder  {
    text-align: center;
    .menu.compact {
      padding: 0.75em 1.5em;
      align-items: center;
      background: #FFFFFF;
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
      border-radius: 30px;
      .item {
        margin: 0 1em;
        padding-left: 20px;
        padding-right: 20px;
        border-radius: 2em;
        text-transform: uppercase;
        letter-spacing: 0.13em;
        font-weight: bold;
        justify-content: center;
        font-size: 12px;
      }
      .item:before {
        width: 0;
      }
      .active.item {
        color: white;
        background: #0F69FA;
        box-shadow: 0px 4px 14px rgba(15, 105, 250, 0.3);
        border-radius: 30px;
      }
    }
  }
  .ui.toggle.checkbox input:checked ~ .box:before, .ui.toggle.checkbox input:checked ~ label:before {
    background-color: rgba(0,0,0,0.15) !important;
  }
  .ui.toggle.checkbox .box:before, .ui.toggle.checkbox label:before {
    background: rgba(0,0,0,0.15);
  }
  .ui.toggle.checkbox .box:after, .ui.toggle.checkbox label:after {
    background: #03e26b;
  }
  .period {
    text-align: center;
    margin-bottom: 1em;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    p {
      display: inline-block;
      font-size: 12px;
      margin: 0;
    }
  }
  .checkbox {
    margin: 0 1em;
  }
  .ui.card {
    text-align: center;
    padding: 0 1em 1em 1em;
    cursor: pointer;
    text-decoration: none;
  }
  .user-count {
    margin-top: 1em;
    .ui.dropdown {
      margin-left: 1em;
      min-width: 75px;
      .menu {
        height: 100px;
        overflow-y: auto;
      }
    }
  }
  .total-amount {
    margin-top: 1em;
  }
  .actions {
    h4 {
      display: inline-block;
    }
  }

}

.pricing-plans-modal, .update-card-modal {
  .coupon-section {
    .checkbox {
      margin: 0 1em 0 0;
    }
    .checkbox-text {
      display: inline-block;
      cursor: pointer;
    }
    .coupon-code {
      margin-top: 0.5em;
      .coupon-code-input {
        margin-right: 1em;
      }
      .coupon-valid {
        color: green;
      }
      .coupon-invalid {
        color: red;
      }
    }
  }
  .net-amount-section {
    margin-top: 1em;
  }
  .coupon-applied-section {
    margin-top: 1em;
  }

  .currency-panes {
    // width: 120px !important;
    // margin: -1em 0 1em 0;
    margin-left: auto;
    width: 120px;
    margin-bottom: 1em;
  }

  .checkout-form {
    .card-section,.address-section {
      .row {
        padding: 0;
      }
    }

    .stripe-mention {
      text-align: right;
    }

    * {
      box-sizing: border-box;
    }

    body,
    html {
      background-color: #f6f9fc;
      font-size: 18px;
      font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    }

    h1 {
      color: #32325d;
      font-weight: 400;
      line-height: 50px;
      font-size: 40px;
      margin: 20px 0;
      padding: 0;
    }

    .Checkout {
      margin: 0 auto;
      max-width: 800px;
      box-sizing: border-box;
      padding: 0 5px;
    }

    label {
      color: rgba(0,0,0,0.8);
      font-weight: 300;
      letter-spacing: 0.025em;
    }

    button {
      white-space: nowrap;
      border: 0;
      outline: 0;
      display: inline-block;
      height: 40px;
      line-height: 40px;
      padding: 0 14px;
      box-shadow: 0 4px 6px rgba(50, 50, 93, .11), 0 1px 3px rgba(0, 0, 0, .08);
      color: #fff;
      border-radius: 4px;
      font-size: 15px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.025em;
      background-color: #2185D0;
      text-decoration: none;
      -webkit-transition: all 150ms ease;
      transition: all 150ms ease;
      margin-top: 10px;
      max-width: 500px;
      width: 100%;;
    }

    form {
      margin-bottom: 40px;
      margin-top: 40px;
      // padding-bottom: 40px;
      // border-bottom: 3px solid #e6ebf1;
    }

    button:hover {
      color: #fff;
      cursor: pointer;
      background-color: #0d71bb;
      transform: translateY(-1px);
      box-shadow: 0 7px 14px rgba(50, 50, 93, .10), 0 3px 6px rgba(0, 0, 0, .08);
    }

    input,
    .StripeElement {
      display: block;
      margin: 10px 0 20px 0;
      max-width: 500px;
      width: 100%;
      padding: 10px 14px;
      font-size: 1em;
      font-family: 'Source Code Pro', monospace;
      box-shadow: rgba(50, 50, 93, 0.14902) 0px 1px 3px, rgba(0, 0, 0, 0.0196078) 0px 1px 0px;
      border: 0;
      outline: 0;
      border-radius: 4px;
      background: white;
      max-height: 40px;
      border: 1px solid lightgray;
    }

    input::placeholder {
      color: #aab7c4;
    }

    input:focus,
    .StripeElement--focus {
      box-shadow: rgba(50, 50, 93, 0.109804) 0px 4px 6px, rgba(0, 0, 0, 0.0784314) 0px 1px 3px;
      -webkit-transition: all 150ms ease;
      transition: all 150ms ease;
    }

    .StripeElement.IdealBankElement,
    .StripeElement.PaymentRequestButton {
      padding: 0;
    }

  }

  .checkout-form {
    .logo {
      width: 100px;
      margin-top: -75px;
    }
    .logo-text {
      font-family: 'muli';
      font-weight: 400;
      font-size: 18px;
      text-align: center;
    }
    .checkout-text {
      font-weight: bold;
      text-align: center;
      text-decoration: underline;
    }
  }

  .card-type-section {
    .error {
      color: red;
    }
    .radio.checkbox {
      margin: 0 2em 0 0;
    }
  }
}

.billing-address-modal {
  .actions {
    text-align: right;
  }
}

.ui.fullscreen.modal.transition.visible.map-fields-modal {
  left: unset !important;
}

.map-fields-tab-view {
  .tab {
    padding: 0;
  }
  .form-field-label {
    font-weight: bold;
    margin-bottom: 0.5em;
  }
}

.trigger-actions-manage-tab, .map-fields-modal {

  .specific-trigger {
    margin-top: 1em;
    .ui.fluid.search.selection.dropdown {
      width: 50%;
      min-width: 500px;
    }
  }

  .form-field-label {
    font-weight: bold;
    margin-bottom: 0.5em;
  }
  .form-field {
    margin-bottom: 2em;
  }
  .fieldsBlock {
    // padding: 1em;
    // border: 1px solid rgba(34, 36, 38, 0.15);
    // box-shadow: 0px 1px 2px 0 rgba(34, 36, 38, 0.15);
    // border-radius: 0.28571429rem;
    // -webkit-box-shadow: 0px 1px 2px 0 rgba(34, 36, 38, 0.15);
    // box-shadow: 0px 1px 2px 0 rgba(34, 36, 38, 0.15);
  }
}

.triggers-tab {
  .empty-triggers-list {
    margin-top: 50px;
    margin-bottom: 125px !important;
    a:hover {
      text-decoration: none;
    }
  }
}

.prospect-settings {
  .field {
    margin-bottom: 1em !important;
  }
  .reply-handling-radio {
    margin-bottom: 1em;
    .ui.radio.checkbox {
      display: block;
    }
  }
}