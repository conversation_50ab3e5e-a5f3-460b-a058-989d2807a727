.ui.grid.stats-tab{
  // margin-top:20px;
  margin-bottom:20px;
  .recharts-responsive-container{
      // width: 100%;
      // height:300px;
      margin-bottom: 10px;
      padding-top:5px;
  }

  .inline-heading {
    display: inline-block;
  }

  .row.no-stats-img {
    z-index: 1;
    margin-bottom: -300px;
    img {
      height: 150px;
      width: 150px;
      margin: auto;
    }
  }

  .date-picker-modal.time-val {
    margin-top: -1em;
    background: #f5f5f5;
    margin-right: 2em;
    .load-button {
      margin-left: 1em;
    }
  }

  .timezone-info {
    padding: 0;
    // text-align: right;
    // color: burlywood;
  }

  .variant-row {
    .label {
      padding-left: 8em;
    }
  }
}

.date-picker-modal{
  // padding-top: 20px !important;
  // padding-bottom: 20px !important;
  strong{
    padding-top: 7px;
  }
}

.custom-date-range-picker {
  width: 125px;
}

.reports-page {

  .pull-right {
    float: right;
  }

  .timezone-info {
    padding: 0;
    // text-align: right;
    // color: burlywood;
    margin-top: 1em;
  }

  .date-picker-modal.time-val {
    margin-top: -1em;
    background: #f5f5f5;
    margin-right: 2em;
    .load-button {
      margin-left: 1em;
    }
  }

  .conditional-filter-section {
    .custom-date-range-picker {
      width: 100%;
    }
    .date-picker-modal.time-val {
      margin-top: 0.5em;
      background: #f5f5f5;
      margin-right: 0;
      display: flex;
      flex-direction: column;
      .flex-row-field {
        margin-bottom: 0.5em;
        display: flex;
        flex-direction: row;
        .time-label {
          font-weight: bold;
          width: 50px;
        }
        .react-datepicker-wrapper {
          flex:1;
        }
      }
      .load-button {
        margin-left: 0;
      }
    }
  }

  // .ui.vertical.menu {
  //   width: 90%;
  // }
  .recharts-responsive-container {
    padding-top: 10px;
    // margin-left: 90px;
    // .recharts-wrapper {
    //   margin-left: -90px;
    // }
  }
  .scatter_resp_contianer_border {
    border-top: 1px solid rgba(0,0,0,0.05);
    // padding-top: 10px;
  }

  // react-multi-select with checkbox component
  // .multi-select {
  //   width: 200px;
  //   margin-right: 20px;
  // }
  .stats-tab {
    .with-pagination-title {
      display: flex;
      flex-direction: row;
      align-items: center;
      h3 {
        margin: 0;
      }
      .pagination-section {
        margin-left: auto;
        // height: 45px;
        // box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);
        .page-items-count {
          display: inline-flex;
          height: inherit;
          align-items: center;
          padding-left: 2em;
        }
        .page-controls {
          display: inline-flex;
          padding-left: 1em;
          padding-right: 1em;
          height: inherit;
          align-items: center;
          .ui.basic.button {
            border: none;
            box-shadow: none;
          }
        }
      }
    }
  }
}