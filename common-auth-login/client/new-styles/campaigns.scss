.content.tab,
.preview-tab {

  .campaign-step {
    .variants {
      padding-left: 2em;

      .ui.feed>.event {
        padding-top: 0px;
        padding-bottom: 0px;
        border-left: 1px dashed lightgray;
      }

      .ui.feed>.event .label {
        // border-left: 3px solid #DDDDDD;
        border-top: 1px dashed lightgray;
        margin-top: 2.25em;
      }

      .ui.feed>.event:last-child .label {
        // border-left-color: transparent;
      }

      .ui.feed>.event>.label {
        // margin-left: 1.6em;
      }

      .ui.feed>.event>.content .extra.text {
        max-width: 100%;
      }

      .ui.feed>.event>.label>img,
      .ui.feed>.event>.label>.icon {
        background-color: #FFFFFF;
        border-radius: 500rem;
        color: rgba(0, 0, 0, 0.4);
        width: 3rem;
        height: 3rem;
        line-height: 1.3;
        // left: -1.6rem;
        opacity: 1;
        position: relative;
      }

      .extra.text {
        min-height: 20px;
      }

      .step.content {
        // border: none;
        box-shadow: none;
        padding: 1em;
        margin-right: 1.5em;
        padding-bottom: 2em !important;
        border: 1px solid rgba(0, 0, 0, 0.1);
        background: rgba(0, 0, 0, 0.02);
        margin-bottom: 2em !important;
        margin-left: 1em !important;

        .stats {
          margin-bottom: 1em;

          .step-title {
            color: rgba(0, 0, 0, 0.4);
          }

          .icon {
            float: right;
          }
        }
      }

      .add-variant-button {
        margin-top: 1em;
        margin-left: 1em;
      }

      .variant-toggle-checkbox {

        float: right;
        padding-top: 2px;

        // START: for small checkbox toggle used to pause/resume variant for A/B testing steps

        .ui.toggle.checkbox .box:before,
        .ui.toggle.checkbox label:before {
          width: 2rem !important;
          height: 1rem !important;
          background-color: darkgray;
        }

        .ui.toggle.checkbox input:checked~.box:before,
        .ui.toggle.checkbox input:checked~label:before {
          background-color: #2cd021 !important;
        }

        .ui.toggle.checkbox input:checked~.box:after,
        .ui.toggle.checkbox input:checked~label:after {
          left: 1rem !important;
        }

        .ui.toggle.checkbox input~.box:after,
        .ui.toggle.checkbox input~label:after {
          left: 0.05rem;
          box-shadow: none;
          /* color: red; */
        }

        .ui.toggle.checkbox .box:after,
        .ui.toggle.checkbox label:after {
          width: 1rem !important;
          height: 1rem !important;
          top: 0 !important;

          // border around the circle button on top of toggle
          border: 1px solid darkgray;
        }

        // END: for small checkbox toggle used to pause/resume variant for A/B testing steps
      }
    }
  }


  .ui.feed>.event {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .ui.feed>.event .label {
    // border-left: 3px solid #DDDDDD;
  }

  .ui.feed>.event:last-child .label {
    // border-left-color: transparent;
  }

  .ui.feed>.event>.label {
    // margin-left: 1.6em;
  }

  .ui.feed>.event>.content .extra.text {
    max-width: 100%;
  }

  .ui.feed>.event>.label>img,
  .ui.feed>.event>.label>.icon {
    background-color: #FFFFFF;
    border-radius: 500rem;
    color: rgba(0, 0, 0, 0.4);
    width: 3rem;
    height: 3rem;
    line-height: 1.3;
    // left: -1.6rem;
    opacity: 1;
    position: relative;
  }

  .html.edition {
    min-height: 250px;
  }

  .extra.text {
    min-height: 20px;
  }

  .step.content {
    cursor: pointer;
    border: none;
    box-shadow: none;
    padding-right: 0;
    margin-right: 1.5em;
    padding-bottom: 2em !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 1em !important;

    .stats {
      margin-bottom: 1em;

      .step-title {
        color: rgba(0, 0, 0, 0.4);
      }

      .icon {
        float: right;
      }
    }
  }

  .step.content.no-cursor {
    cursor: default;
  }

  .proceed-button {
    margin: 1em;
    text-align: right;
  }

  .ui.centered.grid {
    margin: 0;

    .column {
      padding: 0;
    }
  }

}

.preview-tab .step.content .stats {
  display: flex;
  flex-direction: row;
  align-items: center;

  .button {
    margin-left: auto;
  }

  .dropdown {
    margin-right: 5px;

    .icon {
      float: none;
    }
  }
}

// .content.tab{
//   .step.content {
//     cursor: pointer;
//   }
// }

.team-name-input {
  width: 50%;

  input {
    border-bottom: 1px solid rgba(34, 36, 38, 0.15) !important;
    border-top: 0 !important;
    border-left: 0 !important;
    border-right: 0 !important;
  }
}

.campaigns-page {
  .global-page-flex {
    .campaign-list {

      .play_button {
        padding: 8px !important;
        text-align: center;

        i {
          margin: 0px !important;
          font-size: 20px;
        }
      }

      .campaign-name {
        a {
          word-wrap: 'break-word';
        }

        .label {
          margin-left: 5px;
        }
      }

      .campaign-list-table {
        overflow-x: auto;
        padding-bottom: 5em;
      }

      .ui.table {
        .one.wide {
          min-width: 75px;
        }

        .two.wide {
          min-width: 150px;
        }

        .column_actions {
          min-width: 120px !important;
        }

        th {
          padding: 0.7em 0.5em;
        }

        td {
          padding: 0.7em 0.5em;

          .campaign-progress-bar {
            display: table;
            width: 100%;

            div {
              display: table-cell;
              height: 1em;
            }

            .right-rounded {
              border-bottom-right-radius: 5px;
              border-top-right-radius: 5px;
            }

            .left-rounded {
              border-bottom-left-radius: 5px;
              border-top-left-radius: 5px;
            }

            .newProspects {
              background: #0076FF;
              border-bottom-left-radius: 5px;
              border-top-left-radius: 5px;
            }

            .toCheckProspects {
              background: #F59B2D;
            }

            .emailInValidProspects {
              background: #DC5050;
            }

            .inProgressProspects {
              background: #FFCD00;
            }

            .completedProspects {
              background: #03E26B;
            }

            .optedOutProspects {
              background: #000000;
              opacity: 0.6;
            }

            .doNotContactProspects {
              background: darkgray;
              border-bottom-right-radius: 5px;
              border-top-right-radius: 5px;
            }
          }

          .open-rate-format {
            // color: #FFCD00;
          }

          .reply-rate-format {
            color: #03E26B;
          }

          .error-rate-format {
            color: #DC5050;
          }
        }

        .table_campaign_start_stop,
        .table_create_duplicate,
        .table_delete_campaign {
          // text-align: center;
        }
      }

      .empty-campaign-list {
        margin-top: 20px;
        margin-bottom: 125px !important;

        .video-card {
          margin-top: 80px;
          min-height: 250px;
        }

        .video-campaigns-page {
          margin: 20px 0;
          width: 75%;
          min-height: 200px;
          background: url('../assets/rsz_maxresdefault.png') no-repeat center !important;
          box-shadow: 0px 0px 10px 1px grey !important;

          .icon {
            color: red;
            margin: 0 !important;
          }
        }

        .video-campaigns-page:hover {
          box-shadow: 0px 0px 20px 1px grey !important;
        }
      }
    }
  }
}

.campaign-progress-bar-popup {
  a {
    color: inherit;
  }

  div {
    padding-top: 4px;
  }

  .newProspects {
    color: #0076FF;
  }

  .toCheckProspects {
    color: #F59B2D;
  }

  .emailInValidProspects {
    color: #DC5050;
  }

  .inProgressProspects {
    color: #FFCD00;
  }

  .completedProspects {
    color: #03E26B;
  }

  .optedOutProspects {
    color: black;
  }

  .doNotContactProspects {
    color: darkgray;
  }

  .learn-more {
    a {
      color: #4183C4;
    }

    a:hover {
      text-decoration: underline;
    }
  }
}

// REF: https://github.com/reactjs/react-modal#body-class

// .non-modal-email-editor {
//   .align-right {
//     text-align: right;
//   }
// }

.ui.modal:not(.fullscreen)>.close.icon {
  top: -0.50rem;
}

.campaign-step,
.editor-modal {
  .rdw-link-modal-target-option {
    display: none;
  }

  .subject {
    padding-bottom: 1em;

    .label {
      margin-top: auto;
      margin-bottom: auto;
    }

    input {
      border: none;
      border-bottom: 1px solid lightgray;
    }
  }

  .delay-setting {
    margin-bottom: 1em;

    .delay-input {
      width: 100px;
    }
  }

  .ui.segments {
    border: none;
    box-shadow: none;

    .segment {
      border-top: none;
      padding: 0;
    }
  }

  .email-editor {
    // border: 1px solid #ccc;
    // border-radius: 5px;
    margin-top: 1em;

    .ql-toolbar {
      // margin-bottom: 1em;
      border: 1px solid aliceblue;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      background: rgba(0, 0, 0, 0.04);
      padding: 0;

      .ql-formats {
        margin-right: 0;
        padding: 10px 1em;
        border-right: 1px solid rgba(0, 0, 0, 0.06);

        .ql-picker.ql-insertCustomTags {
          border: 1px solid rgba(0, 0, 0, 0.06);
          border-radius: 5px;
          width: 150px;
          background: #dc5050;
          color: white;

          .ql-picker-label:hover,
          .ql-stroke {
            color: rgba(255, 255, 255, 0.8);
            stroke: rgba(255, 255, 255, 0.8);
          }

          .ql-picker-item {
            color: rgba(0, 0, 0, 0.87);
          }
        }

        .ql-picker.ql-header {
          width: 150px;
          border: 1px solid rgba(0, 0, 0, 0.06);
          border-radius: 5px;
          background: rgba(0, 0, 0, 0.06);
        }

        button {
          border: 1px solid rgba(0, 0, 0, 0.06);
          border-radius: 5px;
          background: rgba(0, 0, 0, 0.06);
          margin: 0 2px;
        }


        .ql-picker.ql-expanded .ql-picker-label {
          border: none;
        }
      }
    }

    .subject-body-padding {

      // padding: 1em;
      .ql-container.ql-snow {
        border: none;

        .ql-editor {
          min-height: 200px;
          padding: 12px 0 0 0;

          p {
            // margin-top: 1em;
          }
        }
      }
    }

    .draft-jpuri-wrapper {
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 5px;

      .draft-jpuri-toolbar {
        background: rgba(0, 0, 0, 0.04);
        // margin-top: -7em;
        // margin-bottom: 5em;
      }

      .draft-jpuri-editor {
        padding: 1em;
        min-height: 250px;

        .public-DraftStyleDefault-block {
          margin: 0;
        }

        figure {
          margin: 1em 0;

          .rdw-image-center {
            justify-content: initial;
          }
        }
      }
    }

    textarea {
      width: 100%;
      min-height: 100px;
    }
  }

  // .draft-jpuri-input {
  //   margin-top: 4em;
  // }


}

.preview-tab {
  .content {
    .email-content {
      p {
        // margin-top: 1em;
      }
    }

    // color: black;
  }

  .step.content {
    overflow-x: auto;
    cursor: auto;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .extra.text.email-preview {
      ul {
        list-style-type: disc;
        margin: 1em 0;
        padding-left: 40px;
      }

      ol {
        list-style-type: decimal;
        margin: 1em 0;
        padding-left: 40px;
      }
    }
  }

  // .side-menu.prospect-list {
  //   overflow-x: hidden;
  //   padding: 1em;
  //   width: 300px !important;
  // }

  .prospect-list {
    background: rgba(0, 0, 0, 0.04);
    height: inherit;
    overflow-y: auto;
    padding: 1em;

    .prospects-header {
      display: inline-block;
    }

    .ui.divider {
      margin-top: 0;
    }

    .count {
      text-align: right;
      margin-top: 1em;

      p {
        display: inline-block;
        margin-right: 1em
      }
    }

    .select-prospect {
      display: block;
      margin-bottom: 5px;
    }
  }

  .right-menu {
    text-align: right;
    // margin-top: 1em;
    // margin-bottom: 1em;
  }

  .preview-start-buttons.ui.grid {
    margin: 3em 0;
  }
}

.campaign-settings {
  margin-bottom: 80px !important;

  .ui.vertical.menu {
    width: 90%;
  }

  .proceed-button {
    margin: 1em;
    text-align: right;
  }
}

.schedule-settings-tab {

  // margin-top: 1em;
  .ui.buttons {
    .ui.button {
      border-left: 1px solid white;
    }
  }
}

.email-accounts-settings-tab {
  .ui.grid {
    .row {
      .column {
        .label-dropdown {
          font-weight: bold;
          margin-bottom: 3px;
        }

        .dropdown {
          max-width: 400px;
        }
      }
    }

    // h4 {
    //   padding-top: 22px;
    // }
    // #newSettingsPopup {
    //   float:right;
    // }
  }
}

.warmup-settings-tab {

  // margin-top: 1em;
  .list {
    li {
      margin-bottom: 5px;
    }
  }
}

.unsubscribe-tab {
  // margin-top: 1em;
  // .ui.toggle.checkbox .box:before, .ui.toggle.checkbox label:before {
  //   background: #03e26b;
  // }

  // .isOptOutLink .ui.toggle.checkbox input:checked ~ .box:before, .isOptOutLink .ui.toggle.checkbox input:checked ~ label:before {
  //   background: #03e36b !important;
  // }
  .optout-radio {
    margin-bottom: 2em;

    .radio.checkbox {
      margin-right: 1em;
    }
  }
}

.additional-settings-tab {
  // margin-top: 1em;
}

.email-account-settings-tab,
.email-accounts-settings-page {
  .empty.email-list {
    .ui.segment {
      min-height: 190px;
    }

    .ui.segment:hover {
      cursor: pointer;
      margin-top: -5px;
    }
  }
}

.email-provider-list {
  .modal-content {
    margin-top: 50px;
    margin-bottom: 50px;

    .ui.segment:hover {
      cursor: pointer;
      margin-top: -5px;
    }

    .ui.segment {
      .provider-image {
        width: 96px;
        margin: auto;
      }
    }
  }

  .install-on-marketplace {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .gsuite-options {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    .gsuite-option {
      text-align: center;
      padding-left: 1em;
      padding-right: 1em;
      max-width: 350px;

      .button {
        height: 100px;
      }
    }
  }
}

.no-stats-overlay.ui.dimmer {
  top: 16em !important;
  background-color: rgba(0, 0, 0, 0);
  z-index: 995 !important;

  .ui.scrolling.modal {
    margin-top: 50px !important;
  }
}

.no-stats-overlay {

  .ui.modal,
  .ui.modal:focus {
    box-shadow: None;
    background: transparent;
    outline: none;
  }
}

.blur {
  filter: blur(5px);
}

.insert-template,
.insert-merge-tag {
  display: inline-block;

  .ui.dropdown .menu {
    max-height: 200px;
    overflow-y: auto;
    width: 100%;

    .header {
      margin: 1rem 0rem 0.75rem;
      padding: 0em 1.14285714rem;
      color: rgba(0, 0, 0, 0.85);
      font-size: 0.78571429em;
      font-weight: bold;
      text-transform: uppercase;
    }

    .item {
      position: relative;
      cursor: pointer;
      display: block;
      border: none;
      height: auto;
      text-align: left;
      border-top: none;
      line-height: 1em;
      color: rgba(0, 0, 0, 0.87);
      padding: 0.78571429rem 1.14285714rem !important;
      font-size: 1rem;
      text-transform: none;
      font-weight: normal;
      box-shadow: none;
      -webkit-touch-callout: none;
    }

    .item:hover {
      background: rgba(0, 0, 0, .05);
      color: rgba(0, 0, 0, .95);
      z-index: 13;
    }
  }

  .dropdown.icon {
    float: right;
  }
}

.dropdown.menu {
  height: 300px;
  max-height: 400px !important;
}

td {

  .ui.toggle.checkbox input:checked~.box:before,
  .ui.toggle.checkbox input:checked~label:before {
    background-color: #21BA45 !important;
  }

  .ui.toggle.checkbox input~.box:after,
  .ui.toggle.checkbox input~label:after {
    border: 1px solid #d6d6d6;
    z-index: 1;
  }
}


.isOptOutLink {

  .ui.toggle.checkbox input:checked~.box:before,
  .ui.toggle.checkbox input:checked~label:before {
    background-color: rgba(0, 0, 0, 0.15) !important;
    background: rgba(0, 0, 0, 0.15) !important;
  }
}

.react-grid-Cell--locked:last-of-type {
  z-index: 0 !important;
}

.spam-test-tab {
  margin-bottom: 2em;

  h4 {
    padding-top: 22px;
  }

  .ui.styled.accordion {
    width: 100%;

    .indicator {
      display: inline;
      // float: right;
      margin-right: 1em;

      .icon {
        margin: 0;
        height: 100%;
      }
    }

    .indicator.warning {
      background: #f3bd47;
    }

    .indicator.success {
      background: #c4c51a;
    }

    .indicator.failure {
      background: #f35957;
    }

    .row {
      padding-bottom: 0;

      .score {}

      .code {
        color: blue;
      }

      .description {
        .no-margin {
          margin-bottom: 0;
        }
      }
    }

    .sub-section {
      background: aliceblue;

      .active {
        .segment {
          background: aliceblue;
        }
      }
    }

    .bl-result {
      width: 25%;
      display: inline-block;

      .status-failure {
        color: #f35957;
      }

      .status-success {
        color: #c4c51a;
      }
    }

    .email-account-status {
      .PASS {
        color: #c4c51a;
      }

      .FAIL {
        color: #f35957;
      }
    }
  }
}

// .tox-menu.tox-collection.tox-collection--list.tox-selected-menu {
//   max-height: 200px !important;
// }

.email-editor-modal,
.non-modal-email-editor {
  .ui.dropdown {
    height: 100%;
    margin: 0;
    border: none;
    height: inherit;
    padding-top: 15px;
    border-radius: 0;

    // border-left: 1px solid;
    // border-right: 1px solid;
    .menu {
      max-height: 250px;
      overflow-y: auto;
    }
  }

  .editor-sidebar {
    background: #ffffff;
  }

  .template-link-details {
    padding-right: 1.5em;
    padding-top: 0.25em;
    padding-bottom: 0.25em;
    text-align: center;
    box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
    background: rgba(255, 255, 0, 0.2);
  }

  .content {
    padding: 0 !important;

    .insert-actions-strip {
      height: 45px;
      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 1.5em;

      .insert-merge-tag {
        height: inherit;
        margin-left: auto;
      }

      .insert-template {
        height: inherit;
      }

      .ui.dropdown.button {
        height: 100%;
        margin: 0;
        border: none;
        height: inherit;
        padding-top: 15px;
        border-radius: 0;
        border-left: 1px solid;

        // border-right: 1px solid;
        .menu {
          max-height: 250px;
          overflow-y: auto;
        }
      }
    }

    .delay-strip {
      height: 45px;
      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 1.5em;

      .ui.input {
        width: 75px;
      }
    }

    .subject-strip {
      height: 45px;
      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 1.5em;

      .subject-colon {
        // flex: 0 0 75px;
      }

      .ui.fluid.input {
        flex: 1;

        input {
          border: none;
        }
      }
    }

    .to-email-strip,
    .from-email-strip,
    .reply-to-email-strip {
      height: 45px;
      box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 1.5em;

      // .to-email-value {
      //   margin-left: 0.5em;
      // }
      .ui.dropdown {
        padding: 1em;
      }

      .ui.fluid.input.to-email-value {
        flex: 1;

        input {
          border: none;
        }
      }

      .add-cc-bcc-action {
        margin-left: auto;
        margin-right: 1.5em;
      }
    }

    .cc-bcc-strip {
      height: 45px;
      box-shadow: 0px 1px 0px rgba(56, 36, 36, 0.1);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 1.5em;

      .ui.fluid.input {
        flex: 1;

        input {
          border: none;
        }
      }
    }

    .editor-strip {

      // padding: 1.5em;
      .email-editor {
        margin-top: 0;

        .tox.tox-tinymce {

          // border: none;
          .tox-editor-container {
            // flex-direction: column-reverse;
          }
        }

        .ui.loader:before {
          border-color: lightgrey;
        }
      }
    }
  }
}

.non-modal-email-editor {
  .content {
    border: 1px solid black;
  }

  .actions {
    text-align: right;
    padding-top: 1em;
  }
}

// sets the tinmyce 5 min height
.tox-tinymce {
  min-height: 200px;
}

.specific-campaign-page {
  // margin-bottom: 90px;
  min-width: 964px;

  .settings-tab {
    padding: 2em;
    // border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 5px;

    .ui.grid+.grid {
      margin-top: 0;
    }
  }

  .schedule-settings-tab {
    // background: rgba(0, 118, 255, 0.1);
  }

  .unsubscribe-tab {
    // background: rgba(255, 225, 0, 0.1);
  }

  .additional-settings-tab {
    // background: whitesmoke;
  }

  .warmup-settings-tab {
    // background: rgba(84, 237, 74, 0.1);
  }

}

.specific-campaign-page {
  height: inherit;

  .specific-campaign-page-flex {
    height: inherit;
    display: flex;
    flex-direction: column;

    .heading-strip {
      .heading {
        .campaign-name-input {
          width: 400px;
          font-size: inherit;

          input {
            border-bottom: 0 !important;
            border-top: 0 !important;
            border-left: 0 !important;
            border-right: 0 !important;
            border-radius: 0;
          }

          input:hover {
            border-bottom: 1px solid rgba(34, 36, 38, 0.15) !important;
          }
        }
      }
    }

    .campaign-tab-strip {
      margin: 0 1em 1em 1em;

      .ui.menu {
        // border: none;
        box-shadow: none;
        height: 100%;
        border-bottom: 1px solid rgba(34, 36, 38, 0.15);

        .active.item {
          border-color: #0f69fa;
          color: #0f69fa;
        }
      }
    }

    .filter-strip {
      .action.button {
        height: inherit;
        margin-left: auto;
      }
    }

    .campaign-main-strip {
      flex: 1;
      height: inherit;
      overflow: hidden;
      // overflow: auto;
      // padding: 2em;
    }
  }
}

.content.tab {
  .main-content-strip.new-campaign {
    height: calc(100% - 60px) !important;
    padding: 1em;
  }

  .main-content-strip {

    // height: calc(100% - 60px) !important;
    .ui.centered.grid {
      height: 100%;

      .four.wide.column.info-section {
        display: flex;
        flex-direction: column;

        .append-setting {
          h4 {
            // text-align: center;
            margin-bottom: 1.5em
          }

          margin-left: 1em;
          padding: 1em;
          border-radius: 5px;
          background: rgba(0, 0, 0, 0.04);
          margin-bottom: 10px;
        }

        .summary {
          margin-left: 1em;
          padding: 1em;
          height: 100%;
          border-radius: 5px;
          background: rgba(0, 0, 0, 0.04);
          flex: 1;

          h4 {
            // text-align: center;
            margin-bottom: 1.5em
          }

          .step {
            margin-bottom: 1em;

            .step-title {
              margin-bottom: 5px;
              color: rgba(0, 0, 0, 0.4);
            }
          }
        }
      }
    }
  }
}

.preview-tab {
  .start-campaign-button-strip {
    // margin-top: 1em;
    // margin-bottom: 1em;
    margin: 1em;
    display: flex;
    flex-direction: row;

    .start-button {
      margin-left: auto;
    }
  }
}

.bigger-input-field {
  width: 300px;
}