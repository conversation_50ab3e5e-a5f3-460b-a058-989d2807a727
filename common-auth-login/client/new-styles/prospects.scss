.upload-custom-dropzone {
  min-width: 300px;
  margin-top: 10px;
  border-radius: 5px;
  cursor: pointer;

  .ui.segment {
    width: 100%;
    height: 100%;
  }

  .ui.tertiary.inverted.lightgrey.segment {
    background: lightgray !important;
  }
}

.prospects.tab {
  margin-bottom: 1em;

  .actions {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .empty.prospects {
    .ui.segment {
      min-height: 190px;
    }

    .ui.segment:hover {
      cursor: pointer;
      margin-top: -5px;
    }
  }
}

.upload-prospect-modal {

  .upload-status-info {
    margin-top: 1em;

    label {
      font-weight: bold;
    }
  }

  .upload-prospect {
    .ui.steps {
      width: 100%;
    }
  }

  .ui.bottom.left.popup {
    background-color: lightgray;
  }

  .aws_upload {
    margin-top: 45px;
  }

  .ui.divider {
    width: 100%;
  }

  .filename {
    color: #4183C4;
    font-size: 20px;

  }
}

.ui.grid.empty.prospects-list {
  margin-left: 0;
  margin-right: 0;
}

.prospects-list.modal {
  //assign-more-modal
  height: initial;

  .content {
    padding: 0;

    .prospects-list {
      .assign-more-modal-flex {
        height: inherit;
        display: flex;
        flex-direction: column;

        .assign-more-modal-one-strip {
          flex: 0 0 45px;
          display: flex;
          flex-direction: row;
          padding-left: 1em;
          // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
          border-bottom: 1px solid rgba(40, 40, 40, 0.1);
          align-items: center;

          .pagination-section {
            margin-left: auto;
            height: 45px;
            box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);

            .page-items-count {
              display: inline-flex;
              height: inherit;
              align-items: center;
              padding-left: 2em;
            }

            .page-controls {
              display: inline-flex;
              padding-left: 1em;
              padding-right: 1em;
              height: inherit;
              align-items: center;

              .ui.basic.button {
                border: none;
                box-shadow: none;
              }
            }
          }

          .table-columns {
            // margin-left: auto;
            height: 45px;

            .ui.button {
              height: inherit;
              border-radius: 0;
              margin: 0;

              .icon {
                color: #ffffff;
                opacity: 1;
                margin: 0;
              }
            }
          }

          .search-input {
            height: 44px;
            box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);
            background-color: transparent;

            .ui.input {
              height: inherit;
              width: 350px;

              input {
                border: none;
              }
            }
          }
        }

        .assign-more-modal-main-strip {
          flex: 1;
        }
      }
    }
  }
}

.search-in-dataplatform.modal {
  //search in dataplatform modal
  height: initial;

  .content {
    padding: 0;
    min-height: 200px;

    .prospects-list {
      .search-tools-form-initial {
        display: flex;
        flex-direction: column;
        align-items: center;

        .search-tools-form-initial-item {
          margin-top: 1em;
          min-width: 300px;
        }

        .search-tools-form-initial-item.ui.dropdown {
          // padding: 8px;
        }

        .ui.button {
          margin: 1em 0;
        }

        h4 {
          margin-top: 2em;
        }

        .ui.equal.width.grid {
          .column {
            .ui.card {
              width: 100px;
              height: 100px;
              text-align: center;

              img {
                width: 50px;
                margin: auto;
              }
            }

            .ui.card:hover {
              cursor: pointer;
              margin-top: -5px;
            }
          }
        }
      }

      .import-from-crm-modal-flex {
        height: inherit;
        display: flex;
        flex-direction: column;

        .import-from-crm-modal-one-strip {
          flex: 0 0 45px;
          display: flex;
          flex-direction: row;
          padding-left: 1em;
          // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
          border-bottom: 1px solid rgba(40, 40, 40, 0.1);
          align-items: center;

          .pagination-section {
            margin-left: auto;
            height: 45px;
            box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);

            .page-items-count {
              display: inline-flex;
              height: inherit;
              align-items: center;
              padding-left: 2em;
            }

            .page-controls {
              display: inline-flex;
              padding-left: 1em;
              padding-right: 1em;
              height: inherit;
              align-items: center;

              .ui.basic.button {
                border: none;
                box-shadow: none;
              }
            }
          }

          .table-columns {
            // margin-left: auto;
            height: 45px;

            .ui.button {
              height: inherit;
              border-radius: 0;
              margin: 0;

              .icon {
                color: #ffffff;
                opacity: 1;
                margin: 0;
              }
            }
          }

          .search-tools-form {
            height: 44px;
            margin: 1em 0;

            // border: 1px solid #f5f5f5;
            .ui.input {
              height: inherit;
              width: 200px;

              input {
                border: none;
                border: 1px solid #f5f5f5;
                // box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);
                background-color: transparent;
                border-radius: 0;
              }
            }

            .ui.button {
              height: inherit;
              border: none;
              border-radius: 0;
            }

            .ui.dropdown {
              display: inline-flex;
              flex-direction: row;
              align-items: center;
              height: 100%;
              border: 1px solid #f5f5f5;
              padding: 0 1em;
              width: 200px;

              // box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);
              .icon {
                margin-left: auto;
              }
            }
          }
        }

        .import-from-crm-modal-main-strip {
          flex: 1;

          .button-div {
            padding: 1em;
            text-align: right;
          }
        }
      }
    }
  }
}

.prospects-list {
  height: inherit;

  // .ui.message {
  //   // margin-top: 2em;
  // }
  .outline {
    outline: none;
  }

  .ui.dropdown .menu>.item.export-csv-button {
    padding: 0 !important;
    margin: 0.5em 1em;
  }

  .disableCheckBoxInRow {
    .react-grid-checkbox-container {
      display: none;
    }
  }

  .status.dropdown {
    // float: left;
    z-index: 100 !important;
    min-height: 20px !important;
    padding: 0.58571429em 1.1em 0.58571429em 1em !important;
    background: rgba(0, 0, 0, 0.05);
    // margin-right: 2em;
    margin: 0em 0.25em 0em 0em;

    .default.text {
      color: rgba(0, 0, 0, 0.65) !important;
    }
  }

  .page-header {
    margin-top: 5px;

    .title {
      display: inline-block;
      margin-left: 0.5em;
    }
  }

  .proceed-button {
    margin-top: 1em;
    margin-bottom: 1em;
    text-align: right;
  }

  .count {
    float: right;

    // display: inline-block;
    p {
      display: inline-block;
      margin-right: 1em
    }
  }

  .empty.prospects-list {
    .ui.segment {
      min-height: 190px;
    }

    .ui.segment:hover {
      cursor: pointer;
      margin-top: -5px;
    }

    .assign {
      margin-bottom: 1em;
    }

    .add {
      width: 48%;
      margin-right: 0;
    }

    .upload {
      width: 48%;
      margin-right: 0;
      float: right;
    }

    .add-columns {
      padding-top: 1em;

      .button {}
    }

    .assign-more {
      margin-top: 1em;
    }

  }

  .custom-padding {
    padding-top: 1em !important;
    padding-bottom: 0.5em !important;
  }

  .inline.loader {
    margin-left: 5px
  }

  .action-button-groups {
    .ui.button {
      padding: 0.78571429em;
    }
  }

  .header-campaign-prospects {
    display: flex;

    .heading {
      width: 165px;
      float: left;
    }

    .others {
      width: 100%;
    }
  }

  .tooltip {
    position: relative;
    display: inline-block;
  }

  /* Tooltip text */
  .tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: black;
    color: #fff;
    text-align: center;
    padding: 5px 0;
    border-radius: 6px;

    /* Position the tooltip text - see examples below! */
    position: absolute;
    z-index: 10000;
  }

  /* Show the tooltip text when you mouse over the tooltip container */
  .tooltip:hover .tooltiptext {
    visibility: visible;
  }

  .tooltip .tooltiptext {
    width: 120px;
    bottom: 100%;
    left: 50%;
    margin-left: -60px;
    /* Use half of the width (120/2 = 60), to center the tooltip */
  }

  //pagination css
}

.popUp.input {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom: 1px solid grey;
  width: 100%;
  outline: none;
}


.popUp.input:focus {
  border-bottom: 1px solid blue;
}

.show_hide_popup {
  padding: 3px;

  label {
    cursor: pointer;
  }

  input {
    margin-right: 10px;
    cursor: pointer;
  }
}


.popup-items {
  padding: 5px;
  cursor: ponter;
}

.popup-items.focused {
  background: lightblue;
}

//  .popup-items:hover{
//    background: lightblue;
//  }
.popup-list {
  width: 180px;
  max-height: 250px;
  overflow-y: auto;
  overflow-x: hidden;
  list-style-type: none;
  padding-left: 10px;
  padding-right: 10px;
}

.noPadding {
  padding: 0px !important;
}

.upload.status {
  text-align: center;

  .total {
    font-size: 50px;
  }

  .errors {
    font-size: 50px;
    color: red;
  }

  .duplicates {
    font-size: 50px;
    color: orange;
  }

  .saved {
    font-size: 50px;
    color: #21BA45;
  }
}

.bg-blue {
  background-color: aliceblue;
}

.bg-red {
  background-color: antiquewhite;
}

.show-hide-popup {
  max-height: 250px;
  overflow-y: auto;
}


hr.divider {
  border-top: 1px solid #8c8b8b;
  text-align: center;
}

hr.divider:after {
  content: 'or';
  display: inline-block;
  position: relative;
  top: -14px;
  padding: 0 10px;
  background: #f0f0f0;
  color: #8c8b8b;
  font-size: 18px;
}

.updateProspectModal {
  .ui.attached.tabular.menu {
    .item {
      // width: 50%;
    }

    .active {
      background: #f5f5f5;
    }
  }

  .ui.bottom.attached.segment.tab {
    min-height: 400px;
  }

  .ui.feed>.event>.content .extra.text {
    max-width: 100%;
  }

  .step.content {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 2em !important;
  }
}

.AddFilterProspectModal {

  .sub-clause {
    padding: 0 !important;
  }

  .ui.segment:first-child {
    margin-top: 1rem;
  }

  .clause {
    background: #fafafa;
    border-radius: 2px;
    border: solid 1px #ccc;
    color: #bbb;
    font-size: 11px;
    font-weight: 500;
    letter-spacing: .05em;
    margin: 2px 0 2px 14px !important;
    padding: 2px 3px;
  }
}

.AddFilterProspectModal.showInDiv {
  width: 50%;
  min-width: 800px;
  z-index: 13;

  .ui.form {
    padding-top: 1em;

    .ui.segment {
      padding-top: 2em;
      border-style: dashed;
      background: #f5f5f5;
      margin-top: -1em;
      margin-bottom: 0.2em;
    }

    .ui.grid.filters {
      .row {
        padding-top: 0.2em;
        padding-bottom: 0.2em;
      }
    }

    .ui.error.input {
      background: #FFF6F6;
      border-color: #E0B4B4;
      color: #9F3A38;

      input {
        background: #FFF6F6;
        border-color: #E0B4B4;
        color: #9F3A38;
      }
    }

    .custom-date-range-picker.error {
      background: #FFF6F6;
      border-color: #E0B4B4;
      color: #9F3A38;
    }
  }

  .addPropertyRow {
    margin-bottom: 0.2em;
    margin-top: 1.2em;
  }

  .filterActions {
    margin-top: 1em;
    text-align: right;
  }

  .react-datepicker-popper {
    z-index: 4;
  }

  .react-datepicker__input-container {
    // font-size: 0.78571429em;
  }
}

.addFilterProspectModalNew {

  .sub-clause {
    padding: 0 !important;
  }

  .ui.segment:first-child {
    margin-top: 1rem;
  }

  .clause {
    background: #fafafa;
    border-radius: 2px;
    border: solid 1px #ccc;
    color: #bbb;
    font-size: 11px;
    font-weight: 500;
    letter-spacing: .05em;
    margin: 2px 0 2px 14px !important;
    padding: 2px 3px;
  }
}

.react-grid-Toolbar {
  display: none;
}

.email-validation-popup {
  .info.circle.icon {
    margin-left: 1em;
  }
}

.ui.fullscreen.modal.transition.visible.prospects-list {
  left: unset !important;
}

.specific-prospect,
.specific-account,
.rest-after-threads-pane {
  .top-section {
    .back-button {
      border-radius: 3px;
      background: transparent;
      border-right: 1px solid rgba(0, 0, 0, 0.05);
    }

    h3 {
      display: inline-block;
      margin-left: 1em;
      margin-top: 0;
    }
  }

  .main-content-strip {
    // padding: 2em !important;
  }

  .prospect-card,
  .account-card {
    .multiple-contacts-switch {
      padding: 1em;
      background: #f5f5f5;

      .label {
        cursor: pointer;
      }
    }

    .actions {
      padding: 1em;
    }

    .avatar {
      text-align: center;
      padding: 1em;
    }

    .category-section {
      padding: 1em;
      background: rgba(0, 0, 0, 0.05);
    }

    .campaigns-section {
      padding: 1em;
    }

    .tags-section {
      padding: 1em;

      .ui.label {
        margin-bottom: 5px;
      }
    }

    .default-columns {
      padding: 1em;
    }

    .custom-columns {
      padding: 1em;
      background: rgba(0, 0, 0, 0.05);
    }
  }

  .activity-tab {
    .timeline-details {
      padding: 1em;

      // margin-top: 1em;
      h4 {
        margin-bottom: 2em;
        padding: 0;
      }

      .prospect-timeline {
        border-top: none !important;
      }
    }
  }

  .prospect-campaigns-tab,
  .account-campaigns-tab {
    // margin-top: 2em;
    padding: 0 1em 1em 1em;
  }

  .thread-events {

    //specific thread
    .thread-timeline {
      border-top: none !important;

      .actions {
        text-align: right;
      }
    }
  }

  .ui.search .prompt {
    border-radius: 0 !important;
  }
}

.ui.modal.assign-more-modal {
  // height: 500px;
  height: calc(100% - 100px);

  .content {
    height: 100%;

    .prospects-list {
      height: inherit;
      // .assign-more-modal-flex {
      //   height: inherit;
      //   display: flex;
      //   flex-direction: column;
      //   .filter-strip {
      //     flex: 0 0 45px;
      //     box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      //     display: flex;
      //     flex-direction: row;
      //     align-items: center;
      //     .filter-section {
      //       height: 45px;
      //       //sidebar transition
      //       // -webkit-transition: width 500ms ease;
      //       // transition: width 500ms ease;
      //       -webkit-transition: none;
      //       transition: none;
      //       .ui.button {
      //         height: inherit;
      //         border-radius: 0;
      //         margin: 0;
      //         .icon {
      //           color: #ffffff;
      //           opacity: 1;
      //           margin: 0;
      //         }
      //       }
      //       .filter-sidebar-heading {
      //         // width: 350px;
      //         height: inherit;
      //         background-color: #2185D0;
      //         color: #ffffff;
      //         display: flex;
      //         flex-direction: row;
      //         .clear-filter {
      //           height: inherit;
      //             .ui.button {
      //               height: inherit;
      //               border-radius: 0;
      //               color: #ffffff;
      //               .icon {
      //                 color: #ffffff;
      //                 opacity: 1;
      //                 margin: 0;
      //               }
      //           }
      //         }
      //         .collapse-filter {
      //           height: inherit;
      //           margin-left: auto;
      //             .ui.button {
      //               height: inherit;
      //               border-radius: 0;
      //               .icon {
      //                 color: #ffffff;
      //                 opacity: 1;
      //                 margin: 0;
      //               }
      //           }
      //         }
      //       }
      //     }
      //     .pagination-section {
      //       margin-left: auto;
      //       height: 45px;
      //       box-shadow: -1px 0px 0px rgba(40, 40, 40, 0.1);
      //       .page-items-count {
      //         display: inline-flex;
      //         height: inherit;
      //         align-items: center;
      //         padding-left: 2em;
      //       }
      //       .page-controls {
      //         display: inline-flex;
      //         padding-left: 1em;
      //         padding-right: 1em;
      //         height: inherit;
      //         align-items: center;
      //         .ui.basic.button {
      //           border: none;
      //           box-shadow: none;
      //         }
      //       }
      //     }
      //     .table-columns {
      //       // margin-left: auto;
      //       height: 45px;
      //       .ui.button {
      //         height: inherit;
      //         border-radius: 0;
      //         margin: 0;
      //         .icon {
      //           color: #ffffff;
      //           opacity: 1;
      //           margin: 0;
      //         }
      //       }
      //     }
      //     .actions-dropdown {
      //       height: 45px;
      //       .ui.button.dropdown {
      //         margin: 0;
      //         border: none;
      //         height: inherit;
      //         // padding-top: 15px;
      //         border-radius: 0;
      //         border-left: 1px solid;
      //         // border-right: 1px solid;
      //         .menu {
      //           max-height: 275px;
      //           overflow-y: auto;
      //         }
      //       }
      //     }
      //     .action-button {
      //       margin-left: auto;
      //       height: 45px;
      //       .ui.button {
      //         height: inherit;
      //         border-radius: 0;
      //         margin: 0;
      //         .icon {
      //           color: #ffffff;
      //           opacity: 1;
      //         }
      //       }
      //     }
      //     .search-input {
      //       height: 44px;
      //       box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);
      //       background-color: transparent;
      //       .ui.input {
      //         height: inherit;
      //         width: 350px;
      //         input {
      //           border: none;
      //         }
      //       }
      //     }
      //   }
      //   .pushable {
      //     .ui.sidebar.filter-sidebar {
      //       box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      //       background: #ffffff;
      //       border-right: 1px solid rgba(0, 0, 0, 0.2);
      //       border-top: 1px solid rgba(0, 0, 0, 0.2);
      //       padding: 1em;
      //       height: inherit;
      //       // .ui.segment {
      //       //   background-color: #f5f5f5;
      //       //   label {
      //       //     display: block;
      //       //     font-weight: bold;
      //       //     margin-bottom: 0.25em;
      //       //   }
      //       //   .multiple.selection.dropdown {
      //       //     .ui.label {
      //       //       background-color: rgba(35, 135, 205, 0.2);
      //       //     }
      //       //   }
      //       // }
      //       .conditional-filter-section {
      //         z-index: 13;
      //         margin-top: 0.5em;
      //         margin-bottom: 0.5em;
      //         .ui.segment {
      //           background-color: rgba(40, 40, 40, 0.04);
      //           label {
      //             display: inline-block;
      //             margin-bottom: 0.5em;
      //             color: rgba(0, 0, 0, 0.6);
      //           }
      //         }
      //         .saved-filter-section {
      //           display: flex;
      //           flex-direction: row;
      //           .ui.button.dropdown {
      //             margin-left: auto;
      //             background-color: rgba(40,40,40,0.8);
      //             color: #ffffff;
      //           }
      //           .ui.button.selection.dropdown {
      //             margin-left: auto;
      //             background-color: rgba(40,40,40,0.8);
      //             color: #ffffff;
      //             min-width: 10em;
      //             .text {
      //               font-weight: inherit !important;
      //               color: inherit !important;
      //             }
      //             .menu {
      //               width: 250px;
      //             }
      //           }
      //         }
      //         .owners-dropdown {

      //         }
      //         .ui.divider {
      //           margin: 2em 0;
      //         }
      //         .main-clause {
      //           margin: 1em 0;
      //           .ui.button.dropdown {
      //             background-color: rgba(35, 135, 205, 0.2);
      //             border: 1px solid rgba(35, 135, 205, 0.5);
      //             border-radius: 2px;
      //           }
      //         }
      //         .sub-clause {
      //           margin-top: 0.2em;
      //           margin-bottom: 0.2em;
      //           .ui.button.dropdown {
      //             background-color: rgba(35, 135, 205, 0.2);
      //           }
      //         }
      //         .add-new-condition {
      //           margin-top: 1em;
      //           margin-bottom: 5em;
      //           .ui.fluid.button {
      //             // background-color: rgba(40,40,40,0.16) !important;
      //           }
      //         }
      //         .add-sub-filter {
      //           margin-top: 0.2em;
      //           margin-bottom: 0.2em;
      //           .ui.button {
      //             background-color: rgba(40,40,40,0.16);
      //           }
      //         }
      //         .sub-filters {
      //           .sub-filter-row1 {
      //             padding-top: 0.2em;
      //             padding-bottom: 0.2em;
      //             display: flex;
      //             flex-direction: row;
      //             .sub-filter-row1-column {
      //               width: 50%;
      //             }
      //             .column1 {
      //               margin-right: 0.5em;
      //             }
      //           }
      //           .sub-filter-row2 {
      //             padding-top: 0.2em;
      //             padding-bottom: 0.2em;
      //             display: flex;
      //             flex-direction: row;
      //             .sub-filter-row2-column-main {
      //               flex: 1; //check
      //             }
      //             .sub-filter-row2-column-action {
      //               margin-left: auto;
      //               .ui.button {
      //                 margin: 0 0 0 1em;
      //               }
      //               .remove-sub-filter {
      //                 background-color: rgba(40,40,40,0.1) ;
      //                 color: rgba(40,40,40,0.4);
      //               }
      //             }
      //           }
      //         }
      //         .ui.error.input {
      //           background: #FFF6F6;
      //           border-color: #E0B4B4;
      //           color: #9F3A38;
      //           input {
      //             background: #FFF6F6;
      //             border-color: #E0B4B4;
      //             color: #9F3A38;
      //           }
      //         }
      //         .custom-date-range-picker.error {
      //           background: #FFF6F6;
      //           border-color: #E0B4B4;
      //           color: #9F3A38;
      //         }
      //         .filterActions {
      //           margin-top: 1em;
      //           text-align: right;
      //         }
      //         .react-datepicker-popper {
      //           z-index: 4;
      //         }
      //         .react-datepicker__input-container {
      //           // font-size: 0.78571429em;
      //         }
      //       }
      //     }
      //     .pusher {
      //       height: inherit;
      //       .main-content-strip {
      //         height: inherit;
      //         flex: 1 1 auto;
      //         overflow: auto;
      //         // padding: 2em;
      //         padding-top: 1px;
      //       }
      //       .main-content-strip.content-with-side-menu {
      //         display: flex;
      //         flex-direction: row;
      //         padding: 0;
      //         overflow: hidden;
      //         // transition: padding 500ms;
      //         .side-menu {
      //           flex: 0 0 300px;
      //           height: inherit;
      //           background-color: rgba(40,40,40,0.04);
      //           box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);
      //           overflow-y: auto;
      //           padding-bottom: 2em;
      //           .ui.vertical.menu {
      //             border-radius: 0;
      //             box-shadow: none;
      //             border-right: none;
      //             .item.side-menu-heading {
      //               background-color: rgba(40,40,40,0.04);
      //               .header {
      //                 margin: 0;
      //                 color: rgba(40, 40, 40, 0.6);


      //                 // as per figma
      //                 line-height: 122%;
      //                 font-style: normal;
      //                 font-weight: normal;
      //               }
      //             }
      //             .item {
      //               display: flex;
      //               flex-direction: row;
      //               align-items: center;

      //               // as per figma
      //               font-weight: 500;
      //               line-height: 114%;

      //               .icon {
      //                 margin-left: auto;
      //               }
      //             }
      //             .active.item {
      //               box-shadow: inset -4px 0px 0px #2387CD;
      //               background: none;
      //               border-radius: 0;
      //             }
      //           }
      //         }
      //         .content-beside-menu {
      //           flex: 1; //check
      //           display: flex;
      //           flex-direction: column;
      //           overflow-x: auto;
      //           .main-content-header {
      //             height:41px;
      //             box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      //             display: flex;
      //             flex-direction: row;
      //             justify-content: center;
      //             align-items: center;
      //             .content-heading {
      //               font-weight: bold;
      //               height: inherit;
      //               margin: 0 auto;
      //               padding-top: 12px;
      //               margin: 0 auto 0 2em;
      //             }
      //             .action-button {
      //               height: inherit;
      //               .ui.button {
      //                 height: inherit;
      //                 border-radius: 0;
      //                 margin: 0;
      //               }
      //             }
      //           }
      //           .main-content-section {
      //             flex: 1; //check
      //             overflow: auto;
      //             width: auto;
      //           }
      //         }
      //         .main-content-section {
      //           flex: 1; //check
      //           // width: 200px;
      //           height: inherit;
      //           overflow: auto;
      //           // padding: 2em;
      //         }
      //         .main-content-section.has-filter-banner {
      //           display: flex;
      //           flex-direction: column;
      //           overflow: hidden;
      //           .filter-banner {
      //             flex: 0 0 35px;
      //             // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      //             background-color: rgba(40, 40, 40, 0.04);
      //             // height: inherit;
      //             .filter-banner-wrapper {
      //               display: flex;
      //               justify-content: center;
      //               align-items: center;
      //               padding-left: 1em;
      //               padding-right: 1em;
      //               height: 100%;
      //               cursor: pointer;
      //               .filter-banner-content {
      //                 white-space: nowrap;
      //                 padding: 0.35em 0.5em;
      //               }
      //               .extra-text {
      //                 font-size: 0.85714286rem;//fonnt size of label
      //                 a {
      //                   cursor: pointer;
      //                 }
      //               }
      //             }
      //           }
      //           .after-filter-banner {
      //             flex: 1;
      //             height: inherit;
      //             overflow-y: auto;
      //             padding: 1em;
      //           }
      //           .after-filter-banner.padding2em {
      //             padding: 2em;
      //           }
      //         }
      //       }
      //       .main-content-strip.has-filter-banner {
      //         display: flex;
      //         flex-direction: column;
      //         // transition: padding 500ms;
      //         .filter-banner {
      //           flex: 0 0 35px;
      //           // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
      //           background-color: rgba(40, 40, 40, 0.04);
      //           // height: inherit;
      //           .filter-banner-wrapper {
      //             display: flex;
      //             justify-content: center;
      //             align-items: center;
      //             padding-left: 1em;
      //             padding-right: 1em;
      //             height: 100%;
      //             cursor: pointer;
      //             .filter-banner-content {
      //               white-space: nowrap;
      //               padding: 0.35em 0.5em;
      //             }
      //             .extra-text {
      //               font-size: 0.85714286rem;//fonnt size of label
      //               a {
      //                 cursor: pointer;
      //               }
      //             }
      //           }
      //         }
      //         .after-filter-banner {
      //           flex: 1;
      //           height: inherit;
      //           // padding-bottom: 4em;
      //           overflow-y: auto;
      //           display: flex;
      //           flex-direction: column;
      //           padding: 1em;
      //         }
      //       }
      //     }
      //   }
      // }
    }
  }

  .local-page {
    height: inherit;

    .local-page-flex {
      height: inherit;
      display: flex;
      flex-direction: column;

      .pushable {
        .ui.sidebar.filter-sidebar {
          box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
          background: #ffffff;
          border-right: 1px solid rgba(0, 0, 0, 0.2);
          border-top: 1px solid rgba(0, 0, 0, 0.2);
          padding: 1em;
          height: inherit;

          // .ui.segment {
          //   background-color: #f5f5f5;
          //   label {
          //     display: block;
          //     font-weight: bold;
          //     margin-bottom: 0.25em;
          //   }
          //   .multiple.selection.dropdown {
          //     .ui.label {
          //       background-color: rgba(35, 135, 205, 0.2);
          //     }
          //   }
          // }
          .conditional-filter-section {
            z-index: 13;
            margin-top: 0.5em;
            margin-bottom: 0.5em;

            .ui.segment {
              background-color: rgba(40, 40, 40, 0.04);

              label {
                display: inline-block;
                margin-bottom: 0.5em;
                color: rgba(0, 0, 0, 0.6);
              }
            }

            .saved-filter-section {
              display: flex;
              flex-direction: row;

              .ui.button.dropdown {
                margin-left: auto;
                background-color: rgba(40, 40, 40, 0.8);
                color: #ffffff;
              }

              .ui.button.selection.dropdown {
                margin-left: auto;
                background-color: rgba(40, 40, 40, 0.8);
                color: #ffffff;
                min-width: 10em;

                .text {
                  font-weight: inherit !important;
                  color: inherit !important;
                }

                .menu {
                  width: 250px;
                }
              }
            }

            .owners-dropdown {}

            .ui.divider {
              margin: 2em 0;
            }

            .main-clause {
              margin: 1em 0;

              .ui.button.dropdown {
                background-color: rgba(35, 135, 205, 0.2);
                border: 1px solid rgba(35, 135, 205, 0.5);
                border-radius: 2px;
              }
            }

            .sub-clause {
              margin-top: 0.2em;
              margin-bottom: 0.2em;

              .ui.button.dropdown {
                background-color: rgba(35, 135, 205, 0.2);
              }
            }

            .add-new-condition {
              margin-top: 1em;
              margin-bottom: 5em;

              .ui.fluid.button {
                background-color: rgba(40, 40, 40, 0.16) !important;
              }
            }

            .add-sub-filter {
              margin-top: 0.2em;
              margin-bottom: 0.2em;

              .ui.button {
                background-color: rgba(40, 40, 40, 0.16);
              }
            }

            .sub-filters {
              .sub-filter-row1 {
                padding-top: 0.2em;
                padding-bottom: 0.2em;
                display: flex;
                flex-direction: row;

                .sub-filter-row1-column {
                  width: 50%;
                }

                .column1 {
                  margin-right: 0.5em;
                }
              }

              .sub-filter-row2 {
                padding-top: 0.2em;
                padding-bottom: 0.2em;
                display: flex;
                flex-direction: row;

                .sub-filter-row2-column-main {
                  flex: 1; //check
                }

                .sub-filter-row2-column-action {
                  margin-left: auto;

                  .ui.button {
                    margin: 0 0 0 1em;
                  }

                  .remove-sub-filter {
                    background-color: rgba(40, 40, 40, 0.1);
                    color: rgba(40, 40, 40, 0.4);
                  }
                }
              }
            }

            .ui.error.input {
              background: #FFF6F6;
              border-color: #E0B4B4;
              color: #9F3A38;

              input {
                background: #FFF6F6;
                border-color: #E0B4B4;
                color: #9F3A38;
              }
            }

            .custom-date-range-picker.error {
              background: #FFF6F6;
              border-color: #E0B4B4;
              color: #9F3A38;
            }

            .filterActions {
              margin-top: 1em;
              text-align: right;
            }

            .react-datepicker-popper {
              z-index: 4;
            }

            .react-datepicker__input-container {
              // font-size: 0.78571429em;
            }
          }
        }

        .pusher {
          height: inherit;

          .main-content-strip {
            height: inherit;
            flex: 1 1 auto;
            overflow: auto;
            // padding: 2em;
            padding-top: 1px;
          }

          .main-content-strip.content-with-side-menu {
            display: flex;
            flex-direction: row;
            padding: 0;
            overflow: hidden;

            // transition: padding 500ms;
            .side-menu {
              flex: 0 0 300px;
              height: inherit;
              background-color: rgba(40, 40, 40, 0.04);
              box-shadow: 1px 0px 0px rgba(40, 40, 40, 0.1);
              overflow-y: auto;
              padding-bottom: 2em;

              .ui.vertical.menu {
                border-radius: 0;
                box-shadow: none;
                border-right: none;

                .item.side-menu-heading {
                  background-color: rgba(40, 40, 40, 0.04);

                  .header {
                    margin: 0;
                    color: rgba(40, 40, 40, 0.6);


                    // as per figma
                    line-height: 122%;
                    font-style: normal;
                    font-weight: normal;
                  }
                }

                .item {
                  display: flex;
                  flex-direction: row;
                  align-items: center;

                  // as per figma
                  font-weight: 500;
                  line-height: 114%;

                  .icon {
                    margin-left: auto;
                  }
                }

                .active.item {
                  box-shadow: inset -4px 0px 0px #2387CD;
                  background: none;
                  border-radius: 0;
                }
              }
            }

            .content-beside-menu {
              flex: 1; //check
              display: flex;
              flex-direction: column;
              overflow-x: auto;

              .main-content-header {
                height: 41px;
                box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;

                .content-heading {
                  font-weight: bold;
                  height: inherit;
                  margin: 0 auto;
                  padding-top: 12px;
                  margin: 0 auto 0 2em;
                }

                .action-button {
                  height: inherit;

                  .ui.button {
                    height: inherit;
                    border-radius: 0;
                    margin: 0;
                  }
                }
              }

              .main-content-section {
                flex: 1; //check
                overflow: auto;
                width: auto;
              }
            }

            .main-content-section {
              flex: 1; //check
              // width: 200px;
              height: inherit;
              overflow: auto;
              // padding: 2em;
            }

            .main-content-section.has-filter-banner {
              display: flex;
              flex-direction: column;
              overflow: hidden;

              .filter-banner {
                flex: 0 0 35px;
                // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
                background-color: rgba(40, 40, 40, 0.04);

                // height: inherit;
                .filter-banner-wrapper {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  padding-left: 1em;
                  padding-right: 1em;
                  height: 100%;
                  cursor: pointer;

                  .filter-banner-content {
                    white-space: nowrap;
                    padding: 0.35em 0.5em;
                  }

                  .extra-text {
                    font-size: 0.85714286rem; //fonnt size of label

                    a {
                      cursor: pointer;
                    }
                  }
                }
              }

              .after-filter-banner {
                flex: 1;
                height: inherit;
                overflow-y: auto;
                padding: 1em;
              }

              .after-filter-banner.padding2em {
                padding: 2em;
              }
            }
          }

          .main-content-strip.has-filter-banner {
            display: flex;
            flex-direction: column;

            // transition: padding 500ms;
            .filter-banner {
              flex: 0 0 35px;
              // box-shadow: 0px 1px 0px rgba(40, 40, 40, 0.1);
              background-color: rgba(40, 40, 40, 0.04);

              // height: inherit;
              .filter-banner-wrapper {
                display: flex;
                justify-content: center;
                align-items: center;
                padding-left: 1em;
                padding-right: 1em;
                height: 100%;
                cursor: pointer;

                .filter-banner-content {
                  white-space: nowrap;
                  padding: 0.35em 0.5em;
                }

                .extra-text {
                  font-size: 0.85714286rem; //fonnt size of label

                  a {
                    cursor: pointer;
                  }
                }
              }
            }

            .after-filter-banner {
              flex: 1;
              height: inherit;
              // padding-bottom: 4em;
              overflow-y: auto;
              padding: 1em;
            }
          }
        }
      }
    }
  }
}

.prospect-page,
.prospect0tab {
  .prospects-list {
    height: inherit;

    .global-page-flex {
      .heading-strip {}

      .filter-strip {}

      .after-filter-banner {
        // padding-bottom: 0 !important;
        padding: 1em;
      }
    }
  }

  .conditional-filter-section {
    .custom-date-range-picker {
      width: 100%;
      margin-top: 1px;
      height: 26px;
    }

    .react-datepicker-wrapper {
      display: flex;
    }
  }
}

.prospect-datagrid-external-link {
  .external.icon {
    display: none;
  }
}

.prospect-datagrid-external-link:hover {
  .external.icon {
    display: inherit;
  }
}

.addToAccountModal {
  .ui.search .prompt {
    border-radius: 0 !important;
  }
}


.ui.modal.add-tags-modal {
  .content {
    height: 200px;
    padding: 1px;

    .multiple.selection.dropdown {
      border-color: #f5f5f5;

      .default.text {
        color: rgba(0, 0, 0, 0.6);
      }

      .visible.menu {
        border-color: #f5f5f5;
        box-shadow: none;
        height: 163px;
      }
    }
  }
}

.import-from-crm-modal {
  .header-flex {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .wizard-progress-bar {
    margin: 0 0 1em 0;
  }
}

.inbox-prospect-card {
  background: white;
  width: 18rem !important;

  .multiple-contacts-switch {
    padding: 1em;
    // background: #f5f5f5;

    .label {
      cursor: pointer;
    }
  }

  .actions {
    padding: 1em;
  }

  .avatar {
    text-align: center;
    padding: 1em;
  }

  .category-section {
    padding: 1em;
    background: rgba(243, 244, 246, 1);
  }

  .campaigns-section {
    padding: 1em;
  }

  .tags-section {
    padding: 1em;

    .ui.label {
      margin-bottom: 5px;
    }
  }

  .default-columns {
    padding: 1em;
  }

  .custom-columns {
    padding: 1em;
    background: rgba(243, 244, 246, 1);
  }
}