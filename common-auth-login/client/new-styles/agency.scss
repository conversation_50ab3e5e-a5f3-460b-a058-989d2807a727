.agency-dashboard {
  .ui.table {
    a:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }
  .custom-date-range-picker {
    width: 100%;
  }
  .date-picker-modal.time-val {
    margin-top: 0.5em;
    background: #f5f5f5;
    margin-right: 0;
    display: flex;
    flex-direction: column;
    .flex-row-field {
      margin-bottom: 0.5em;
      display: flex;
      flex-direction: row;
      .time-label {
        font-weight: bold;
        width: 50px;
      }
      .react-datepicker-wrapper {
        flex:1;
      }
    }
    .load-button {
      margin-left: 0;
    }
  }
}

.agency-page-header {
  margin-top: 5px;
  .title {
    display: inline-block;
    margin-left: 0.5em;
  }
}