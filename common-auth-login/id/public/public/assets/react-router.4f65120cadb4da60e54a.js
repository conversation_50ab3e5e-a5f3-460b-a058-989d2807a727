"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[242],{2719:function(n,t,e){e.d(t,{l_:function(){return U},AW:function(){return P},F0:function(){return g},rs:function(){return S},s6:function(){return M},LX:function(){return N},EN:function(){return F}});var r=e(1498),o=e(7363),a=e.n(o),i=e(7692),u=e(9337),c=e(6233);function s(n){var t=n.pathname,e=n.search,r=n.hash,o=t||"/";return e&&"?"!==e&&(o+="?"===e.charAt(0)?e:"?"+e),r&&"#"!==r&&(o+="#"===r.charAt(0)?r:"#"+r),o}function p(n,t,e,r){var o;"string"===typeof n?(o=function(n){var t=n||"/",e="",r="",o=t.indexOf("#");-1!==o&&(r=t.substr(o),t=t.substr(0,o));var a=t.indexOf("?");return-1!==a&&(e=t.substr(a),t=t.substr(0,a)),{pathname:t,search:"?"===e?"":e,hash:"#"===r?"":r}}(n),o.state=t):(void 0===(o=(0,i.Z)({},n)).pathname&&(o.pathname=""),o.search?"?"!==o.search.charAt(0)&&(o.search="?"+o.search):o.search="",o.hash?"#"!==o.hash.charAt(0)&&(o.hash="#"+o.hash):o.hash="",void 0!==t&&void 0===o.state&&(o.state=t));try{o.pathname=decodeURI(o.pathname)}catch(a){throw a instanceof URIError?new URIError('Pathname "'+o.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):a}return e&&(o.key=e),r?o.pathname?"/"!==o.pathname.charAt(0)&&(o.pathname=(0,u.Z)(o.pathname,r.pathname)):o.pathname=r.pathname:o.pathname||(o.pathname="/"),o}function h(){var n=null;var t=[];return{setPrompt:function(t){return n=t,function(){n===t&&(n=null)}},confirmTransitionTo:function(t,e,r,o){if(null!=n){var a="function"===typeof n?n(t,e):n;"string"===typeof a?"function"===typeof r?r(a,o):o(!0):o(!1!==a)}else o(!0)},appendListener:function(n){var e=!0;function r(){e&&n.apply(void 0,arguments)}return t.push(r),function(){e=!1,t=t.filter(function(n){return n!==r})}},notifyListeners:function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];t.forEach(function(n){return n.apply(void 0,e)})}}}"undefined"===typeof window||!window.document||window.document.createElement;function l(n,t,e){return Math.min(Math.max(n,t),e)}var f=e(7905),m=e(8109),d=e(9455),v=e.n(d),y=(e(338),e(1972)),C=e(1281),Z=e.n(C),E=function(n){var t=(0,f.Z)();return t.displayName=n,t},M=E("Router"),g=function(n){function t(t){var e;return(e=n.call(this,t)||this).state={location:t.history.location},e._isMounted=!1,e._pendingLocation=null,t.staticContext||(e.unlisten=t.history.listen(function(n){e._isMounted?e.setState({location:n}):e._pendingLocation=n})),e}(0,r.Z)(t,n),t.computeRootMatch=function(n){return{path:"/",url:"/",params:{},isExact:"/"===n}};var e=t.prototype;return e.componentDidMount=function(){this._isMounted=!0,this._pendingLocation&&this.setState({location:this._pendingLocation})},e.componentWillUnmount=function(){this.unlisten&&this.unlisten()},e.render=function(){return a().createElement(M.Provider,{children:this.props.children||null,value:{history:this.props.history,location:this.state.location,match:t.computeRootMatch(this.state.location.pathname),staticContext:this.props.staticContext}})},t}(a().Component);a().Component;var x=function(n){function t(){return n.apply(this,arguments)||this}(0,r.Z)(t,n);var e=t.prototype;return e.componentDidMount=function(){this.props.onMount&&this.props.onMount.call(this,this)},e.componentDidUpdate=function(n){this.props.onUpdate&&this.props.onUpdate.call(this,this,n)},e.componentWillUnmount=function(){this.props.onUnmount&&this.props.onUnmount.call(this,this)},e.render=function(){return null},t}(a().Component);var w={},k=1e4,A=0;function _(n,t){return void 0===n&&(n="/"),void 0===t&&(t={}),"/"===n?n:function(n){if(w[n])return w[n];var t=v().compile(n);return A<k&&(w[n]=t,A++),t}(n)(t,{pretty:!0})}function U(n){var t=n.computedMatch,e=n.to,r=n.push,o=void 0!==r&&r;return a().createElement(M.Consumer,null,function(n){n||(0,m.Z)(!1);var r=n.history,u=n.staticContext,s=o?r.push:r.replace,h=p(t?"string"===typeof e?_(e,t.params):(0,i.Z)({},e,{pathname:_(e.pathname,t.params)}):e);return u?(s(h),null):a().createElement(x,{onMount:function(){s(h)},onUpdate:function(n,t){var e,r,o=p(t.to);e=o,r=(0,i.Z)({},h,{key:o.key}),e.pathname===r.pathname&&e.search===r.search&&e.hash===r.hash&&e.key===r.key&&(0,c.Z)(e.state,r.state)||s(h)},to:e})})}var b={},R=1e4,L=0;function N(n,t){void 0===t&&(t={}),("string"===typeof t||Array.isArray(t))&&(t={path:t});var e=t,r=e.path,o=e.exact,a=void 0!==o&&o,i=e.strict,u=void 0!==i&&i,c=e.sensitive,s=void 0!==c&&c;return[].concat(r).reduce(function(t,e){if(!e&&""!==e)return null;if(t)return t;var r=function(n,t){var e=""+t.end+t.strict+t.sensitive,r=b[e]||(b[e]={});if(r[n])return r[n];var o=[],a={regexp:v()(n,o,t),keys:o};return L<R&&(r[n]=a,L++),a}(e,{end:a,strict:u,sensitive:s}),o=r.regexp,i=r.keys,c=o.exec(n);if(!c)return null;var p=c[0],h=c.slice(1),l=n===p;return a&&!l?null:{path:e,url:"/"===e&&""===p?"/":p,isExact:l,params:i.reduce(function(n,t,e){return n[t.name]=h[e],n},{})}},null)}var P=function(n){function t(){return n.apply(this,arguments)||this}return(0,r.Z)(t,n),t.prototype.render=function(){var n=this;return a().createElement(M.Consumer,null,function(t){t||(0,m.Z)(!1);var e=n.props.location||t.location,r=n.props.computedMatch?n.props.computedMatch:n.props.path?N(e.pathname,n.props):t.match,o=(0,i.Z)({},t,{location:e,match:r}),u=n.props,c=u.children,s=u.component,p=u.render;return Array.isArray(c)&&0===c.length&&(c=null),a().createElement(M.Provider,{value:o},o.match?c?"function"===typeof c?c(o):c:s?a().createElement(s,o):p?p(o):null:"function"===typeof c?c(o):null)})},t}(a().Component);function W(n){return"/"===n.charAt(0)?n:"/"+n}function D(n,t){if(!n)return t;var e=W(n);return 0!==t.pathname.indexOf(e)?t:(0,i.Z)({},t,{pathname:t.pathname.substr(e.length)})}function I(n){return"string"===typeof n?n:s(n)}function O(n){return function(){(0,m.Z)(!1)}}function T(){}a().Component;var S=function(n){function t(){return n.apply(this,arguments)||this}return(0,r.Z)(t,n),t.prototype.render=function(){var n=this;return a().createElement(M.Consumer,null,function(t){t||(0,m.Z)(!1);var e,r,o=n.props.location||t.location;return a().Children.forEach(n.props.children,function(n){if(null==r&&a().isValidElement(n)){e=n;var u=n.props.path||n.props.from;r=u?N(o.pathname,(0,i.Z)({},n.props,{path:u})):t.match}}),r?a().cloneElement(e,{location:o,computedMatch:r}):null})},t}(a().Component);function F(n){var t="withRouter("+(n.displayName||n.name)+")",e=function(t){var e=t.wrappedComponentRef,r=(0,y.Z)(t,["wrappedComponentRef"]);return a().createElement(M.Consumer,null,function(t){return t||(0,m.Z)(!1),a().createElement(n,(0,i.Z)({},r,t,{ref:e}))})};return e.displayName=t,e.WrappedComponent=n,Z()(e,n)}a().useContext}}]);
//# sourceMappingURL=react-router.1b8d8179ed4d8fed18dc26e19be7a729.js.map