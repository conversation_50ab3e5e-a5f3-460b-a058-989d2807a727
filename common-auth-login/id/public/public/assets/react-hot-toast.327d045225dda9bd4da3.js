"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[537],{4475:function(t,e,a){a.d(e,{x7:function(){return U},ZP:function(){return Y}});var o=a(7363),r=a(8384),i=(t,e)=>(t=>"function"==typeof t)(t)?t(e):t,s=(()=>{let t=0;return()=>(++t).toString()})(),n=(()=>{let t;return()=>{if(void 0===t&&typeof window<"u"){let e=matchMedia("(prefers-reduced-motion: reduce)");t=!e||e.matches}return t}})(),l=new Map,d=t=>{if(l.has(t))return;let e=setTimeout((()=>{l.delete(t),m({type:4,toastId:t})}),1e3);l.set(t,e)},c=(t,e)=>{switch(e.type){case 0:return{...t,toasts:[e.toast,...t.toasts].slice(0,20)};case 1:return e.toast.id&&(t=>{let e=l.get(t);e&&clearTimeout(e)})(e.toast.id),{...t,toasts:t.toasts.map((t=>t.id===e.toast.id?{...t,...e.toast}:t))};case 2:let{toast:a}=e;return t.toasts.find((t=>t.id===a.id))?c(t,{type:1,toast:a}):c(t,{type:0,toast:a});case 3:let{toastId:o}=e;return o?d(o):t.toasts.forEach((t=>{d(t.id)})),{...t,toasts:t.toasts.map((t=>t.id===o||void 0===o?{...t,visible:!1}:t))};case 4:return void 0===e.toastId?{...t,toasts:[]}:{...t,toasts:t.toasts.filter((t=>t.id!==e.toastId))};case 5:return{...t,pausedAt:e.time};case 6:let r=e.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map((t=>({...t,pauseDuration:t.pauseDuration+r})))}}},p=[],u={toasts:[],pausedAt:void 0},m=t=>{u=c(u,t),p.forEach((t=>{t(u)}))},f={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},y=t=>(e,a)=>{let o=((t,e="blank",a)=>({createdAt:Date.now(),visible:!0,type:e,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...a,id:(null==a?void 0:a.id)||s()}))(e,t,a);return m({type:2,toast:o}),o.id},h=(t,e)=>y("blank")(t,e);h.error=y("error"),h.success=y("success"),h.loading=y("loading"),h.custom=y("custom"),h.dismiss=t=>{m({type:3,toastId:t})},h.remove=t=>m({type:4,toastId:t}),h.promise=(t,e,a)=>{let o=h.loading(e.loading,{...a,...null==a?void 0:a.loading});return t.then((t=>(h.success(i(e.success,t),{id:o,...a,...null==a?void 0:a.success}),t))).catch((t=>{h.error(i(e.error,t),{id:o,...a,...null==a?void 0:a.error})})),t};var g=(t,e)=>{m({type:1,toast:{id:t,height:e}})},b=()=>{m({type:5,time:Date.now()})},x=t=>{let{toasts:e,pausedAt:a}=((t={})=>{let[e,a]=(0,o.useState)(u);(0,o.useEffect)((()=>(p.push(a),()=>{let t=p.indexOf(a);t>-1&&p.splice(t,1)})),[e]);let r=e.toasts.map((e=>{var a,o;return{...t,...t[e.type],...e,duration:e.duration||(null==(a=t[e.type])?void 0:a.duration)||(null==t?void 0:t.duration)||f[e.type],style:{...t.style,...null==(o=t[e.type])?void 0:o.style,...e.style}}}));return{...e,toasts:r}})(t);(0,o.useEffect)((()=>{if(a)return;let t=Date.now(),o=e.map((e=>{if(e.duration===1/0)return;let a=(e.duration||0)+e.pauseDuration-(t-e.createdAt);if(!(a<0))return setTimeout((()=>h.dismiss(e.id)),a);e.visible&&h.dismiss(e.id)}));return()=>{o.forEach((t=>t&&clearTimeout(t)))}}),[e,a]);let r=(0,o.useCallback)((()=>{a&&m({type:6,time:Date.now()})}),[a]),i=(0,o.useCallback)(((t,a)=>{let{reverseOrder:o=!1,gutter:r=8,defaultPosition:i}=a||{},s=e.filter((e=>(e.position||i)===(t.position||i)&&e.height)),n=s.findIndex((e=>e.id===t.id)),l=s.filter(((t,e)=>e<n&&t.visible)).length;return s.filter((t=>t.visible)).slice(...o?[l+1]:[0,l]).reduce(((t,e)=>t+(e.height||0)+r),0)}),[e]);return{toasts:e,handlers:{updateHeight:g,startPause:b,endPause:r,calculateOffset:i}}},v=r.F4`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,w=r.F4`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,E=r.F4`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,z=(0,r.zo)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${v} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${w} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${E} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,$=r.F4`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,k=(0,r.zo)("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${$} 1s linear infinite;
`,F=r.F4`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,C=r.F4`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,D=(0,r.zo)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${F} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${C} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,P=(0,r.zo)("div")`
  position: absolute;
`,I=(0,r.zo)("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,O=r.F4`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,A=(0,r.zo)("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${O} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,N=({toast:t})=>{let{icon:e,type:a,iconTheme:r}=t;return void 0!==e?"string"==typeof e?o.createElement(A,null,e):e:"blank"===a?null:o.createElement(I,null,o.createElement(k,{...r}),"loading"!==a&&o.createElement(P,null,"error"===a?o.createElement(z,{...r}):o.createElement(D,{...r})))},M=t=>`\n0% {transform: translate3d(0,${-200*t}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,T=t=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*t}%,-1px) scale(.6); opacity:0;}\n`,j=(0,r.zo)("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,H=(0,r.zo)("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,_=o.memo((({toast:t,position:e,style:a,children:s})=>{let l=t.height?((t,e)=>{let a=t.includes("top")?1:-1,[o,i]=n()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[M(a),T(a)];return{animation:e?`${(0,r.F4)(o)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${(0,r.F4)(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(t.position||e||"top-center",t.visible):{opacity:0},d=o.createElement(N,{toast:t}),c=o.createElement(H,{...t.ariaProps},i(t.message,t));return o.createElement(j,{className:t.className,style:{...l,...a,...t.style}},"function"==typeof s?s({icon:d,message:c}):o.createElement(o.Fragment,null,d,c))}));(0,r.cY)(o.createElement);var S=({id:t,className:e,style:a,onHeightUpdate:r,children:i})=>{let s=o.useCallback((e=>{if(e){let a=()=>{let a=e.getBoundingClientRect().height;r(t,a)};a(),new MutationObserver(a).observe(e,{subtree:!0,childList:!0,characterData:!0})}}),[t,r]);return o.createElement("div",{ref:s,className:e,style:a},i)},L=r.iv`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,U=({reverseOrder:t,position:e="top-center",toastOptions:a,gutter:r,children:s,containerStyle:l,containerClassName:d})=>{let{toasts:c,handlers:p}=x(a);return o.createElement("div",{style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...l},className:d,onMouseEnter:p.startPause,onMouseLeave:p.endPause},c.map((a=>{let l=a.position||e,d=((t,e)=>{let a=t.includes("top"),o=a?{top:0}:{bottom:0},r=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:n()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${e*(a?1:-1)}px)`,...o,...r}})(l,p.calculateOffset(a,{reverseOrder:t,gutter:r,defaultPosition:e}));return o.createElement(S,{id:a.id,key:a.id,onHeightUpdate:p.updateHeight,className:a.visible?L:"",style:d},"custom"===a.type?i(a.message,a):s?s(a):o.createElement(_,{toast:a,position:l}))})))},Y=h}}]);
//# sourceMappingURL=react-hot-toast.9d5dc56defd4e49869e2d3ca82038fd5.js.map