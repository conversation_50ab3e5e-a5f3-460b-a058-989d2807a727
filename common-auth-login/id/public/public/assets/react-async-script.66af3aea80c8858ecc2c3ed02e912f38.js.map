{"version": 3, "file": "react-async-script.xxxxxxxxxxxxxxxxxxxx.js", "mappings": "yMAAA,SAASA,IAA2Q,OAA9PA,EAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUH,EAASY,MAAMC,KAAMR,UAAY,CAS5T,IAAIS,EAAa,CAAC,EAEdC,EAAU,EACC,SAASC,EAAgBC,EAAcC,GAEpD,OADAA,EAAUA,GAAW,CAAC,EACf,SAA6BC,GAClC,IAAIC,EAAuBD,EAAiBE,aAAeF,EAAiBG,MAAQ,YAEhFC,EAEJ,SAAUC,GAfd,IAAwBC,EAAUC,EAkB5B,SAASH,EAAkBI,EAAOC,GAChC,IAAIC,EAKJ,OAHAA,EAAQL,EAAWb,KAAKE,KAAMc,EAAOC,IAAYf,MAC3CiB,MAAQ,CAAC,EACfD,EAAME,YAAc,GACbF,CACT,CAzB4BH,EAgBMF,GAhBhBC,EAgBHF,GAhBoCd,UAAYR,OAAO+B,OAAON,EAAWjB,WAAYgB,EAAShB,UAAUwB,YAAcR,EAAUA,EAASS,UAAYR,EA2BpK,IAAIS,EAASZ,EAAkBd,UAmM/B,OAjMA0B,EAAOC,mCAAqC,WAK1C,OAJKvB,KAAKwB,mBACRxB,KAAKwB,iBAAmB,uBAAyBtB,KAG5CF,KAAKwB,gBACd,EAEAF,EAAOG,eAAiB,WAEtB,OADAzB,KAAKkB,YAAsC,oBAAjBd,EAA8BA,IAAiBA,EAClEJ,KAAKkB,WACd,EAEAI,EAAOI,4BAA8B,SAAqCT,GACxE,IAAIU,EAAS3B,KAGbA,KAAK4B,SAASX,GAAO,WACnB,OAAOU,EAAOb,MAAMe,mBAAqBF,EAAOb,MAAMe,kBAAkBF,EAAOV,MACjF,GACF,EAEAK,EAAOQ,uCAAyC,WAC9C,IAAIC,EAAW9B,EAAWD,KAAKkB,aAE/B,IAAKa,IAAaA,EAASC,OACzB,MAAM,IAAIC,MAAM,yBAGlB,IAAK,IAAIC,KAAUH,EAASI,UAC1BJ,EAASI,UAAUD,GAAQH,UAGtBK,OAAO/B,EAAQgC,aACxB,EAEAf,EAAOgB,kBAAoB,WACzB,IAAIC,EAASvC,KAETwC,EAAYxC,KAAKyB,iBACjB9B,EAAMK,KAAKuB,qCACXkB,EAAWpC,EACXqC,EAAaD,EAASC,WACtBL,EAAeI,EAASJ,aACxBM,EAAWF,EAASE,SAUxB,GARID,GAA4C,qBAAvBN,OAAOM,KAC9BzC,EAAWuC,GAAa,CACtBR,QAAQ,EACRG,UAAW,CAAC,IAKZlC,EAAWuC,GAAY,CACzB,IAAII,EAAQ3C,EAAWuC,GAEvB,OAAII,IAAUA,EAAMZ,QAAUY,EAAMC,cAClC7C,KAAK0B,4BAA4BkB,QAKnCA,EAAMT,UAAUxC,GAAO,SAAUiD,GAC/B,OAAOL,EAAOb,4BAA4BkB,EAC5C,EAGF,CAQA,IAAIT,EAAY,CAAC,EAEjBA,EAAUxC,GAAO,SAAUiD,GACzB,OAAOL,EAAOb,4BAA4BkB,EAC5C,EAEA3C,EAAWuC,GAAa,CACtBR,QAAQ,EACRG,UAAWA,GAEb,IAAIW,EAASC,SAASC,cAAc,UAIpC,IAAK,IAAIC,KAHTH,EAAOI,IAAMV,EACbM,EAAOK,OAAQ,EAEO9C,EAAQ+C,WAC5BN,EAAOO,aAAaJ,EAAW5C,EAAQ+C,WAAWH,IAGhDN,IACFG,EAAOQ,GAAKX,GAGd,IAAIY,EAAoC,SAA2CC,GACjF,GAAIvD,EAAWuC,GAAY,CACzB,IACIiB,EADWxD,EAAWuC,GACEL,UAE5B,IAAK,IAAID,KAAUuB,EACbD,EAAKC,EAAavB,YACbuB,EAAavB,EAG1B,CACF,EAEIG,GAAkC,qBAAXD,SACzBA,OAAOC,GAAgB,WACrB,OAAOE,EAAOT,wCAChB,GAGFgB,EAAOY,OAAS,WACd,IAAI3B,EAAW9B,EAAWuC,GAEtBT,IACFA,EAASC,QAAS,EAClBuB,GAAkC,SAAUI,GAC1C,OAAItB,IAIJsB,EAAS5B,IACF,EACT,IAEJ,EAEAe,EAAOc,QAAU,WACf,IAAI7B,EAAW9B,EAAWuC,GAEtBT,IACFA,EAASc,SAAU,EACnBU,GAAkC,SAAUI,GAE1C,OADAA,EAAS5B,IACF,CACT,IAEJ,EAEAgB,SAASc,KAAKC,YAAYhB,EAC5B,EAEAxB,EAAOyC,qBAAuB,WAE5B,IAAIvB,EAAYxC,KAAKkB,YAErB,IAAgC,IAA5Bb,EAAQ2D,gBAGV,IAFA,IAAIC,EAAalB,SAASmB,qBAAqB,UAEtC3E,EAAI,EAAGA,EAAI0E,EAAWxE,OAAQF,GAAK,EACtC0E,EAAW1E,GAAG2D,IAAIiB,QAAQ3B,IAAc,GACtCyB,EAAW1E,GAAG6E,YAChBH,EAAW1E,GAAG6E,WAAWC,YAAYJ,EAAW1E,IAOxD,IAAIwC,EAAW9B,EAAWuC,GAEtBT,WACKA,EAASI,UAAUnC,KAAKuB,uCAEC,IAA5BlB,EAAQ2D,wBACH/D,EAAWuC,GAGxB,EAEAlB,EAAOgD,OAAS,WACd,IAAI5B,EAAarC,EAAQqC,WAErB6B,EAAcvE,KAAKc,MAEnB0D,GADoBD,EAAY1C,kBACjB0C,EAAYC,cAC3BC,EArNZ,SAAuC/E,EAAQgF,GAAY,GAAc,MAAVhF,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOqF,EAAavF,OAAOwF,KAAKlF,GAAqB,IAAKH,EAAI,EAAGA,EAAIoF,EAAWlF,OAAQF,IAAOI,EAAMgF,EAAWpF,GAAQmF,EAASP,QAAQxE,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAqNzRuF,CAA8BN,EAAa,CAAC,oBAAqB,iBAQlF,OALI7B,GAAgC,qBAAXN,SACvBqC,EAAW/B,GAA4C,qBAAvBN,OAAOM,GAA8BN,OAAOM,QAAcoC,GAG5FL,EAAWM,IAAMP,GACV,IAAAxB,eAAc1C,EAAkBmE,EACzC,EAEO/D,CACT,CAhNA,CAgNE,EAAAsE,WAKEC,GAAqB,IAAAC,aAAW,SAAUpE,EAAOiE,GACnD,OAAO,IAAA/B,eAActC,EAAmBvB,EAAS,CAAC,EAAG2B,EAAO,CAC1D0D,aAAcO,IAElB,IAKA,OAJAE,EAAmBzE,YAAc,qBAAuBD,EAAuB,IAC/E0E,EAAmBE,UAAY,CAC7BtD,kBAAmB,UAEd,IAAaoD,EAAoB3E,EAC1C,CACF,C", "sources": ["webpack://sr-common-auth/./node_modules/react-async-script/lib/esm/async-script-loader.js"], "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "this", "SCRIPT_MAP", "idCount", "makeAsyncScript", "getScriptURL", "options", "WrappedComponent", "wrappedComponentName", "displayName", "name", "AsyncScriptLoader", "_Component", "subClass", "superClass", "props", "context", "_this", "state", "__scriptURL", "create", "constructor", "__proto__", "_proto", "asyncScriptLoaderGetScriptLoaderID", "__scriptLoaderID", "setupScriptURL", "asyncScriptLoaderHandleLoad", "_this2", "setState", "asyncScriptOnLoad", "asyncScriptLoaderTriggerOnScriptLoaded", "mapEntry", "loaded", "Error", "obs<PERSON>ey", "observers", "window", "callback<PERSON><PERSON>", "componentDidMount", "_this3", "scriptURL", "_options", "globalName", "scriptId", "entry", "errored", "script", "document", "createElement", "attribute", "src", "async", "attributes", "setAttribute", "id", "callObserverFuncAndRemoveObserver", "func", "observersMap", "onload", "observer", "onerror", "body", "append<PERSON><PERSON><PERSON>", "componentWillUnmount", "removeOnUnmount", "allScripts", "getElementsByTagName", "indexOf", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "render", "_this$props", "forwardedRef", "childProps", "excluded", "sourceKeys", "keys", "_objectWithoutPropertiesLoose", "undefined", "ref", "Component", "ForwardedComponent", "forwardRef", "propTypes"], "sourceRoot": ""}