{"version": 3, "file": "react-side-effect.xxxxxxxxxxxxxxxxxxxx.js", "mappings": "qHAIA,IAF0BA,EAEtBC,EAAQ,EAAQ,MAChBC,GAHsBF,EAGWC,IAHwB,kBAAPD,GAAoB,YAAaA,EAAMA,EAAY,QAAIA,EAK7G,SAASG,EAAgBC,EAAKC,EAAKC,GAYjC,OAXID,KAAOD,EACTG,OAAOC,eAAeJ,EAAKC,EAAK,CAC9BC,MAAOA,EACPG,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZP,EAAIC,GAAOC,EAGNF,CACT,CAQA,IAAIQ,IAAiC,qBAAXC,SAA0BA,OAAOC,WAAYD,OAAOC,SAASC,eAgGvFC,EAAOC,QA/FP,SAAwBC,EAAoBC,EAA2BC,GACrE,GAAkC,oBAAvBF,EACT,MAAM,IAAIG,MAAM,iDAGlB,GAAyC,oBAA9BF,EACT,MAAM,IAAIE,MAAM,wDAGlB,GAAgC,qBAArBD,GAAgE,oBAArBA,EACpD,MAAM,IAAIC,MAAM,mEAOlB,OAAO,SAAcC,GACnB,GAAgC,oBAArBA,EACT,MAAM,IAAID,MAAM,sDAGlB,IACIE,EADAC,EAAmB,GAGvB,SAASC,IACPF,EAAQL,EAAmBM,EAAiBE,KAAI,SAAUC,GACxD,OAAOA,EAASC,KAClB,KAEIC,EAAWjB,UACbO,EAA0BI,GACjBH,IACTG,EAAQH,EAAiBG,GAE7B,CAEA,IAAIM,EAEJ,SAAUC,GA9Cd,IAAwBC,EAAUC,EAiD5B,SAASH,IACP,OAAOC,EAAeG,MAAMC,KAAMC,YAAcD,IAClD,CAnD4BF,EA+CDF,GA/CTC,EA+CHF,GA9CVO,UAAY7B,OAAO8B,OAAOL,EAAWI,WAC9CL,EAASK,UAAUE,YAAcP,EACjCA,EAASQ,UAAYP,EAoDjBH,EAAWW,KAAO,WAChB,OAAOjB,CACT,EAEAM,EAAWY,OAAS,WAClB,GAAIZ,EAAWjB,UACb,MAAM,IAAIS,MAAM,oFAGlB,IAAIqB,EAAgBnB,EAGpB,OAFAA,OAAQoB,EACRnB,EAAmB,GACZkB,CACT,EAEA,IAAIE,EAASf,EAAWO,UAqBxB,OAnBAQ,EAAOC,0BAA4B,WACjCrB,EAAiBsB,KAAKZ,MACtBT,GACF,EAEAmB,EAAOG,mBAAqB,WAC1BtB,GACF,EAEAmB,EAAOI,qBAAuB,WAC5B,IAAIC,EAAQzB,EAAiB0B,QAAQhB,MACrCV,EAAiB2B,OAAOF,EAAO,GAC/BxB,GACF,EAEAmB,EAAOQ,OAAS,WACd,OAAOlD,EAAea,cAAcO,EAAkBY,KAAKN,MAC7D,EAEOC,CACT,CA9CA,CA8CE5B,EAAMoD,eAMR,OAJAlD,EAAgB0B,EAAY,cAAe,cA1E7C,SAAwBP,GACtB,OAAOA,EAAiBgC,aAAehC,EAAiBiC,MAAQ,WAClE,CAwE6DC,CAAelC,GAAoB,KAE9FnB,EAAgB0B,EAAY,YAAajB,GAElCiB,CACT,CACF,C", "sources": ["webpack://sr-common-auth/./node_modules/react-side-effect/lib/index.js"], "names": ["ex", "React", "React__default", "_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "canUseDOM", "window", "document", "createElement", "module", "exports", "reducePropsToState", "handleStateChangeOnClient", "mapStateOnServer", "Error", "WrappedComponent", "state", "mountedInstances", "emitChange", "map", "instance", "props", "SideEffect", "_PureComponent", "subClass", "superClass", "apply", "this", "arguments", "prototype", "create", "constructor", "__proto__", "peek", "rewind", "recordedState", "undefined", "_proto", "UNSAFE_componentWillMount", "push", "componentDidUpdate", "componentWillUnmount", "index", "indexOf", "splice", "render", "PureComponent", "displayName", "name", "getDisplayName"], "sourceRoot": ""}