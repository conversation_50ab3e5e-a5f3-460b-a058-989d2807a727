"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[44],{8946:function(e,t,n){n.d(t,{X:function(){return r}});const r=!1},3218:function(e,t,n){n.d(t,{O:function(){return d}});var r=n(3224),s=n(8545),o=n(4100),i=n(9273);const a=1e3;let c,l,u;function d(e){(0,r.Hj)("dom",e),(0,r.D2)("dom",h)}function h(){if(!i.m.document)return;const e=r.rK.bind(null,"dom"),t=p(e,!0);i.m.document.addEventListener("click",t,!1),i.m.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach((t=>{const n=i.m[t]&&i.m[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&((0,s.hl)(n,"addEventListener",(function(t){return function(n,r,s){if("click"===n||"keypress"==n)try{const r=this,o=r.__sentry_instrumentation_handlers__=r.__sentry_instrumentation_handlers__||{},i=o[n]=o[n]||{refCount:0};if(!i.handler){const r=p(e);i.handler=r,t.call(this,n,r,s)}i.refCount++}catch(o){}return t.call(this,n,r,s)}})),(0,s.hl)(n,"removeEventListener",(function(e){return function(t,n,r){if("click"===t||"keypress"==t)try{const n=this,s=n.__sentry_instrumentation_handlers__||{},o=s[t];o&&(o.refCount--,o.refCount<=0&&(e.call(this,t,o.handler,r),o.handler=void 0,delete s[t]),0===Object.keys(s).length&&delete n.__sentry_instrumentation_handlers__)}catch(s){}return e.call(this,t,n,r)}})))}))}function p(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(e){try{return e.target}catch(t){return null}}(n);if(function(e,t){return"keypress"===e&&(!t||!t.tagName||"INPUT"!==t.tagName&&"TEXTAREA"!==t.tagName&&!t.isContentEditable)}(n.type,r))return;(0,s.xp)(n,"_sentryCaptured",!0),r&&!r._sentryId&&(0,s.xp)(r,"_sentryId",(0,o.DM)());const d="keypress"===n.type?"input":n.type;if(!function(e){if(e.type!==l)return!1;try{if(!e.target||e.target._sentryId!==u)return!1}catch(t){}return!0}(n)){e({event:n,name:d,global:t}),l=n.type,u=r?r._sentryId:void 0}clearTimeout(c),c=i.m.setTimeout((()=>{u=void 0,l=void 0}),a)}}},2472:function(e,t,n){n.d(t,{a:function(){return c}});var r=n(3224),s=n(5192),o=n(8545),i=n(9273);let a;function c(e){const t="history";(0,r.Hj)(t,e),(0,r.D2)(t,l)}function l(){if(!(0,s.B)())return;const e=i.m.onpopstate;function t(e){return function(...t){const n=t.length>2?t[2]:void 0;if(n){const e=a,t=String(n);a=t;const s={from:e,to:t};(0,r.rK)("history",s)}return e.apply(this,t)}}i.m.onpopstate=function(...t){const n=i.m.location.href,s=a;a=n;const o={from:s,to:n};if((0,r.rK)("history",o),e)try{return e.apply(this,t)}catch(c){}},(0,o.hl)(i.m.history,"pushState",t),(0,o.hl)(i.m.history,"replaceState",t)}},1048:function(e,t,n){n.d(t,{xU:function(){return a},UK:function(){return c}});var r=n(3224),s=n(8545),o=n(7701),i=n(9273);const a="__sentry_xhr_v3__";function c(e){(0,r.Hj)("xhr",e),(0,r.D2)("xhr",l)}function l(){if(!i.m.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;(0,s.hl)(e,"open",(function(e){return function(...t){const n=Date.now(),i=(0,o.HD)(t[0])?t[0].toUpperCase():void 0,c=function(e){if((0,o.HD)(e))return e;try{return e.toString()}catch(t){}return}(t[1]);if(!i||!c)return e.apply(this,t);this[a]={method:i,url:c,request_headers:{}},"POST"===i&&c.match(/sentry_key/)&&(this.__sentry_own_request__=!0);const l=()=>{const e=this[a];if(e&&4===this.readyState){try{e.status_code=this.status}catch(t){}const s={endTimestamp:Date.now(),startTimestamp:n,xhr:this};(0,r.rK)("xhr",s)}};return"onreadystatechange"in this&&"function"===typeof this.onreadystatechange?(0,s.hl)(this,"onreadystatechange",(function(e){return function(...t){return l(),e.apply(this,t)}})):this.addEventListener("readystatechange",l),(0,s.hl)(this,"setRequestHeader",(function(e){return function(...t){const[n,r]=t,s=this[a];return s&&(0,o.HD)(n)&&(0,o.HD)(r)&&(s.request_headers[n.toLowerCase()]=r),e.apply(this,t)}})),e.apply(this,t)}})),(0,s.hl)(e,"send",(function(e){return function(...t){const n=this[a];if(!n)return e.apply(this,t);void 0!==t[0]&&(n.body=t[0]);const s={startTimestamp:Date.now(),xhr:this};return(0,r.rK)("xhr",s),e.apply(this,t)}}))}},8434:function(e,t,n){n.d(t,{f7:function(){return C},sn:function(){return T},Fv:function(){return w},PR:function(){return _}});var r=n(4042),s=n(9839),o=n(7670),i=n(2511),a=n(2519),c=n(8169),l=n(6616),u=n(486),d=n(8946),h=n(8312),p=n(9273),m=n(461),f=n(5923),y=n(4633);const g=2147483647;let v,k,S=0,b={};function _(){const e=(0,m.QV)();if(e&&a.Z1){e.mark&&p.m.performance.mark("sentry-tracing-init");const t=(0,h.to)((({metric:e})=>{const t=e.entries[e.entries.length-1];if(!t)return;const n=(0,m.XL)(a.Z1),r=(0,m.XL)(t.startTime);d.X&&l.kg.log("[Measurements] Adding FID"),b.fid={value:e.value,unit:"millisecond"},b["mark.fid"]={value:n+r,unit:"second"}})),n=(0,h.PR)((({metric:e})=>{const t=e.entries[e.entries.length-1];t&&(d.X&&l.kg.log("[Measurements] Adding CLS"),b.cls={value:e.value,unit:""},k=t)}),!0),r=(0,h.$A)((({metric:e})=>{const t=e.entries[e.entries.length-1];t&&(d.X&&l.kg.log("[Measurements] Adding LCP"),b.lcp={value:e.value,unit:"millisecond"},v=t)}),!0),s=(0,h._4)((({metric:e})=>{e.entries[e.entries.length-1]&&(d.X&&l.kg.log("[Measurements] Adding TTFB"),b.ttfb={value:e.value,unit:"millisecond"})}));return()=>{t(),n(),r(),s()}}return()=>{}}function w(){(0,h._j)("longtask",(({entries:e})=>{for(const t of e){if(!(0,r.HN)())return;const e=(0,m.XL)(a.Z1+t.startTime),n=(0,m.XL)(t.duration),i=(0,s.qp)({name:"Main UI thread blocked",op:"ui.long-task",startTime:e,attributes:{[o.S3]:"auto.ui.browser.metrics"}});i&&i.end(e+n)}}))}function T(){(0,h._j)("event",(({entries:e})=>{for(const t of e){if(!(0,r.HN)())return;if("click"===t.name){const e=(0,m.XL)(a.Z1+t.startTime),n=(0,m.XL)(t.duration),r={name:(0,c.Rt)(t.target),op:`ui.interaction.${t.name}`,startTime:e,attributes:{[o.S3]:"auto.ui.browser.metrics"}},i=(0,c.iY)(t.target);i&&(r.attributes["ui.component_name"]=i);const l=(0,s.qp)(r);l&&l.end(e+n)}}}))}function C(e){const t=(0,m.QV)();if(!t||!p.m.performance.getEntries||!a.Z1)return;d.X&&l.kg.log("[Tracing] Adding & adjusting spans using Performance API");const n=(0,m.XL)(a.Z1),s=t.getEntries(),{op:h,start_timestamp:g}=(0,r.XU)(e);if(s.slice(S).forEach((t=>{const r=(0,m.XL)(t.startTime),s=(0,m.XL)(t.duration);if(!("navigation"===h&&g&&n+r<g))switch(t.entryType){case"navigation":!function(e,t,n){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((r=>{E(e,t,r,n)})),E(e,t,"secureConnection",n,"TLS/SSL","connectEnd"),E(e,t,"fetch",n,"cache","domainLookupStart"),E(e,t,"domainLookup",n,"DNS"),function(e,t,n){t.responseEnd&&((0,m.Y)(e,n+(0,m.XL)(t.requestStart),n+(0,m.XL)(t.responseEnd),{op:"browser",name:"request",attributes:{[o.S3]:"auto.ui.browser.metrics"}}),(0,m.Y)(e,n+(0,m.XL)(t.responseStart),n+(0,m.XL)(t.responseEnd),{op:"browser",name:"response",attributes:{[o.S3]:"auto.ui.browser.metrics"}}))}(e,t,n)}(e,t,n);break;case"mark":case"paint":case"measure":{!function(e,t,n,r,s){const i=s+n,a=i+r;(0,m.Y)(e,i,a,{name:t.name,op:t.entryType,attributes:{[o.S3]:"auto.resource.browser.metrics"}})}(e,t,r,s,n);const i=(0,y.Y)(),a=t.startTime<i.firstHiddenTime;"first-paint"===t.name&&a&&(d.X&&l.kg.log("[Measurements] Adding FP"),b.fp={value:t.startTime,unit:"millisecond"}),"first-contentful-paint"===t.name&&a&&(d.X&&l.kg.log("[Measurements] Adding FCP"),b.fcp={value:t.startTime,unit:"millisecond"});break}case"resource":!function(e,t,n,r,s,i){if("xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType)return;const a=(0,u.en)(n),c={[o.S3]:"auto.resource.browser.metrics"};I(c,t,"transferSize","http.response_transfer_size"),I(c,t,"encodedBodySize","http.response_content_length"),I(c,t,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in t&&(c["resource.render_blocking_status"]=t.renderBlockingStatus);a.protocol&&(c["url.scheme"]=a.protocol.split(":").pop());a.host&&(c["server.address"]=a.host);c["url.same_origin"]=n.includes(p.m.location.origin);const l=i+r,d=l+s;(0,m.Y)(e,l,d,{name:n.replace(p.m.location.origin,""),op:t.initiatorType?`resource.${t.initiatorType}`:"resource.other",attributes:c})}(e,t,t.name,r,s,n)}})),S=Math.max(s.length-1,0),function(e){const t=p.m.navigator;if(!t)return;const n=t.connection;n&&(n.effectiveType&&e.setAttribute("effectiveConnectionType",n.effectiveType),n.type&&e.setAttribute("connectionType",n.type),(0,m.nl)(n.rtt)&&(b["connection.rtt"]={value:n.rtt,unit:"millisecond"}));(0,m.nl)(t.deviceMemory)&&e.setAttribute("deviceMemory",`${t.deviceMemory} GB`);(0,m.nl)(t.hardwareConcurrency)&&e.setAttribute("hardwareConcurrency",String(t.hardwareConcurrency))}(e),"pageload"===h){!function(e){const t=(0,f.W)();if(!t)return;const{responseStart:n,requestStart:r}=t;r<=n&&(d.X&&l.kg.log("[Measurements] Adding TTFB Request Time"),e["ttfb.requestTime"]={value:n-r,unit:"millisecond"})}(b),["fcp","fp","lcp"].forEach((e=>{if(!b[e]||!g||n>=g)return;const t=b[e].value,r=n+(0,m.XL)(t),s=Math.abs(1e3*(r-g)),o=s-t;d.X&&l.kg.log(`[Measurements] Normalized ${e} from ${t} to ${s} (${o})`),b[e].value=s}));const t=b["mark.fid"];t&&b.fid&&((0,m.Y)(e,t.value,t.value+(0,m.XL)(b.fid.value),{name:"first input delay",op:"ui.action",attributes:{[o.S3]:"auto.ui.browser.metrics"}}),delete b["mark.fid"]),"fcp"in b||delete b.cls,Object.keys(b).forEach((e=>{(0,i.o)(e,b[e].value,b[e].unit)})),function(e){v&&(d.X&&l.kg.log("[Measurements] Adding LCP Data"),v.element&&e.setAttribute("lcp.element",(0,c.Rt)(v.element)),v.id&&e.setAttribute("lcp.id",v.id),v.url&&e.setAttribute("lcp.url",v.url.trim().slice(0,200)),e.setAttribute("lcp.size",v.size));k&&k.sources&&(d.X&&l.kg.log("[Measurements] Adding CLS Data"),k.sources.forEach(((t,n)=>e.setAttribute(`cls.source.${n+1}`,(0,c.Rt)(t.node)))))}(e)}v=void 0,k=void 0,b={}}function E(e,t,n,r,s,i){const a=i?t[i]:t[`${n}End`],c=t[`${n}Start`];c&&a&&(0,m.Y)(e,r+(0,m.XL)(c),r+(0,m.XL)(a),{op:"browser",name:s||n,attributes:{[o.S3]:"auto.ui.browser.metrics"}})}function I(e,t,n,r){const s=t[n];null!=s&&s<g&&(e[r]=s)}},2550:function(e,t,n){n.d(t,{N:function(){return p}});var r=n(2174),s=n(87),o=n(4042),i=n(7670),a=n(9839),c=n(2519),l=n(8169),u=n(8545),d=n(8312),h=n(461);function p(){if((0,h.QV)()&&c.Z1){const e=(0,d.YF)((({metric:e})=>{const t=(0,s.s3)();if(!t||void 0==e.value)return;const n=e.entries.find((t=>t.duration===e.value&&m[t.name]));if(!n)return;const d=m[n.name],p=t.getOptions(),f=(0,h.XL)(c.Z1+n.startTime),y=(0,h.XL)(e.value),g=(0,s.nZ)(),v=(0,o.HN)(),k=v?(0,o.Gx)(v):void 0,S=k?(0,o.XU)(k).description:void 0,b=g.getUser(),_=t.getIntegrationByName("Replay"),w=_&&_.getReplayId(),T=void 0!==b?b.email||b.id||b.ip_address:void 0,C=(0,r.x)([g,"access",e=>e.getScopeData,"call",e=>e(),"access",e=>e.contexts,"optionalAccess",e=>e.profile,"optionalAccess",e=>e.profile_id]),E=(0,l.Rt)(n.target),I=(0,u.Jr)({release:p.release,environment:p.environment,transaction:S,[i.JQ]:e.value,user:T||void 0,profile_id:C||void 0,replay_id:w||void 0}),x=(0,a.qp)({name:E,op:`ui.interaction.${d}`,attributes:I,startTime:f,experimental:{standalone:!0}});x.addEvent("inp",{[i.E1]:"millisecond",[i.Wb]:e.value}),x.end(f+y)}));return()=>{e()}}return()=>{}}const m={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"}},8312:function(e,t,n){n.d(t,{PR:function(){return X},to:function(){return V},YF:function(){return J},$A:function(){return K},_j:function(){return G},_4:function(){return Y}});var r=n(6616),s=n(6930),o=n(8946);const i=(e,t,n,r)=>{let s,o;return i=>{t.value>=0&&(i||r)&&(o=t.value-(s||0),(o||void 0===s)&&(s=t.value,t.delta=o,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,n),e(t)))}};var a=n(9273);var c=n(5923);const l=()=>{const e=(0,c.W)();return e&&e.activationStart||0},u=(e,t)=>{const n=(0,c.W)();let r="navigate";n&&(a.m.document&&a.m.document.prerendering||l()>0?r="prerender":a.m.document&&a.m.document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-")));return{name:e,value:"undefined"===typeof t?-1:t,rating:"good",delta:0,entries:[],id:`v3-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},d=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(r){}},h=e=>{const t=t=>{("pagehide"===t.type||a.m.document&&"hidden"===a.m.document.visibilityState)&&e(t)};a.m.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},p=e=>{let t=!1;return n=>{t||(e(n),t=!0)}};var m=n(4633);const f=e=>{a.m.document&&a.m.document.prerendering?addEventListener("prerenderingchange",(()=>e()),!0):e()},y=[1800,3e3],g=[.1,.25],v=(e,t={})=>{((e,t={})=>{f((()=>{const n=(0,m.Y)(),r=u("FCP");let s;const o=d("paint",(e=>{e.forEach((e=>{"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<n.firstHiddenTime&&(r.value=Math.max(e.startTime-l(),0),r.entries.push(e),s(!0)))}))}));o&&(s=i(e,r,y,t.reportAllChanges))}))})(p((()=>{const n=u("CLS",0);let r,s=0,o=[];const a=e=>{e.forEach((e=>{if(!e.hadRecentInput){const t=o[0],n=o[o.length-1];s&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(s+=e.value,o.push(e)):(s=e.value,o=[e])}})),s>n.value&&(n.value=s,n.entries=o,r())},c=d("layout-shift",a);c&&(r=i(e,n,g,t.reportAllChanges),h((()=>{a(c.takeRecords()),r(!0)})),setTimeout(r,0))})))},k=[100,300],S=(e,t={})=>{f((()=>{const n=(0,m.Y)(),r=u("FID");let s;const o=e=>{e.startTime<n.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),s(!0))},a=e=>{e.forEach(o)},c=d("first-input",a);s=i(e,r,k,t.reportAllChanges),c&&h(p((()=>{a(c.takeRecords()),c.disconnect()})))}))};let b=0,_=1/0,w=0;const T=e=>{e.forEach((e=>{e.interactionId&&(_=Math.min(_,e.interactionId),w=Math.max(w,e.interactionId),b=w?(w-_)/7+1:0)}))};let C;const E=()=>{"interactionCount"in performance||C||(C=d("event",T,{type:"event",buffered:!0,durationThreshold:0}))},I=[200,500],x=()=>(C?b:performance.interactionCount||0)-0,M=[],R={},A=e=>{const t=M[M.length-1],n=R[e.interactionId];if(n||M.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{const t={id:e.interactionId,latency:e.duration,entries:[e]};R[t.id]=t,M.push(t)}M.sort(((e,t)=>t.latency-e.latency)),M.splice(10).forEach((e=>{delete R[e.id]}))}},D=(e,t={})=>{f((()=>{E();const n=u("INP");let r;const s=e=>{e.forEach((e=>{if(e.interactionId&&A(e),"first-input"===e.entryType){!M.some((t=>t.entries.some((t=>e.duration===t.duration&&e.startTime===t.startTime))))&&A(e)}}));const t=(()=>{const e=Math.min(M.length-1,Math.floor(x()/50));return M[e]})();t&&t.latency!==n.value&&(n.value=t.latency,n.entries=t.entries,r())},o=d("event",s,{durationThreshold:null!=t.durationThreshold?t.durationThreshold:40});r=i(e,n,I,t.reportAllChanges),o&&("PerformanceEventTiming"in a.m&&"interactionId"in PerformanceEventTiming.prototype&&o.observe({type:"first-input",buffered:!0}),h((()=>{s(o.takeRecords()),n.value<0&&x()>0&&(n.value=0,n.entries=[]),r(!0)})))}))},L=[2500,4e3],O={},N=(e,t={})=>{f((()=>{const n=(0,m.Y)(),r=u("LCP");let s;const o=e=>{const t=e[e.length-1];t&&t.startTime<n.firstHiddenTime&&(r.value=Math.max(t.startTime-l(),0),r.entries=[t],s())},c=d("largest-contentful-paint",o);if(c){s=i(e,r,L,t.reportAllChanges);const n=p((()=>{O[r.id]||(o(c.takeRecords()),c.disconnect(),O[r.id]=!0,s(!0))}));["keydown","click"].forEach((e=>{a.m.document&&addEventListener(e,(()=>setTimeout(n,0)),!0)})),h(n)}}))},F=[800,1800],P=e=>{a.m.document&&a.m.document.prerendering?f((()=>P(e))):a.m.document&&"complete"!==a.m.document.readyState?addEventListener("load",(()=>P(e)),!0):setTimeout(e,0)},B=(e,t={})=>{const n=u("TTFB"),r=i(e,n,F,t.reportAllChanges);P((()=>{const e=(0,c.W)();if(e){const t=e.responseStart;if(t<=0||t>performance.now())return;n.value=Math.max(t-l(),0),n.entries=[e],r(!0)}}))},z={},U={};let j,H,W,$,q;function X(e,t=!1){return se("cls",e,Z,j,t)}function K(e,t=!1){return se("lcp",e,te,W,t)}function V(e){return se("fid",e,ee,H)}function Y(e){return se("ttfb",e,ne,$)}function J(e){return se("inp",e,re,q)}function G(e,t){return oe(e,t),U[e]||(!function(e){const t={};"event"===e&&(t.durationThreshold=0);d(e,(t=>{Q(e,{entries:t})}),t)}(e),U[e]=!0),ie(e,t)}function Q(e,t){const n=z[e];if(n&&n.length)for(const a of n)try{a(t)}catch(i){o.X&&r.kg.error(`Error while triggering instrumentation handler.\nType: ${e}\nName: ${(0,s.$P)(a)}\nError:`,i)}}function Z(){return v((e=>{Q("cls",{metric:e}),j=e}),{reportAllChanges:!0})}function ee(){return S((e=>{Q("fid",{metric:e}),H=e}))}function te(){return N((e=>{Q("lcp",{metric:e}),W=e}))}function ne(){return B((e=>{Q("ttfb",{metric:e}),$=e}))}function re(){return D((e=>{Q("inp",{metric:e}),q=e}))}function se(e,t,n,r,s=!1){let o;return oe(e,t),U[e]||(o=n(),U[e]=!0),r&&t({metric:r}),ie(e,t,s?o:void 0)}function oe(e,t){z[e]=z[e]||[],z[e].push(t)}function ie(e,t,n){return()=>{n&&n();const r=z[e];if(!r)return;const s=r.indexOf(t);-1!==s&&r.splice(s,1)}}},9273:function(e,t,n){n.d(t,{m:function(){return r}});const r=n(5793).n},461:function(e,t,n){n.d(t,{QV:function(){return c},nl:function(){return i},XL:function(){return l},Y:function(){return a}});var r=n(4042),s=n(9839),o=n(9273);function i(e){return"number"===typeof e&&isFinite(e)}function a(e,t,n,{...o}){const i=(0,r.XU)(e).start_timestamp;return i&&i>t&&"function"===typeof e.updateStartTime&&e.updateStartTime(t),(0,s._d)(e,(()=>{const e=(0,s.qp)({startTime:t,...o});return e&&e.end(n),e}))}function c(){return o.m&&o.m.addEventListener&&o.m.performance}function l(e){return e/1e3}},5923:function(e,t,n){n.d(t,{W:function(){return s}});var r=n(9273);const s=()=>r.m.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},4633:function(e,t,n){n.d(t,{Y:function(){return i}});var r=n(9273);let s=-1;const o=e=>{"hidden"===r.m.document.visibilityState&&s>-1&&(s="visibilitychange"===e.type?e.timeStamp:0,removeEventListener("visibilitychange",o,!0),removeEventListener("prerenderingchange",o,!0))},i=()=>(r.m.document&&s<0&&(s="hidden"!==r.m.document.visibilityState||r.m.document.prerendering?1/0:0,addEventListener("visibilitychange",o,!0),addEventListener("prerenderingchange",o,!0)),{get firstHiddenTime(){return s}})},4082:function(e,t,n){n.d(t,{G:function(){return kr}});var r=n(8643),s=n(2174),o=n(4124),i=n(87),a=n(5745),c=n(5506),l=n(3130),u=n(4042),d=n(7670),h=n(8057),p=n(5793),m=n(2615),f=n(8545),y=n(8169),g=n(2519),v=n(6616),k=n(4100),S=n(712),b=n(8571),_=n(300),w=n(9796),T=n(9987),C=n(8312),E=n(1048),I=n(3218),x=n(2472);const M=p.n,R="sentryReplaySession",A="replay_event",D="Unable to send Replay",L=15e4,O=5e3,N=2e7,F=36e5;function P(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}var B;function z(e){const t=P([e,"optionalAccess",e=>e.host]);return Boolean(P([t,"optionalAccess",e=>e.shadowRoot])===e)}function U(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function j(e){try{const n=e.rules||e.cssRules;return n?((t=Array.from(n,H).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),t):null}catch(n){return null}var t}function H(e){let t;if(function(e){return"styleSheet"in e}(e))try{t=j(e.styleSheet)||function(e){const{cssText:t}=e;if(t.split('"').length<3)return t;const n=["@import",`url(${JSON.stringify(e.href)})`];return""===e.layerName?n.push("layer"):e.layerName&&n.push(`layer(${e.layerName})`),e.supportsText&&n.push(`supports(${e.supportsText})`),e.media.length&&n.push(e.media.mediaText),n.join(" ")+";"}(e)}catch(n){}else if(function(e){return"selectorText"in e}(e)&&e.selectorText.includes(":"))return function(e){const t=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm;return e.replace(t,"$1\\$2")}(e.cssText);return t||e.cssText}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(B||(B={}));class W{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){if(!e)return-1;const t=P([this,"access",e=>e.getMeta,"call",t=>t(e),"optionalAccess",e=>e.id]);return r=()=>-1,null!=(n=t)?n:r();var n,r}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){const n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)}replace(e,t){const n=this.getNode(e);if(n){const e=this.nodeMetaMap.get(n);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function $({maskInputOptions:e,tagName:t,type:n}){return"OPTION"===t&&(t="SELECT"),Boolean(e[t.toLowerCase()]||n&&e[n]||"password"===n||"INPUT"===t&&!n&&e.text)}function q({isMasked:e,element:t,value:n,maskInputFn:r}){let s=n||"";return e?(r&&(s=r(s,t)),"*".repeat(s.length)):s}function X(e){return e.toLowerCase()}function K(e){return e.toUpperCase()}const V="__rrweb_original__";function Y(e){const t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?X(t):null}function J(e,t,n){return"INPUT"!==t||"radio"!==n&&"checkbox"!==n?e.value:e.getAttribute("value")||""}let G=1;const Q=new RegExp("[^a-z0-9-_:]"),Z=-2;function ee(){return G++}let te,ne;const re=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,se=/^(?:[a-z+]+:)?\/\//i,oe=/^www\..*/i,ie=/^(data:)([^,]*),(.*)/i;function ae(e,t){return(e||"").replace(re,((e,n,r,s,o,i)=>{const a=r||o||i,c=n||s||"";if(!a)return e;if(se.test(a)||oe.test(a))return`url(${c}${a}${c})`;if(ie.test(a))return`url(${c}${a}${c})`;if("/"===a[0])return`url(${c}${function(e){let t="";return t=e.indexOf("//")>-1?e.split("/").slice(0,3).join("/"):e.split("/")[0],t=t.split("?")[0],t}(t)+a}${c})`;const l=t.split("/"),u=a.split("/");l.pop();for(const t of u)"."!==t&&(".."===t?l.pop():l.push(t));return`url(${c}${l.join("/")}${c})`}))}const ce=/^[^ \t\n\r\u000c]+/,le=/^[, \t\n\r\u000c]+/;function ue(e,t){if(!t||""===t.trim())return t;const n=e.createElement("a");return n.href=t,n.href}function de(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function he(){const e=document.createElement("a");return e.href="",e.href}function pe(e,t,n,r,s,o){return r?"src"===n||"href"===n&&("use"!==t||"#"!==r[0])||"xlink:href"===n&&"#"!==r[0]?ue(e,r):"background"!==n||"table"!==t&&"td"!==t&&"th"!==t?"srcset"===n?function(e,t){if(""===t.trim())return t;let n=0;function r(e){let r;const s=e.exec(t.substring(n));return s?(r=s[0],n+=r.length,r):""}const s=[];for(;r(le),!(n>=t.length);){let o=r(ce);if(","===o.slice(-1))o=ue(e,o.substring(0,o.length-1)),s.push(o);else{let r="";o=ue(e,o);let i=!1;for(;;){const e=t.charAt(n);if(""===e){s.push((o+r).trim());break}if(i)")"===e&&(i=!1);else{if(","===e){n+=1,s.push((o+r).trim());break}"("===e&&(i=!0)}r+=e,n+=1}}}return s.join(", ")}(e,r):"style"===n?ae(r,he()):"object"===t&&"data"===n?ue(e,r):"function"===typeof o?o(n,r,s):r:ue(e,r):r}function me(e,t,n){return("video"===e||"audio"===e)&&"autoplay"===t}function fe(e,t,n=1/0,r=0){return e?e.nodeType!==e.ELEMENT_NODE||r>n?-1:t(e)?r:fe(e.parentNode,t,n,r+1):-1}function ye(e,t){return n=>{const r=n;if(null===r)return!1;try{if(e)if("string"===typeof e){if(r.matches(`.${e}`))return!0}else if(function(e,t){for(let n=e.classList.length;n--;){const r=e.classList[n];if(t.test(r))return!0}return!1}(r,e))return!0;return!(!t||!r.matches(t))}catch(s){return!1}}}function ge(e,t,n,r,s,o){try{const i=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===i)return!1;if("INPUT"===i.tagName){const e=i.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(e))return!0}let a=-1,c=-1;if(o){if(c=fe(i,ye(r,s)),c<0)return!0;a=fe(i,ye(t,n),c>=0?c:1/0)}else{if(a=fe(i,ye(t,n)),a<0)return!1;c=fe(i,ye(r,s),a>=0?a:1/0)}return a>=0?!(c>=0)||a<=c:!(c>=0)&&!!o}catch(i){}return!!o}function ve(e,t){const{doc:n,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskAttributeFn:c,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,inlineStylesheet:p,maskInputOptions:m={},maskTextFn:f,maskInputFn:y,dataURLOptions:g={},inlineImages:v,recordCanvas:k,keepIframeSrcFn:S,newlyAddedElement:b=!1}=t,_=function(e,t){if(!t.hasNode(e))return;const n=t.getId(e);return 1===n?void 0:n}(n,r);switch(e.nodeType){case e.DOCUMENT_NODE:return"CSS1Compat"!==e.compatMode?{type:B.Document,childNodes:[],compatMode:e.compatMode}:{type:B.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:B.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:_};case e.ELEMENT_NODE:return function(e,t){const{doc:n,blockClass:r,blockSelector:s,unblockSelector:o,inlineStylesheet:i,maskInputOptions:a={},maskAttributeFn:c,maskInputFn:l,dataURLOptions:u={},inlineImages:d,recordCanvas:h,keepIframeSrcFn:p,newlyAddedElement:m=!1,rootId:f,maskAllText:y,maskTextClass:g,unmaskTextClass:v,maskTextSelector:k,unmaskTextSelector:S}=t,b=function(e,t,n,r){try{if(r&&e.matches(r))return!1;if("string"===typeof t){if(e.classList.contains(t))return!0}else for(let n=e.classList.length;n--;){const r=e.classList[n];if(t.test(r))return!0}if(n)return e.matches(n)}catch(s){}return!1}(e,r,s,o),_=function(e){if(e instanceof HTMLFormElement)return"form";const t=X(e.tagName);return Q.test(t)?"div":t}(e);let w={};const T=e.attributes.length;for(let I=0;I<T;I++){const t=e.attributes[I];t.name&&!me(_,t.name,t.value)&&(w[t.name]=pe(n,_,X(t.name),t.value,e,c))}if("link"===_&&i){const t=Array.from(n.styleSheets).find((t=>t.href===e.href));let r=null;t&&(r=j(t)),r&&(delete w.rel,delete w.href,w._cssText=ae(r,t.href))}if("style"===_&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){const t=j(e.sheet);t&&(w._cssText=ae(t,he()))}if("input"===_||"textarea"===_||"select"===_||"option"===_){const t=e,n=Y(t),r=J(t,K(_),n),s=t.checked;if("submit"!==n&&"button"!==n&&r){const e=ge(t,g,k,v,S,$({type:n,tagName:K(_),maskInputOptions:a}));w.value=q({isMasked:e,element:t,value:r,maskInputFn:l})}s&&(w.checked=s)}"option"===_&&(e.selected&&!a.select?w.selected=!0:delete w.selected);if("canvas"===_&&h)if("2d"===e.__context)(function(e){const t=e.getContext("2d");if(!t)return!0;for(let n=0;n<e.width;n+=50)for(let r=0;r<e.height;r+=50){const s=t.getImageData,o=V in s?s[V]:s;if(new Uint32Array(o.call(t,n,r,Math.min(50,e.width-n),Math.min(50,e.height-r)).data.buffer).some((e=>0!==e)))return!1}return!0})(e)||(w.rr_dataURL=e.toDataURL(u.type,u.quality));else if(!("__context"in e)){const t=e.toDataURL(u.type,u.quality),n=document.createElement("canvas");n.width=e.width,n.height=e.height;t!==n.toDataURL(u.type,u.quality)&&(w.rr_dataURL=t)}if("img"===_&&d){te||(te=n.createElement("canvas"),ne=te.getContext("2d"));const t=e,r=t.crossOrigin;t.crossOrigin="anonymous";const s=()=>{t.removeEventListener("load",s);try{te.width=t.naturalWidth,te.height=t.naturalHeight,ne.drawImage(t,0,0),w.rr_dataURL=te.toDataURL(u.type,u.quality)}catch(e){console.warn(`Cannot inline img src=${t.currentSrc}! Error: ${e}`)}r?w.crossOrigin=r:t.removeAttribute("crossorigin")};t.complete&&0!==t.naturalWidth?s():t.addEventListener("load",s)}"audio"!==_&&"video"!==_||(w.rr_mediaState=e.paused?"paused":"played",w.rr_mediaCurrentTime=e.currentTime);m||(e.scrollLeft&&(w.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(w.rr_scrollTop=e.scrollTop));if(b){const{width:t,height:n}=e.getBoundingClientRect();w={class:w.class,rr_width:`${t}px`,rr_height:`${n}px`}}"iframe"!==_||p(w.src)||(e.contentDocument||(w.rr_src=w.src),delete w.src);let C;try{customElements.get(_)&&(C=!0)}catch(E){}return{type:B.Element,tagName:_,attributes:w,childNodes:[],isSVG:de(e)||void 0,needBlock:b,rootId:f,isCustom:C}}(e,{doc:n,blockClass:s,blockSelector:o,unblockSelector:i,inlineStylesheet:p,maskAttributeFn:c,maskInputOptions:m,maskInputFn:y,dataURLOptions:g,inlineImages:v,recordCanvas:k,keepIframeSrcFn:S,newlyAddedElement:b,rootId:_,maskAllText:a,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h});case e.TEXT_NODE:return function(e,t){const{maskAllText:n,maskTextClass:r,unmaskTextClass:s,maskTextSelector:o,unmaskTextSelector:i,maskTextFn:a,maskInputOptions:c,maskInputFn:l,rootId:u}=t,d=e.parentNode&&e.parentNode.tagName;let h=e.textContent;const p="STYLE"===d||void 0,m="SCRIPT"===d||void 0,f="TEXTAREA"===d||void 0;if(p&&h){try{e.nextSibling||e.previousSibling||P([e,"access",e=>e.parentNode,"access",e=>e.sheet,"optionalAccess",e=>e.cssRules])&&(h=j(e.parentNode.sheet))}catch(g){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${g}`,e)}h=ae(h,he())}m&&(h="SCRIPT_PLACEHOLDER");const y=ge(e,r,o,s,i,n);p||m||f||!h||!y||(h=a?a(h,e.parentElement):h.replace(/[\S]/g,"*"));f&&h&&(c.textarea||y)&&(h=l?l(h,e.parentNode):h.replace(/[\S]/g,"*"));if("OPTION"===d&&h){h=q({isMasked:ge(e,r,o,s,i,$({type:null,tagName:d,maskInputOptions:c})),element:e,value:h,maskInputFn:l})}return{type:B.Text,textContent:h||"",isStyle:p,rootId:u}}(e,{maskAllText:a,maskTextClass:l,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,maskTextFn:f,maskInputOptions:m,maskInputFn:y,rootId:_});case e.CDATA_SECTION_NODE:return{type:B.CDATA,textContent:"",rootId:_};case e.COMMENT_NODE:return{type:B.Comment,textContent:e.textContent||"",rootId:_};default:return!1}}function ke(e){return void 0===e||null===e?"":e.toLowerCase()}function Se(e,t){const{doc:n,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:h=!1,inlineStylesheet:p=!0,maskInputOptions:m={},maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:k={},inlineImages:S=!1,recordCanvas:b=!1,onSerialize:_,onIframeLoad:w,iframeLoadTimeout:T=5e3,onStylesheetLoad:C,stylesheetLoadTimeout:E=5e3,keepIframeSrcFn:I=(()=>!1),newlyAddedElement:x=!1}=t;let{preserveWhiteSpace:M=!0}=t;const R=ve(e,{doc:n,mirror:r,blockClass:s,blockSelector:o,maskAllText:a,unblockSelector:i,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,dataURLOptions:k,inlineImages:S,recordCanvas:b,keepIframeSrcFn:I,newlyAddedElement:x});if(!R)return console.warn(e,"not serialized"),null;let A;A=r.hasNode(e)?r.getId(e):!function(e,t){if(t.comment&&e.type===B.Comment)return!0;if(e.type===B.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)&&"script"===e.attributes.as||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"===typeof e.attributes.href&&e.attributes.href.endsWith(".js")))return!0;if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(ke(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===ke(e.attributes.name)||"icon"===ke(e.attributes.rel)||"apple-touch-icon"===ke(e.attributes.rel)||"shortcut icon"===ke(e.attributes.rel))))return!0;if("meta"===e.tagName){if(t.headMetaDescKeywords&&ke(e.attributes.name).match(/^description|keywords$/))return!0;if(t.headMetaSocial&&(ke(e.attributes.property).match(/^(og|twitter|fb):/)||ke(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===ke(e.attributes.name)))return!0;if(t.headMetaRobots&&("robots"===ke(e.attributes.name)||"googlebot"===ke(e.attributes.name)||"bingbot"===ke(e.attributes.name)))return!0;if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;if(t.headMetaAuthorship&&("author"===ke(e.attributes.name)||"generator"===ke(e.attributes.name)||"framework"===ke(e.attributes.name)||"publisher"===ke(e.attributes.name)||"progid"===ke(e.attributes.name)||ke(e.attributes.property).match(/^article:/)||ke(e.attributes.property).match(/^product:/)))return!0;if(t.headMetaVerification&&("google-site-verification"===ke(e.attributes.name)||"yandex-verification"===ke(e.attributes.name)||"csrf-token"===ke(e.attributes.name)||"p:domain_verify"===ke(e.attributes.name)||"verify-v1"===ke(e.attributes.name)||"verification"===ke(e.attributes.name)||"shopify-checkout-api-token"===ke(e.attributes.name)))return!0}}return!1}(R,v)&&(M||R.type!==B.Text||R.isStyle||R.textContent.replace(/^\s+|\s+$/gm,"").length)?ee():Z;const D=Object.assign(R,{id:A});if(r.add(e,D),A===Z)return null;_&&_(e);let L=!h;if(D.type===B.Element){L=L&&!D.needBlock,delete D.needBlock;const t=e.shadowRoot;t&&U(t)&&(D.isShadowHost=!0)}if((D.type===B.Document||D.type===B.Element)&&L){v.headWhitespace&&D.type===B.Element&&"head"===D.tagName&&(M=!1);const t={doc:n,mirror:r,blockClass:s,blockSelector:o,maskAllText:a,unblockSelector:i,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:h,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:k,inlineImages:S,recordCanvas:b,preserveWhiteSpace:M,onSerialize:_,onIframeLoad:w,iframeLoadTimeout:T,onStylesheetLoad:C,stylesheetLoadTimeout:E,keepIframeSrcFn:I};for(const n of Array.from(e.childNodes)){const e=Se(n,t);e&&D.childNodes.push(e)}if(function(e){return e.nodeType===e.ELEMENT_NODE}(e)&&e.shadowRoot)for(const n of Array.from(e.shadowRoot.childNodes)){const r=Se(n,t);r&&(U(e.shadowRoot)&&(r.isShadow=!0),D.childNodes.push(r))}}return e.parentNode&&z(e.parentNode)&&U(e.parentNode)&&(D.isShadow=!0),D.type===B.Element&&"iframe"===D.tagName&&function(e,t,n){const r=e.contentWindow;if(!r)return;let s,o=!1;try{s=r.document.readyState}catch(a){return}if("complete"!==s){const r=setTimeout((()=>{o||(t(),o=!0)}),n);return void e.addEventListener("load",(()=>{clearTimeout(r),o=!0,t()}))}const i="about:blank";if(r.location.href!==i||e.src===i||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}(e,(()=>{const t=e.contentDocument;if(t&&w){const n=Se(t,{doc:t,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:k,inlineImages:S,recordCanvas:b,preserveWhiteSpace:M,onSerialize:_,onIframeLoad:w,iframeLoadTimeout:T,onStylesheetLoad:C,stylesheetLoadTimeout:E,keepIframeSrcFn:I});n&&w(e,n)}}),T),D.type===B.Element&&"link"===D.tagName&&"stylesheet"===D.attributes.rel&&function(e,t,n){let r,s=!1;try{r=e.sheet}catch(i){return}if(r)return;const o=setTimeout((()=>{s||(t(),s=!0)}),n);e.addEventListener("load",(()=>{clearTimeout(o),s=!0,t()}))}(e,(()=>{if(C){const t=Se(e,{doc:n,mirror:r,blockClass:s,blockSelector:o,unblockSelector:i,maskAllText:a,maskTextClass:c,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:p,maskInputOptions:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:v,dataURLOptions:k,inlineImages:S,recordCanvas:b,preserveWhiteSpace:M,onSerialize:_,onIframeLoad:w,iframeLoadTimeout:T,onStylesheetLoad:C,stylesheetLoadTimeout:E,keepIframeSrcFn:I});t&&C(e,t)}}),E),D}function be(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}function _e(e,t,n=document){const r={capture:!0,passive:!0};return n.addEventListener(e,t,r),()=>n.removeEventListener(e,t,r)}const we="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let Te={map:{},getId(){return console.error(we),-1},getNode(){return console.error(we),null},removeNodeFromMap(){console.error(we)},has(){return console.error(we),!1},reset(){console.error(we)}};function Ce(e,t,n={}){let r=null,s=0;return function(...o){const i=Date.now();s||!1!==n.leading||(s=i);const a=t-(i-s),c=this;a<=0||a>t?(r&&(!function(...e){qe("clearTimeout")(...e)}(r),r=null),s=i,e.apply(c,o)):r||!1===n.trailing||(r=Xe((()=>{s=!1===n.leading?0:Date.now(),r=null,e.apply(c,o)}),a))}}function Ee(e,t,n,r,s=window){const o=s.Object.getOwnPropertyDescriptor(e,t);return s.Object.defineProperty(e,t,r?n:{set(e){Xe((()=>{n.set.call(this,e)}),0),o&&o.set&&o.set.call(this,e)}}),()=>Ee(e,t,o||{},!0)}function Ie(e,t,n){try{if(!(t in e))return()=>{};const r=e[t],s=n(r);return"function"===typeof s&&(s.prototype=s.prototype||{},Object.defineProperties(s,{__rrweb_original__:{enumerable:!1,value:r}})),e[t]=s,()=>{e[t]=r}}catch(r){return()=>{}}}"undefined"!==typeof window&&window.Proxy&&window.Reflect&&(Te=new Proxy(Te,{get(e,t,n){return"map"===t&&console.error(we),Reflect.get(e,t,n)}}));let xe=Date.now;function Me(e){const t=e.document;return{left:t.scrollingElement?t.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:be([t,"optionalAccess",e=>e.documentElement,"access",e=>e.scrollLeft])||be([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.parentElement,"optionalAccess",e=>e.scrollLeft])||be([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.scrollLeft])||0,top:t.scrollingElement?t.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:be([t,"optionalAccess",e=>e.documentElement,"access",e=>e.scrollTop])||be([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.parentElement,"optionalAccess",e=>e.scrollTop])||be([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.scrollTop])||0}}function Re(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function Ae(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function De(e){if(!e)return null;return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}function Le(e,t,n,r,s){if(!e)return!1;const o=De(e);if(!o)return!1;const i=ye(t,n);if(!s){const e=r&&o.matches(r);return i(o)&&!e}const a=fe(o,i);let c=-1;return!(a<0)&&(r&&(c=fe(o,ye(null,r))),a>-1&&c<0||a<c)}function Oe(e,t){return t.getId(e)===Z}function Ne(e,t){if(z(e))return!1;const n=t.getId(e);return!t.has(n)||(!e.parentNode||e.parentNode.nodeType!==e.DOCUMENT_NODE)&&(!e.parentNode||Ne(e.parentNode,t))}function Fe(e){return Boolean(e.changedTouches)}function Pe(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))}function Be(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function ze(e){return Boolean(be([e,"optionalAccess",e=>e.shadowRoot]))}/[1-9][0-9]{12}/.test(Date.now().toString())||(xe=()=>(new Date).getTime());class Ue{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){return(0,r.h)(this.styleIDMap.get(e),(()=>-1))}has(e){return this.styleIDMap.has(e)}add(e,t){if(this.has(e))return this.getId(e);let n;return n=void 0===t?this.id++:t,this.styleIDMap.set(e,n),this.idStyleMap.set(n,e),n}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function je(e){let t=null;return be([e,"access",e=>e.getRootNode,"optionalCall",e=>e(),"optionalAccess",e=>e.nodeType])===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(t=e.getRootNode().host),t}function He(e){const t=e.ownerDocument;if(!t)return!1;const n=function(e){let t,n=e;for(;t=je(n);)n=t;return n}(e);return t.contains(n)}function We(e){const t=e.ownerDocument;return!!t&&(t.contains(e)||He(e))}const $e={};function qe(e){const t=$e[e];if(t)return t;const n=window.document;let r=window[e];if(n&&"function"===typeof n.createElement)try{const t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);const s=t.contentWindow;s&&s[e]&&(r=s[e]),n.head.removeChild(t)}catch(s){}return $e[e]=r.bind(window)}function Xe(...e){return qe("setTimeout")(...e)}var Ke=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(Ke||{}),Ve=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(Ve||{}),Ye=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(Ye||{}),Je=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(Je||{});function Ge(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}function Qe(e){return"__ln"in e}class Ze{constructor(){this.length=0,this.head=null,this.tail=null}get(e){if(e>=this.length)throw new Error("Position outside of list range");let t=this.head;for(let n=0;n<e;n++)t=Ge([t,"optionalAccess",e=>e.next])||null;return t}addNode(e){const t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&Qe(e.previousSibling)){const n=e.previousSibling.__ln.next;t.next=n,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,n&&(n.previous=t)}else if(e.nextSibling&&Qe(e.nextSibling)&&e.nextSibling.__ln.previous){const n=e.nextSibling.__ln.previous;t.previous=n,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,n&&(n.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){const t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}const et=(e,t)=>`${e}@${t}`;class tt{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const e=[],t=new Set,n=new Ze,r=e=>{let t=e,n=Z;for(;n===Z;)t=t&&t.nextSibling,n=t&&this.mirror.getId(t);return n},s=s=>{if(!s.parentNode||!We(s))return;const o=z(s.parentNode)?this.mirror.getId(je(s)):this.mirror.getId(s.parentNode),i=r(s);if(-1===o||-1===i)return n.addNode(s);const a=Se(s,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{Pe(e,this.mirror)&&this.iframeManager.addIframe(e),Be(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),ze(s)&&this.shadowDomManager.addShadowRoot(s.shadowRoot,this.doc)},onIframeLoad:(e,t)=>{this.iframeManager.attachIframe(e,t),this.shadowDomManager.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});a&&(e.push({parentId:o,nextId:i,node:a}),t.add(a.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const a of this.movedSet)rt(this.removes,a,this.mirror)&&!this.movedSet.has(a.parentNode)||s(a);for(const a of this.addedSet)ot(this.droppedSet,a)||rt(this.removes,a,this.mirror)?ot(this.movedSet,a)?s(a):this.droppedSet.add(a):s(a);let o=null;for(;n.length;){let e=null;if(o){const t=this.mirror.getId(o.value.parentNode),n=r(o.value);-1!==t&&-1!==n&&(e=o)}if(!e){let t=n.tail;for(;t;){const n=t;if(t=t.previous,n){const t=this.mirror.getId(n.value.parentNode);if(-1===r(n.value))continue;if(-1!==t){e=n;break}{const t=n.value;if(t.parentNode&&t.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const r=t.parentNode.host;if(-1!==this.mirror.getId(r)){e=n;break}}}}}}if(!e){for(;n.head;)n.removeNode(n.head.value);break}o=e.previous,n.removeNode(e.value),s(e.value)}const i={texts:this.texts.map((e=>({id:this.mirror.getId(e.node),value:e.value}))).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),attributes:this.attributes.map((e=>{const{attributes:t}=e;if("string"===typeof t.style){const n=JSON.stringify(e.styleDiff),r=JSON.stringify(e._unchangedStyles);n.length<t.style.length&&(n+r).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),removes:this.removes,adds:e};(i.texts.length||i.attributes.length||i.removes.length||i.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(i))},this.processMutation=e=>{if(!Oe(e.target,this.mirror))switch(e.type){case"characterData":{const t=e.target.textContent;Le(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||t===e.oldValue||this.texts.push({value:ge(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&t?this.maskTextFn?this.maskTextFn(t,De(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break}case"attributes":{const n=e.target;let r=e.attributeName,s=e.target.getAttribute(r);if("value"===r){const t=Y(n),r=n.tagName;s=J(n,r,t);const o=$({maskInputOptions:this.maskInputOptions,tagName:r,type:t});s=q({isMasked:ge(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,o),element:n,value:s,maskInputFn:this.maskInputFn})}if(Le(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||s===e.oldValue)return;let o=this.attributeMap.get(e.target);if("IFRAME"===n.tagName&&"src"===r&&!this.keepIframeSrcFn(s)){if(n.contentDocument)return;r="rr_src"}if(o||(o={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(o),this.attributeMap.set(e.target,o)),"type"===r&&"INPUT"===n.tagName&&"password"===(e.oldValue||"").toLowerCase()&&n.setAttribute("data-rr-is-password","true"),!me(n.tagName,r)&&(o.attributes[r]=pe(this.doc,X(n.tagName),X(r),s,n,this.maskAttributeFn),"style"===r)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(t){this.unattachedDoc=this.doc}const r=this.unattachedDoc.createElement("span");e.oldValue&&r.setAttribute("style",e.oldValue);for(const e of Array.from(n.style)){const t=n.style.getPropertyValue(e),s=n.style.getPropertyPriority(e);t!==r.style.getPropertyValue(e)||s!==r.style.getPropertyPriority(e)?o.styleDiff[e]=""===s?t:[t,s]:o._unchangedStyles[e]=[t,s]}for(const e of Array.from(r.style))""===n.style.getPropertyValue(e)&&(o.styleDiff[e]=!1)}break}case"childList":if(Le(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;e.addedNodes.forEach((t=>this.genAdds(t,e.target))),e.removedNodes.forEach((t=>{const n=this.mirror.getId(t),r=z(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);Le(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||Oe(t,this.mirror)||!function(e,t){return-1!==t.getId(e)}(t,this.mirror)||(this.addedSet.has(t)?(nt(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===n||Ne(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[et(n,r)]?nt(this.movedSet,t):this.removes.push({parentId:r,id:n,isShadow:!(!z(e.target)||!U(e.target))||void 0})),this.mapRemoves.push(t))}))}},this.genAdds=(e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!this.addedSet.has(e)&&!this.movedSet.has(e)){if(this.mirror.hasNode(e)){if(Oe(e,this.mirror))return;this.movedSet.add(e);let n=null;t&&this.mirror.hasNode(t)&&(n=this.mirror.getId(t)),n&&-1!==n&&(this.movedMap[et(this.mirror.getId(e),n)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);Le(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(e.childNodes.forEach((e=>this.genAdds(e))),ze(e)&&e.shadowRoot.childNodes.forEach((t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)})))}}}init(e){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((t=>{this[t]=e[t]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function nt(e,t){e.delete(t),t.childNodes.forEach((t=>nt(e,t)))}function rt(e,t,n){return 0!==e.length&&st(e,t,n)}function st(e,t,n){const{parentNode:r}=t;if(!r)return!1;const s=n.getId(r);return!!e.some((e=>e.id===s))||st(e,r,n)}function ot(e,t){return 0!==e.size&&it(e,t)}function it(e,t){const{parentNode:n}=t;return!!n&&(!!e.has(n)||it(e,n))}let at;function ct(e){at=e}function lt(){at=void 0}const ut=e=>{if(!at)return e;return(...t)=>{try{return e(...t)}catch(n){if(at&&!0===at(n))return()=>{};throw n}}};function dt(e){let t,n=e[0],r=1;for(;r<e.length;){const s=e[r],o=e[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(t=n,n=o(n)):"call"!==s&&"optionalCall"!==s||(n=o(((...e)=>n.call(t,...e))),t=void 0)}return n}const ht=[];function pt(e){try{if("composedPath"in e){const t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch(t){}return e&&e.target}function mt(e,t){const n=new tt;ht.push(n),n.init(e);let r=window.MutationObserver||window.__rrMutationObserver;const s=dt([window,"optionalAccess",e=>e.Zone,"optionalAccess",e=>e.__symbol__,"optionalCall",e=>e("MutationObserver")]);s&&window[s]&&(r=window[s]);const o=new r(ut((t=>{e.onMutation&&!1===e.onMutation(t)||n.processMutations.bind(n)(t)})));return o.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),o}function ft({mouseInteractionCb:e,doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,sampling:i}){if(!1===i.mouseInteraction)return()=>{};const a=!0===i.mouseInteraction||void 0===i.mouseInteraction?{}:i.mouseInteraction,c=[];let l=null;return Object.keys(Ye).filter((e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==a[e])).forEach((i=>{let a=X(i);const u=(t=>i=>{const a=pt(i);if(Le(a,r,s,o,!0))return;let c=null,u=t;if("pointerType"in i){switch(i.pointerType){case"mouse":c=Je.Mouse;break;case"touch":c=Je.Touch;break;case"pen":c=Je.Pen}c===Je.Touch?Ye[t]===Ye.MouseDown?u="TouchStart":Ye[t]===Ye.MouseUp&&(u="TouchEnd"):Je.Pen}else Fe(i)&&(c=Je.Touch);null!==c?(l=c,(u.startsWith("Touch")&&c===Je.Touch||u.startsWith("Mouse")&&c===Je.Mouse)&&(c=null)):Ye[t]===Ye.Click&&(c=l,l=null);const d=Fe(i)?i.changedTouches[0]:i;if(!d)return;const h=n.getId(a),{clientX:p,clientY:m}=d;ut(e)({type:Ye[u],id:h,x:p,y:m,...null!==c&&{pointerType:c}})})(i);if(window.PointerEvent)switch(Ye[i]){case Ye.MouseDown:case Ye.MouseUp:a=a.replace("mouse","pointer");break;case Ye.TouchStart:case Ye.TouchEnd:return}c.push(_e(a,u,t))})),ut((()=>{c.forEach((e=>e()))}))}function yt({scrollCb:e,doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,sampling:i}){return _e("scroll",ut(Ce(ut((i=>{const a=pt(i);if(!a||Le(a,r,s,o,!0))return;const c=n.getId(a);if(a===t&&t.defaultView){const n=Me(t.defaultView);e({id:c,x:n.left,y:n.top})}else e({id:c,x:a.scrollLeft,y:a.scrollTop})})),i.scroll||100)),t)}const gt=["INPUT","TEXTAREA","SELECT"],vt=new WeakMap;function kt({inputCb:e,doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,ignoreClass:i,ignoreSelector:a,maskInputOptions:c,maskInputFn:l,sampling:u,userTriggeredOnInput:d,maskTextClass:h,unmaskTextClass:p,maskTextSelector:m,unmaskTextSelector:f}){function y(e){let n=pt(e);const u=e.isTrusted,y=n&&K(n.tagName);if("OPTION"===y&&(n=n.parentElement),!n||!y||gt.indexOf(y)<0||Le(n,r,s,o,!0))return;const v=n;if(v.classList.contains(i)||a&&v.matches(a))return;const k=Y(n);let S=J(v,y,k),b=!1;const _=$({maskInputOptions:c,tagName:y,type:k}),w=ge(n,h,m,p,f,_);"radio"!==k&&"checkbox"!==k||(b=n.checked),S=q({isMasked:w,element:n,value:S,maskInputFn:l}),g(n,d?{text:S,isChecked:b,userTriggered:u}:{text:S,isChecked:b});const T=n.name;"radio"===k&&T&&b&&t.querySelectorAll(`input[type="radio"][name="${T}"]`).forEach((e=>{if(e!==n){const t=q({isMasked:w,element:e,value:J(e,y,k),maskInputFn:l});g(e,d?{text:t,isChecked:!b,userTriggered:!1}:{text:t,isChecked:!b})}}))}function g(t,r){const s=vt.get(t);if(!s||s.text!==r.text||s.isChecked!==r.isChecked){vt.set(t,r);const s=n.getId(t);ut(e)({...r,id:s})}}const v=("last"===u.input?["change"]:["input","change"]).map((e=>_e(e,ut(y),t))),k=t.defaultView;if(!k)return()=>{v.forEach((e=>e()))};const S=k.Object.getOwnPropertyDescriptor(k.HTMLInputElement.prototype,"value"),b=[[k.HTMLInputElement.prototype,"value"],[k.HTMLInputElement.prototype,"checked"],[k.HTMLSelectElement.prototype,"value"],[k.HTMLTextAreaElement.prototype,"value"],[k.HTMLSelectElement.prototype,"selectedIndex"],[k.HTMLOptionElement.prototype,"selected"]];return S&&S.set&&v.push(...b.map((e=>Ee(e[0],e[1],{set(){ut(y)({target:this,isTrusted:!1})}},!1,k)))),ut((()=>{v.forEach((e=>e()))}))}function St(e){return function(e,t){if(Tt("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||Tt("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||Tt("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||Tt("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){const n=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(n)}else if(e.parentStyleSheet){const n=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(n)}return t}(e,[])}function bt(e,t,n){let r,s;return e?(e.ownerNode?r=t.getId(e.ownerNode):s=n.getId(e),{styleId:s,id:r}):{}}function _t({mirror:e,stylesheetManager:t},n){let r=null;r="#document"===n.nodeName?e.getId(n):e.getId(n.host);const s="#document"===n.nodeName?dt([n,"access",e=>e.defaultView,"optionalAccess",e=>e.Document]):dt([n,"access",e=>e.ownerDocument,"optionalAccess",e=>e.defaultView,"optionalAccess",e=>e.ShadowRoot]),o=dt([s,"optionalAccess",e=>e.prototype])?Object.getOwnPropertyDescriptor(dt([s,"optionalAccess",e=>e.prototype]),"adoptedStyleSheets"):void 0;return null!==r&&-1!==r&&s&&o?(Object.defineProperty(n,"adoptedStyleSheets",{configurable:o.configurable,enumerable:o.enumerable,get(){return dt([o,"access",e=>e.get,"optionalAccess",e=>e.call,"call",e=>e(this)])},set(e){const n=dt([o,"access",e=>e.set,"optionalAccess",e=>e.call,"call",t=>t(this,e)]);if(null!==r&&-1!==r)try{t.adoptStyleSheets(e,r)}catch(s){}return n}}),ut((()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:o.configurable,enumerable:o.enumerable,get:o.get,set:o.set})}))):()=>{}}function wt(e,t={}){const n=e.doc.defaultView;if(!n)return()=>{};const r=mt(e,e.doc),s=function({mousemoveCb:e,sampling:t,doc:n,mirror:r}){if(!1===t.mousemove)return()=>{};const s="number"===typeof t.mousemove?t.mousemove:50,o="number"===typeof t.mousemoveCallback?t.mousemoveCallback:500;let i,a=[];const c=Ce(ut((t=>{const n=Date.now()-i;e(a.map((e=>(e.timeOffset-=n,e))),t),a=[],i=null})),o),l=ut(Ce(ut((e=>{const t=pt(e),{clientX:n,clientY:s}=Fe(e)?e.changedTouches[0]:e;i||(i=xe()),a.push({x:n,y:s,id:r.getId(t),timeOffset:xe()-i}),c("undefined"!==typeof DragEvent&&e instanceof DragEvent?Ve.Drag:e instanceof MouseEvent?Ve.MouseMove:Ve.TouchMove)})),s,{trailing:!1})),u=[_e("mousemove",l,n),_e("touchmove",l,n),_e("drag",l,n)];return ut((()=>{u.forEach((e=>e()))}))}(e),o=ft(e),i=yt(e),a=function({viewportResizeCb:e},{win:t}){let n=-1,r=-1;return _e("resize",ut(Ce(ut((()=>{const t=Re(),s=Ae();n===t&&r===s||(e({width:Number(s),height:Number(t)}),n=t,r=s)})),200)),t)}(e,{win:n}),c=kt(e),l=function({mediaInteractionCb:e,blockClass:t,blockSelector:n,unblockSelector:r,mirror:s,sampling:o,doc:i}){const a=ut((i=>Ce(ut((o=>{const a=pt(o);if(!a||Le(a,t,n,r,!0))return;const{currentTime:c,volume:l,muted:u,playbackRate:d}=a;e({type:i,id:s.getId(a),currentTime:c,volume:l,muted:u,playbackRate:d})})),o.media||500))),c=[_e("play",a(0),i),_e("pause",a(1),i),_e("seeked",a(2),i),_e("volumechange",a(3),i),_e("ratechange",a(4),i)];return ut((()=>{c.forEach((e=>e()))}))}(e),u=function({styleSheetRuleCb:e,mirror:t,stylesheetManager:n},{win:r}){if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype)return()=>{};const s=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(s,{apply:ut(((r,s,o)=>{const[i,a]=o,{id:c,styleId:l}=bt(s,t,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&e({id:c,styleId:l,adds:[{rule:i,index:a}]}),r.apply(s,o)}))});const o=r.CSSStyleSheet.prototype.deleteRule;let i,a;r.CSSStyleSheet.prototype.deleteRule=new Proxy(o,{apply:ut(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=bt(s,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,removes:[{index:i}]}),r.apply(s,o)}))}),r.CSSStyleSheet.prototype.replace&&(i=r.CSSStyleSheet.prototype.replace,r.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:ut(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=bt(s,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,replace:i}),r.apply(s,o)}))})),r.CSSStyleSheet.prototype.replaceSync&&(a=r.CSSStyleSheet.prototype.replaceSync,r.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:ut(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=bt(s,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,replaceSync:i}),r.apply(s,o)}))}));const c={};Ct("CSSGroupingRule")?c.CSSGroupingRule=r.CSSGroupingRule:(Ct("CSSMediaRule")&&(c.CSSMediaRule=r.CSSMediaRule),Ct("CSSConditionRule")&&(c.CSSConditionRule=r.CSSConditionRule),Ct("CSSSupportsRule")&&(c.CSSSupportsRule=r.CSSSupportsRule));const l={};return Object.entries(c).forEach((([r,s])=>{l[r]={insertRule:s.prototype.insertRule,deleteRule:s.prototype.deleteRule},s.prototype.insertRule=new Proxy(l[r].insertRule,{apply:ut(((r,s,o)=>{const[i,a]=o,{id:c,styleId:l}=bt(s.parentStyleSheet,t,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&e({id:c,styleId:l,adds:[{rule:i,index:[...St(s),a||0]}]}),r.apply(s,o)}))}),s.prototype.deleteRule=new Proxy(l[r].deleteRule,{apply:ut(((r,s,o)=>{const[i]=o,{id:a,styleId:c}=bt(s.parentStyleSheet,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,removes:[{index:[...St(s),i]}]}),r.apply(s,o)}))})})),ut((()=>{r.CSSStyleSheet.prototype.insertRule=s,r.CSSStyleSheet.prototype.deleteRule=o,i&&(r.CSSStyleSheet.prototype.replace=i),a&&(r.CSSStyleSheet.prototype.replaceSync=a),Object.entries(c).forEach((([e,t])=>{t.prototype.insertRule=l[e].insertRule,t.prototype.deleteRule=l[e].deleteRule}))}))}(e,{win:n}),d=_t(e,e.doc),h=function({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:n,stylesheetManager:r},{win:s}){const o=s.CSSStyleDeclaration.prototype.setProperty;s.CSSStyleDeclaration.prototype.setProperty=new Proxy(o,{apply:ut(((s,i,a)=>{const[c,l,u]=a;if(n.has(c))return o.apply(i,[c,l,u]);const{id:d,styleId:h}=bt(dt([i,"access",e=>e.parentRule,"optionalAccess",e=>e.parentStyleSheet]),t,r.styleMirror);return(d&&-1!==d||h&&-1!==h)&&e({id:d,styleId:h,set:{property:c,value:l,priority:u},index:St(i.parentRule)}),s.apply(i,a)}))});const i=s.CSSStyleDeclaration.prototype.removeProperty;return s.CSSStyleDeclaration.prototype.removeProperty=new Proxy(i,{apply:ut(((s,o,a)=>{const[c]=a;if(n.has(c))return i.apply(o,[c]);const{id:l,styleId:u}=bt(dt([o,"access",e=>e.parentRule,"optionalAccess",e=>e.parentStyleSheet]),t,r.styleMirror);return(l&&-1!==l||u&&-1!==u)&&e({id:l,styleId:u,remove:{property:c},index:St(o.parentRule)}),s.apply(o,a)}))}),ut((()=>{s.CSSStyleDeclaration.prototype.setProperty=o,s.CSSStyleDeclaration.prototype.removeProperty=i}))}(e,{win:n}),p=e.collectFonts?function({fontCb:e,doc:t}){const n=t.defaultView;if(!n)return()=>{};const r=[],s=new WeakMap,o=n.FontFace;n.FontFace=function(e,t,n){const r=new o(e,t,n);return s.set(r,{family:e,buffer:"string"!==typeof t,descriptors:n,fontSource:"string"===typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),r};const i=Ie(t.fonts,"add",(function(t){return function(n){return Xe(ut((()=>{const t=s.get(n);t&&(e(t),s.delete(n))})),0),t.apply(this,[n])}}));return r.push((()=>{n.FontFace=o})),r.push(i),ut((()=>{r.forEach((e=>e()))}))}(e):()=>{},m=function(e){const{doc:t,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,selectionCb:i}=e;let a=!0;const c=ut((()=>{const e=t.getSelection();if(!e||a&&dt([e,"optionalAccess",e=>e.isCollapsed]))return;a=e.isCollapsed||!1;const c=[],l=e.rangeCount||0;for(let t=0;t<l;t++){const i=e.getRangeAt(t),{startContainer:a,startOffset:l,endContainer:u,endOffset:d}=i;Le(a,r,s,o,!0)||Le(u,r,s,o,!0)||c.push({start:n.getId(a),startOffset:l,end:n.getId(u),endOffset:d})}i({ranges:c})}));return c(),_e("selectionchange",c)}(e),f=function({doc:e,customElementCb:t}){const n=e.defaultView;return n&&n.customElements?Ie(n.customElements,"define",(function(e){return function(n,r,s){try{t({define:{name:n}})}catch(o){}return e.apply(this,[n,r,s])}})):()=>{}}(e),y=[];for(const g of e.plugins)y.push(g.observer(g.callback,n,g.options));return ut((()=>{ht.forEach((e=>e.reset())),r.disconnect(),s(),o(),i(),a(),c(),l(),u(),d(),h(),p(),m(),f(),y.forEach((e=>e()))}))}function Tt(e){return"undefined"!==typeof window[e]}function Ct(e){return Boolean("undefined"!==typeof window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class Et{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,t,n,r){const s=n||this.getIdToRemoteIdMap(e),o=r||this.getRemoteIdToIdMap(e);let i=s.get(t);return i||(i=this.generateIdFn(),s.set(t,i),o.set(i,t)),i}getIds(e,t){const n=this.getIdToRemoteIdMap(e),r=this.getRemoteIdToIdMap(e);return t.map((t=>this.getId(e,t,n,r)))}getRemoteId(e,t,n){const r=n||this.getRemoteIdToIdMap(e);if("number"!==typeof t)return t;const s=r.get(t);return s||-1}getRemoteIds(e,t){const n=this.getRemoteIdToIdMap(e);return t.map((t=>this.getRemoteId(e,t,n)))}reset(e){if(!e)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){let t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}class It{constructor(){this.crossOriginIframeMirror=new Et(ee),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}}class xt{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}}class Mt{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}}class Rt{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new Ue,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){if(0===e.length)return;const n={id:t,styleIds:[]},r=[];for(const s of e){let e;this.styleMirror.has(s)?e=this.styleMirror.getId(s):(e=this.styleMirror.add(s),r.push({styleId:e,rules:Array.from(s.rules||CSSRule,((e,t)=>({rule:H(e),index:t})))})),n.styleIds.push(e)}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class At{constructor(){this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}periodicallyClear(){!function(...e){qe("requestAnimationFrame")(...e)}((()=>{this.clear(),this.loop&&this.periodicallyClear()}))}inOtherBuffer(e,t){const n=this.nodeMap.get(e);return n&&Array.from(n).some((e=>e!==t))}add(e,t){this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}clear(){this.nodeMap=new WeakMap}destroy(){this.loop=!1}}let Dt,Lt;const Ot=new W;function Nt(e={}){const{emit:t,checkoutEveryNms:n,checkoutEveryNth:r,blockClass:o="rr-block",blockSelector:i=null,unblockSelector:a=null,ignoreClass:c="rr-ignore",ignoreSelector:l=null,maskAllText:u=!1,maskTextClass:d="rr-mask",unmaskTextClass:h=null,maskTextSelector:p=null,unmaskTextSelector:m=null,inlineStylesheet:f=!0,maskAllInputs:y,maskInputOptions:g,slimDOMOptions:v,maskAttributeFn:k,maskInputFn:S,maskTextFn:b,maxCanvasSize:_=null,packFn:w,sampling:T={},dataURLOptions:C={},mousemoveWait:E,recordCanvas:I=!1,recordCrossOriginIframes:x=!1,recordAfter:M=("DOMContentLoaded"===e.recordAfter?e.recordAfter:"load"),userTriggeredOnInput:R=!1,collectFonts:A=!1,inlineImages:D=!1,plugins:L,keepIframeSrcFn:O=(()=>!1),ignoreCSSAttributes:N=new Set([]),errorHandler:F,onMutation:P,getCanvasManager:B}=e;ct(F);const z=!x||window.parent===window;let U=!1;if(!z)try{window.parent.document&&(U=!1)}catch(ne){U=!0}if(z&&!t)throw new Error("emit function is required");void 0!==E&&void 0===T.mousemove&&(T.mousemove=E),Ot.reset();const j=!0===y?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==g?g:{},H=!0===v||"all"===v?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===v,headMetaDescKeywords:"all"===v}:v||{};let $;!function(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let t=e[0];if(!(0 in e))throw new TypeError("1 argument is required");do{if(this===t)return!0}while(t=t&&t.parentNode);return!1})}();let q=0;const X=e=>{for(const t of L||[])t.eventProcessor&&(e=t.eventProcessor(e));return w&&!U&&(e=w(e)),e};Dt=(e,o)=>{const i=e;if(i.timestamp=xe(),!(0,s.x)([ht,"access",e=>e[0],"optionalAccess",e=>e.isFrozen,"call",e=>e()])||i.type===Ke.FullSnapshot||i.type===Ke.IncrementalSnapshot&&i.data.source===Ve.Mutation||ht.forEach((e=>e.unfreeze())),z)(0,s.x)([t,"optionalCall",e=>e(X(i),o)]);else if(U){const e={type:"rrweb",event:X(i),origin:window.location.origin,isCheckout:o};window.parent.postMessage(e,"*")}if(i.type===Ke.FullSnapshot)$=i,q=0;else if(i.type===Ke.IncrementalSnapshot){if(i.data.source===Ve.Mutation&&i.data.isAttachIframe)return;q++;const e=r&&q>=r,t=n&&$&&i.timestamp-$.timestamp>n;(e||t)&&te(!0)}};const K=e=>{Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.Mutation,...e}})},V=e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.Scroll,...e}}),Y=e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.CanvasMutation,...e}}),J=new Rt({mutationCb:K,adoptedStyleSheetCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.AdoptedStyleSheet,...e}})}),G=new It;for(const s of L||[])s.getMirror&&s.getMirror({nodeMirror:Ot,crossOriginIframeMirror:G.crossOriginIframeMirror,crossOriginIframeStyleMirror:G.crossOriginIframeStyleMirror});const Q=new At,Z=function(e,t){try{return e?e(t):new Mt}catch(n){return console.warn("Unable to initialize CanvasManager"),new Mt}}(B,{mirror:Ot,win:window,mutationCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.CanvasMutation,...e}}),recordCanvas:I,blockClass:o,blockSelector:i,unblockSelector:a,maxCanvasSize:_,sampling:T.canvas,dataURLOptions:C,errorHandler:F}),ee=new xt,te=(e=!1)=>{Dt({type:Ke.Meta,data:{href:window.location.href,width:Ae(),height:Re()}},e),J.reset(),ee.init(),ht.forEach((e=>e.lock()));const t=function(e,t){const{mirror:n=new W,blockClass:r="rr-block",blockSelector:s=null,unblockSelector:o=null,maskAllText:i=!1,maskTextClass:a="rr-mask",unmaskTextClass:c=null,maskTextSelector:l=null,unmaskTextSelector:u=null,inlineStylesheet:d=!0,inlineImages:h=!1,recordCanvas:p=!1,maskAllInputs:m=!1,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOM:v=!1,dataURLOptions:k,preserveWhiteSpace:S,onSerialize:b,onIframeLoad:_,iframeLoadTimeout:w,onStylesheetLoad:T,stylesheetLoadTimeout:C,keepIframeSrcFn:E=(()=>!1)}=t||{};return Se(e,{doc:e,mirror:n,blockClass:r,blockSelector:s,unblockSelector:o,maskAllText:i,maskTextClass:a,unmaskTextClass:c,maskTextSelector:l,unmaskTextSelector:u,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===m?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===m?{}:m,maskAttributeFn:f,maskTextFn:y,maskInputFn:g,slimDOMOptions:!0===v||"all"===v?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===v,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===v?{}:v,dataURLOptions:k,inlineImages:h,recordCanvas:p,preserveWhiteSpace:S,onSerialize:b,onIframeLoad:_,iframeLoadTimeout:w,onStylesheetLoad:T,stylesheetLoadTimeout:C,keepIframeSrcFn:E,newlyAddedElement:!1})}(document,{mirror:Ot,blockClass:o,blockSelector:i,unblockSelector:a,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:m,inlineStylesheet:f,maskAllInputs:j,maskAttributeFn:k,maskInputFn:S,maskTextFn:b,slimDOM:H,dataURLOptions:C,recordCanvas:I,inlineImages:D,onSerialize:e=>{Pe(e,Ot)&&G.addIframe(e),Be(e,Ot)&&J.trackLinkElement(e),ze(e)&&ee.addShadowRoot(e.shadowRoot,document)},onIframeLoad:(e,t)=>{G.attachIframe(e,t),ee.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{J.attachLinkElement(e,t)},keepIframeSrcFn:O});if(!t)return console.warn("Failed to snapshot the document");Dt({type:Ke.FullSnapshot,data:{node:t,initialOffset:Me(window)}}),ht.forEach((e=>e.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&J.adoptStyleSheets(document.adoptedStyleSheets,Ot.getId(document))};Lt=te;try{const e=[],t=e=>ut(wt)({onMutation:P,mutationCb:K,mousemoveCb:(e,t)=>Dt({type:Ke.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.MouseInteraction,...e}}),scrollCb:V,viewportResizeCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.ViewportResize,...e}}),inputCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.Input,...e}}),mediaInteractionCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.MediaInteraction,...e}}),styleSheetRuleCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.StyleSheetRule,...e}}),styleDeclarationCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.StyleDeclaration,...e}}),canvasMutationCb:Y,fontCb:e=>Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.Font,...e}}),selectionCb:e=>{Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.Selection,...e}})},customElementCb:e=>{Dt({type:Ke.IncrementalSnapshot,data:{source:Ve.CustomElement,...e}})},blockClass:o,ignoreClass:c,ignoreSelector:l,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:m,maskInputOptions:j,inlineStylesheet:f,sampling:T,recordCanvas:I,inlineImages:D,userTriggeredOnInput:R,collectFonts:A,doc:e,maskAttributeFn:k,maskInputFn:S,maskTextFn:b,keepIframeSrcFn:O,blockSelector:i,unblockSelector:a,slimDOMOptions:H,dataURLOptions:C,mirror:Ot,iframeManager:G,stylesheetManager:J,shadowDomManager:ee,processedNodeManager:Q,canvasManager:Z,ignoreCSSAttributes:N,plugins:(0,s.x)([L,"optionalAccess",e=>e.filter,"call",e=>e((e=>e.observer)),"optionalAccess",e=>e.map,"call",e=>e((e=>({observer:e.observer,options:e.options,callback:t=>Dt({type:Ke.Plugin,data:{plugin:e.name,payload:t}})})))])||[]},{});G.addLoadListener((n=>{try{e.push(t(n.contentDocument))}catch(r){console.warn(r)}}));const n=()=>{te(),e.push(t(document))};return"interactive"===document.readyState||"complete"===document.readyState?n():(e.push(_e("DOMContentLoaded",(()=>{Dt({type:Ke.DomContentLoaded,data:{}}),"DOMContentLoaded"===M&&n()}))),e.push(_e("load",(()=>{Dt({type:Ke.Load,data:{}}),"load"===M&&n()}),window))),()=>{e.forEach((e=>e())),Q.destroy(),Lt=void 0,lt()}}catch(re){console.warn(re)}}Nt.mirror=Ot,Nt.takeFullSnapshot=function(e){if(!Lt)throw new Error("please take full snapshot after start recording");Lt(e)};const Ft=3;function Pt(e){return e>9999999999?e:1e3*e}function Bt(e){return e>9999999999?e/1e3:e}function zt(e,t){"sentry.transaction"!==t.category&&(["ui.click","ui.input"].includes(t.category)?e.triggerUserActivity():e.checkAndHandleExpiredSession(),e.addUpdate((()=>(e.throttledAddEvent({type:Ke.Custom,timestamp:1e3*(t.timestamp||0),data:{tag:"breadcrumb",payload:(0,m.Fv)(t,10,1e3)}}),"console"===t.category))))}const Ut="button,a";function jt(e){return e.closest(Ut)||e}function Ht(e){const t=Wt(e);return t&&t instanceof Element?jt(t):t}function Wt(e){return function(e){return"object"===typeof e&&!!e&&"target"in e}(e)?e.target:e}let $t;function qt(e){return $t||($t=[],(0,f.hl)(M,"open",(function(e){return function(...t){if($t)try{$t.forEach((e=>e()))}catch(n){}return e.apply(M,t)}}))),$t.push(e),()=>{const t=$t?$t.indexOf(e):-1;t>-1&&$t.splice(t,1)}}class Xt{constructor(e,t,n=zt){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=t.timeout/1e3,this._threshold=t.threshold/1e3,this._scollTimeout=t.scrollTimeout/1e3,this._replay=e,this._ignoreSelector=t.ignoreSelector,this._addBreadcrumbEvent=n}addListeners(){const e=qt((()=>{this._lastMutation=Vt()}));this._teardown=()=>{e(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(e,t){if(function(e,t){if(!Kt.includes(e.tagName))return!0;if("INPUT"===e.tagName&&!["submit","button"].includes(e.getAttribute("type")||""))return!0;if("A"===e.tagName&&(e.hasAttribute("download")||e.hasAttribute("target")&&"_self"!==e.getAttribute("target")))return!0;if(t&&e.matches(t))return!0;return!1}(t,this._ignoreSelector)||!function(e){return!(!e.data||"number"!==typeof e.data.nodeId||!e.timestamp)}(e))return;const n={timestamp:Bt(e.timestamp),clickBreadcrumb:e,clickCount:0,node:t};this._clicks.some((e=>e.node===n.node&&Math.abs(e.timestamp-n.timestamp)<1))||(this._clicks.push(n),1===this._clicks.length&&this._scheduleCheckClicks())}registerMutation(e=Date.now()){this._lastMutation=Bt(e)}registerScroll(e=Date.now()){this._lastScroll=Bt(e)}registerClick(e){const t=jt(e);this._handleMultiClick(t)}_handleMultiClick(e){this._getClicks(e).forEach((e=>{e.clickCount++}))}_getClicks(e){return this._clicks.filter((t=>t.node===e))}_checkClicks(){const e=[],t=Vt();this._clicks.forEach((n=>{!n.mutationAfter&&this._lastMutation&&(n.mutationAfter=n.timestamp<=this._lastMutation?this._lastMutation-n.timestamp:void 0),!n.scrollAfter&&this._lastScroll&&(n.scrollAfter=n.timestamp<=this._lastScroll?this._lastScroll-n.timestamp:void 0),n.timestamp+this._timeout<=t&&e.push(n)}));for(const n of e){const e=this._clicks.indexOf(n);e>-1&&(this._generateBreadcrumbs(n),this._clicks.splice(e,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(e){const t=this._replay,n=e.scrollAfter&&e.scrollAfter<=this._scollTimeout,r=e.mutationAfter&&e.mutationAfter<=this._threshold,s=!n&&!r,{clickCount:o,clickBreadcrumb:i}=e;if(s){const n=1e3*Math.min(e.mutationAfter||this._timeout,this._timeout),r=n<1e3*this._timeout?"mutation":"timeout",s={type:"default",message:i.message,timestamp:i.timestamp,category:"ui.slowClickDetected",data:{...i.data,url:M.location.href,route:t.getCurrentRoute(),timeAfterClickMs:n,endReason:r,clickCount:o||1}};this._addBreadcrumbEvent(t,s)}else if(o>1){const e={type:"default",message:i.message,timestamp:i.timestamp,category:"ui.multiClick",data:{...i.data,url:M.location.href,route:t.getCurrentRoute(),clickCount:o,metric:!0}};this._addBreadcrumbEvent(t,e)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=setTimeout((()=>this._checkClicks()),1e3)}}const Kt=["A","BUTTON","INPUT"];function Vt(){return Date.now()/1e3}function Yt(e,t){try{if(!function(e){return e.type===Ft}(t))return;const{source:n}=t.data;if(n===Ve.Mutation&&e.registerMutation(t.timestamp),n===Ve.Scroll&&e.registerScroll(t.timestamp),function(e){return e.data.source===Ve.MouseInteraction}(t)){const{type:n,id:r}=t.data,s=Nt.mirror.getNode(r);s instanceof HTMLElement&&n===Ye.Click&&e.registerClick(s)}}catch(n){}}function Jt(e){return{timestamp:Date.now()/1e3,type:"default",...e}}var Gt;!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(Gt||(Gt={}));const Qt=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]);function Zt(e){const t={};!e["data-sentry-component"]&&e["data-sentry-element"]&&(e["data-sentry-component"]=e["data-sentry-element"]);for(const n in e)if(Qt.has(n)){let r=n;"data-testid"!==n&&"data-test-id"!==n||(r="testId"),t[r]=e[n]}return t}const en=e=>t=>{if(!e.isEnabled())return;const n=function(e){const{target:t,message:n}=function(e){const t="click"===e.name;let n,r=null;try{r=t?Ht(e.event):Wt(e.event),n=(0,y.Rt)(r,{maxStringLength:200})||"<unknown>"}catch(s){n="<unknown>"}return{target:r,message:n}}(e);return Jt({category:`ui.${e.name}`,...tn(t,n)})}(t);if(!n)return;const r="click"===t.name,s=r?t.event:void 0;var o,i,a;!(r&&e.clickDetector&&s&&s.target)||s.altKey||s.metaKey||s.ctrlKey||s.shiftKey||(o=e.clickDetector,i=n,a=Ht(t.event),o.handleClick(i,a)),zt(e,n)};function tn(e,t){const n=Nt.mirror.getId(e),r=n&&Nt.mirror.getNode(n),s=r&&Nt.mirror.getMeta(r),o=s&&function(e){return e.type===Gt.Element}(s)?s:null;return{message:t,data:o?{nodeId:n,node:{id:n,tagName:o.tagName,textContent:Array.from(o.childNodes).map((e=>e.type===Gt.Text&&e.textContent)).filter(Boolean).map((e=>e.trim())).join(""),attributes:Zt(o.attributes)}}:{}}}function nn(e,t){if(!e.isEnabled())return;e.updateUserActivity();const n=function(e){const{metaKey:t,shiftKey:n,ctrlKey:r,altKey:s,key:o,target:i}=e;if(!i||function(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||e.isContentEditable}(i)||!o)return null;const a=t||r||s,c=1===o.length;if(!a&&c)return null;const l=(0,y.Rt)(i,{maxStringLength:200})||"<unknown>",u=tn(i,l);return Jt({category:"ui.keyDown",message:l,data:{...u.data,metaKey:t,shiftKey:n,ctrlKey:r,altKey:s,key:o}})}(t);n&&zt(e,n)}const rn={resource:function(e){const{entryType:t,initiatorType:n,name:r,responseEnd:s,startTime:o,decodedBodySize:i,encodedBodySize:a,responseStatus:c,transferSize:l}=e;if(["fetch","xmlhttprequest"].includes(n))return null;return{type:`${t}.${n}`,start:on(o),end:on(s),name:r,data:{size:l,statusCode:c,decodedBodySize:i,encodedBodySize:a}}},paint:function(e){const{duration:t,entryType:n,name:r,startTime:s}=e,o=on(s);return{type:n,name:r,start:o,end:o+t,data:void 0}},navigation:function(e){const{entryType:t,name:n,decodedBodySize:r,duration:s,domComplete:o,encodedBodySize:i,domContentLoadedEventStart:a,domContentLoadedEventEnd:c,domInteractive:l,loadEventStart:u,loadEventEnd:d,redirectCount:h,startTime:p,transferSize:m,type:f}=e;if(0===s)return null;return{type:`${t}.${f}`,start:on(p),end:on(o),name:n,data:{size:m,decodedBodySize:r,encodedBodySize:i,duration:s,domInteractive:l,domContentLoadedEventStart:a,domContentLoadedEventEnd:c,loadEventStart:u,loadEventEnd:d,domComplete:o,redirectCount:h}}}};function sn(e){return rn[e.entryType]?rn[e.entryType](e):null}function on(e){return((g.Z1||M.performance.timeOrigin)+e)/1e3}function an(e){function t(t){e.performanceEntries.includes(t)||e.performanceEntries.push(t)}function n({entries:e}){e.forEach(t)}const r=[];return["navigation","paint","resource"].forEach((e=>{r.push((0,C._j)(e,n))})),r.push((0,C.$A)((({metric:t})=>{e.replayPerformanceEntries.push(function(e){const t=e.entries,n=t[t.length-1],r=n?n.element:void 0,s=e.value,o=on(s);return{type:"largest-contentful-paint",name:"largest-contentful-paint",start:o,end:o,data:{value:s,size:s,nodeId:r?Nt.mirror.getId(r):void 0}}}(t))}))),()=>{r.forEach((e=>e()))}}const cn=!1,ln='var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,e){for(var i=new n(31),a=0;a<31;++a)i[a]=e+=1<<t[a-1];var s=new r(i[30]);for(a=1;a<30;++a)for(var o=i[a];o<i[a+1];++o)s[o]=o-i[a]<<5|a;return{b:i,r:s}},o=s(e,2),f=o.b,h=o.r;f[28]=258,h[258]=28;for(var l=s(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,a=0,s=new n(r);a<i;++a)t[a]&&++s[t[a]-1];var o,f=new n(r);for(a=1;a<r;++a)f[a]=f[a-1]+s[a-1]<<1;if(e){o=new n(1<<r);var h=15-r;for(a=0;a<i;++a)if(t[a])for(var l=a<<4|t[a],c=r-t[a],v=f[t[a]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>h]=l}else for(o=new n(i),a=0;a<i;++a)t[a]&&(o[a]=u[f[t[a]-1]++]>>15-t[a]);return o},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var w=new t(32);for(c=0;c<32;++c)w[c]=5;var p=d(g,9,0),y=d(w,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==r||r<0)&&(r=0),(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},A=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},_=function(r,e){for(var i=[],a=0;a<r.length;++a)r[a]&&i.push({s:a,f:r[a]});var s=i.length,o=i.slice();if(!s)return{t:F,l:0};if(1==s){var f=new t(i[0].s+1);return f[i[0].s]=1,{t:f,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var h=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:h.f+l.f,l:h,r:l};c!=s-1;)h=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:h.f+l.f,l:h,r:l};var d=o[0].s;for(a=1;a<s;++a)o[a].s>d&&(d=o[a].s);var g=new n(d+1),w=x(i[c-1],g,0);if(w>e){a=0;var p=0,y=w-e,m=1<<y;for(o.sort((function(t,n){return g[n.s]-g[t.s]||t.f-n.f}));a<s;++a){var b=o[a].s;if(!(g[b]>e))break;p+=m-(1<<w-g[b]),g[b]=e}for(p>>=y;p>0;){var M=o[a].s;g[M]<e?p-=1<<e-g[M]++-1:++a}for(;a>=0&&p;--a){var E=o[a].s;g[E]==e&&(--g[E],++p)}w=e}return{t:new t(g),l:w}},x=function(t,n,r){return-1==t.s?Math.max(x(t.l,n,r+1),x(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,a=t[0],s=1,o=function(t){e[i++]=t},f=1;f<=r;++f)if(t[f]==a&&f!=r)++s;else{if(!a&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(a),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(a);s=1,a=t[f]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<e;++a)t[i+a+4]=r[a];return 8*(i+4+e)},C=function(t,r,s,o,f,h,l,u,c,v,m){z(r,m++,s),++f[256];for(var b=_(f,15),M=b.t,E=b.l,x=_(h,15),C=x.t,U=x.l,F=D(M),I=F.c,S=F.n,L=D(C),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=_(q,7),H=G.t,J=G.l,K=19;K>4&&!H[a[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(f,g)+T(h,w)+l,X=T(f,M)+T(h,C)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(C,U,0),R=C;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[a[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=p,P=g,Q=y,R=w;for(B=0;B<u;++B){var rt=o[B];if(rt>255){A(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;A(r,m,Q[et]),m+=R[et],et>3&&(A(r,m,rt>>5&8191),m+=i[et])}else A(r,m,N[rt]),m+=P[rt]}return A(r,m,N[256]),m+P[256]},U=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}},L=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,a=0|r.length,s=0;s!=a;){for(var o=Math.min(s+2655,a);s<o;++s)i+=e+=r[s];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},O=function(a,s,o,f,u){if(!u&&(u={l:1},s.dictionary)){var c=s.dictionary.subarray(-32768),v=new t(c.length+a.length);v.set(c),v.set(a,c.length),a=v,u.w=c.length}return function(a,s,o,f,u,c){var v=c.z||a.length,d=new t(f+v+5*(1+Math.ceil(v/7e3))+u),g=d.subarray(f,d.length-u),w=c.l,p=7&(c.r||0);if(s){p&&(g[0]=c.r>>3);for(var y=U[s-1],M=y>>13,E=8191&y,z=(1<<o)-1,A=c.p||new n(32768),_=c.h||new n(z+1),x=Math.ceil(o/3),D=2*x,T=function(t){return(a[t]^a[t+1]<<x^a[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=_[H];if(A[J]=K,_[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!w)){p=C(a,g,0,F,I,S,O,q,G,j-G,p),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(a[j+Q]==a[j+Q-W]){for(var $=0;$<Z&&a[j+$]==a[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-A[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=A[J])&32767}if(R){F[q++]=268435456|h[Q]<<18|l[R];var it=31&h[Q],at=31&l[R];O+=e[it]+i[at],++I[257+it],++S[at],B=j+Q,++L}else F[q++]=a[j],++I[a[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=a[j],++I[a[j]];p=C(a,g,w,F,I,S,O,q,G,j-G,p),w||(c.r=7&p|g[p/8|0]<<3,p-=7,c.h=_,c.p=A,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+w;j+=65535){var st=j+65535;st>=v&&(g[p/8|0]=w,st=v),p=k(g,p+1,a.subarray(j,st))}c.i=v}return b(d,0,f+m(p)+u)}(a,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):12+s.mem,o,f,u)},j=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},q=function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&j(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}},B=function(t){return 10+(t.filename?t.filename.length+1:0)},G=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(O(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;a&&(this.b.set(n.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(a),32768),this.s.z=n.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n}();var H=function(){function t(t,n){this.c=L(),this.v=1,G.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),G.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=O(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=L();i.p(n.dictionary),j(t,2,i.d())}}(r,this.o),this.v=0),n&&j(r,r.length-4,this.c.d()),this.ondata(r,n)},t}(),J="undefined"!=typeof TextEncoder&&new TextEncoder,K="undefined"!=typeof TextDecoder&&new TextDecoder;try{K.decode(F,{stream:!0})}catch(t){}var N=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(P(t),this.d=n||!1)},t}();function P(n,r){if(r){for(var e=new t(n.length),i=0;i<n.length;++i)e[i]=n.charCodeAt(i);return e}if(J)return J.encode(n);var a=n.length,s=new t(n.length+(n.length>>1)),o=0,f=function(t){s[o++]=t};for(i=0;i<a;++i){if(o+5>s.length){var h=new t(o+8+(a-i<<1));h.set(s),s=h}var l=n.charCodeAt(i);l<128||r?f(l):l<2048?(f(192|l>>6),f(128|63&l)):l>55295&&l<57344?(f(240|(l=65536+(1047552&l)|1023&n.charCodeAt(++i))>>18),f(128|l>>12&63),f(128|l>>6&63),f(128|63&l)):(f(224|l>>12),f(128|l>>6&63),f(128|63&l))}return b(s,0,o)}function Q(t){return function(t,n){n||(n={});var r=S(),e=t.length;r.p(t);var i=O(t,n,B(n),8),a=i.length;return q(i,n),j(i,a-8,r.d()),j(i,a-4,e),i}(P(t))}const R=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(let r=0,e=t.length;r<e;r++)n+=t[r].length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new H,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new N(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},V={clear:()=>{R.clear()},addEvent:t=>R.addEvent(t),finish:()=>R.finish(),compress:t=>Q(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in V&&"function"==typeof V[n])try{const t=V[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});';function un(e,t){cn&&(v.kg.info(e),t&&hn(e))}function dn(e,t){cn&&(v.kg.info(e),t&&setTimeout((()=>{hn(e)}),0))}function hn(e){(0,o.n)({category:"console",data:{logger:"replay"},level:"info",message:e},{level:"info"})}class pn extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class mn{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(e){const t=JSON.stringify(e).length;if(this._totalSize+=t,this._totalSize>N)throw new pn;this.events.push(e)}finish(){return new Promise((e=>{const t=this.events;this.clear(),e(JSON.stringify(t))}))}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){const e=this.events.map((e=>e.timestamp)).sort()[0];return e?Pt(e):null}}class fn{constructor(e){this._worker=e,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise(((e,t)=>{this._worker.addEventListener("message",(({data:n})=>{n.success?e():t()}),{once:!0}),this._worker.addEventListener("error",(e=>{t(e)}),{once:!0})}))),this._ensureReadyPromise}destroy(){un("[Replay] Destroying compression worker"),this._worker.terminate()}postMessage(e,t){const n=this._getAndIncrementId();return new Promise(((r,s)=>{const o=({data:t})=>{const i=t;if(i.method===e&&i.id===n){if(this._worker.removeEventListener("message",o),!i.success)return cn&&v.kg.error("[Replay]",i.response),void s(new Error("Error in compression worker"));r(i.response)}};this._worker.addEventListener("message",o),this._worker.postMessage({id:n,method:e,arg:t})}))}_getAndIncrementId(){return this._id++}}class yn{constructor(e){this._worker=new fn(e),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(e){const t=Pt(e.timestamp);(!this._earliestTimestamp||t<this._earliestTimestamp)&&(this._earliestTimestamp=t);const n=JSON.stringify(e);return this._totalSize+=n.length,this._totalSize>N?Promise.reject(new pn):this._sendEventToWorker(n)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,(e=>{cn&&v.kg.warn('[Replay] Sending "clear" message to worker failed',e)}))}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(e){return this._worker.postMessage("addEvent",e)}async _finishRequest(){const e=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,e}}class gn{constructor(e){this._fallback=new mn,this._compression=new yn(e),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(e){this._used.hasCheckout=e}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(e){return this._used.addEvent(e)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(e){return void un("[Replay] Failed to load the compression worker, falling back to simple buffer")}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){const{events:e,hasCheckout:t}=this._fallback,n=[];for(const s of e)n.push(this._compression.addEvent(s));this._compression.hasCheckout=t,this._used=this._compression;try{await Promise.all(n)}catch(r){cn&&v.kg.warn("[Replay] Failed to add events when switching buffers.",r)}}}function vn({useCompression:e,workerUrl:t}){if(e&&window.Worker){const e=function(e){try{const t=e||function(){if("undefined"===typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__)return function(){const e=new Blob([ln]);return URL.createObjectURL(e)}();return""}();if(!t)return;un("[Replay] Using compression worker"+(e?` from ${e}`:""));const n=new Worker(t);return new gn(n)}catch(t){un("[Replay] Failed to create compression worker")}}(t);if(e)return e}return un("[Replay] Using simple buffer"),new mn}function kn(){try{return"sessionStorage"in M&&!!M.sessionStorage}catch(e){return!1}}function Sn(e){!function(){if(!kn())return;try{M.sessionStorage.removeItem(R)}catch(e){}}(),e.session=void 0}function bn(e){return void 0!==e&&Math.random()<e}function _n(e){const t=Date.now();return{id:e.id||(0,k.DM)(),started:e.started||t,lastActivity:e.lastActivity||t,segmentId:e.segmentId||0,sampled:e.sampled,previousSessionId:e.previousSessionId}}function wn(e){if(kn())try{M.sessionStorage.setItem(R,JSON.stringify(e))}catch(t){}}function Tn({sessionSampleRate:e,allowBuffering:t,stickySession:n=!1},{previousSessionId:r}={}){const s=function(e,t){return bn(e)?"session":!!t&&"buffer"}(e,t),o=_n({sampled:s,previousSessionId:r});return n&&wn(o),o}function Cn(e,t,n=+new Date){return null===e||void 0===t||t<0||0!==t&&e+t<=n}function En(e,{maxReplayDuration:t,sessionIdleExpire:n,targetTime:r=Date.now()}){return Cn(e.started,t,r)||Cn(e.lastActivity,n,r)}function In(e,{sessionIdleExpire:t,maxReplayDuration:n}){return!!En(e,{sessionIdleExpire:t,maxReplayDuration:n})&&("buffer"!==e.sampled||0!==e.segmentId)}function xn({traceInternals:e,sessionIdleExpire:t,maxReplayDuration:n,previousSessionId:r},s){const o=s.stickySession&&function(e){if(!kn())return null;try{const t=M.sessionStorage.getItem(R);if(!t)return null;const n=JSON.parse(t);return dn("[Replay] Loading existing session",e),_n(n)}catch(t){return null}}(e);return o?In(o,{sessionIdleExpire:t,maxReplayDuration:n})?(dn("[Replay] Session in sessionStorage is expired, creating new one..."),Tn(s,{previousSessionId:o.id})):o:(dn("[Replay] Creating new session",e),Tn(s,{previousSessionId:r}))}function Mn(e,t,n){return!!An(e,t)&&(Rn(e,t,n),!0)}async function Rn(e,t,n){if(!e.eventBuffer)return null;try{n&&"buffer"===e.recordingMode&&e.eventBuffer.clear(),n&&(e.eventBuffer.hasCheckout=!0);const r=function(e,t){try{if("function"===typeof t&&function(e){return e.type===Ke.Custom}(e))return t(e)}catch(n){return cn&&v.kg.error("[Replay] An error occured in the `beforeAddRecordingEvent` callback, skipping the event...",n),null}return e}(t,e.getOptions().beforeAddRecordingEvent);if(!r)return;return await e.eventBuffer.addEvent(r)}catch(r){const t=r&&r instanceof pn?"addEventSizeExceeded":"addEvent";cn&&v.kg.error(r),await e.stop({reason:t});const n=(0,i.s3)();n&&n.recordDroppedEvent("internal_sdk_error","replay")}}function An(e,t){if(!e.eventBuffer||e.isPaused()||!e.isEnabled())return!1;const n=Pt(t.timestamp);return!(n+e.timeouts.sessionIdlePause<Date.now())&&(!(n>e.getContext().initialTimestamp+e.getOptions().maxReplayDuration)||(un(`[Replay] Skipping event with timestamp ${n} because it is after maxReplayDuration`,e.getOptions()._experiments.traceInternals),!1))}function Dn(e){return!e.type}function Ln(e){return"transaction"===e.type}function On(e){return"feedback"===e.type}function Nn(e){return(t,n)=>{if(!e.isEnabled()||!Dn(t)&&!Ln(t))return;const r=n&&n.statusCode;!r||r<200||r>=300||(Ln(t)?function(e,t){const n=e.getContext();t.contexts&&t.contexts.trace&&t.contexts.trace.trace_id&&n.traceIds.size<100&&n.traceIds.add(t.contexts.trace.trace_id)}(e,t):function(e,t){const n=e.getContext();t.event_id&&n.errorIds.size<100&&n.errorIds.add(t.event_id);if("buffer"!==e.recordingMode||!t.tags||!t.tags.replayId)return;const{beforeErrorSampling:r}=e.getOptions();if("function"===typeof r&&!r(t))return;setTimeout((()=>{e.sendBufferedReplayOrFlush()}))}(e,t))}}function Fn(e){return t=>{e.isEnabled()&&Dn(t)&&function(e,t){const n=t.exception&&t.exception.values&&t.exception.values[0].value;if("string"!==typeof n)return;if(n.match(/reactjs\.org\/docs\/error-decoder\.html\?invariant=(418|419|422|423|425)/)||n.match(/(does not match server-rendered HTML|Hydration failed because)/i)){zt(e,Jt({category:"replay.hydrate-error"}))}}(e,t)}}function Pn(e){const t=(0,i.s3)();t&&t.on("beforeAddBreadcrumb",(t=>function(e,t){if(!e.isEnabled()||!Bn(t))return;const n=function(e){if(!Bn(e)||["fetch","xhr","sentry.event","sentry.transaction"].includes(e.category)||e.category.startsWith("ui."))return null;if("console"===e.category)return function(e){const t=e.data&&e.data.arguments;if(!Array.isArray(t)||0===t.length)return Jt(e);let n=!1;const r=t.map((e=>{if(!e)return e;if("string"===typeof e)return e.length>O?(n=!0,`${e.slice(0,O)}\u2026`):e;if("object"===typeof e)try{const t=(0,m.Fv)(e,7);return JSON.stringify(t).length>O?(n=!0,`${JSON.stringify(t,null,2).slice(0,O)}\u2026`):t}catch(t){}return e}));return Jt({...e,data:{...e.data,arguments:r,...n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(e);return Jt(e)}(t);n&&zt(e,n)}(e,t)))}function Bn(e){return!!e.category}function zn(e){return Object.assign(((t,n)=>{if(!e.isEnabled())return t;if(function(e){return"replay_event"===e.type}(t))return delete t.breadcrumbs,t;if(!Dn(t)&&!Ln(t)&&!On(t))return t;if(!e.checkAndHandleExpiredSession())return t;if(On(t))return e.flush(),t.contexts.feedback.replay_id=e.getSessionId(),function(e,t){e.triggerUserActivity(),e.addUpdate((()=>!t.timestamp||(e.throttledAddEvent({type:Ke.Custom,timestamp:1e3*t.timestamp,data:{tag:"breadcrumb",payload:{timestamp:t.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:t.event_id}}}}),!1)))}(e,t),t;if(function(e,t){return!(e.type||!e.exception||!e.exception.values||!e.exception.values.length)&&!(!t.originalException||!t.originalException.__rrweb__)}(t,n)&&!e.getOptions()._experiments.captureExceptions)return cn&&v.kg.log("[Replay] Ignoring error from rrweb internals",t),null;const r=function(e,t){return"buffer"===e.recordingMode&&t.message!==D&&!(!t.exception||t.type)&&bn(e.getOptions().errorSampleRate)}(e,t);return(r||"session"===e.recordingMode)&&(t.tags={...t.tags,replayId:e.getSessionId()}),t}),{id:"Replay"})}function Un(e,t){return t.map((({type:t,start:n,end:r,name:s,data:o})=>{const i=e.throttledAddEvent({type:Ke.Custom,timestamp:n,data:{tag:"performanceSpan",payload:{op:t,description:s,startTimestamp:n,endTimestamp:r,data:o}}});return"string"===typeof i?Promise.resolve(null):i}))}function jn(e){return t=>{if(!e.isEnabled())return;const n=function(e){const{from:t,to:n}=e,r=Date.now()/1e3;return{type:"navigation.push",start:r,end:r,name:n,data:{previous:t}}}(t);null!==n&&(e.getContext().urls.push(n.name),e.triggerUserActivity(),e.addUpdate((()=>(Un(e,[n]),!1))))}}function Hn(e,t){e.isEnabled()&&null!==t&&(function(e,t){return(!cn||!e.getOptions()._experiments.traceInternals)&&(0,a.W)(t,(0,i.s3)())}(e,t.name)||e.addUpdate((()=>(Un(e,[t]),!0))))}function Wn(e){if(!e)return;const t=new TextEncoder;try{if("string"===typeof e)return t.encode(e).length;if(e instanceof URLSearchParams)return t.encode(e.toString()).length;if(e instanceof FormData){const n=Gn(e);return t.encode(n).length}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength}catch(n){}}function $n(e){if(!e)return;const t=parseInt(e,10);return isNaN(t)?void 0:t}function qn(e){try{if("string"===typeof e)return[e];if(e instanceof URLSearchParams)return[e.toString()];if(e instanceof FormData)return[Gn(e)];if(!e)return[void 0]}catch(t){return cn&&v.kg.warn("[Replay] Failed to serialize body",e),[void 0,"BODY_PARSE_ERROR"]}return cn&&v.kg.info("[Replay] Skipping network body because of body type",e),[void 0,"UNPARSEABLE_BODY_TYPE"]}function Xn(e,t){if(!e)return{headers:{},size:void 0,_meta:{warnings:[t]}};const n={...e._meta},r=n.warnings||[];return n.warnings=[...r,t],e._meta=n,e}function Kn(e,t){if(!t)return null;const{startTimestamp:n,endTimestamp:r,url:s,method:o,statusCode:i,request:a,response:c}=t;return{type:e,start:n/1e3,end:r/1e3,name:s,data:(0,f.Jr)({method:o,statusCode:i,request:a,response:c})}}function Vn(e){return{headers:{},size:e,_meta:{warnings:["URL_SKIPPED"]}}}function Yn(e,t,n){if(!t&&0===Object.keys(e).length)return;if(!t)return{headers:e};if(!n)return{headers:e,size:t};const r={headers:e,size:t},{body:s,warnings:o}=function(e){if(!e||"string"!==typeof e)return{body:e};const t=e.length>L,n=function(e){const t=e[0],n=e[e.length-1];return"["===t&&"]"===n||"{"===t&&"}"===n}(e);if(t){const t=e.slice(0,L);return n?{body:t,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${t}\u2026`,warnings:["TEXT_TRUNCATED"]}}if(n)try{return{body:JSON.parse(e)}}catch(r){}return{body:e}}(n);return r.body=s,o&&o.length>0&&(r._meta={warnings:o}),r}function Jn(e,t){return Object.keys(e).reduce(((n,r)=>{const s=r.toLowerCase();return t.includes(s)&&e[r]&&(n[s]=e[r]),n}),{})}function Gn(e){return new URLSearchParams(e).toString()}function Qn(e,t){const n=function(e,t=M.document.baseURI){if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith(M.location.origin))return e;const n=new URL(e,t);if(n.origin!==new URL(t).origin)return e;const r=n.href;if(!e.endsWith("/")&&r.endsWith("/"))return r.slice(0,-1);return r}(e);return(0,S.U0)(n,t)}async function Zn(e,t,n){try{const r=await async function(e,t,n){const r=Date.now(),{startTimestamp:s=r,endTimestamp:o=r}=t,{url:i,method:a,status_code:c=0,request_body_size:l,response_body_size:u}=e.data,d=Qn(i,n.networkDetailAllowUrls)&&!Qn(i,n.networkDetailDenyUrls),h=d?function({networkCaptureBodies:e,networkRequestHeaders:t},n,r){const s=n?function(e,t){if(1===e.length&&"string"!==typeof e[0])return nr(e[0],t);if(2===e.length)return nr(e[1],t);return{}}(n,t):{};if(!e)return Yn(s,r,void 0);const o=er(n),[i,a]=qn(o),c=Yn(s,r,i);if(a)return Xn(c,a);return c}(n,t.input,l):Vn(l),p=await async function(e,{networkCaptureBodies:t,networkResponseHeaders:n},r,s){if(!e&&void 0!==s)return Vn(s);const o=r?tr(r.headers,n):{};if(!r||!t&&void 0!==s)return Yn(o,s,void 0);const[i,a]=await async function(e){const t=function(e){try{return e.clone()}catch(t){cn&&v.kg.warn("[Replay] Failed to clone response body",t)}}(e);if(!t)return[void 0,"BODY_PARSE_ERROR"];try{const e=await function(e){return new Promise(((t,n)=>{const r=setTimeout((()=>n(new Error("Timeout while trying to read response body"))),500);(async function(e){return await e.text()})(e).then((e=>t(e)),(e=>n(e))).finally((()=>clearTimeout(r)))}))}(t);return[e]}catch(n){return cn&&v.kg.warn("[Replay] Failed to get text body from response",n),[void 0,"BODY_PARSE_ERROR"]}}(r),c=function(e,{networkCaptureBodies:t,responseBodySize:n,captureDetails:r,headers:s}){try{const o=e&&e.length&&void 0===n?Wn(e):n;return r?Yn(s,o,t?e:void 0):Vn(o)}catch(o){return cn&&v.kg.warn("[Replay] Failed to serialize response body",o),Yn(s,n,void 0)}}(i,{networkCaptureBodies:t,responseBodySize:s,captureDetails:e,headers:o});if(a)return Xn(c,a);return c}(d,n,t.response,u);return{startTimestamp:s,endTimestamp:o,url:i,method:a,statusCode:c,request:h,response:p}}(e,t,n),s=Kn("resource.fetch",r);Hn(n.replay,s)}catch(r){cn&&v.kg.error("[Replay] Failed to capture fetch breadcrumb",r)}}function er(e=[]){if(2===e.length&&"object"===typeof e[1])return e[1].body}function tr(e,t){const n={};return t.forEach((t=>{e.get(t)&&(n[t]=e.get(t))})),n}function nr(e,t){if(!e)return{};const n=e.headers;return n?n instanceof Headers?tr(n,t):Array.isArray(n)?{}:Jn(n,t):{}}async function rr(e,t,n){try{const r=function(e,t,n){const r=Date.now(),{startTimestamp:s=r,endTimestamp:o=r,input:i,xhr:a}=t,{url:c,method:l,status_code:u=0,request_body_size:d,response_body_size:h}=e.data;if(!c)return null;if(!a||!Qn(c,n.networkDetailAllowUrls)||Qn(c,n.networkDetailDenyUrls)){return{startTimestamp:s,endTimestamp:o,url:c,method:l,statusCode:u,request:Vn(d),response:Vn(h)}}const p=a[E.xU],m=p?Jn(p.request_headers,n.networkRequestHeaders):{},f=Jn(function(e){const t=e.getAllResponseHeaders();if(!t)return{};return t.split("\r\n").reduce(((e,t)=>{const[n,r]=t.split(": ");return e[n.toLowerCase()]=r,e}),{})}(a),n.networkResponseHeaders),[y,g]=n.networkCaptureBodies?qn(i):[void 0],[k,S]=n.networkCaptureBodies?function(e){const t=[];try{return[e.responseText]}catch(n){t.push(n)}try{return function(e,t){try{if("string"===typeof e)return[e];if(e instanceof Document)return[e.body.outerHTML];if("json"===t&&e&&"object"===typeof e)return[JSON.stringify(e)];if(!e)return[void 0]}catch(n){return cn&&v.kg.warn("[Replay] Failed to serialize body",e),[void 0,"BODY_PARSE_ERROR"]}return cn&&v.kg.info("[Replay] Skipping network body because of body type",e),[void 0,"UNPARSEABLE_BODY_TYPE"]}(e.response,e.responseType)}catch(n){t.push(n)}return cn&&v.kg.warn("[Replay] Failed to get xhr response body",...t),[void 0]}(a):[void 0],b=Yn(m,d,y),_=Yn(f,h,k);return{startTimestamp:s,endTimestamp:o,url:c,method:l,statusCode:u,request:g?Xn(b,g):b,response:S?Xn(_,S):_}}(e,t,n),s=Kn("resource.xhr",r);Hn(n.replay,s)}catch(r){cn&&v.kg.error("[Replay] Failed to capture xhr breadcrumb",r)}}function sr(e,t){const{xhr:n,input:r}=t;if(!n)return;const s=Wn(r),o=n.getResponseHeader("content-length")?$n(n.getResponseHeader("content-length")):function(e,t){try{return Wn("json"===t&&e&&"object"===typeof e?JSON.stringify(e):e)}catch(n){return}}(n.response,n.responseType);void 0!==s&&(e.data.request_body_size=s),void 0!==o&&(e.data.response_body_size=o)}function or(e){const t=(0,i.s3)();try{const{networkDetailAllowUrls:n,networkDetailDenyUrls:r,networkCaptureBodies:s,networkRequestHeaders:o,networkResponseHeaders:i}=e.getOptions(),a={replay:e,networkDetailAllowUrls:n,networkDetailDenyUrls:r,networkCaptureBodies:s,networkRequestHeaders:o,networkResponseHeaders:i};t&&t.on("beforeAddBreadcrumb",((e,t)=>function(e,t,n){if(!t.data)return;try{(function(e){return"xhr"===e.category})(t)&&function(e){return e&&e.xhr}(n)&&(sr(t,n),rr(t,n,e)),function(e){return"fetch"===e.category}(t)&&function(e){return e&&e.response}(n)&&(!function(e,t){const{input:n,response:r}=t,s=Wn(n?er(n):void 0),o=r?$n(r.headers.get("content-length")):void 0;void 0!==s&&(e.data.request_body_size=s),void 0!==o&&(e.data.response_body_size=o)}(t,n),Zn(t,n,e))}catch(r){cn&&v.kg.warn("Error when enriching network breadcrumb")}}(a,e,t)))}catch(n){}}function ir(e){const{jsHeapSizeLimit:t,totalJSHeapSize:n,usedJSHeapSize:r}=e,s=Date.now()/1e3;return{type:"memory",name:"memory",start:s,end:s,data:{memory:{jsHeapSizeLimit:t,totalJSHeapSize:n,usedJSHeapSize:r}}}}function ar(e){let t=!1;return(n,r)=>{if(!e.checkAndHandleExpiredSession())return void(cn&&v.kg.warn("[Replay] Received replay event after session expired."));const s=r||!t;t=!0,e.clickDetector&&Yt(e.clickDetector,n),e.addUpdate((()=>{if("buffer"===e.recordingMode&&s&&e.setInitialState(),!Mn(e,n,s))return!0;if(!s)return!1;if(function(e,t){if(!t||!e.session||0!==e.session.segmentId)return;Mn(e,function(e){const t=e.getOptions();return{type:Ke.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:e.isRecordingCanvas(),sessionSampleRate:t.sessionSampleRate,errorSampleRate:t.errorSampleRate,useCompressionOption:t.useCompression,blockAllMedia:t.blockAllMedia,maskAllText:t.maskAllText,maskAllInputs:t.maskAllInputs,useCompression:!!e.eventBuffer&&"worker"===e.eventBuffer.type,networkDetailHasUrls:t.networkDetailAllowUrls.length>0,networkCaptureBodies:t.networkCaptureBodies,networkRequestHasHeaders:t.networkRequestHeaders.length>0,networkResponseHasHeaders:t.networkResponseHeaders.length>0}}}}(e),!1)}(e,s),e.session&&e.session.previousSessionId)return!0;if("buffer"===e.recordingMode&&e.session&&e.eventBuffer){const t=e.eventBuffer.getEarliestTimestamp();t&&(un(`[Replay] Updating session start time to earliest event in buffer to ${new Date(t)}`,e.getOptions()._experiments.traceInternals),e.session.started=t,e.getOptions().stickySession&&wn(e.session))}return"session"===e.recordingMode&&e.flush(),!0}))}}async function cr({recordingData:e,replayId:t,segmentId:n,eventContext:r,timestamp:s,session:o}){const a=function({recordingData:e,headers:t}){let n;const r=`${JSON.stringify(t)}\n`;if("string"===typeof e)n=`${r}${e}`;else{const t=(new TextEncoder).encode(r);n=new Uint8Array(t.length+e.length),n.set(t),n.set(e,t.length)}return n}({recordingData:e,headers:{segment_id:n}}),{urls:c,errorIds:u,traceIds:d,initialTimestamp:h}=r,p=(0,i.s3)(),m=(0,i.nZ)(),f=p&&p.getTransport(),y=p&&p.getDsn();if(!p||!f||!y||!o.sampled)return(0,_.WD)({});const g={type:A,replay_start_timestamp:h/1e3,timestamp:s/1e3,error_ids:u,trace_ids:d,urls:c,replay_id:t,segment_id:n,replay_type:o.sampled},v=await async function({client:e,scope:t,replayId:n,event:r}){const s={event_id:n,integrations:"object"!==typeof e._integrations||null===e._integrations||Array.isArray(e._integrations)?void 0:Object.keys(e._integrations)};e.emit("preprocessEvent",r,s);const o=await(0,l.R)(e.getOptions(),r,s,t,e,(0,i.aF)());if(!o)return null;o.platform=o.platform||"javascript";const a=e.getSdkMetadata(),{name:c,version:u}=a&&a.sdk||{};return o.sdk={...o.sdk,name:c||"sentry.javascript.unknown",version:u||"0.0.0"},o}({scope:m,client:p,replayId:t,event:g});if(!v)return p.recordDroppedEvent("event_processor","replay",g),un("An event processor returned `null`, will not send event."),(0,_.WD)({});delete v.sdkProcessingMetadata;const k=function(e,t,n,r){return(0,b.Jd)((0,b.Cd)(e,(0,b.HY)(e),r,n),[[{type:"replay_event"},e],[{type:"replay_recording",length:"string"===typeof t?(new TextEncoder).encode(t).length:t.length},t]])}(v,a,y,p.getOptions().tunnel);let S;try{S=await f.send(k)}catch(C){const e=new Error(D);try{e.cause=C}catch(E){}throw e}if("number"===typeof S.statusCode&&(S.statusCode<200||S.statusCode>=300))throw new lr(S.statusCode);const T=(0,w.WG)({},S);if((0,w.Q)(T,"replay"))throw new ur(T);return S}class lr extends Error{constructor(e){super(`Transport returned status code ${e}`)}}class ur extends Error{constructor(e){super("Rate limit hit"),this.rateLimits=e}}async function dr(e,t={count:0,interval:5e3}){const{recordingData:n,options:r}=e;if(n.length)try{return await cr(e),!0}catch(s){if(s instanceof lr||s instanceof ur)throw s;if((0,c.v)("Replays",{_retryCount:t.count}),cn&&r._experiments&&r._experiments.captureExceptions&&(0,c.Tb)(s),t.count>=3){const e=new Error(`${D} - max retries exceeded`);try{e.cause=s}catch(o){}throw e}return t.interval*=++t.count,new Promise(((n,r)=>{setTimeout((async()=>{try{await dr(e,t),n(!0)}catch(s){r(s)}}),t.interval)}))}}const hr="__THROTTLED";function pr(e,t,n){const r=new Map;let s=!1;return(...o)=>{const i=Math.floor(Date.now()/1e3);if((e=>{const t=e-n;r.forEach(((e,n)=>{n<t&&r.delete(n)}))})(i),[...r.values()].reduce(((e,t)=>e+t),0)>=t){const e=s;return s=!0,e?"__SKIPPED":hr}s=!1;const a=r.get(i)||0;return r.set(i,a+1),e(...o)}}class mr{constructor({options:e,recordingOptions:t}){mr.prototype.__init.call(this),mr.prototype.__init2.call(this),mr.prototype.__init3.call(this),mr.prototype.__init4.call(this),mr.prototype.__init5.call(this),mr.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=t,this._options=e,this._debouncedFlush=function(e,t,n){let r,s,o;const i=n&&n.maxWait?Math.max(n.maxWait,t):0;function a(){return c(),r=e(),r}function c(){void 0!==s&&clearTimeout(s),void 0!==o&&clearTimeout(o),s=o=void 0}function l(){return s&&clearTimeout(s),s=setTimeout(a,t),i&&void 0===o&&(o=setTimeout(a,i)),r}return l.cancel=c,l.flush=function(){return void 0!==s||void 0!==o?a():r},l}((()=>this._flush()),this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=pr(((e,t)=>function(e,t,n){return An(e,t)?Rn(e,t,n):Promise.resolve(null)}(this,e,t)),300,5);const{slowClickTimeout:n,slowClickIgnoreSelectors:r}=this.getOptions(),s=n?{threshold:Math.min(3e3,n),timeout:n,scrollTimeout:300,ignoreSelector:r?r.join(","):""}:void 0;s&&(this.clickDetector=new Xt(this,s))}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return Boolean(this._canvas)}getOptions(){return this._options}initializeSampling(e){const{errorSampleRate:t,sessionSampleRate:n}=this._options;t<=0&&n<=0||(this._initializeSessionForSampling(e),this.session?!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",dn(`[Replay] Starting replay in ${this.recordingMode} mode`,this._options._experiments.traceInternals),this._initializeRecording()):this._handleException(new Error("Unable to initialize and create session")))}start(){if(this._isEnabled&&"session"===this.recordingMode)throw new Error("Replay recording is already in progress");if(this._isEnabled&&"buffer"===this.recordingMode)throw new Error("Replay buffering is in progress, call `flush()` to save the replay");dn("[Replay] Starting replay in session mode",this._options._experiments.traceInternals),this._updateUserActivity();const e=xn({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this._initializeRecording()}startBuffering(){if(this._isEnabled)throw new Error("Replay recording is already in progress");dn("[Replay] Starting replay in buffer mode",this._options._experiments.traceInternals);const e=xn({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{const e=this._canvas;this._stopRecording=Nt({...this._recordingOptions,..."buffer"===this.recordingMode&&{checkoutEveryNms:6e4},emit:ar(this),onMutation:this._onMutationHandler,...e?{recordCanvas:e.recordCanvas,getCanvasManager:e.getCanvasManager,sampling:e.sampling,dataURLOptions:e.dataURLOptions}:{}})}catch(e){this._handleException(e)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this._handleException(e),!1}}async stop({forceFlush:e=!1,reason:t}={}){if(this._isEnabled){this._isEnabled=!1;try{un("[Replay] Stopping Replay"+(t?` triggered by ${t}`:""),this._options._experiments.traceInternals),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),e&&await this._flush({force:!0}),this.eventBuffer&&this.eventBuffer.destroy(),this.eventBuffer=null,Sn(this)}catch(n){this._handleException(n)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording(),un("[Replay] Pausing replay",this._options._experiments.traceInternals))}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording(),un("[Replay] Resuming replay",this._options._experiments.traceInternals))}async sendBufferedReplayOrFlush({continueRecording:e=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();const t=Date.now();un("[Replay] Converting buffer to session",this._options._experiments.traceInternals),await this.flushImmediate();const n=this.stopRecording();e&&n&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(t),this._updateSessionActivity(t),this._maybeSaveSession()),this.startRecording())}addUpdate(e){const t=e();"buffer"!==this.recordingMode&&!0!==t&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),this._stopRecording)this.checkAndHandleExpiredSession(),this._updateSessionActivity();else{if(!this._checkSession())return;this.resume()}}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session&&this.session.id}checkAndHandleExpiredSession(){if(!(this._lastActivity&&Cn(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled))return!!this._checkSession();this.pause()}setInitialState(){const e=`${M.location.pathname}${M.location.hash}${M.location.search}`,t=`${M.location.origin}${e}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=t,this._context.initialTimestamp=Date.now(),this._context.urls.push(t)}throttledAddEvent(e,t){const n=this._throttledAddEvent(e,t);if(n===hr){const e=Jt({category:"replay.throttled"});this.addUpdate((()=>!Mn(this,{type:5,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e,metric:!0}})))}return n}getCurrentRoute(){const e=this.lastActiveSpan||(0,u.HN)(),t=e&&(0,u.Gx)(e),n=(t&&(0,u.XU)(t).data||{})[d.Zj];if(t&&n&&["route","custom"].includes(n))return(0,u.XU)(t).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=vn({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_handleException(e){cn&&v.kg.error("[Replay]",e),cn&&this._options._experiments&&this._options._experiments.captureExceptions&&(0,c.Tb)(e)}_initializeSessionForSampling(e){const t=this._options.errorSampleRate>0,n=xn({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:t});this.session=n}_checkSession(){if(!this.session)return!1;const e=this.session;return!In(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(e),!1)}async _refreshSession(e){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(e.id))}_addListeners(){try{M.document.addEventListener("visibilitychange",this._handleVisibilityChange),M.addEventListener("blur",this._handleWindowBlur),M.addEventListener("focus",this._handleWindowFocus),M.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(!function(e){const t=(0,i.s3)();(0,I.O)(en(e)),(0,x.a)(jn(e)),Pn(e),or(e);const n=zn(e);(0,c.Qy)(n),t&&(t.on("beforeSendEvent",Fn(e)),t.on("afterSendEvent",Nn(e)),t.on("createDsc",(t=>{const n=e.getSessionId();n&&e.isEnabled()&&"session"===e.recordingMode&&e.checkAndHandleExpiredSession()&&(t.replay_id=n)})),t.on("spanStart",(t=>{e.lastActiveSpan=t})),t.on("spanEnd",(t=>{e.lastActiveSpan=t})),t.on("beforeSendFeedback",((t,n)=>{const r=e.getSessionId();n&&n.includeReplay&&e.isEnabled()&&r&&t.contexts&&t.contexts.feedback&&(t.contexts.feedback.replay_id=r)})))}(this),this._hasInitializedCoreListeners=!0)}catch(e){this._handleException(e)}this._performanceCleanupCallback=an(this)}_removeListeners(){try{M.document.removeEventListener("visibilitychange",this._handleVisibilityChange),M.removeEventListener("blur",this._handleWindowBlur),M.removeEventListener("focus",this._handleWindowFocus),M.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this._handleException(e)}}__init(){this._handleVisibilityChange=()=>{"visible"===M.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()}}__init2(){this._handleWindowBlur=()=>{const e=Jt({category:"ui.blur"});this._doChangeToBackgroundTasks(e)}}__init3(){this._handleWindowFocus=()=>{const e=Jt({category:"ui.focus"});this._doChangeToForegroundTasks(e)}}__init4(){this._handleKeyboardEvent=e=>{nn(this,e)}}_doChangeToBackgroundTasks(e){if(!this.session)return;En(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush())}_doChangeToForegroundTasks(e){if(!this.session)return;this.checkAndHandleExpiredSession()?e&&this._createCustomBreadcrumb(e):un("[Replay] Document has become active, but session has expired")}_updateUserActivity(e=Date.now()){this._lastActivity=e}_updateSessionActivity(e=Date.now()){this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}_createCustomBreadcrumb(e){this.addUpdate((()=>{this.throttledAddEvent({type:Ke.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})}))}_addPerformanceEntries(){const e=(t=this.performanceEntries,t.map(sn).filter(Boolean)).concat(this.replayPerformanceEntries);var t;return this.performanceEntries=[],this.replayPerformanceEntries=[],Promise.all(Un(this,e))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){const{session:e,eventBuffer:t}=this;if(!e||!t)return;if(e.segmentId)return;const n=t.getEarliestTimestamp();n&&n<this._context.initialTimestamp&&(this._context.initialTimestamp=n)}_popEventContext(){const e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}async _runFlush(){const e=this.getSessionId();if(this.session&&this.eventBuffer&&e){if(await this._addPerformanceEntries(),this.eventBuffer&&this.eventBuffer.hasEvents&&(await async function(e){try{return Promise.all(Un(e,[ir(M.performance.memory)]))}catch(t){return[]}}(this),this.eventBuffer&&e===this.getSessionId()))try{this._updateInitialTimestampFromEventBuffer();const t=Date.now();if(t-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw new Error("Session is too long, not sending replay");const n=this._popEventContext(),r=this.session.segmentId++;this._maybeSaveSession();const s=await this.eventBuffer.finish();await dr({replayId:e,recordingData:s,segmentId:r,eventContext:n,session:this.session,options:this.getOptions(),timestamp:t})}catch(t){this._handleException(t),this.stop({reason:"sendReplay"});const e=(0,i.s3)();e&&e.recordDroppedEvent("send_error","replay")}}else cn&&v.kg.error("[Replay] No session or eventBuffer found to flush.")}__init5(){this._flush=async({force:e=!1}={})=>{if(!this._isEnabled&&!e)return;if(!this.checkAndHandleExpiredSession())return void(cn&&v.kg.error("[Replay] Attempting to finish replay event after session expired."));if(!this.session)return;const t=this.session.started,n=Date.now()-t;this._debouncedFlush.cancel();const r=n<this._options.minReplayDuration,s=n>this._options.maxReplayDuration+5e3;if(r||s)return un(`[Replay] Session duration (${Math.floor(n/1e3)}s) is too ${r?"short":"long"}, not sending replay.`,this._options._experiments.traceInternals),void(r&&this._debouncedFlush());const o=this.eventBuffer;if(o&&0===this.session.segmentId&&!o.hasCheckout&&un("[Replay] Flushing initial segment without checkout.",this._options._experiments.traceInternals),!this._flushLock)return this._flushLock=this._runFlush(),await this._flushLock,void(this._flushLock=void 0);try{await this._flushLock}catch(i){cn&&v.kg.error(i)}finally{this._debouncedFlush()}}}_maybeSaveSession(){this.session&&this._options.stickySession&&wn(this.session)}__init6(){this._onMutationHandler=e=>{const t=e.length,n=this._options.mutationLimit,r=n&&t>n;if(t>this._options.mutationBreadcrumbLimit||r){const e=Jt({category:"replay.mutations",data:{count:t,limit:r}});this._createCustomBreadcrumb(e)}return!r||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}}function fr(e,t){return[...e,...t].join(",")}const yr='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',gr=["content-length","content-type","accept"];let vr=!1;const kr=e=>new Sr(e);class Sr{static __initStatic(){this.id="Replay"}constructor({flushMinDelay:e=5e3,flushMaxDelay:t=5500,minReplayDuration:n=4999,maxReplayDuration:r=36e5,stickySession:s=!0,useCompression:o=!0,workerUrl:i,_experiments:a={},maskAllText:c=!0,maskAllInputs:l=!0,blockAllMedia:u=!0,mutationBreadcrumbLimit:d=750,mutationLimit:h=1e4,slowClickTimeout:p=7e3,slowClickIgnoreSelectors:m=[],networkDetailAllowUrls:f=[],networkDetailDenyUrls:y=[],networkCaptureBodies:g=!0,networkRequestHeaders:v=[],networkResponseHeaders:k=[],mask:S=[],maskAttributes:b=["title","placeholder"],unmask:_=[],block:w=[],unblock:C=[],ignore:E=[],maskFn:I,beforeAddRecordingEvent:x,beforeErrorSampling:M}={}){this.name=Sr.id;const R=function({mask:e,unmask:t,block:n,unblock:r,ignore:s}){return{maskTextSelector:fr(e,[".sentry-mask","[data-sentry-mask]"]),unmaskTextSelector:fr(t,[]),blockSelector:fr(n,[".sentry-block","[data-sentry-block]",'base[href="/"]']),unblockSelector:fr(r,[]),ignoreSelector:fr(s,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'])}}({mask:S,unmask:_,block:w,unblock:C,ignore:E});if(this._recordingOptions={maskAllInputs:l,maskAllText:c,maskInputOptions:{password:!0},maskTextFn:I,maskInputFn:I,maskAttributeFn:(e,t,n)=>function({el:e,key:t,maskAttributes:n,maskAllText:r,privacyOptions:s,value:o}){return r?s.unmaskTextSelector&&e.matches(s.unmaskTextSelector)?o:n.includes(t)||"value"===t&&"INPUT"===e.tagName&&["submit","button"].includes(e.getAttribute("type")||"")?o.replace(/[\S]/g,"*"):o:o}({maskAttributes:b,maskAllText:c,privacyOptions:R,key:e,value:t,el:n}),...R,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:e=>{try{e.__rrweb__=!0}catch(t){}}},this._initialOptions={flushMinDelay:e,flushMaxDelay:t,minReplayDuration:Math.min(n,15e3),maxReplayDuration:Math.min(r,F),stickySession:s,useCompression:o,workerUrl:i,blockAllMedia:u,maskAllInputs:l,maskAllText:c,mutationBreadcrumbLimit:d,mutationLimit:h,slowClickTimeout:p,slowClickIgnoreSelectors:m,networkDetailAllowUrls:f,networkDetailDenyUrls:y,networkCaptureBodies:g,networkRequestHeaders:br(v),networkResponseHeaders:br(k),beforeAddRecordingEvent:x,beforeErrorSampling:M,_experiments:a},this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${yr}`:yr),this._isInitialized&&(0,T.j)())throw new Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return vr}set _isInitialized(e){vr=e}setupOnce(){(0,T.j)()&&(this._setup(),setTimeout((()=>this._initialize())))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(e){return this._replay&&this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(e):Promise.resolve()}getReplayId(){if(this._replay&&this._replay.isEnabled())return this._replay.getSessionId()}_initialize(){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(),this._replay.initializeSampling())}_setup(){const e=function(e){const t=(0,i.s3)(),n=t&&t.getOptions(),r={sessionSampleRate:0,errorSampleRate:0,...(0,f.Jr)(e)};if(!n)return(0,v.Cf)((()=>{console.warn("SDK client is not available.")})),r;const s=(0,h.o)(n.replaysSessionSampleRate),o=(0,h.o)(n.replaysOnErrorSampleRate);null==s&&null==o&&(0,v.Cf)((()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}));null!=s&&(r.sessionSampleRate=s);null!=o&&(r.errorSampleRate=o);return r}(this._initialOptions);this._replay=new mr({options:e,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(){try{const e=(0,i.s3)().getIntegrationByName("ReplayCanvas");if(!e)return;this._replay._canvas=e.getOptions()}catch(e){}}}function br(e){return[...gr,...e.map((e=>e.toLowerCase()))]}Sr.__initStatic()}}]);
//# sourceMappingURL=@sentry-internal.369c50a8a41e22a0f20917ddbad2ebf0.js.map