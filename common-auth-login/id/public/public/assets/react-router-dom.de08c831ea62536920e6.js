"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[109],{7341:function(n,t,e){e.d(t,{VK:function(){return R},rU:function(){return K}});var r=e(2719),o=e(1498),a=e(7363),i=e.n(a),c=e(7692),u=e(9337),f=e(8109);function s(n){return"/"===n.charAt(0)?n:"/"+n}function l(n){return"/"===n.charAt(0)?n.substr(1):n}function h(n,t){return function(n,t){return 0===n.toLowerCase().indexOf(t.toLowerCase())&&-1!=="/?#".indexOf(n.charAt(t.length))}(n,t)?n.substr(t.length):n}function d(n){return"/"===n.charAt(n.length-1)?n.slice(0,-1):n}function v(n){var t=n.pathname,e=n.search,r=n.hash,o=t||"/";return e&&"?"!==e&&(o+="?"===e.charAt(0)?e:"?"+e),r&&"#"!==r&&(o+="#"===r.charAt(0)?r:"#"+r),o}function p(n,t,e,r){var o;"string"===typeof n?(o=function(n){var t=n||"/",e="",r="",o=t.indexOf("#");-1!==o&&(r=t.substr(o),t=t.substr(0,o));var a=t.indexOf("?");return-1!==a&&(e=t.substr(a),t=t.substr(0,a)),{pathname:t,search:"?"===e?"":e,hash:"#"===r?"":r}}(n),o.state=t):(void 0===(o=(0,c.Z)({},n)).pathname&&(o.pathname=""),o.search?"?"!==o.search.charAt(0)&&(o.search="?"+o.search):o.search="",o.hash?"#"!==o.hash.charAt(0)&&(o.hash="#"+o.hash):o.hash="",void 0!==t&&void 0===o.state&&(o.state=t));try{o.pathname=decodeURI(o.pathname)}catch(a){throw a instanceof URIError?new URIError('Pathname "'+o.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):a}return e&&(o.key=e),r?o.pathname?"/"!==o.pathname.charAt(0)&&(o.pathname=(0,u.Z)(o.pathname,r.pathname)):o.pathname=r.pathname:o.pathname||(o.pathname="/"),o}function w(){var n=null;var t=[];return{setPrompt:function(t){return n=t,function(){n===t&&(n=null)}},confirmTransitionTo:function(t,e,r,o){if(null!=n){var a="function"===typeof n?n(t,e):n;"string"===typeof a?"function"===typeof r?r(a,o):o(!0):o(!1!==a)}else o(!0)},appendListener:function(n){var e=!0;function r(){e&&n.apply(void 0,arguments)}return t.push(r),function(){e=!1,t=t.filter((function(n){return n!==r}))}},notifyListeners:function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];t.forEach((function(n){return n.apply(void 0,e)}))}}}var m=!("undefined"===typeof window||!window.document||!window.document.createElement);function y(n,t){t(window.confirm(n))}var g="popstate",O="hashchange";function P(){try{return window.history.state||{}}catch(n){return{}}}function k(n){void 0===n&&(n={}),m||(0,f.Z)(!1);var t=window.history,e=function(){var n=window.navigator.userAgent;return(-1===n.indexOf("Android 2.")&&-1===n.indexOf("Android 4.0")||-1===n.indexOf("Mobile Safari")||-1!==n.indexOf("Chrome")||-1!==n.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history}(),r=!(-1===window.navigator.userAgent.indexOf("Trident")),o=n,a=o.forceRefresh,i=void 0!==a&&a,u=o.getUserConfirmation,l=void 0===u?y:u,k=o.keyLength,x=void 0===k?6:k,A=n.basename?d(s(n.basename)):"";function b(n){var t=n||{},e=t.key,r=t.state,o=window.location,a=o.pathname+o.search+o.hash;return A&&(a=h(a,A)),p(a,r,e)}function E(){return Math.random().toString(36).substr(2,x)}var C=w();function L(n){(0,c.Z)(B,n),B.length=t.length,C.notifyListeners(B.location,B.action)}function T(n){(function(n){return void 0===n.state&&-1===navigator.userAgent.indexOf("CriOS")})(n)||S(b(n.state))}function R(){S(b(P()))}var Z=!1;function S(n){if(Z)Z=!1,L();else{C.confirmTransitionTo(n,"POP",l,(function(t){t?L({action:"POP",location:n}):function(n){var t=B.location,e=I.indexOf(t.key);-1===e&&(e=0);var r=I.indexOf(n.key);-1===r&&(r=0);var o=e-r;o&&(Z=!0,K(o))}(n)}))}}var U=b(P()),I=[U.key];function H(n){return A+v(n)}function K(n){t.go(n)}var N=0;function _(n){1===(N+=n)&&1===n?(window.addEventListener(g,T),r&&window.addEventListener(O,R)):0===N&&(window.removeEventListener(g,T),r&&window.removeEventListener(O,R))}var F=!1;var B={length:t.length,action:"POP",location:U,createHref:H,push:function(n,r){var o="PUSH",a=p(n,r,E(),B.location);C.confirmTransitionTo(a,o,l,(function(n){if(n){var r=H(a),c=a.key,u=a.state;if(e)if(t.pushState({key:c,state:u},null,r),i)window.location.href=r;else{var f=I.indexOf(B.location.key),s=I.slice(0,f+1);s.push(a.key),I=s,L({action:o,location:a})}else window.location.href=r}}))},replace:function(n,r){var o="REPLACE",a=p(n,r,E(),B.location);C.confirmTransitionTo(a,o,l,(function(n){if(n){var r=H(a),c=a.key,u=a.state;if(e)if(t.replaceState({key:c,state:u},null,r),i)window.location.replace(r);else{var f=I.indexOf(B.location.key);-1!==f&&(I[f]=a.key),L({action:o,location:a})}else window.location.replace(r)}}))},go:K,goBack:function(){K(-1)},goForward:function(){K(1)},block:function(n){void 0===n&&(n=!1);var t=C.setPrompt(n);return F||(_(1),F=!0),function(){return F&&(F=!1,_(-1)),t()}},listen:function(n){var t=C.appendListener(n);return _(1),function(){_(-1),t()}}};return B}var x="hashchange",A={hashbang:{encodePath:function(n){return"!"===n.charAt(0)?n:"!/"+l(n)},decodePath:function(n){return"!"===n.charAt(0)?n.substr(1):n}},noslash:{encodePath:l,decodePath:s},slash:{encodePath:s,decodePath:s}};function b(n){var t=n.indexOf("#");return-1===t?n:n.slice(0,t)}function E(){var n=window.location.href,t=n.indexOf("#");return-1===t?"":n.substring(t+1)}function C(n){window.location.replace(b(window.location.href)+"#"+n)}function L(n){void 0===n&&(n={}),m||(0,f.Z)(!1);var t=window.history,e=(window.navigator.userAgent.indexOf("Firefox"),n),r=e.getUserConfirmation,o=void 0===r?y:r,a=e.hashType,i=void 0===a?"slash":a,u=n.basename?d(s(n.basename)):"",l=A[i],g=l.encodePath,O=l.decodePath;function P(){var n=O(E());return u&&(n=h(n,u)),p(n)}var k=w();function L(n){(0,c.Z)(B,n),B.length=t.length,k.notifyListeners(B.location,B.action)}var T=!1,R=null;function Z(){var n,t,e=E(),r=g(e);if(e!==r)C(r);else{var a=P(),i=B.location;if(!T&&(t=a,(n=i).pathname===t.pathname&&n.search===t.search&&n.hash===t.hash))return;if(R===v(a))return;R=null,function(n){if(T)T=!1,L();else{var t="POP";k.confirmTransitionTo(n,t,o,(function(e){e?L({action:t,location:n}):function(n){var t=B.location,e=H.lastIndexOf(v(t));-1===e&&(e=0);var r=H.lastIndexOf(v(n));-1===r&&(r=0);var o=e-r;o&&(T=!0,K(o))}(n)}))}}(a)}}var S=E(),U=g(S);S!==U&&C(U);var I=P(),H=[v(I)];function K(n){t.go(n)}var N=0;function _(n){1===(N+=n)&&1===n?window.addEventListener(x,Z):0===N&&window.removeEventListener(x,Z)}var F=!1;var B={length:t.length,action:"POP",location:I,createHref:function(n){var t=document.querySelector("base"),e="";return t&&t.getAttribute("href")&&(e=b(window.location.href)),e+"#"+g(u+v(n))},push:function(n,t){var e="PUSH",r=p(n,void 0,void 0,B.location);k.confirmTransitionTo(r,e,o,(function(n){if(n){var t=v(r),o=g(u+t);if(E()!==o){R=t,function(n){window.location.hash=n}(o);var a=H.lastIndexOf(v(B.location)),i=H.slice(0,a+1);i.push(t),H=i,L({action:e,location:r})}else L()}}))},replace:function(n,t){var e="REPLACE",r=p(n,void 0,void 0,B.location);k.confirmTransitionTo(r,e,o,(function(n){if(n){var t=v(r),o=g(u+t);E()!==o&&(R=t,C(o));var a=H.indexOf(v(B.location));-1!==a&&(H[a]=t),L({action:e,location:r})}}))},go:K,goBack:function(){K(-1)},goForward:function(){K(1)},block:function(n){void 0===n&&(n=!1);var t=k.setPrompt(n);return F||(_(1),F=!0),function(){return F&&(F=!1,_(-1)),t()}},listen:function(n){var t=k.appendListener(n);return _(1),function(){_(-1),t()}}};return B}var T=e(1972),R=function(n){function t(){for(var t,e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return(t=n.call.apply(n,[this].concat(r))||this).history=k(t.props),t}return(0,o.Z)(t,n),t.prototype.render=function(){return i().createElement(r.F0,{history:this.history,children:this.props.children})},t}(i().Component);i().Component;var Z=function(n,t){return"function"===typeof n?n(t):n},S=function(n,t){return"string"===typeof n?p(n,null,null,t):n},U=function(n){return n},I=i().forwardRef;"undefined"===typeof I&&(I=U);var H=I((function(n,t){var e=n.innerRef,r=n.navigate,o=n.onClick,a=(0,T.Z)(n,["innerRef","navigate","onClick"]),u=a.target,f=(0,c.Z)({},a,{onClick:function(n){try{o&&o(n)}catch(t){throw n.preventDefault(),t}n.defaultPrevented||0!==n.button||u&&"_self"!==u||function(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}(n)||(n.preventDefault(),r())}});return f.ref=U!==I&&t||e,i().createElement("a",f)}));var K=I((function(n,t){var e=n.component,o=void 0===e?H:e,a=n.replace,u=n.to,s=n.innerRef,l=(0,T.Z)(n,["component","replace","to","innerRef"]);return i().createElement(r.s6.Consumer,null,(function(n){n||(0,f.Z)(!1);var e=n.history,r=S(Z(u,n.location),n.location),h=r?e.createHref(r):"",d=(0,c.Z)({},l,{href:h,navigate:function(){var t=Z(u,n.location);(a?e.replace:e.push)(t)}});return U!==I?d.ref=t||s:d.innerRef=s,i().createElement(o,d)}))})),N=function(n){return n},_=i().forwardRef;"undefined"===typeof _&&(_=N);_((function(n,t){var e=n["aria-current"],o=void 0===e?"page":e,a=n.activeClassName,u=void 0===a?"active":a,s=n.activeStyle,l=n.className,h=n.exact,d=n.isActive,v=n.location,p=n.strict,w=n.style,m=n.to,y=n.innerRef,g=(0,T.Z)(n,["aria-current","activeClassName","activeStyle","className","exact","isActive","location","strict","style","to","innerRef"]);return i().createElement(r.s6.Consumer,null,(function(n){n||(0,f.Z)(!1);var e=v||n.location,a=S(Z(m,e),e),O=a.pathname,P=O&&O.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1"),k=P?(0,r.LX)(e.pathname,{path:P,exact:h,strict:p}):null,x=!!(d?d(k,e):k),A=x?function(){for(var n=arguments.length,t=new Array(n),e=0;e<n;e++)t[e]=arguments[e];return t.filter((function(n){return n})).join(" ")}(l,u):l,b=x?(0,c.Z)({},w,{},s):w,E=(0,c.Z)({"aria-current":x&&o||null,className:A,style:b,to:a},g);return N!==_?E.ref=t||y:E.innerRef=y,i().createElement(K,E)}))}))}}]);
//# sourceMappingURL=react-router-dom.58ec3cdf54d6250739dda965ec47bfd1.js.map