/*! For license information please see es6-promise.17c399175934fd4916f8.js.LICENSE.txt */
(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[998],{4411:function(t,n,e){"use strict";t.exports=e(132).polyfill()},132:function(t,n,e){t.exports=function(){"use strict";function t(t){var n=typeof t;return null!==t&&("object"===n||"function"===n)}function n(t){return"function"===typeof t}var r=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},o=0,i=void 0,s=void 0,u=function(t,n){b[o]=t,b[o+1]=n,2===(o+=2)&&(s?s(g):S())};function c(t){s=t}function a(t){u=t}var f="undefined"!==typeof window?window:void 0,l=f||{},h=l.MutationObserver||l.WebKitMutationObserver,v="undefined"===typeof self&&"undefined"!==typeof process&&"[object process]"==={}.toString.call(process),p="undefined"!==typeof Uint8ClampedArray&&"undefined"!==typeof importScripts&&"undefined"!==typeof MessageChannel;function _(){return function(){return process.nextTick(g)}}function d(){return"undefined"!==typeof i?function(){i(g)}:w()}function y(){var t=0,n=new h(g),e=document.createTextNode("");return n.observe(e,{characterData:!0}),function(){e.data=t=++t%2}}function m(){var t=new MessageChannel;return t.port1.onmessage=g,function(){return t.port2.postMessage(0)}}function w(){var t=setTimeout;return function(){return t(g,1)}}var b=new Array(1e3);function g(){for(var t=0;t<o;t+=2)(0,b[t])(b[t+1]),b[t]=void 0,b[t+1]=void 0;o=0}function A(){try{var t=e(4327);return i=t.runOnLoop||t.runOnContext,d()}catch(n){return w()}}var S=void 0;function j(t,n){var e=arguments,r=this,o=new this.constructor(T);void 0===o[C]&&Z(o);var i=r._state;return i?function(){var t=e[i-1];u((function(){return Q(i,o,t,r._result)}))}():B(r,o,t,n),o}function E(t){var n=this;if(t&&"object"===typeof t&&t.constructor===n)return t;var e=new n(T);return U(e,t),e}S=v?_():h?y():p?m():void 0===f?A():w();var C=Math.random().toString(36).substring(16);function T(){}var k=void 0,M=1,O=2,P=new H;function x(){return new TypeError("You cannot resolve a promise with itself")}function Y(){return new TypeError("A promises callback cannot return that same promise.")}function F(t){try{return t.then}catch(n){return P.error=n,P}}function D(t,n,e,r){try{t.call(n,e,r)}catch(o){return o}}function K(t,n,e){u((function(t){var r=!1,o=D(e,n,(function(e){r||(r=!0,n!==e?U(t,e):q(t,e))}),(function(n){r||(r=!0,z(t,n))}),"Settle: "+(t._label||" unknown promise"));!r&&o&&(r=!0,z(t,o))}),t)}function L(t,n){n._state===M?q(t,n._result):n._state===O?z(t,n._result):B(n,void 0,(function(n){return U(t,n)}),(function(n){return z(t,n)}))}function N(t,e,r){e.constructor===t.constructor&&r===j&&e.constructor.resolve===E?L(t,e):r===P?(z(t,P.error),P.error=null):void 0===r?q(t,e):n(r)?K(t,e,r):q(t,e)}function U(n,e){n===e?z(n,x()):t(e)?N(n,e,F(e)):q(n,e)}function W(t){t._onerror&&t._onerror(t._result),G(t)}function q(t,n){t._state===k&&(t._result=n,t._state=M,0!==t._subscribers.length&&u(G,t))}function z(t,n){t._state===k&&(t._state=O,t._result=n,u(W,t))}function B(t,n,e,r){var o=t._subscribers,i=o.length;t._onerror=null,o[i]=n,o[i+M]=e,o[i+O]=r,0===i&&t._state&&u(G,t)}function G(t){var n=t._subscribers,e=t._state;if(0!==n.length){for(var r=void 0,o=void 0,i=t._result,s=0;s<n.length;s+=3)r=n[s],o=n[s+e],r?Q(e,r,o,i):o(i);t._subscribers.length=0}}function H(){this.error=null}var I=new H;function J(t,n){try{return t(n)}catch(e){return I.error=e,I}}function Q(t,e,r,o){var i=n(r),s=void 0,u=void 0,c=void 0,a=void 0;if(i){if((s=J(r,o))===I?(a=!0,u=s.error,s.error=null):c=!0,e===s)return void z(e,Y())}else s=o,c=!0;e._state!==k||(i&&c?U(e,s):a?z(e,u):t===M?q(e,s):t===O&&z(e,s))}function R(t,n){try{n((function(n){U(t,n)}),(function(n){z(t,n)}))}catch(e){z(t,e)}}var V=0;function X(){return V++}function Z(t){t[C]=V++,t._state=void 0,t._result=void 0,t._subscribers=[]}function $(t,n){this._instanceConstructor=t,this.promise=new t(T),this.promise[C]||Z(this.promise),r(n)?(this.length=n.length,this._remaining=n.length,this._result=new Array(this.length),0===this.length?q(this.promise,this._result):(this.length=this.length||0,this._enumerate(n),0===this._remaining&&q(this.promise,this._result))):z(this.promise,tt())}function tt(){return new Error("Array Methods must be provided an Array")}function nt(t){return new $(this,t).promise}function et(t){var n=this;return r(t)?new n((function(e,r){for(var o=t.length,i=0;i<o;i++)n.resolve(t[i]).then(e,r)})):new n((function(t,n){return n(new TypeError("You must pass an array to race."))}))}function rt(t){var n=new this(T);return z(n,t),n}function ot(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function it(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function st(t){this[C]=X(),this._result=this._state=void 0,this._subscribers=[],T!==t&&("function"!==typeof t&&ot(),this instanceof st?R(this,t):it())}function ut(){var t=void 0;if("undefined"!==typeof e.g)t=e.g;else if("undefined"!==typeof self)t=self;else try{t=Function("return this")()}catch(o){throw new Error("polyfill failed because global object is unavailable in this environment")}var n=t.Promise;if(n){var r=null;try{r=Object.prototype.toString.call(n.resolve())}catch(o){}if("[object Promise]"===r&&!n.cast)return}t.Promise=st}return $.prototype._enumerate=function(t){for(var n=0;this._state===k&&n<t.length;n++)this._eachEntry(t[n],n)},$.prototype._eachEntry=function(t,n){var e=this._instanceConstructor,r=e.resolve;if(r===E){var o=F(t);if(o===j&&t._state!==k)this._settledAt(t._state,n,t._result);else if("function"!==typeof o)this._remaining--,this._result[n]=t;else if(e===st){var i=new e(T);N(i,t,o),this._willSettleAt(i,n)}else this._willSettleAt(new e((function(n){return n(t)})),n)}else this._willSettleAt(r(t),n)},$.prototype._settledAt=function(t,n,e){var r=this.promise;r._state===k&&(this._remaining--,t===O?z(r,e):this._result[n]=e),0===this._remaining&&q(r,this._result)},$.prototype._willSettleAt=function(t,n){var e=this;B(t,void 0,(function(t){return e._settledAt(M,n,t)}),(function(t){return e._settledAt(O,n,t)}))},st.all=nt,st.race=et,st.resolve=E,st.reject=rt,st._setScheduler=c,st._setAsap=a,st._asap=u,st.prototype={constructor:st,then:j,catch:function(t){return this.then(null,t)}},st.polyfill=ut,st.Promise=st,st}()}}]);
//# sourceMappingURL=es6-promise.860cea8372232994e10e80b0e5cbdebc.js.map