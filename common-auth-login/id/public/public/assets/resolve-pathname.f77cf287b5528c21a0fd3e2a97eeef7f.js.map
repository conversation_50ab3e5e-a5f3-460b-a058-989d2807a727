{"version": 3, "file": "resolve-pathname.xxxxxxxxxxxxxxxxxxxx.js", "mappings": "mHAAA,SAASA,EAAWC,GAClB,MAA8B,MAAvBA,EAASC,OAAO,EACzB,CAGA,SAASC,EAAUC,EAAMC,GACvB,IAAK,IAAIC,EAAID,EAAOE,EAAID,EAAI,EAAGE,EAAIJ,EAAKK,OAAQF,EAAIC,EAAGF,GAAK,EAAGC,GAAK,EAClEH,EAAKE,GAAKF,EAAKG,GAGjBH,EAAKM,KACP,CA+DA,IA5DA,SAAyBC,EAAIC,QACdC,IAATD,IAAoBA,EAAO,IAE/B,IAkBIE,EAlBAC,EAAWJ,GAAMA,EAAGK,MAAM,MAAS,GACnCC,EAAaL,GAAQA,EAAKI,MAAM,MAAS,GAEzCE,EAAUP,GAAMX,EAAWW,GAC3BQ,EAAYP,GAAQZ,EAAWY,GAC/BQ,EAAaF,GAAWC,EAW5B,GATIR,GAAMX,EAAWW,GAEnBM,EAAYF,EACHA,EAAQN,SAEjBQ,EAAUP,MACVO,EAAYA,EAAUI,OAAON,KAG1BE,EAAUR,OAAQ,MAAO,IAG9B,GAAIQ,EAAUR,OAAQ,CACpB,IAAIa,EAAOL,EAAUA,EAAUR,OAAS,GACxCK,EAA4B,MAATQ,GAAyB,OAATA,GAA0B,KAATA,CACtD,MACER,GAAmB,EAIrB,IADA,IAAIS,EAAK,EACAjB,EAAIW,EAAUR,OAAQH,GAAK,EAAGA,IAAK,CAC1C,IAAIkB,EAAOP,EAAUX,GAER,MAATkB,EACFrB,EAAUc,EAAWX,GACH,OAATkB,GACTrB,EAAUc,EAAWX,GACrBiB,KACSA,IACTpB,EAAUc,EAAWX,GACrBiB,IAEJ,CAEA,IAAKH,EAAY,KAAOG,IAAMA,EAAIN,EAAUQ,QAAQ,OAGlDL,GACiB,KAAjBH,EAAU,IACRA,EAAU,IAAOjB,EAAWiB,EAAU,KAExCA,EAAUQ,QAAQ,IAEpB,IAAIC,EAAST,EAAUU,KAAK,KAI5B,OAFIb,GAA0C,MAAtBY,EAAOE,QAAQ,KAAYF,GAAU,KAEtDA,CACT,C", "sources": ["webpack://sr-common-auth/./node_modules/resolve-pathname/esm/resolve-pathname.js"], "names": ["isAbsolute", "pathname", "char<PERSON>t", "spliceOne", "list", "index", "i", "k", "n", "length", "pop", "to", "from", "undefined", "hasTrailingSlash", "toParts", "split", "fromParts", "isToAbs", "isFromAbs", "mustEndAbs", "concat", "last", "up", "part", "unshift", "result", "join", "substr"], "sourceRoot": ""}