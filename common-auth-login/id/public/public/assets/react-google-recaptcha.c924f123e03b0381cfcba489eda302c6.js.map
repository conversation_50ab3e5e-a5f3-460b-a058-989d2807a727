{"version": 3, "file": "react-google-recaptcha.xxxxxxxxxxxxxxxxxxxx.js", "mappings": "sLAAIA,EAAY,CAAC,UAAW,WAAY,QAAS,OAAQ,WAAY,YAAa,YAAa,OAAQ,SAAU,aAAc,QAAS,KAAM,YAC9I,SAASC,IAAiS,OAApRA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcP,OAAOS,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,GAAU,CAAE,OAAOL,CAAQ,EAAUJ,EAASa,MAAMC,KAAMR,UAAY,CAElV,SAASS,EAAuBC,GAAQ,QAAa,IAATA,EAAmB,MAAM,IAAIC,eAAe,6DAAgE,OAAOD,CAAM,CAErK,SAASE,EAAgBC,EAAGC,GAA6I,OAAxIF,EAAkBjB,OAAOoB,eAAiBpB,OAAOoB,eAAelB,OAAS,SAAyBgB,EAAGC,GAAsB,OAAjBD,EAAEG,UAAYF,EAAUD,CAAG,EAAUD,EAAgBC,EAAGC,EAAI,CAGvM,IAAIG,EAAyB,SAAUC,GAJvC,IAAwBC,EAAUC,EAMhC,SAASH,IACP,IAAII,EAMJ,OALAA,EAAQH,EAAiBZ,KAAKE,OAASA,MACjCc,cAAgBD,EAAMC,cAAczB,KAAKY,EAAuBY,IACtEA,EAAME,cAAgBF,EAAME,cAAc1B,KAAKY,EAAuBY,IACtEA,EAAMG,aAAeH,EAAMG,aAAa3B,KAAKY,EAAuBY,IACpEA,EAAMI,mBAAqBJ,EAAMI,mBAAmB5B,KAAKY,EAAuBY,IACzEA,CACT,CAdgCD,EAKNF,GALJC,EAKPF,GALwCb,UAAYT,OAAO+B,OAAON,EAAWhB,WAAYe,EAASf,UAAUuB,YAAcR,EAAUP,EAAgBO,EAAUC,GAe7K,IAAIQ,EAASX,EAAUb,UAuIvB,OAtIAwB,EAAOC,mBAAqB,SAA4BC,GACtD,OAAItB,KAAKuB,MAAMC,WACTxB,KAAKuB,MAAMC,WAAWC,WACjBzB,KAAKuB,MAAMC,WAAWC,WAAWH,GAEnCtB,KAAKuB,MAAMC,WAAWF,GAExB,IACT,EACAF,EAAOM,SAAW,WAChB,IAAIC,EAAc3B,KAAKqB,mBAAmB,eAC1C,OAAIM,QAAkCC,IAAnB5B,KAAK6B,UACfF,EAAY3B,KAAK6B,WAEnB,IACT,EACAT,EAAOU,YAAc,WACnB,OAAI9B,KAAKuB,MAAMC,iBAAiCI,IAAnB5B,KAAK6B,UACzB7B,KAAK6B,UAEP,IACT,EACAT,EAAOW,QAAU,WACf,IAAIA,EAAU/B,KAAKqB,mBAAmB,WACtC,GAAIU,QAA8BH,IAAnB5B,KAAK6B,UAClB,OAAOE,EAAQ/B,KAAK6B,WAEpB7B,KAAKgC,mBAAoB,CAE7B,EACAZ,EAAOa,aAAe,WACpB,IAAIC,EAASlC,KACb,OAAO,IAAImC,QAAQ,SAAUC,EAASC,GACpCH,EAAOI,iBAAmBF,EAC1BF,EAAOK,gBAAkBF,EACzBH,EAAOH,SACT,EACF,EACAX,EAAOoB,MAAQ,WACb,IAAIC,EAAWzC,KAAKqB,mBAAmB,SACnCoB,QAA+Bb,IAAnB5B,KAAK6B,WACnBY,EAASzC,KAAK6B,UAElB,EACAT,EAAOsB,WAAa,WAClB,IAAID,EAAWzC,KAAKqB,mBAAmB,SACnCoB,GACFA,GAEJ,EACArB,EAAON,cAAgB,WACjBd,KAAKuB,MAAMoB,UACb3C,KAAKuB,MAAMoB,YAEX3C,KAAKgB,aAAa,KAEtB,EACAI,EAAOL,cAAgB,WACjBf,KAAKuB,MAAMqB,WACb5C,KAAKuB,MAAMqB,YAET5C,KAAKuC,kBACPvC,KAAKuC,yBACEvC,KAAKsC,wBACLtC,KAAKuC,gBAEhB,EACAnB,EAAOJ,aAAe,SAAsB6B,GACtC7C,KAAKuB,MAAMuB,UACb9C,KAAKuB,MAAMuB,SAASD,GAElB7C,KAAKsC,mBACPtC,KAAKsC,iBAAiBO,UACf7C,KAAKuC,uBACLvC,KAAKsC,iBAEhB,EACAlB,EAAO2B,eAAiB,WACtB,IAAIC,EAAShD,KAAKqB,mBAAmB,UACrC,GAAI2B,QAA6BpB,IAAnB5B,KAAK6B,UAAyB,CAC1C,IAAIoB,EAAUC,SAASC,cAAc,OACrCnD,KAAK6B,UAAYmB,EAAOC,EAAS,CAC/BG,QAASpD,KAAKuB,MAAM6B,QACpBC,SAAUrD,KAAKgB,aACfsC,MAAOtD,KAAKuB,MAAM+B,MAClBC,KAAMvD,KAAKuB,MAAMgC,KACjBC,SAAUxD,KAAKuB,MAAMiC,SACrB,mBAAoBxD,KAAKc,cACzB,iBAAkBd,KAAKe,cACvB0C,KAAMzD,KAAKuB,MAAMkC,KACjBC,OAAQ1D,KAAKuB,MAAMmC,OACnBC,GAAI3D,KAAKuB,MAAMoC,GACfC,MAAO5D,KAAKuB,MAAMqC,MAClBC,SAAU7D,KAAKuB,MAAMsC,WAEvB7D,KAAK8D,QAAQC,YAAYd,EAC3B,CACIjD,KAAKgC,mBAAqBhC,KAAKuB,MAAMC,iBAAiCI,IAAnB5B,KAAK6B,YAC1D7B,KAAKgC,mBAAoB,EACzBhC,KAAK+B,UAET,EACAX,EAAO4C,kBAAoB,WACzBhE,KAAK+C,gBACP,EACA3B,EAAO6C,mBAAqB,WAC1BjE,KAAK+C,gBACP,EACA3B,EAAOH,mBAAqB,SAA4BiD,GACtDlE,KAAK8D,QAAUI,CACjB,EACA9C,EAAO4B,OAAS,WAGd,IAAImB,EAAcnE,KAAKuB,MAcrB6C,GAbUD,EAAYf,QACXe,EAAYrB,SACfqB,EAAYb,MACba,EAAYZ,KACRY,EAAYX,SACXW,EAAYxB,UACZwB,EAAYvB,UACjBuB,EAAYV,KACVU,EAAYT,OACRS,EAAY3C,WACjB2C,EAAYP,MACfO,EAAYR,GACNQ,EAAYN,SAjJ7B,SAAuCnE,EAAQ2E,GAAY,GAAc,MAAV3E,EAAgB,MAAO,CAAC,EAAG,IAA2DC,EAAKJ,EAA5DD,EAAS,CAAC,EAAOgF,EAAanF,OAAOoF,KAAK7E,GAAqB,IAAKH,EAAI,EAAGA,EAAI+E,EAAW7E,OAAQF,IAAOI,EAAM2E,EAAW/E,GAAQ8E,EAASG,QAAQ7E,IAAQ,IAAaL,EAAOK,GAAOD,EAAOC,IAAQ,OAAOL,CAAQ,CAkJ/RmF,CAA8BN,EAAalF,IAE1D,OAAoB,gBAAoB,MAAOC,EAAS,CAAC,EAAGkF,EAAY,CACtEM,IAAK1E,KAAKiB,qBAEd,EACOR,CACT,CAnJ6B,CAmJ3B,aAEFA,EAAUkE,YAAc,YACxBlE,EAAUmE,UAAY,CACpBxB,QAAS,sBACTN,SAAU,SACVtB,WAAY,WACZ8B,MAAO,UAAgB,CAAC,OAAQ,UAChCC,KAAM,UAAgB,CAAC,QAAS,UAChCC,SAAU,WACVb,UAAW,SACXC,UAAW,SACXa,KAAM,UAAgB,CAAC,UAAW,SAAU,cAC5CC,OAAQ,WACRC,GAAI,WACJC,MAAO,UAAgB,CAAC,cAAe,aAAc,WACrDC,SAAU,UAEZpD,EAAUoE,aAAe,CACvB/B,SAAU,WAAqB,EAC/BQ,MAAO,QACPC,KAAM,QACNC,SAAU,EACVC,KAAM,SACNG,MAAO,e,cCjLLkB,EAAe,iBAEnB,SAASC,IACP,MAAyB,qBAAXC,QAA0BA,OAAOC,kBAAoB,CAAC,CACtE,CASA,ICbA,GDae,OARf,WACE,IAAIC,EAAiBH,IACjBI,EAAWD,EAAeE,gBAAkB,gBAAkB,iBAClE,OAAIF,EAAezD,WACV,WAAa0D,EAAW,mCAAqCL,EAAe,mBAE9E,WAAaK,EAAW,4BAA8BL,EAAe,kBAC9E,EAC6C,CAC3CA,aAAcA,EACdO,WAde,aAefC,WAAYP,IAAaQ,MAAQ,CAC/BA,MAAOR,IAAaQ,OAClB,CAAC,GALP,CAMG9E,E", "sources": ["webpack://sr-common-auth/./node_modules/react-google-recaptcha/lib/esm/recaptcha.js", "webpack://sr-common-auth/./node_modules/react-google-recaptcha/lib/esm/recaptcha-wrapper.js", "webpack://sr-common-auth/./node_modules/react-google-recaptcha/lib/esm/index.js"], "names": ["_excluded", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "this", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "ReCAPTCHA", "_React$Component", "subClass", "superClass", "_this", "handleExpired", "handleErrored", "handleChange", "handleRecaptchaRef", "create", "constructor", "_proto", "getCaptchaFunction", "fnName", "props", "gre<PERSON><PERSON>a", "enterprise", "getValue", "getResponse", "undefined", "_widgetId", "getWidgetId", "execute", "_executeRequested", "executeAsync", "_this2", "Promise", "resolve", "reject", "executionResolve", "executionReject", "reset", "resetter", "forceReset", "onExpired", "onErrored", "token", "onChange", "explicitRender", "render", "wrapper", "document", "createElement", "sitekey", "callback", "theme", "type", "tabindex", "size", "stoken", "hl", "badge", "isolated", "<PERSON><PERSON>a", "append<PERSON><PERSON><PERSON>", "componentDidMount", "componentDidUpdate", "elem", "_this$props", "childProps", "excluded", "sourceKeys", "keys", "indexOf", "_objectWithoutPropertiesLoose", "ref", "displayName", "propTypes", "defaultProps", "callback<PERSON><PERSON>", "getOptions", "window", "recaptchaOptions", "dynamicOptions", "hostname", "useRecaptchaNet", "globalName", "attributes", "nonce"], "sourceRoot": ""}