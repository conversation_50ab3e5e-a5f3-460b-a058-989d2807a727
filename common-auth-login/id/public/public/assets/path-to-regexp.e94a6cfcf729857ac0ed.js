(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[457],{9455:function(e,t,r){var n=r(9677);e.exports=h,e.exports.parse=i,e.exports.compile=function(e,t){return p(i(e,t),t)},e.exports.tokensToFunction=p,e.exports.tokensToRegExp=s;var o=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function i(e,t){for(var r,n=[],i=0,a=0,p="",l=t&&t.delimiter||"/";null!=(r=o.exec(e));){var f=r[0],s=r[1],h=r.index;if(p+=e.slice(a,h),a=h+f.length,s)p+=s[1];else{var g=e[a],x=r[2],m=r[3],d=r[4],v=r[5],w=r[6],y=r[7];p&&(n.push(p),p="");var E=null!=x&&null!=g&&g!==x,b="+"===w||"*"===w,k="?"===w||"*"===w,R=r[2]||l,$=d||v;n.push({name:m||i++,prefix:x||"",delimiter:R,optional:k,repeat:b,partial:E,asterisk:!!y,pattern:$?c($):y?".*":"[^"+u(R)+"]+?"})}}return a<e.length&&(p+=e.substr(a)),p&&n.push(p),n}function a(e){return encodeURI(e).replace(/[\/?#]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()}))}function p(e,t){for(var r=new Array(e.length),o=0;o<e.length;o++)"object"===typeof e[o]&&(r[o]=new RegExp("^(?:"+e[o].pattern+")$",f(t)));return function(t,o){for(var i="",p=t||{},u=(o||{}).pretty?a:encodeURIComponent,c=0;c<e.length;c++){var l=e[c];if("string"!==typeof l){var f,s=p[l.name];if(null==s){if(l.optional){l.partial&&(i+=l.prefix);continue}throw new TypeError('Expected "'+l.name+'" to be defined')}if(n(s)){if(!l.repeat)throw new TypeError('Expected "'+l.name+'" to not repeat, but received `'+JSON.stringify(s)+"`");if(0===s.length){if(l.optional)continue;throw new TypeError('Expected "'+l.name+'" to not be empty')}for(var h=0;h<s.length;h++){if(f=u(s[h]),!r[c].test(f))throw new TypeError('Expected all "'+l.name+'" to match "'+l.pattern+'", but received `'+JSON.stringify(f)+"`");i+=(0===h?l.prefix:l.delimiter)+f}}else{if(f=l.asterisk?encodeURI(s).replace(/[?#]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})):u(s),!r[c].test(f))throw new TypeError('Expected "'+l.name+'" to match "'+l.pattern+'", but received "'+f+'"');i+=l.prefix+f}}else i+=l}return i}}function u(e){return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function c(e){return e.replace(/([=!:$\/()])/g,"\\$1")}function l(e,t){return e.keys=t,e}function f(e){return e&&e.sensitive?"":"i"}function s(e,t,r){n(t)||(r=t||r,t=[]);for(var o=(r=r||{}).strict,i=!1!==r.end,a="",p=0;p<e.length;p++){var c=e[p];if("string"===typeof c)a+=u(c);else{var s=u(c.prefix),h="(?:"+c.pattern+")";t.push(c),c.repeat&&(h+="(?:"+s+h+")*"),a+=h=c.optional?c.partial?s+"("+h+")?":"(?:"+s+"("+h+"))?":s+"("+h+")"}}var g=u(r.delimiter||"/"),x=a.slice(-g.length)===g;return o||(a=(x?a.slice(0,-g.length):a)+"(?:"+g+"(?=$))?"),a+=i?"$":o&&x?"":"(?="+g+"|$)",l(new RegExp("^"+a,f(r)),t)}function h(e,t,r){return n(t)||(r=t||r,t=[]),r=r||{},e instanceof RegExp?function(e,t){var r=e.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)t.push({name:n,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return l(e,t)}(e,t):n(e)?function(e,t,r){for(var n=[],o=0;o<e.length;o++)n.push(h(e[o],t,r).source);return l(new RegExp("(?:"+n.join("|")+")",f(r)),t)}(e,t,r):function(e,t,r){return s(i(e,r),t,r)}(e,t,r)}},9677:function(e){e.exports=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)}}}]);
//# sourceMappingURL=path-to-regexp.cef0f3e7765c9c423eb6e1109b4e282c.js.map