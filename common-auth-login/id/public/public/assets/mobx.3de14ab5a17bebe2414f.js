"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[545],{9621:function(e,t,n){n.d(t,{so:function(){return G},le:function(){return bt},$$:function(){return Me},mJ:function(){return it},wM:function(){return rt},aD:function(){return Tt},Fl:function(){return Ve},jQ:function(){return Ht},cp:function(){return z},Gf:function(){return Wt},Ei:function(){return Vn},LJ:function(){return Bn},Pb:function(){return Xn},rC:function(){return pn},LO:function(){return je},ZN:function(){return an},rg:function(){return et}});function r(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("number"===typeof e?"[MobX] minified error nr: "+e+(n.length?" "+n.map(String).join(","):"")+". Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.ts":"[MobX] "+e)}var i={};function o(){return"undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:"undefined"!==typeof self?self:i}var a=Object.assign,s=Object.getOwnPropertyDescriptor,u=Object.defineProperty,c=Object.prototype,l=[];Object.freeze(l);var h={};Object.freeze(h);var f="undefined"!==typeof Proxy,_=Object.toString();function v(){f||r("Proxy not available")}function d(e){var t=!1;return function(){if(!t)return t=!0,e.apply(this,arguments)}}var p=function(){};function b(e){return"function"===typeof e}function g(e){switch(typeof e){case"string":case"symbol":case"number":return!0}return!1}function y(e){return null!==e&&"object"===typeof e}function m(e){var t;if(!y(e))return!1;var n=Object.getPrototypeOf(e);return null==n||(null==(t=n.constructor)?void 0:t.toString())===_}function O(e){var t=null==e?void 0:e.constructor;return!!t&&("GeneratorFunction"===t.name||"GeneratorFunction"===t.displayName)}function w(e,t,n){u(e,t,{enumerable:!1,writable:!0,configurable:!0,value:n})}function A(e,t,n){u(e,t,{enumerable:!1,writable:!1,configurable:!0,value:n})}function S(e,t){var n="isMobX"+e;return t.prototype[n]=!0,function(e){return y(e)&&!0===e[n]}}function x(e){return e instanceof Map}function j(e){return e instanceof Set}var k="undefined"!==typeof Object.getOwnPropertySymbols;var E="undefined"!==typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:k?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames;function P(e){return null===e?null:"object"===typeof e?""+e:e}function V(e,t){return c.hasOwnProperty.call(e,t)}var T=Object.getOwnPropertyDescriptors||function(e){var t={};return E(e).forEach((function(n){t[n]=s(e,n)})),t};function C(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function N(e,t,n){return t&&C(e.prototype,t),n&&C(e,n),e}function R(){return R=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},R.apply(this,arguments)}function D(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function L(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function M(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"===typeof e)return B(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?B(e,t):void 0}}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(n=e[Symbol.iterator]()).next.bind(n)}var I=Symbol("mobx-stored-annotations");function U(e){return Object.assign((function(t,n){K(t,n,e)}),e)}function K(e,t,n){V(e,I)||w(e,I,R({},e[I])),function(e){return e.annotationType_===J}(n)||(e[I][t]=n)}var G=Symbol("mobx administration"),q=function(){function e(e){void 0===e&&(e="Atom"),this.name_=void 0,this.isPendingUnobservation_=!1,this.isBeingObserved_=!1,this.observers_=new Set,this.diffValue_=0,this.lastAccessedBy_=0,this.lowestObserverState_=He.NOT_TRACKING_,this.onBOL=void 0,this.onBUOL=void 0,this.name_=e}var t=e.prototype;return t.onBO=function(){this.onBOL&&this.onBOL.forEach((function(e){return e()}))},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(e){return e()}))},t.reportObserved=function(){return dt(this)},t.reportChanged=function(){_t(),pt(this),vt()},t.toString=function(){return this.name_},e}(),H=S("Atom",q);function z(e,t,n){void 0===t&&(t=p),void 0===n&&(n=p);var r,i=new q(e);return t!==p&&Ut(Bt,i,t,r),n!==p&&It(i,n),i}var W={identity:function(e,t){return e===t},structural:function(e,t){return ur(e,t)},default:function(e,t){return Object.is?Object.is(e,t):e===t?0!==e||1/e===1/t:e!==e&&t!==t},shallow:function(e,t){return ur(e,t,1)}};function X(e,t,n){return nn(e)?e:Array.isArray(e)?je.array(e,{name:n}):m(e)?je.object(e,void 0,{name:n}):x(e)?je.map(e,{name:n}):j(e)?je.set(e,{name:n}):"function"!==typeof e||Nt(e)||en(e)?e:O(e)?Qt(e):Ct(n,e)}function F(e){return e}var J="override";function Y(e,t){return{annotationType_:e,options_:t,make_:$,extend_:Q}}function $(e,t,n,r){var i;if(null==(i=this.options_)?void 0:i.bound)return null===this.extend_(e,t,n,!1)?0:1;if(r===e.target_)return null===this.extend_(e,t,n,!1)?0:2;if(Nt(n.value))return 1;var o=Z(e,this,t,n,!1);return u(r,t,o),2}function Q(e,t,n,r){var i=Z(e,this,t,n);return e.defineProperty_(t,i,r)}function Z(e,t,n,r,i){var o,a,s,u,c,l,h,f;void 0===i&&(i=ct.safeDescriptors),f=r,t.annotationType_,f.value;var _,v=r.value;(null==(o=t.options_)?void 0:o.bound)&&(v=v.bind(null!=(_=e.proxy_)?_:e.target_));return{value:Le(null!=(a=null==(s=t.options_)?void 0:s.name)?a:n.toString(),v,null!=(u=null==(c=t.options_)?void 0:c.autoAction)&&u,(null==(l=t.options_)?void 0:l.bound)?null!=(h=e.proxy_)?h:e.target_:void 0),configurable:!i||e.isPlainObject_,enumerable:!1,writable:!i}}function ee(e,t){return{annotationType_:e,options_:t,make_:te,extend_:ne}}function te(e,t,n,r){var i;if(r===e.target_)return null===this.extend_(e,t,n,!1)?0:2;if((null==(i=this.options_)?void 0:i.bound)&&!en(e.target_[t])&&null===this.extend_(e,t,n,!1))return 0;if(en(n.value))return 1;var o=re(e,this,t,n,!1,!1);return u(r,t,o),2}function ne(e,t,n,r){var i,o=re(e,this,t,n,null==(i=this.options_)?void 0:i.bound);return e.defineProperty_(t,o,r)}function re(e,t,n,r,i,o){var a;void 0===o&&(o=ct.safeDescriptors),a=r,t.annotationType_,a.value;var s,u=r.value;i&&(u=u.bind(null!=(s=e.proxy_)?s:e.target_));return{value:Qt(u),configurable:!o||e.isPlainObject_,enumerable:!1,writable:!o}}function ie(e,t){return{annotationType_:e,options_:t,make_:oe,extend_:ae}}function oe(e,t,n){return null===this.extend_(e,t,n,!1)?0:1}function ae(e,t,n,r){return function(e,t,n,r){t.annotationType_,r.get;0}(0,this,0,n),e.defineComputedProperty_(t,R({},this.options_,{get:n.get,set:n.set}),r)}function se(e,t){return{annotationType_:e,options_:t,make_:ue,extend_:ce}}function ue(e,t,n){return null===this.extend_(e,t,n,!1)?0:1}function ce(e,t,n,r){var i,o;return function(e,t,n,r){t.annotationType_;0}(0,this),e.defineObservableProperty_(t,n.value,null!=(i=null==(o=this.options_)?void 0:o.enhancer)?i:X,r)}var le="true",he=fe();function fe(e){return{annotationType_:le,options_:e,make_:_e,extend_:ve}}function _e(e,t,n,r){var i,o,a,s;if(n.get)return Ve.make_(e,t,n,r);if(n.set){var c=Le(t.toString(),n.set);return r===e.target_?null===e.defineProperty_(t,{configurable:!ct.safeDescriptors||e.isPlainObject_,set:c})?0:2:(u(r,t,{configurable:!0,set:c}),2)}if(r!==e.target_&&"function"===typeof n.value)return O(n.value)?((null==(s=this.options_)?void 0:s.autoBind)?Qt.bound:Qt).make_(e,t,n,r):((null==(a=this.options_)?void 0:a.autoBind)?Ct.bound:Ct).make_(e,t,n,r);var l,h=!1===(null==(i=this.options_)?void 0:i.deep)?je.ref:je;"function"===typeof n.value&&(null==(o=this.options_)?void 0:o.autoBind)&&(n.value=n.value.bind(null!=(l=e.proxy_)?l:e.target_));return h.make_(e,t,n,r)}function ve(e,t,n,r){var i,o,a;if(n.get)return Ve.extend_(e,t,n,r);if(n.set)return e.defineProperty_(t,{configurable:!ct.safeDescriptors||e.isPlainObject_,set:Le(t.toString(),n.set)},r);"function"===typeof n.value&&(null==(i=this.options_)?void 0:i.autoBind)&&(n.value=n.value.bind(null!=(a=e.proxy_)?a:e.target_));return(!1===(null==(o=this.options_)?void 0:o.deep)?je.ref:je).extend_(e,t,n,r)}var de={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};function pe(e){return e||de}Object.freeze(de);var be=se("observable"),ge=se("observable.ref",{enhancer:F}),ye=se("observable.shallow",{enhancer:function(e,t,n){return void 0===e||null===e||Xn(e)||Vn(e)||Bn(e)||Un(e)?e:Array.isArray(e)?je.array(e,{name:n,deep:!1}):m(e)?je.object(e,void 0,{name:n,deep:!1}):x(e)?je.map(e,{name:n,deep:!1}):j(e)?je.set(e,{name:n,deep:!1}):void 0}}),me=se("observable.struct",{enhancer:function(e,t){return ur(e,t)?t:e}}),Oe=U(be);function we(e){return!0===e.deep?X:!1===e.deep?F:function(e){var t,n;return e&&null!=(t=null==(n=e.options_)?void 0:n.enhancer)?t:X}(e.defaultDecorator)}function Ae(e,t,n){if(!g(t))return nn(e)?e:m(e)?je.object(e,t,n):Array.isArray(e)?je.array(e,t):x(e)?je.map(e,t):j(e)?je.set(e,t):"object"===typeof e&&null!==e?e:je.box(e,t);K(e,t,be)}Object.assign(Ae,Oe);var Se,xe,je=a(Ae,{box:function(e,t){var n=pe(t);return new Ge(e,we(n),n.name,!0,n.equals)},array:function(e,t){var n=pe(t);return(!1===ct.useProxies||!1===n.proxy?rr:On)(e,we(n),n.name)},map:function(e,t){var n=pe(t);return new Ln(e,we(n),n.name)},set:function(e,t){var n=pe(t);return new In(e,we(n),n.name)},object:function(e,t,n){return zt(!1===ct.useProxies||!1===(null==n?void 0:n.proxy)?Hn({},n):function(e,t){var n,r;return v(),e=Hn(e,t),null!=(r=(n=e[G]).proxy_)?r:n.proxy_=new Proxy(e,cn)}({},n),e,t)},ref:U(ge),shallow:U(ye),deep:Oe,struct:U(me)}),ke="computed",Ee=ie(ke),Pe=ie("computed.struct",{equals:W.structural}),Ve=function(e,t){if(g(t))return K(e,t,Ee);if(m(e))return U(ie(ke,e));var n=m(t)?t:{};return n.get=e,n.name||(n.name=e.name||""),new We(n)};Object.assign(Ve,Ee),Ve.struct=U(Pe);var Te,Ce=0,Ne=1,Re=null!=(Se=null==(xe=s((function(){}),"name"))?void 0:xe.configurable)&&Se,De={value:"action",configurable:!0,writable:!1,enumerable:!1};function Le(e,t,n,r){function i(){return Be(e,n,t,r||this,arguments)}return void 0===n&&(n=!1),i.isMobxAction=!0,Re&&(De.value=e,Object.defineProperty(i,"name",De)),i}function Be(e,t,n,i,o){var a=function(e,t,n,r){var i=!1,o=0;0;var a=ct.trackingDerivation,s=!t||!a;_t();var u=ct.allowStateChanges;s&&(tt(),u=Ie(!0));var c=rt(!0),l={runAsAction_:s,prevDerivation_:a,prevAllowStateChanges_:u,prevAllowStateReads_:c,notifySpy_:i,startTime_:o,actionId_:Ne++,parentActionId_:Ce};return Ce=l.actionId_,l}(0,t);try{return n.apply(i,o)}catch(s){throw a.error_=s,s}finally{!function(e){Ce!==e.actionId_&&r(30);Ce=e.parentActionId_,void 0!==e.error_&&(ct.suppressReactionErrors=!0);Ue(e.prevAllowStateChanges_),it(e.prevAllowStateReads_),vt(),e.runAsAction_&&nt(e.prevDerivation_);0;ct.suppressReactionErrors=!1}(a)}}function Me(e,t){var n=Ie(e);try{return t()}finally{Ue(n)}}function Ie(e){var t=ct.allowStateChanges;return ct.allowStateChanges=e,t}function Ue(e){ct.allowStateChanges=e}Te=Symbol.toPrimitive;var Ke,Ge=function(e){function t(t,n,r,i,o){var a;return void 0===r&&(r="ObservableValue"),void 0===i&&(i=!0),void 0===o&&(o=W.default),(a=e.call(this,r)||this).enhancer=void 0,a.name_=void 0,a.equals=void 0,a.hasUnreportedChange_=!1,a.interceptors_=void 0,a.changeListeners_=void 0,a.value_=void 0,a.dehancer=void 0,a.enhancer=n,a.name_=r,a.equals=o,a.value_=n(t,void 0,r),a}D(t,e);var n=t.prototype;return n.dehanceValue=function(e){return void 0!==this.dehancer?this.dehancer(e):e},n.set=function(e){this.value_;if((e=this.prepareNewValue_(e))!==ct.UNCHANGED){0,this.setNewValue_(e)}},n.prepareNewValue_=function(e){if($e(this),ln(this)){var t=fn(this,{object:this,type:gn,newValue:e});if(!t)return ct.UNCHANGED;e=t.newValue}return e=this.enhancer(e,this.value_,this.name_),this.equals(this.value_,e)?ct.UNCHANGED:e},n.setNewValue_=function(e){var t=this.value_;this.value_=e,this.reportChanged(),_n(this)&&dn(this,{type:gn,object:this,newValue:e,oldValue:t})},n.get=function(){return this.reportObserved(),this.dehanceValue(this.value_)},n.intercept_=function(e){return hn(this,e)},n.observe_=function(e,t){return t&&e({observableKind:"value",debugObjectName:this.name_,object:this,type:gn,newValue:this.value_,oldValue:void 0}),vn(this,e)},n.raw=function(){return this.value_},n.toJSON=function(){return this.get()},n.toString=function(){return this.name_+"["+this.value_+"]"},n.valueOf=function(){return P(this.get())},n[Te]=function(){return this.valueOf()},t}(q),qe=S("ObservableValue",Ge);Ke=Symbol.toPrimitive;var He,ze,We=function(){function e(e){this.dependenciesState_=He.NOT_TRACKING_,this.observing_=[],this.newObserving_=null,this.isBeingObserved_=!1,this.isPendingUnobservation_=!1,this.observers_=new Set,this.diffValue_=0,this.runId_=0,this.lastAccessedBy_=0,this.lowestObserverState_=He.UP_TO_DATE_,this.unboundDepsCount_=0,this.value_=new Fe(null),this.name_=void 0,this.triggeredBy_=void 0,this.isComputing_=!1,this.isRunningSetter_=!1,this.derivation=void 0,this.setter_=void 0,this.isTracing_=ze.NONE,this.scope_=void 0,this.equals_=void 0,this.requiresReaction_=void 0,this.keepAlive_=void 0,this.onBOL=void 0,this.onBUOL=void 0,e.get||r(31),this.derivation=e.get,this.name_=e.name||"ComputedValue",e.set&&(this.setter_=Le("ComputedValue-setter",e.set)),this.equals_=e.equals||(e.compareStructural||e.struct?W.structural:W.default),this.scope_=e.context,this.requiresReaction_=!!e.requiresReaction,this.keepAlive_=!!e.keepAlive}var t=e.prototype;return t.onBecomeStale_=function(){!function(e){if(e.lowestObserverState_!==He.UP_TO_DATE_)return;e.lowestObserverState_=He.POSSIBLY_STALE_,e.observers_.forEach((function(e){e.dependenciesState_===He.UP_TO_DATE_&&(e.dependenciesState_=He.POSSIBLY_STALE_,e.onBecomeStale_())}))}(this)},t.onBO=function(){this.onBOL&&this.onBOL.forEach((function(e){return e()}))},t.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(e){return e()}))},t.get=function(){if(this.isComputing_&&r(32,this.name_,this.derivation),0!==ct.inBatch||0!==this.observers_.size||this.keepAlive_){if(dt(this),Ye(this)){var e=ct.trackingContext;this.keepAlive_&&!e&&(ct.trackingContext=this),this.trackAndCompute()&&function(e){if(e.lowestObserverState_===He.STALE_)return;e.lowestObserverState_=He.STALE_,e.observers_.forEach((function(t){t.dependenciesState_===He.POSSIBLY_STALE_?t.dependenciesState_=He.STALE_:t.dependenciesState_===He.UP_TO_DATE_&&(e.lowestObserverState_=He.UP_TO_DATE_)}))}(this),ct.trackingContext=e}}else Ye(this)&&(this.warnAboutUntrackedRead_(),_t(),this.value_=this.computeValue_(!1),vt());var t=this.value_;if(Je(t))throw t.cause;return t},t.set=function(e){if(this.setter_){this.isRunningSetter_&&r(33,this.name_),this.isRunningSetter_=!0;try{this.setter_.call(this.scope_,e)}finally{this.isRunningSetter_=!1}}else r(34,this.name_)},t.trackAndCompute=function(){var e=this.value_,t=this.dependenciesState_===He.NOT_TRACKING_,n=this.computeValue_(!0),r=t||Je(e)||Je(n)||!this.equals_(e,n);return r&&(this.value_=n),r},t.computeValue_=function(e){this.isComputing_=!0;var t,n=Ie(!1);if(e)t=Qe(this,this.derivation,this.scope_);else if(!0===ct.disableErrorBoundaries)t=this.derivation.call(this.scope_);else try{t=this.derivation.call(this.scope_)}catch(r){t=new Fe(r)}return Ue(n),this.isComputing_=!1,t},t.suspend_=function(){this.keepAlive_||(Ze(this),this.value_=void 0)},t.observe_=function(e,t){var n=this,r=!0,i=void 0;return Rt((function(){var o=n.get();if(!r||t){var a=tt();e({observableKind:"computed",debugObjectName:n.name_,type:gn,object:n,newValue:o,oldValue:i}),nt(a)}r=!1,i=o}))},t.warnAboutUntrackedRead_=function(){},t.toString=function(){return this.name_+"["+this.derivation.toString()+"]"},t.valueOf=function(){return P(this.get())},t[Ke]=function(){return this.valueOf()},e}(),Xe=S("ComputedValue",We);!function(e){e[e.NOT_TRACKING_=-1]="NOT_TRACKING_",e[e.UP_TO_DATE_=0]="UP_TO_DATE_",e[e.POSSIBLY_STALE_=1]="POSSIBLY_STALE_",e[e.STALE_=2]="STALE_"}(He||(He={})),function(e){e[e.NONE=0]="NONE",e[e.LOG=1]="LOG",e[e.BREAK=2]="BREAK"}(ze||(ze={}));var Fe=function(e){this.cause=void 0,this.cause=e};function Je(e){return e instanceof Fe}function Ye(e){switch(e.dependenciesState_){case He.UP_TO_DATE_:return!1;case He.NOT_TRACKING_:case He.STALE_:return!0;case He.POSSIBLY_STALE_:for(var t=rt(!0),n=tt(),r=e.observing_,i=r.length,o=0;o<i;o++){var a=r[o];if(Xe(a)){if(ct.disableErrorBoundaries)a.get();else try{a.get()}catch(s){return nt(n),it(t),!0}if(e.dependenciesState_===He.STALE_)return nt(n),it(t),!0}}return ot(e),nt(n),it(t),!1}}function $e(e){}function Qe(e,t,n){var r=rt(!0);ot(e),e.newObserving_=new Array(e.observing_.length+100),e.unboundDepsCount_=0,e.runId_=++ct.runId;var i,o=ct.trackingDerivation;if(ct.trackingDerivation=e,ct.inBatch++,!0===ct.disableErrorBoundaries)i=t.call(n);else try{i=t.call(n)}catch(a){i=new Fe(a)}return ct.inBatch--,ct.trackingDerivation=o,function(e){for(var t=e.observing_,n=e.observing_=e.newObserving_,r=He.UP_TO_DATE_,i=0,o=e.unboundDepsCount_,a=0;a<o;a++){var s=n[a];0===s.diffValue_&&(s.diffValue_=1,i!==a&&(n[i]=s),i++),s.dependenciesState_>r&&(r=s.dependenciesState_)}n.length=i,e.newObserving_=null,o=t.length;for(;o--;){var u=t[o];0===u.diffValue_&&ht(u,e),u.diffValue_=0}for(;i--;){var c=n[i];1===c.diffValue_&&(c.diffValue_=0,lt(c,e))}r!==He.UP_TO_DATE_&&(e.dependenciesState_=r,e.onBecomeStale_())}(e),it(r),i}function Ze(e){var t=e.observing_;e.observing_=[];for(var n=t.length;n--;)ht(t[n],e);e.dependenciesState_=He.NOT_TRACKING_}function et(e){var t=tt();try{return e()}finally{nt(t)}}function tt(){var e=ct.trackingDerivation;return ct.trackingDerivation=null,e}function nt(e){ct.trackingDerivation=e}function rt(e){var t=ct.allowStateReads;return ct.allowStateReads=e,t}function it(e){ct.allowStateReads=e}function ot(e){if(e.dependenciesState_!==He.UP_TO_DATE_){e.dependenciesState_=He.UP_TO_DATE_;for(var t=e.observing_,n=t.length;n--;)t[n].lowestObserverState_=He.UP_TO_DATE_}}var at=function(){this.version=6,this.UNCHANGED={},this.trackingDerivation=null,this.trackingContext=null,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!1,this.allowStateReads=!0,this.enforceActions=!0,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1,this.useProxies=!0,this.verifyProxies=!1,this.safeDescriptors=!0},st=!0,ut=!1,ct=function(){var e=o();return e.__mobxInstanceCount>0&&!e.__mobxGlobals&&(st=!1),e.__mobxGlobals&&e.__mobxGlobals.version!==(new at).version&&(st=!1),st?e.__mobxGlobals?(e.__mobxInstanceCount+=1,e.__mobxGlobals.UNCHANGED||(e.__mobxGlobals.UNCHANGED={}),e.__mobxGlobals):(e.__mobxInstanceCount=1,e.__mobxGlobals=new at):(setTimeout((function(){ut||r(35)}),1),new at)}();function lt(e,t){e.observers_.add(t),e.lowestObserverState_>t.dependenciesState_&&(e.lowestObserverState_=t.dependenciesState_)}function ht(e,t){e.observers_.delete(t),0===e.observers_.size&&ft(e)}function ft(e){!1===e.isPendingUnobservation_&&(e.isPendingUnobservation_=!0,ct.pendingUnobservations.push(e))}function _t(){ct.inBatch++}function vt(){if(0===--ct.inBatch){mt();for(var e=ct.pendingUnobservations,t=0;t<e.length;t++){var n=e[t];n.isPendingUnobservation_=!1,0===n.observers_.size&&(n.isBeingObserved_&&(n.isBeingObserved_=!1,n.onBUO()),n instanceof We&&n.suspend_())}ct.pendingUnobservations=[]}}function dt(e){var t=ct.trackingDerivation;return null!==t?(t.runId_!==e.lastAccessedBy_&&(e.lastAccessedBy_=t.runId_,t.newObserving_[t.unboundDepsCount_++]=e,!e.isBeingObserved_&&ct.trackingContext&&(e.isBeingObserved_=!0,e.onBO())),!0):(0===e.observers_.size&&ct.inBatch>0&&ft(e),!1)}function pt(e){e.lowestObserverState_!==He.STALE_&&(e.lowestObserverState_=He.STALE_,e.observers_.forEach((function(e){e.dependenciesState_===He.UP_TO_DATE_&&e.onBecomeStale_(),e.dependenciesState_=He.STALE_})))}var bt=function(){function e(e,t,n,r){void 0===e&&(e="Reaction"),void 0===r&&(r=!1),this.name_=void 0,this.onInvalidate_=void 0,this.errorHandler_=void 0,this.requiresObservable_=void 0,this.observing_=[],this.newObserving_=[],this.dependenciesState_=He.NOT_TRACKING_,this.diffValue_=0,this.runId_=0,this.unboundDepsCount_=0,this.isDisposed_=!1,this.isScheduled_=!1,this.isTrackPending_=!1,this.isRunning_=!1,this.isTracing_=ze.NONE,this.name_=e,this.onInvalidate_=t,this.errorHandler_=n,this.requiresObservable_=r}var t=e.prototype;return t.onBecomeStale_=function(){this.schedule_()},t.schedule_=function(){this.isScheduled_||(this.isScheduled_=!0,ct.pendingReactions.push(this),mt())},t.isScheduled=function(){return this.isScheduled_},t.runReaction_=function(){if(!this.isDisposed_){_t(),this.isScheduled_=!1;var e=ct.trackingContext;if(ct.trackingContext=this,Ye(this)){this.isTrackPending_=!0;try{this.onInvalidate_()}catch(t){this.reportExceptionInDerivation_(t)}}ct.trackingContext=e,vt()}},t.track=function(e){if(!this.isDisposed_){_t();0,this.isRunning_=!0;var t=ct.trackingContext;ct.trackingContext=this;var n=Qe(this,e,void 0);ct.trackingContext=t,this.isRunning_=!1,this.isTrackPending_=!1,this.isDisposed_&&Ze(this),Je(n)&&this.reportExceptionInDerivation_(n.cause),vt()}},t.reportExceptionInDerivation_=function(e){var t=this;if(this.errorHandler_)this.errorHandler_(e,this);else{if(ct.disableErrorBoundaries)throw e;var n="[mobx] uncaught error in '"+this+"'";ct.suppressReactionErrors||console.error(n,e),ct.globalReactionErrorHandlers.forEach((function(n){return n(e,t)}))}},t.dispose=function(){this.isDisposed_||(this.isDisposed_=!0,this.isRunning_||(_t(),Ze(this),vt()))},t.getDisposer_=function(){var e=this.dispose.bind(this);return e[G]=this,e},t.toString=function(){return"Reaction["+this.name_+"]"},t.trace=function(e){void 0===e&&(e=!1),function(){r("trace() is not available in production builds");for(var e=!1,t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];"boolean"===typeof n[n.length-1]&&(e=n.pop());var o=function(e){switch(e.length){case 0:return ct.trackingDerivation;case 1:return ir(e[0]);case 2:return ir(e[0],e[1])}}(n);if(!o)return r("'trace(break?)' can only be used inside a tracked computed value or a Reaction. Consider passing in the computed value or reaction explicitly");o.isTracing_===ze.NONE&&console.log("[mobx.trace] '"+o.name_+"' tracing enabled");o.isTracing_=e?ze.BREAK:ze.LOG}(this,e)},e}();var gt=100,yt=function(e){return e()};function mt(){ct.inBatch>0||ct.isRunningReactions||yt(Ot)}function Ot(){ct.isRunningReactions=!0;for(var e=ct.pendingReactions,t=0;e.length>0;){++t===gt&&(console.error("[mobx] cycle in reaction: "+e[0]),e.splice(0));for(var n=e.splice(0),r=0,i=n.length;r<i;r++)n[r].runReaction_()}ct.isRunningReactions=!1}var wt=S("Reaction",bt);var At="action",St="autoAction",xt="<unnamed action>",jt=Y(At),kt=Y("action.bound",{bound:!0}),Et=Y(St,{autoAction:!0}),Pt=Y("autoAction.bound",{autoAction:!0,bound:!0});function Vt(e){return function(t,n){return b(t)?Le(t.name||xt,t,e):b(n)?Le(t,n,e):g(n)?K(t,n,e?Et:jt):g(t)?U(Y(e?St:At,{name:t,autoAction:e})):void 0}}var Tt=Vt(!1);Object.assign(Tt,jt);var Ct=Vt(!0);function Nt(e){return b(e)&&!0===e.isMobxAction}function Rt(e,t){var n,r;void 0===t&&(t=h);var i,o=null!=(n=null==(r=t)?void 0:r.name)?n:"Autorun";if(!t.scheduler&&!t.delay)i=new bt(o,(function(){this.track(u)}),t.onError,t.requiresObservable);else{var a=Lt(t),s=!1;i=new bt(o,(function(){s||(s=!0,a((function(){s=!1,i.isDisposed_||i.track(u)})))}),t.onError,t.requiresObservable)}function u(){e(i)}return i.schedule_(),i.getDisposer_()}Object.assign(Ct,Et),Tt.bound=U(kt),Ct.bound=U(Pt);var Dt=function(e){return e()};function Lt(e){return e.scheduler?e.scheduler:e.delay?function(t){return setTimeout(t,e.delay)}:Dt}var Bt="onBO",Mt="onBUO";function It(e,t,n){return Ut(Mt,e,t,n)}function Ut(e,t,n,r){var i="function"===typeof r?ir(t,n):ir(t),o=b(r)?r:n,a=e+"L";return i[a]?i[a].add(o):i[a]=new Set([o]),function(){var e=i[a];e&&(e.delete(o),0===e.size&&delete i[a])}}var Kt="never",Gt="always",qt="observed";function Ht(e){!0===e.isolateGlobalState&&function(){if((ct.pendingReactions.length||ct.inBatch||ct.isRunningReactions)&&r(36),ut=!0,st){var e=o();0===--e.__mobxInstanceCount&&(e.__mobxGlobals=void 0),ct=new at}}();var t=e.useProxies,n=e.enforceActions;if(void 0!==t&&(ct.useProxies=t===Gt||t!==Kt&&"undefined"!==typeof Proxy),"ifavailable"===t&&(ct.verifyProxies=!0),void 0!==n){var i=n===Gt?Gt:n===qt;ct.enforceActions=i,ct.allowStateChanges=!0!==i&&i!==Gt}["computedRequiresReaction","reactionRequiresObservable","observableRequiresReaction","disableErrorBoundaries","safeDescriptors"].forEach((function(t){t in e&&(ct[t]=!!e[t])})),ct.allowStateReads=!ct.observableRequiresReaction,e.reactionScheduler&&function(e){var t=yt;yt=function(n){return e((function(){return t(n)}))}}(e.reactionScheduler)}function zt(e,t,n,r){var i=T(t),o=Hn(e,r)[G];_t();try{E(i).forEach((function(e){o.extend_(e,i[e],!n||(!(e in n)||n[e]))}))}finally{vt()}return e}function Wt(e,t){return Xt(ir(e,t))}function Xt(e){var t,n={name:e.name_};return e.observing_&&e.observing_.length>0&&(n.dependencies=(t=e.observing_,Array.from(new Set(t))).map(Xt)),n}var Ft=0;function Jt(){this.message="FLOW_CANCELLED"}Jt.prototype=Object.create(Error.prototype);var Yt=ee("flow"),$t=ee("flow.bound",{bound:!0}),Qt=Object.assign((function(e,t){if(g(t))return K(e,t,Yt);var n=e,r=n.name||"<unnamed flow>",i=function(){var e,t=arguments,i=++Ft,o=Tt(r+" - runid: "+i+" - init",n).apply(this,t),a=void 0,s=new Promise((function(t,n){var s=0;function u(e){var t;a=void 0;try{t=Tt(r+" - runid: "+i+" - yield "+s++,o.next).call(o,e)}catch(u){return n(u)}l(t)}function c(e){var t;a=void 0;try{t=Tt(r+" - runid: "+i+" - yield "+s++,o.throw).call(o,e)}catch(u){return n(u)}l(t)}function l(e){if(!b(null==e?void 0:e.then))return e.done?t(e.value):(a=Promise.resolve(e.value)).then(u,c);e.then(l,n)}e=n,u(void 0)}));return s.cancel=Tt(r+" - runid: "+i+" - cancel",(function(){try{a&&Zt(a);var t=o.return(void 0),n=Promise.resolve(t.value);n.then(p,p),Zt(n),e(new Jt)}catch(r){e(r)}})),s};return i.isMobXFlow=!0,i}),Yt);function Zt(e){b(e.cancel)&&e.cancel()}function en(e){return!0===(null==e?void 0:e.isMobXFlow)}function tn(e,t){return!!e&&(void 0!==t?!!Xn(e)&&e[G].values_.has(t):Xn(e)||!!e[G]||H(e)||wt(e)||Xe(e))}function nn(e){return tn(e)}function rn(e,t,n){return e.set(t,n),n}function on(e,t){if(null==e||"object"!==typeof e||e instanceof Date||!nn(e))return e;if(qe(e)||Xe(e))return on(e.get(),t);if(t.has(e))return t.get(e);if(Vn(e)){var n=rn(t,e,new Array(e.length));return e.forEach((function(e,r){n[r]=on(e,t)})),n}if(Un(e)){var i=rn(t,e,new Set);return e.forEach((function(e){i.add(on(e,t))})),i}if(Bn(e)){var o=rn(t,e,new Map);return e.forEach((function(e,n){o.set(n,on(e,t))})),o}var a=rn(t,e,{});return function(e){if(Xn(e))return e[G].ownKeys_();r(38)}(e).forEach((function(n){c.propertyIsEnumerable.call(e,n)&&(a[n]=on(e[n],t))})),a}function an(e,t){return on(e,new Map)}function sn(e,t){void 0===t&&(t=void 0),_t();try{return e.apply(t)}finally{vt()}}function un(e){return e[G]}Qt.bound=U($t);var cn={has:function(e,t){return un(e).has_(t)},get:function(e,t){return un(e).get_(t)},set:function(e,t,n){var r;return!!g(t)&&(null==(r=un(e).set_(t,n,!0))||r)},deleteProperty:function(e,t){var n;return!!g(t)&&(null==(n=un(e).delete_(t,!0))||n)},defineProperty:function(e,t,n){var r;return null==(r=un(e).defineProperty_(t,n))||r},ownKeys:function(e){return un(e).ownKeys_()},preventExtensions:function(e){r(13)}};function ln(e){return void 0!==e.interceptors_&&e.interceptors_.length>0}function hn(e,t){var n=e.interceptors_||(e.interceptors_=[]);return n.push(t),d((function(){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function fn(e,t){var n=tt();try{for(var i=[].concat(e.interceptors_||[]),o=0,a=i.length;o<a&&((t=i[o](t))&&!t.type&&r(14),t);o++);return t}finally{nt(n)}}function _n(e){return void 0!==e.changeListeners_&&e.changeListeners_.length>0}function vn(e,t){var n=e.changeListeners_||(e.changeListeners_=[]);return n.push(t),d((function(){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function dn(e,t){var n=tt(),r=e.changeListeners_;if(r){for(var i=0,o=(r=r.slice()).length;i<o;i++)r[i](t);nt(n)}}function pn(e,t,n){var r=Hn(e,n)[G];_t();try{0,null!=t||(t=function(e){return V(e,I)||w(e,I,R({},e[I])),e[I]}(e)),E(t).forEach((function(e){return r.make_(e,t[e])}))}finally{vt()}return e}var bn="splice",gn="update",yn={get:function(e,t){var n=e[G];return t===G?n:"length"===t?n.getArrayLength_():"string"!==typeof t||isNaN(t)?V(wn,t)?wn[t]:e[t]:n.get_(parseInt(t))},set:function(e,t,n){var r=e[G];return"length"===t&&r.setArrayLength_(n),"symbol"===typeof t||isNaN(t)?e[t]=n:r.set_(parseInt(t),n),!0},preventExtensions:function(){r(15)}},mn=function(){function e(e,t,n,r){void 0===e&&(e="ObservableArray"),this.owned_=void 0,this.legacyMode_=void 0,this.atom_=void 0,this.values_=[],this.interceptors_=void 0,this.changeListeners_=void 0,this.enhancer_=void 0,this.dehancer=void 0,this.proxy_=void 0,this.lastKnownLength_=0,this.owned_=n,this.legacyMode_=r,this.atom_=new q(e),this.enhancer_=function(e,n){return t(e,n,"ObservableArray[..]")}}var t=e.prototype;return t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.dehanceValues_=function(e){return void 0!==this.dehancer&&e.length>0?e.map(this.dehancer):e},t.intercept_=function(e){return hn(this,e)},t.observe_=function(e,t){return void 0===t&&(t=!1),t&&e({observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:"splice",index:0,added:this.values_.slice(),addedCount:this.values_.length,removed:[],removedCount:0}),vn(this,e)},t.getArrayLength_=function(){return this.atom_.reportObserved(),this.values_.length},t.setArrayLength_=function(e){("number"!==typeof e||isNaN(e)||e<0)&&r("Out of range: "+e);var t=this.values_.length;if(e!==t)if(e>t){for(var n=new Array(e-t),i=0;i<e-t;i++)n[i]=void 0;this.spliceWithArray_(t,0,n)}else this.spliceWithArray_(e,t-e)},t.updateArrayLength_=function(e,t){e!==this.lastKnownLength_&&r(16),this.lastKnownLength_+=t,this.legacyMode_&&t>0&&nr(e+t+1)},t.spliceWithArray_=function(e,t,n){var r=this;this.atom_;var i=this.values_.length;if(void 0===e?e=0:e>i?e=i:e<0&&(e=Math.max(0,i+e)),t=1===arguments.length?i-e:void 0===t||null===t?0:Math.max(0,Math.min(t,i-e)),void 0===n&&(n=l),ln(this)){var o=fn(this,{object:this.proxy_,type:bn,index:e,removedCount:t,added:n});if(!o)return l;t=o.removedCount,n=o.added}if(n=0===n.length?n:n.map((function(e){return r.enhancer_(e,void 0)})),this.legacyMode_){var a=n.length-t;this.updateArrayLength_(i,a)}var s=this.spliceItemsIntoValues_(e,t,n);return 0===t&&0===n.length||this.notifyArraySplice_(e,n,s),this.dehanceValues_(s)},t.spliceItemsIntoValues_=function(e,t,n){var r;if(n.length<1e4)return(r=this.values_).splice.apply(r,[e,t].concat(n));var i=this.values_.slice(e,e+t),o=this.values_.slice(e+t);this.values_.length=e+n.length-t;for(var a=0;a<n.length;a++)this.values_[e+a]=n[a];for(var s=0;s<o.length;s++)this.values_[e+n.length+s]=o[s];return i},t.notifyArrayChildUpdate_=function(e,t,n){var r=!this.owned_&&!1,i=_n(this),o=i||r?{observableKind:"array",object:this.proxy_,type:gn,debugObjectName:this.atom_.name_,index:e,newValue:t,oldValue:n}:null;this.atom_.reportChanged(),i&&dn(this,o)},t.notifyArraySplice_=function(e,t,n){var r=!this.owned_&&!1,i=_n(this),o=i||r?{observableKind:"array",object:this.proxy_,debugObjectName:this.atom_.name_,type:bn,index:e,removed:n,added:t,removedCount:n.length,addedCount:t.length}:null;this.atom_.reportChanged(),i&&dn(this,o)},t.get_=function(e){if(e<this.values_.length)return this.atom_.reportObserved(),this.dehanceValue_(this.values_[e]);console.warn("[mobx.array] Attempt to read an array index ("+e+") that is out of bounds ("+this.values_.length+"). Please check length first. Out of bound indices will not be tracked by MobX")},t.set_=function(e,t){var n=this.values_;if(e<n.length){this.atom_;var i=n[e];if(ln(this)){var o=fn(this,{type:gn,object:this.proxy_,index:e,newValue:t});if(!o)return;t=o.newValue}(t=this.enhancer_(t,i))!==i&&(n[e]=t,this.notifyArrayChildUpdate_(e,t,i))}else e===n.length?this.spliceWithArray_(e,0,[t]):r(17,e,n.length)},e}();function On(e,t,n,r){void 0===n&&(n="ObservableArray"),void 0===r&&(r=!1),v();var i=new mn(n,t,r,!1);A(i.values_,G,i);var o=new Proxy(i.values_,yn);if(i.proxy_=o,e&&e.length){var a=Ie(!0);i.spliceWithArray_(0,0,e),Ue(a)}return o}var wn={clear:function(){return this.splice(0)},replace:function(e){var t=this[G];return t.spliceWithArray_(0,t.values_.length,e)},toJSON:function(){return this.slice()},splice:function(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=this[G];switch(arguments.length){case 0:return[];case 1:return o.spliceWithArray_(e);case 2:return o.spliceWithArray_(e,t)}return o.spliceWithArray_(e,t,r)},spliceWithArray:function(e,t,n){return this[G].spliceWithArray_(e,t,n)},push:function(){for(var e=this[G],t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.spliceWithArray_(e.values_.length,0,n),e.values_.length},pop:function(){return this.splice(Math.max(this[G].values_.length-1,0),1)[0]},shift:function(){return this.splice(0,1)[0]},unshift:function(){for(var e=this[G],t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.spliceWithArray_(0,0,n),e.values_.length},reverse:function(){return ct.trackingDerivation&&r(37,"reverse"),this.replace(this.slice().reverse()),this},sort:function(){ct.trackingDerivation&&r(37,"sort");var e=this.slice();return e.sort.apply(e,arguments),this.replace(e),this},remove:function(e){var t=this[G],n=t.dehanceValues_(t.values_).indexOf(e);return n>-1&&(this.splice(n,1),!0)}};function An(e,t){"function"===typeof Array.prototype[e]&&(wn[e]=t(e))}function Sn(e){return function(){var t=this[G];t.atom_.reportObserved();var n=t.dehanceValues_(t.values_);return n[e].apply(n,arguments)}}function xn(e){return function(t,n){var r=this,i=this[G];return i.atom_.reportObserved(),i.dehanceValues_(i.values_)[e]((function(e,i){return t.call(n,e,i,r)}))}}function jn(e){return function(){var t=this,n=this[G];n.atom_.reportObserved();var r=n.dehanceValues_(n.values_),i=arguments[0];return arguments[0]=function(e,n,r){return i(e,n,r,t)},r[e].apply(r,arguments)}}An("concat",Sn),An("flat",Sn),An("includes",Sn),An("indexOf",Sn),An("join",Sn),An("lastIndexOf",Sn),An("slice",Sn),An("toString",Sn),An("toLocaleString",Sn),An("every",xn),An("filter",xn),An("find",xn),An("findIndex",xn),An("flatMap",xn),An("forEach",xn),An("map",xn),An("some",xn),An("reduce",jn),An("reduceRight",jn);var kn,En,Pn=S("ObservableArrayAdministration",mn);function Vn(e){return y(e)&&Pn(e[G])}var Tn={},Cn="add",Nn="delete";kn=Symbol.iterator,En=Symbol.toStringTag;var Rn,Dn,Ln=function(){function e(e,t,n){void 0===t&&(t=X),void 0===n&&(n="ObservableMap"),this.enhancer_=void 0,this.name_=void 0,this[G]=Tn,this.data_=void 0,this.hasMap_=void 0,this.keysAtom_=void 0,this.interceptors_=void 0,this.changeListeners_=void 0,this.dehancer=void 0,this.enhancer_=t,this.name_=n,b(Map)||r(18),this.keysAtom_=z("ObservableMap.keys()"),this.data_=new Map,this.hasMap_=new Map,this.merge(e)}var t=e.prototype;return t.has_=function(e){return this.data_.has(e)},t.has=function(e){var t=this;if(!ct.trackingDerivation)return this.has_(e);var n=this.hasMap_.get(e);if(!n){var r=n=new Ge(this.has_(e),F,"ObservableMap.key?",!1);this.hasMap_.set(e,r),It(r,(function(){return t.hasMap_.delete(e)}))}return n.get()},t.set=function(e,t){var n=this.has_(e);if(ln(this)){var r=fn(this,{type:n?gn:Cn,object:this,newValue:t,name:e});if(!r)return this;t=r.newValue}return n?this.updateValue_(e,t):this.addValue_(e,t),this},t.delete=function(e){var t=this;if((this.keysAtom_,ln(this))&&!fn(this,{type:Nn,object:this,name:e}))return!1;if(this.has_(e)){var n=_n(this),r=n?{observableKind:"map",debugObjectName:this.name_,type:Nn,object:this,oldValue:this.data_.get(e).value_,name:e}:null;return sn((function(){var n;t.keysAtom_.reportChanged(),null==(n=t.hasMap_.get(e))||n.setNewValue_(!1),t.data_.get(e).setNewValue_(void 0),t.data_.delete(e)})),n&&dn(this,r),!0}return!1},t.updateValue_=function(e,t){var n=this.data_.get(e);if((t=n.prepareNewValue_(t))!==ct.UNCHANGED){var r=_n(this),i=r?{observableKind:"map",debugObjectName:this.name_,type:gn,object:this,oldValue:n.value_,name:e,newValue:t}:null;0,n.setNewValue_(t),r&&dn(this,i)}},t.addValue_=function(e,t){var n=this;this.keysAtom_,sn((function(){var r,i=new Ge(t,n.enhancer_,"ObservableMap.key",!1);n.data_.set(e,i),t=i.value_,null==(r=n.hasMap_.get(e))||r.setNewValue_(!0),n.keysAtom_.reportChanged()}));var r=_n(this),i=r?{observableKind:"map",debugObjectName:this.name_,type:Cn,object:this,name:e,newValue:t}:null;r&&dn(this,i)},t.get=function(e){return this.has(e)?this.dehanceValue_(this.data_.get(e).get()):this.dehanceValue_(void 0)},t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.keys=function(){return this.keysAtom_.reportObserved(),this.data_.keys()},t.values=function(){var e=this,t=this.keys();return hr({next:function(){var n=t.next(),r=n.done,i=n.value;return{done:r,value:r?void 0:e.get(i)}}})},t.entries=function(){var e=this,t=this.keys();return hr({next:function(){var n=t.next(),r=n.done,i=n.value;return{done:r,value:r?void 0:[i,e.get(i)]}}})},t[kn]=function(){return this.entries()},t.forEach=function(e,t){for(var n,r=M(this);!(n=r()).done;){var i=n.value,o=i[0],a=i[1];e.call(t,a,o,this)}},t.merge=function(e){var t=this;return Bn(e)&&(e=new Map(e)),sn((function(){m(e)?function(e){var t=Object.keys(e);if(!k)return t;var n=Object.getOwnPropertySymbols(e);return n.length?[].concat(t,n.filter((function(t){return c.propertyIsEnumerable.call(e,t)}))):t}(e).forEach((function(n){return t.set(n,e[n])})):Array.isArray(e)?e.forEach((function(e){var n=e[0],r=e[1];return t.set(n,r)})):x(e)?(e.constructor!==Map&&r(19,e),e.forEach((function(e,n){return t.set(n,e)}))):null!==e&&void 0!==e&&r(20,e)})),this},t.clear=function(){var e=this;sn((function(){et((function(){for(var t,n=M(e.keys());!(t=n()).done;){var r=t.value;e.delete(r)}}))}))},t.replace=function(e){var t=this;return sn((function(){for(var n,i=function(e){if(x(e)||Bn(e))return e;if(Array.isArray(e))return new Map(e);if(m(e)){var t=new Map;for(var n in e)t.set(n,e[n]);return t}return r(21,e)}(e),o=new Map,a=!1,s=M(t.data_.keys());!(n=s()).done;){var u=n.value;if(!i.has(u))if(t.delete(u))a=!0;else{var c=t.data_.get(u);o.set(u,c)}}for(var l,h=M(i.entries());!(l=h()).done;){var f=l.value,_=f[0],v=f[1],d=t.data_.has(_);if(t.set(_,v),t.data_.has(_)){var p=t.data_.get(_);o.set(_,p),d||(a=!0)}}if(!a)if(t.data_.size!==o.size)t.keysAtom_.reportChanged();else for(var b=t.data_.keys(),g=o.keys(),y=b.next(),O=g.next();!y.done;){if(y.value!==O.value){t.keysAtom_.reportChanged();break}y=b.next(),O=g.next()}t.data_=o})),this},t.toString=function(){return"[object ObservableMap]"},t.toJSON=function(){return Array.from(this)},t.observe_=function(e,t){return vn(this,e)},t.intercept_=function(e){return hn(this,e)},N(e,[{key:"size",get:function(){return this.keysAtom_.reportObserved(),this.data_.size}},{key:En,get:function(){return"Map"}}]),e}(),Bn=S("ObservableMap",Ln);var Mn={};Rn=Symbol.iterator,Dn=Symbol.toStringTag;var In=function(){function e(e,t,n){void 0===t&&(t=X),void 0===n&&(n="ObservableSet"),this.name_=void 0,this[G]=Mn,this.data_=new Set,this.atom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.dehancer=void 0,this.enhancer_=void 0,this.name_=n,b(Set)||r(22),this.atom_=z(this.name_),this.enhancer_=function(e,r){return t(e,r,n)},e&&this.replace(e)}var t=e.prototype;return t.dehanceValue_=function(e){return void 0!==this.dehancer?this.dehancer(e):e},t.clear=function(){var e=this;sn((function(){et((function(){for(var t,n=M(e.data_.values());!(t=n()).done;){var r=t.value;e.delete(r)}}))}))},t.forEach=function(e,t){for(var n,r=M(this);!(n=r()).done;){var i=n.value;e.call(t,i,i,this)}},t.add=function(e){var t=this;if((this.atom_,ln(this))&&!fn(this,{type:Cn,object:this,newValue:e}))return this;if(!this.has(e)){sn((function(){t.data_.add(t.enhancer_(e,void 0)),t.atom_.reportChanged()}));var n=!1,r=_n(this),i=r?{observableKind:"set",debugObjectName:this.name_,type:Cn,object:this,newValue:e}:null;n,r&&dn(this,i)}return this},t.delete=function(e){var t=this;if(ln(this)&&!fn(this,{type:Nn,object:this,oldValue:e}))return!1;if(this.has(e)){var n=_n(this),r=n?{observableKind:"set",debugObjectName:this.name_,type:Nn,object:this,oldValue:e}:null;return sn((function(){t.atom_.reportChanged(),t.data_.delete(e)})),n&&dn(this,r),!0}return!1},t.has=function(e){return this.atom_.reportObserved(),this.data_.has(this.dehanceValue_(e))},t.entries=function(){var e=0,t=Array.from(this.keys()),n=Array.from(this.values());return hr({next:function(){var r=e;return e+=1,r<n.length?{value:[t[r],n[r]],done:!1}:{done:!0}}})},t.keys=function(){return this.values()},t.values=function(){this.atom_.reportObserved();var e=this,t=0,n=Array.from(this.data_.values());return hr({next:function(){return t<n.length?{value:e.dehanceValue_(n[t++]),done:!1}:{done:!0}}})},t.replace=function(e){var t=this;return Un(e)&&(e=new Set(e)),sn((function(){Array.isArray(e)||j(e)?(t.clear(),e.forEach((function(e){return t.add(e)}))):null!==e&&void 0!==e&&r("Cannot initialize set from "+e)})),this},t.observe_=function(e,t){return vn(this,e)},t.intercept_=function(e){return hn(this,e)},t.toJSON=function(){return Array.from(this)},t.toString=function(){return"[object ObservableSet]"},t[Rn]=function(){return this.values()},N(e,[{key:"size",get:function(){return this.atom_.reportObserved(),this.data_.size}},{key:Dn,get:function(){return"Set"}}]),e}(),Un=S("ObservableSet",In),Kn=Object.create(null),Gn="remove",qn=function(){function e(e,t,n,r){void 0===t&&(t=new Map),void 0===r&&(r=he),this.target_=void 0,this.values_=void 0,this.name_=void 0,this.defaultAnnotation_=void 0,this.keysAtom_=void 0,this.changeListeners_=void 0,this.interceptors_=void 0,this.proxy_=void 0,this.isPlainObject_=void 0,this.appliedAnnotations_=void 0,this.pendingKeys_=void 0,this.target_=e,this.values_=t,this.name_=n,this.defaultAnnotation_=r,this.keysAtom_=new q("ObservableObject.keys"),this.isPlainObject_=m(this.target_)}var t=e.prototype;return t.getObservablePropValue_=function(e){return this.values_.get(e).get()},t.setObservablePropValue_=function(e,t){var n=this.values_.get(e);if(n instanceof We)return n.set(t),!0;if(ln(this)){var r=fn(this,{type:gn,object:this.proxy_||this.target_,name:e,newValue:t});if(!r)return null;t=r.newValue}if((t=n.prepareNewValue_(t))!==ct.UNCHANGED){var i=_n(this),o=i?{type:gn,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,oldValue:n.value_,name:e,newValue:t}:null;0,n.setNewValue_(t),i&&dn(this,o)}return!0},t.get_=function(e){return ct.trackingDerivation&&!V(this.target_,e)&&this.has_(e),this.target_[e]},t.set_=function(e,t,n){return void 0===n&&(n=!1),V(this.target_,e)?this.values_.has(e)?this.setObservablePropValue_(e,t):n?Reflect.set(this.target_,e,t):(this.target_[e]=t,!0):this.extend_(e,{value:t,enumerable:!0,writable:!0,configurable:!0},this.defaultAnnotation_,n)},t.has_=function(e){if(!ct.trackingDerivation)return e in this.target_;this.pendingKeys_||(this.pendingKeys_=new Map);var t=this.pendingKeys_.get(e);return t||(t=new Ge(e in this.target_,F,"ObservableObject.key?",!1),this.pendingKeys_.set(e,t)),t.get()},t.make_=function(e,t){if(!0===t&&(t=this.defaultAnnotation_),!1!==t){if(Jn(this,t,e),!(e in this.target_)){var n;if(null==(n=this.target_[I])?void 0:n[e])return;r(1,t.annotationType_,this.name_+"."+e.toString())}for(var i=this.target_;i&&i!==c;){var o=s(i,e);if(o){var a=t.make_(this,e,o,i);if(0===a)return;if(1===a)break}i=Object.getPrototypeOf(i)}Fn(this,t,e)}},t.extend_=function(e,t,n,r){if(void 0===r&&(r=!1),!0===n&&(n=this.defaultAnnotation_),!1===n)return this.defineProperty_(e,t,r);Jn(this,n,e);var i=n.extend_(this,e,t,r);return i&&Fn(this,n,e),i},t.defineProperty_=function(e,t,n){void 0===n&&(n=!1);try{_t();var r=this.delete_(e);if(!r)return r;if(ln(this)){var i=fn(this,{object:this.proxy_||this.target_,name:e,type:Cn,newValue:t.value});if(!i)return null;var o=i.newValue;t.value!==o&&(t=R({},t,{value:o}))}if(n){if(!Reflect.defineProperty(this.target_,e,t))return!1}else u(this.target_,e,t);this.notifyPropertyAddition_(e,t.value)}finally{vt()}return!0},t.defineObservableProperty_=function(e,t,n,r){void 0===r&&(r=!1);try{_t();var i=this.delete_(e);if(!i)return i;if(ln(this)){var o=fn(this,{object:this.proxy_||this.target_,name:e,type:Cn,newValue:t});if(!o)return null;t=o.newValue}var a=Wn(e),s={configurable:!ct.safeDescriptors||this.isPlainObject_,enumerable:!0,get:a.get,set:a.set};if(r){if(!Reflect.defineProperty(this.target_,e,s))return!1}else u(this.target_,e,s);var c=new Ge(t,n,"ObservableObject.key",!1);this.values_.set(e,c),this.notifyPropertyAddition_(e,c.value_)}finally{vt()}return!0},t.defineComputedProperty_=function(e,t,n){void 0===n&&(n=!1);try{_t();var r=this.delete_(e);if(!r)return r;if(ln(this))if(!fn(this,{object:this.proxy_||this.target_,name:e,type:Cn,newValue:void 0}))return null;t.name||(t.name="ObservableObject.key"),t.context=this.proxy_||this.target_;var i=Wn(e),o={configurable:!ct.safeDescriptors||this.isPlainObject_,enumerable:!1,get:i.get,set:i.set};if(n){if(!Reflect.defineProperty(this.target_,e,o))return!1}else u(this.target_,e,o);this.values_.set(e,new We(t)),this.notifyPropertyAddition_(e,void 0)}finally{vt()}return!0},t.delete_=function(e,t){if(void 0===t&&(t=!1),!V(this.target_,e))return!0;if(ln(this)&&!fn(this,{object:this.proxy_||this.target_,name:e,type:Gn}))return null;try{var n,r;_t();var i,o=_n(this),a=this.values_.get(e),u=void 0;if(!a&&o)u=null==(i=s(this.target_,e))?void 0:i.value;if(t){if(!Reflect.deleteProperty(this.target_,e))return!1}else delete this.target_[e];if(a&&(this.values_.delete(e),a instanceof Ge&&(u=a.value_),pt(a)),this.keysAtom_.reportChanged(),null==(n=this.pendingKeys_)||null==(r=n.get(e))||r.set(e in this.target_),o){var c={type:Gn,observableKind:"object",object:this.proxy_||this.target_,debugObjectName:this.name_,oldValue:u,name:e};0,o&&dn(this,c)}}finally{vt()}return!0},t.observe_=function(e,t){return vn(this,e)},t.intercept_=function(e){return hn(this,e)},t.notifyPropertyAddition_=function(e,t){var n,r,i=_n(this);if(i){var o=i?{type:Cn,observableKind:"object",debugObjectName:this.name_,object:this.proxy_||this.target_,name:e,newValue:t}:null;0,i&&dn(this,o)}null==(n=this.pendingKeys_)||null==(r=n.get(e))||r.set(!0),this.keysAtom_.reportChanged()},t.ownKeys_=function(){return this.keysAtom_.reportObserved(),E(this.target_)},t.keys_=function(){return this.keysAtom_.reportObserved(),Object.keys(this.target_)},e}();function Hn(e,t){var n;if(V(e,G))return e;var r=null!=(n=null==t?void 0:t.name)?n:"ObservableObject",i=new qn(e,new Map,String(r),function(e){var t;return e?null!=(t=e.defaultDecorator)?t:fe(e):void 0}(t));return w(e,G,i),e}var zn=S("ObservableObjectAdministration",qn);function Wn(e){return Kn[e]||(Kn[e]={get:function(){return this[G].getObservablePropValue_(e)},set:function(t){return this[G].setObservablePropValue_(e,t)}})}function Xn(e){return!!y(e)&&zn(e[G])}function Fn(e,t,n){var r;null==(r=e.target_[I])||delete r[n]}function Jn(e,t,n){}var Yn,$n,Qn=0,Zn=function(){};Yn=Zn,$n=Array.prototype,Object.setPrototypeOf?Object.setPrototypeOf(Yn.prototype,$n):void 0!==Yn.prototype.__proto__?Yn.prototype.__proto__=$n:Yn.prototype=$n;var er=function(e){function t(t,n,r,i){var o;void 0===r&&(r="ObservableArray"),void 0===i&&(i=!1),o=e.call(this)||this;var a=new mn(r,n,i,!0);if(a.proxy_=L(o),A(L(o),G,a),t&&t.length){var s=Ie(!0);o.spliceWithArray(0,0,t),Ue(s)}return o}D(t,e);var n=t.prototype;return n.concat=function(){this[G].atom_.reportObserved();for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.prototype.concat.apply(this.slice(),t.map((function(e){return Vn(e)?e.slice():e})))},n[Symbol.iterator]=function(){var e=this,t=0;return hr({next:function(){return t<e.length?{value:e[t++],done:!1}:{done:!0,value:void 0}}})},N(t,[{key:"length",get:function(){return this[G].getArrayLength_()},set:function(e){this[G].setArrayLength_(e)}},{key:Symbol.toStringTag,get:function(){return"Array"}}]),t}(Zn);function tr(e){u(er.prototype,""+e,function(e){return{enumerable:!1,configurable:!0,get:function(){return this[G].get_(e)},set:function(t){this[G].set_(e,t)}}}(e))}function nr(e){if(e>Qn){for(var t=Qn;t<e+100;t++)tr(t);Qn=e}}function rr(e,t,n){return new er(e,t,n)}function ir(e,t){if("object"===typeof e&&null!==e){if(Vn(e))return void 0!==t&&r(23),e[G].atom_;if(Un(e))return e[G];if(Bn(e)){if(void 0===t)return e.keysAtom_;var n=e.data_.get(t)||e.hasMap_.get(t);return n||r(25,t,ar(e)),n}if(Xn(e)){if(!t)return r(26);var i=e[G].values_.get(t);return i||r(27,t,ar(e)),i}if(H(e)||Xe(e)||wt(e))return e}else if(b(e)&&wt(e[G]))return e[G];r(28)}function or(e,t){return e||r(29),void 0!==t?or(ir(e,t)):H(e)||Xe(e)||wt(e)||Bn(e)||Un(e)?e:e[G]?e[G]:void r(24,e)}function ar(e,t){var n;if(void 0!==t)n=ir(e,t);else{if(Nt(e))return e.name;n=Xn(e)||Bn(e)||Un(e)?or(e):ir(e)}return n.name_}Object.entries(wn).forEach((function(e){var t=e[0],n=e[1];"concat"!==t&&w(er.prototype,t,n)})),nr(1e3);var sr=c.toString;function ur(e,t,n){return void 0===n&&(n=-1),cr(e,t,n)}function cr(e,t,n,r,i){if(e===t)return 0!==e||1/e===1/t;if(null==e||null==t)return!1;if(e!==e)return t!==t;var o=typeof e;if(!b(o)&&"object"!==o&&"object"!=typeof t)return!1;var a=sr.call(e);if(a!==sr.call(t))return!1;switch(a){case"[object RegExp]":case"[object String]":return""+e===""+t;case"[object Number]":return+e!==+e?+t!==+t:0===+e?1/+e===1/t:+e===+t;case"[object Date]":case"[object Boolean]":return+e===+t;case"[object Symbol]":return"undefined"!==typeof Symbol&&Symbol.valueOf.call(e)===Symbol.valueOf.call(t);case"[object Map]":case"[object Set]":n>=0&&n++}e=lr(e),t=lr(t);var s="[object Array]"===a;if(!s){if("object"!=typeof e||"object"!=typeof t)return!1;var u=e.constructor,c=t.constructor;if(u!==c&&!(b(u)&&u instanceof u&&b(c)&&c instanceof c)&&"constructor"in e&&"constructor"in t)return!1}if(0===n)return!1;n<0&&(n=-1),i=i||[];for(var l=(r=r||[]).length;l--;)if(r[l]===e)return i[l]===t;if(r.push(e),i.push(t),s){if((l=e.length)!==t.length)return!1;for(;l--;)if(!cr(e[l],t[l],n-1,r,i))return!1}else{var h,f=Object.keys(e);if(l=f.length,Object.keys(t).length!==l)return!1;for(;l--;)if(!V(t,h=f[l])||!cr(e[h],t[h],n-1,r,i))return!1}return r.pop(),i.pop(),!0}function lr(e){return Vn(e)?e.slice():x(e)||Bn(e)||j(e)||Un(e)?Array.from(e.entries()):e}function hr(e){return e[Symbol.iterator]=fr,e}function fr(){return this}["Symbol","Map","Set"].forEach((function(e){"undefined"===typeof o()[e]&&r("MobX requires global '"+e+"' to be available or polyfilled")})),"object"===typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:function(e){return console.warn("[mobx.spy] Is a no-op in production builds"),function(){}},extras:{getDebugName:ar},$mobx:G})}}]);
//# sourceMappingURL=mobx.c842d7a276574ac4b3737033602a64ce.js.map