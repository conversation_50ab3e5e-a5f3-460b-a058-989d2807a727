"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[537],{4475:function(e,t,a){a.d(t,{x7:function(){return L},ZP:function(){return R}});var o=a(7363),r=a(8384),i=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,s=(()=>{let e=0;return()=>(++e).toString()})(),n=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),l=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:a}=t;return l(e,{type:e.toasts.find(e=>e.id===a.id)?1:0,toast:a});case 3:let{toastId:o}=t;return{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+r}))}}},d=[],c={toasts:[],pausedAt:void 0},u=e=>{c=l(c,e),d.forEach(e=>{e(c)})},p={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},m=e=>(t,a)=>{let o=((e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||s()}))(t,e,a);return u({type:2,toast:o}),o.id},f=(e,t)=>m("blank")(e,t);f.error=m("error"),f.success=m("success"),f.loading=m("loading"),f.custom=m("custom"),f.dismiss=e=>{u({type:3,toastId:e})},f.remove=e=>u({type:4,toastId:e}),f.promise=(e,t,a)=>{let o=f.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let r=t.success?i(t.success,e):void 0;return r?f.success(r,{id:o,...a,...null==a?void 0:a.success}):f.dismiss(o),e}).catch(e=>{let r=t.error?i(t.error,e):void 0;r?f.error(r,{id:o,...a,...null==a?void 0:a.error}):f.dismiss(o)}),e};var y=(e,t)=>{u({type:1,toast:{id:e,height:t}})},h=()=>{u({type:5,time:Date.now()})},g=new Map,b=e=>{let{toasts:t,pausedAt:a}=((e={})=>{let[t,a]=(0,o.useState)(c),r=(0,o.useRef)(c);(0,o.useEffect)(()=>(r.current!==c&&a(c),d.push(a),()=>{let e=d.indexOf(a);e>-1&&d.splice(e,1)}),[]);let i=t.toasts.map(t=>{var a,o,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(o=e[t.type])?void 0:o.duration)||(null==e?void 0:e.duration)||p[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}});return{...t,toasts:i}})(e);(0,o.useEffect)(()=>{if(a)return;let e=Date.now(),o=t.map(t=>{if(t.duration===1/0)return;let a=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(a<0))return setTimeout(()=>f.dismiss(t.id),a);t.visible&&f.dismiss(t.id)});return()=>{o.forEach(e=>e&&clearTimeout(e))}},[t,a]);let r=(0,o.useCallback)(()=>{a&&u({type:6,time:Date.now()})},[a]),i=(0,o.useCallback)((e,a)=>{let{reverseOrder:o=!1,gutter:r=8,defaultPosition:i}=a||{},s=t.filter(t=>(t.position||i)===(e.position||i)&&t.height),n=s.findIndex(t=>t.id===e.id),l=s.filter((e,t)=>t<n&&e.visible).length;return s.filter(e=>e.visible).slice(...o?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+r,0)},[t]);return(0,o.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)((e,t=1e3)=>{if(g.has(e))return;let a=setTimeout(()=>{g.delete(e),u({type:4,toastId:e})},t);g.set(e,a)})(e.id,e.removeDelay);else{let t=g.get(e.id);t&&(clearTimeout(t),g.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:y,startPause:h,endPause:r,calculateOffset:i}}},v=r.F4`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,x=r.F4`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,w=r.F4`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,E=(0,r.zo)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${v} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${x} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${w} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,z=r.F4`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,$=(0,r.zo)("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${z} 1s linear infinite;
`,k=r.F4`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,D=r.F4`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,F=(0,r.zo)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${k} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${D} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,C=(0,r.zo)("div")`
  position: absolute;
`,P=(0,r.zo)("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,I=r.F4`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,O=(0,r.zo)("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${I} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,A=({toast:e})=>{let{icon:t,type:a,iconTheme:r}=e;return void 0!==t?"string"==typeof t?o.createElement(O,null,t):t:"blank"===a?null:o.createElement(P,null,o.createElement($,{...r}),"loading"!==a&&o.createElement(C,null,"error"===a?o.createElement(E,{...r}):o.createElement(F,{...r})))},N=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,_=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,M=(0,r.zo)("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,T=(0,r.zo)("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,j=o.memo(({toast:e,position:t,style:a,children:s})=>{let l=e.height?((e,t)=>{let a=e.includes("top")?1:-1,[o,i]=n()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[N(a),_(a)];return{animation:t?`${(0,r.F4)(o)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${(0,r.F4)(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},d=o.createElement(A,{toast:e}),c=o.createElement(T,{...e.ariaProps},i(e.message,e));return o.createElement(M,{className:e.className,style:{...l,...a,...e.style}},"function"==typeof s?s({icon:d,message:c}):o.createElement(o.Fragment,null,d,c))});(0,r.cY)(o.createElement);var H=({id:e,className:t,style:a,onHeightUpdate:r,children:i})=>{let s=o.useCallback(t=>{if(t){let a=()=>{let a=t.getBoundingClientRect().height;r(e,a)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return o.createElement("div",{ref:s,className:t,style:a},i)},S=r.iv`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,L=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:r,children:s,containerStyle:l,containerClassName:d})=>{let{toasts:c,handlers:u}=b(a);return o.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...l},className:d,onMouseEnter:u.startPause,onMouseLeave:u.endPause},c.map(a=>{let l=a.position||t,d=((e,t)=>{let a=e.includes("top"),o=a?{top:0}:{bottom:0},r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:n()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...o,...r}})(l,u.calculateOffset(a,{reverseOrder:e,gutter:r,defaultPosition:t}));return o.createElement(H,{id:a.id,key:a.id,onHeightUpdate:u.updateHeight,className:a.visible?S:"",style:d},"custom"===a.type?i(a.message,a):s?s(a):o.createElement(j,{toast:a,position:l}))}))},R=f}}]);
//# sourceMappingURL=react-hot-toast.51498d637b1dd919f6cfa17f04f1be33.js.map