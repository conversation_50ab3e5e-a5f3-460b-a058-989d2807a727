"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[313],{2704:function(e){e.exports=(e,t)=>{if("string"!==typeof e||"string"!==typeof t)throw new TypeError("Expected the arguments to be of type `string`");if(""===t)return[e];const n=e.indexOf(t);return-1===n?[e]:[e.slice(0,n),e.slice(n+t.length)]}}}]);
//# sourceMappingURL=split-on-first.d84e17a19ded9bbce741293aa1b767f5.js.map