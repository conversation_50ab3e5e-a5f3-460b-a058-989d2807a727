"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[333],{5145:function(e,n,t){var o,r=t(7363),i=(o=r)&&"object"===typeof o&&"default"in o?o.default:o;function u(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var c=!("undefined"===typeof window||!window.document||!window.document.createElement);e.exports=function(e,n,t){if("function"!==typeof e)throw new Error("Expected reducePropsToState to be a function.");if("function"!==typeof n)throw new Error("Expected handleStateChangeOnClient to be a function.");if("undefined"!==typeof t&&"function"!==typeof t)throw new Error("Expected mapStateOnServer to either be undefined or a function.");return function(o){if("function"!==typeof o)throw new Error("Expected WrappedComponent to be a React component.");var p,a=[];function f(){p=e(a.map((function(e){return e.props}))),d.canUseDOM?n(p):t&&(p=t(p))}var d=function(e){var n,t;function r(){return e.apply(this,arguments)||this}t=e,(n=r).prototype=Object.create(t.prototype),n.prototype.constructor=n,n.__proto__=t,r.peek=function(){return p},r.rewind=function(){if(r.canUseDOM)throw new Error("You may only call rewind() on the server. Call peek() to read the current state.");var e=p;return p=void 0,a=[],e};var u=r.prototype;return u.UNSAFE_componentWillMount=function(){a.push(this),f()},u.componentDidUpdate=function(){f()},u.componentWillUnmount=function(){var e=a.indexOf(this);a.splice(e,1),f()},u.render=function(){return i.createElement(o,this.props)},r}(r.PureComponent);return u(d,"displayName","SideEffect("+function(e){return e.displayName||e.name||"Component"}(o)+")"),u(d,"canUseDOM",c),d}}}}]);
//# sourceMappingURL=react-side-effect.0322710b831233d01424ea3a12cdd15a.js.map