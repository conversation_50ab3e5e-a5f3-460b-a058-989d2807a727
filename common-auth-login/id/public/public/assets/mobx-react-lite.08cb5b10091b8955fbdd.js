"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[795],{3315:function(e,r,t){t.d(r,{Qj:function(){return S},FY:function(){return p},Pi:function(){return j}});var n=t(9621),o=t(7363),i=t.n(o);if(!o.useState)throw new Error("mobx-react-lite requires React with Hooks support");if(!n.rC)throw new Error("mobx-react-lite@3 requires mobx at least version 6 to be available");var a=t(1533);function u(e){e()}function c(e){return(0,n.Gf)(e)}var f=function(){function e(e){var r=this;Object.defineProperty(this,"finalize",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"registrations",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"sweepTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sweep",{enumerable:!0,configurable:!0,writable:!0,value:function(e){void 0===e&&(e=1e4),clearTimeout(r.sweepTimeout),r.sweepTimeout=void 0;var t=Date.now();r.registrations.forEach((function(n,o){t-n.registeredAt>=e&&(r.finalize(n.value),r.registrations.delete(o))})),r.registrations.size>0&&r.scheduleSweep()}}),Object.defineProperty(this,"finalizeAllImmediately",{enumerable:!0,configurable:!0,writable:!0,value:function(){r.sweep(0)}})}return Object.defineProperty(e.prototype,"register",{enumerable:!1,configurable:!0,writable:!0,value:function(e,r,t){this.registrations.set(t,{value:r,registeredAt:Date.now()}),this.scheduleSweep()}}),Object.defineProperty(e.prototype,"unregister",{enumerable:!1,configurable:!0,writable:!0,value:function(e){this.registrations.delete(e)}}),Object.defineProperty(e.prototype,"scheduleSweep",{enumerable:!1,configurable:!0,writable:!0,value:function(){void 0===this.sweepTimeout&&(this.sweepTimeout=setTimeout(this.sweep,1e4))}}),e}(),l=new("undefined"!==typeof FinalizationRegistry?FinalizationRegistry:f)((function(e){var r;null===(r=e.reaction)||void 0===r||r.dispose(),e.reaction=null})),s=!1;function p(){return s}var d=function(e,r){var t="function"===typeof Symbol&&e[Symbol.iterator];if(!t)return e;var n,o,i=t.call(e),a=[];try{for(;(void 0===r||r-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(u){o={error:u}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(o)throw o.error}}return a};function b(e){return"observer".concat(e)}var m=function(){};function y(){return new m}function v(e,r){if(void 0===r&&(r="observed"),p())return e();var t=d(i().useState(y),1)[0],o=d(i().useState(),2)[1],a=function(){return o([])},u=i().useRef(null);u.current||(u.current={reaction:null,mounted:!1,changedBeforeMount:!1});var f,s,m=u.current;if(m.reaction||(m.reaction=new n.le(b(r),(function(){m.mounted?a():m.changedBeforeMount=!0})),l.register(t,m,m)),i().useDebugValue(m.reaction,c),i().useEffect((function(){return l.unregister(m),m.mounted=!0,m.reaction?m.changedBeforeMount&&(m.changedBeforeMount=!1,a()):(m.reaction=new n.le(b(r),(function(){a()})),a()),function(){m.reaction.dispose(),m.reaction=null,m.mounted=!1,m.changedBeforeMount=!1}}),[]),m.reaction.track((function(){try{f=e()}catch(r){s=r}})),s)throw s;return f}var w="function"===typeof Symbol&&Symbol.for,h=w?Symbol.for("react.forward_ref"):"function"===typeof o.forwardRef&&(0,o.forwardRef)((function(e){return null})).$$typeof,g=w?Symbol.for("react.memo"):"function"===typeof o.memo&&(0,o.memo)((function(e){return null})).$$typeof;function j(e,r){var t;if(g&&e.$$typeof===g)throw new Error("[mobx-react-lite] You are trying to use `observer` on a function component wrapped in either another `observer` or `React.memo`. The observer already applies 'React.memo' for you.");if(p())return e;var n=null!==(t=null===r||void 0===r?void 0:r.forwardRef)&&void 0!==t&&t,i=e,a=e.displayName||e.name;if(h&&e.$$typeof===h&&(n=!0,"function"!==typeof(i=e.render)))throw new Error("[mobx-react-lite] `render` property of ForwardRef was not a function");var u,c,f=function(e,r){return v((function(){return i(e,r)}),a)};return""!==a&&(f.displayName=a),e.contextTypes&&(f.contextTypes=e.contextTypes),n&&(f=(0,o.forwardRef)(f)),f=(0,o.memo)(f),u=e,c=f,Object.keys(u).forEach((function(e){O[e]||Object.defineProperty(c,e,Object.getOwnPropertyDescriptor(u,e))})),f}var O={$$typeof:!0,render:!0,compare:!0,type:!0,displayName:!0};function S(e){var r=e.children,t=e.render,n=r||t;return"function"!==typeof n?null:v(n)}S.displayName="Observer";var P,R;(R=a.unstable_batchedUpdates)||(R=u),(0,n.jQ)({reactionScheduler:R});P=l.finalizeAllImmediately}}]);
//# sourceMappingURL=mobx-react-lite.6fa7b63711b6bf9906c302ad0d48906e.js.map