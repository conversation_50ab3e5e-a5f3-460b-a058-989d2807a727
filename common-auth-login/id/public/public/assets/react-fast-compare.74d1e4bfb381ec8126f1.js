"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[424],{5439:function(e){var r=Array.isArray,t=Object.keys,n=Object.prototype.hasOwnProperty,f="undefined"!==typeof Element;function o(e,a){if(e===a)return!0;if(e&&a&&"object"==typeof e&&"object"==typeof a){var i,c,u,s=r(e),m=r(a);if(s&&m){if((c=e.length)!=a.length)return!1;for(i=c;0!==i--;)if(!o(e[i],a[i]))return!1;return!0}if(s!=m)return!1;var g=e instanceof Date,h=a instanceof Date;if(g!=h)return!1;if(g&&h)return e.getTime()==a.getTime();var l=e instanceof RegExp,p=a instanceof RegExp;if(l!=p)return!1;if(l&&p)return e.toString()==a.toString();var y=t(e);if((c=y.length)!==t(a).length)return!1;for(i=c;0!==i--;)if(!n.call(a,y[i]))return!1;if(f&&e instanceof Element&&a instanceof Element)return e===a;for(i=c;0!==i--;)if(("_owner"!==(u=y[i])||!e.$$typeof)&&!o(e[u],a[u]))return!1;return!0}return e!==e&&a!==a}e.exports=function(e,r){try{return o(e,r)}catch(t){if(t.message&&t.message.match(/stack|recursion/i)||-2146828260===t.number)return console.warn("Warning: react-fast-compare does not handle circular references.",t.name,t.message),!1;throw t}}}}]);
//# sourceMappingURL=react-fast-compare.ff080fd0b322f37be3daf0248f0db1f6.js.map