(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[457],{9455:function(e,t,r){var n=r(9677);e.exports=x,e.exports.parse=i,e.exports.compile=function(e,t){return c(i(e,t),t)},e.exports.tokensToFunction=c,e.exports.tokensToRegExp=g;var o=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function i(e,t){for(var r,n=[],i=0,p=0,u="",c=t&&t.delimiter||"/";null!=(r=o.exec(e));){var l=r[0],s=r[1],h=r.index;if(u+=e.slice(p,h),p=h+l.length,s)u+=s[1];else{var g=e[p],x=r[2],d=r[3],m=r[4],v=r[5],y=r[6],w=r[7];u&&(n.push(u),u="");var E=null!=x&&null!=g&&g!==x,b="+"===y||"*"===y,k="?"===y||"*"===y,R=x||c,$=m||v,C=x||("string"===typeof n[n.length-1]?n[n.length-1]:"");n.push({name:d||i++,prefix:x||"",delimiter:R,optional:k,repeat:b,partial:E,asterisk:!!w,pattern:$?f($):w?".*":a(R,C)})}}return p<e.length&&(u+=e.substr(p)),u&&n.push(u),n}function a(e,t){return!t||t.indexOf(e)>-1?"[^"+l(e)+"]+?":l(t)+"|(?:(?!"+l(t)+")[^"+l(e)+"])+?"}function p(e){return encodeURI(e).replace(/[\/?#]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}function u(e){return encodeURI(e).replace(/[?#]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})}function c(e,t){for(var r=new Array(e.length),o=0;o<e.length;o++)"object"===typeof e[o]&&(r[o]=new RegExp("^(?:"+e[o].pattern+")$",h(t)));return function(t,o){for(var i="",a=t||{},c=(o||{}).pretty?p:encodeURIComponent,l=0;l<e.length;l++){var f=e[l];if("string"!==typeof f){var s,h=a[f.name];if(null==h){if(f.optional){f.partial&&(i+=f.prefix);continue}throw new TypeError('Expected "'+f.name+'" to be defined')}if(n(h)){if(!f.repeat)throw new TypeError('Expected "'+f.name+'" to not repeat, but received `'+JSON.stringify(h)+"`");if(0===h.length){if(f.optional)continue;throw new TypeError('Expected "'+f.name+'" to not be empty')}for(var g=0;g<h.length;g++){if(s=c(h[g]),!r[l].test(s))throw new TypeError('Expected all "'+f.name+'" to match "'+f.pattern+'", but received `'+JSON.stringify(s)+"`");i+=(0===g?f.prefix:f.delimiter)+s}}else{if(s=f.asterisk?u(h):c(h),!r[l].test(s))throw new TypeError('Expected "'+f.name+'" to match "'+f.pattern+'", but received "'+s+'"');i+=f.prefix+s}}else i+=f}return i}}function l(e){return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function f(e){return e.replace(/([=!:$\/()])/g,"\\$1")}function s(e,t){return e.keys=t,e}function h(e){return e&&e.sensitive?"":"i"}function g(e,t,r){n(t)||(r=t||r,t=[]);for(var o=(r=r||{}).strict,i=!1!==r.end,a="",p=0;p<e.length;p++){var u=e[p];if("string"===typeof u)a+=l(u);else{var c=l(u.prefix),f="(?:"+u.pattern+")";t.push(u),u.repeat&&(f+="(?:"+c+f+")*"),a+=f=u.optional?u.partial?c+"("+f+")?":"(?:"+c+"("+f+"))?":c+"("+f+")"}}var g=l(r.delimiter||"/"),x=a.slice(-g.length)===g;return o||(a=(x?a.slice(0,-g.length):a)+"(?:"+g+"(?=$))?"),a+=i?"$":o&&x?"":"(?="+g+"|$)",s(new RegExp("^"+a,h(r)),t)}function x(e,t,r){return n(t)||(r=t||r,t=[]),r=r||{},e instanceof RegExp?function(e,t){var r=e.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)t.push({name:n,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return s(e,t)}(e,t):n(e)?function(e,t,r){for(var n=[],o=0;o<e.length;o++)n.push(x(e[o],t,r).source);return s(new RegExp("(?:"+n.join("|")+")",h(r)),t)}(e,t,r):function(e,t,r){return g(i(e,r),t,r)}(e,t,r)}},9677:function(e){e.exports=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)}}}]);
//# sourceMappingURL=path-to-regexp.275fc4db3863e2e19c533ff97b736c52.js.map