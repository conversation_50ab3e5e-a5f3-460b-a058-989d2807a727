"use strict";(self.webpackChunksr_common_auth=self.webpackChunksr_common_auth||[]).push([[969],{1970:function(r,e,t){t.d(e,{Z:function(){return u}});var n=t(7363),o=t(2652),a=t.n(o),i=t(1281),c=t.n(i);function d(){return d=Object.assign||function(r){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])}return r},d.apply(this,arguments)}var s={},p=0;function u(r,e){return e=e||{},function(t){var o=t.displayName||t.name||"Component",i=function(o){var a,i;function c(r,e){var t;return(t=o.call(this,r,e)||this).state={},t.__scriptURL="",t}i=o,(a=c).prototype=Object.create(i.prototype),a.prototype.constructor=a,a.__proto__=i;var d=c.prototype;return d.asyncScriptLoaderGetScriptLoaderID=function(){return this.__scriptLoaderID||(this.__scriptLoaderID="async-script-loader-"+p++),this.__scriptLoaderID},d.setupScriptURL=function(){return this.__scriptURL="function"===typeof r?r():r,this.__scriptURL},d.asyncScriptLoaderHandleLoad=function(r){var e=this;this.setState(r,(function(){return e.props.asyncScriptOnLoad&&e.props.asyncScriptOnLoad(e.state)}))},d.asyncScriptLoaderTriggerOnScriptLoaded=function(){var r=s[this.__scriptURL];if(!r||!r.loaded)throw new Error("Script is not loaded.");for(var t in r.observers)r.observers[t](r);delete window[e.callbackName]},d.componentDidMount=function(){var r=this,t=this.setupScriptURL(),n=this.asyncScriptLoaderGetScriptLoaderID(),o=e,a=o.globalName,i=o.callbackName,c=o.scriptId;if(a&&"undefined"!==typeof window[a]&&(s[t]={loaded:!0,observers:{}}),s[t]){var d=s[t];return d&&(d.loaded||d.errored)?void this.asyncScriptLoaderHandleLoad(d):void(d.observers[n]=function(e){return r.asyncScriptLoaderHandleLoad(e)})}var p={};p[n]=function(e){return r.asyncScriptLoaderHandleLoad(e)},s[t]={loaded:!1,observers:p};var u=document.createElement("script");for(var f in u.src=t,u.async=!0,e.attributes)u.setAttribute(f,e.attributes[f]);c&&(u.id=c);var l=function(r){if(s[t]){var e=s[t].observers;for(var n in e)r(e[n])&&delete e[n]}};i&&"undefined"!==typeof window&&(window[i]=function(){return r.asyncScriptLoaderTriggerOnScriptLoaded()}),u.onload=function(){var r=s[t];r&&(r.loaded=!0,l((function(e){return!i&&(e(r),!0)})))},u.onerror=function(){var r=s[t];r&&(r.errored=!0,l((function(e){return e(r),!0})))},document.body.appendChild(u)},d.componentWillUnmount=function(){var r=this.__scriptURL;if(!0===e.removeOnUnmount)for(var t=document.getElementsByTagName("script"),n=0;n<t.length;n+=1)t[n].src.indexOf(r)>-1&&t[n].parentNode&&t[n].parentNode.removeChild(t[n]);var o=s[r];o&&(delete o.observers[this.asyncScriptLoaderGetScriptLoaderID()],!0===e.removeOnUnmount&&delete s[r])},d.render=function(){var r=e.globalName,o=this.props,a=(o.asyncScriptOnLoad,o.forwardedRef),i=function(r,e){if(null==r)return{};var t,n,o={},a=Object.keys(r);for(n=0;n<a.length;n++)t=a[n],e.indexOf(t)>=0||(o[t]=r[t]);return o}(o,["asyncScriptOnLoad","forwardedRef"]);return r&&"undefined"!==typeof window&&(i[r]="undefined"!==typeof window[r]?window[r]:void 0),i.ref=a,(0,n.createElement)(t,i)},c}(n.Component),u=(0,n.forwardRef)((function(r,e){return(0,n.createElement)(i,d({},r,{forwardedRef:e}))}));return u.displayName="AsyncScriptLoader("+o+")",u.propTypes={asyncScriptOnLoad:a().func},c()(u,t)}}}}]);
//# sourceMappingURL=react-async-script.66af3aea80c8858ecc2c3ed02e912f38.js.map